"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[4969,2208,405,7301],{1952:function(e,a,t){t.r(a),t.d(a,{default:function(){return b}});var i=t(641);const o={class:"left-panel"},l={class:"right-panel"},s={class:"right-panel-search"};function r(e,a,t,r,c,d){const n=(0,i.g2)("el-option"),u=(0,i.g2)("el-select"),h=(0,i.g2)("el-button"),k=(0,i.g2)("el-icon"),p=(0,i.g2)("el-dropdown-item"),b=(0,i.g2)("el-dropdown-menu"),g=(0,i.g2)("el-dropdown"),m=(0,i.g2)("el-input"),f=(0,i.g2)("el-header"),w=(0,i.g2)("el-table-column"),F=(0,i.g2)("el-button-group"),y=(0,i.g2)("el-popconfirm"),C=(0,i.g2)("scTable"),j=(0,i.g2)("el-main"),v=(0,i.g2)("el-container"),_=(0,i.g2)("cookieDialog"),$=(0,i.g2)("taskDialog"),x=(0,i.g2)("liveDialog"),S=(0,i.gN)("loading");return(0,i.uX)(),(0,i.CE)(i.FK,null,[(0,i.bo)(((0,i.uX)(),(0,i.Wv)(v,{"element-loading-text":"执行中..."},{default:(0,i.k6)((()=>[(0,i.bF)(f,null,{default:(0,i.k6)((()=>[(0,i.Lk)("div",o,[(0,i.bF)(u,{modelValue:c.jObjectSearch.areaId,"onUpdate:modelValue":a[0]||(a[0]=e=>c.jObjectSearch.areaId=e),filterable:"",onChange:d.upsearch,style:{"padding-right":"10px"}},{default:(0,i.k6)((()=>[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(c.area,(e=>((0,i.uX)(),(0,i.Wv)(n,{key:e.Fid,label:e.FName,value:e.Fid},null,8,["label","value"])))),128))])),_:1},8,["modelValue","onChange"]),(0,i.bF)(h,{type:"primary",icon:"el-icon-plus",onClick:a[1]||(a[1]=e=>d.addCookies())},{default:(0,i.k6)((()=>[(0,i.eW)("新增账号")])),_:1}),(0,i.bF)(g,{trigger:"click",class:"m-r"},{dropdown:(0,i.k6)((()=>[(0,i.bF)(b,{class:"drop-common"},{default:(0,i.k6)((()=>[(0,i.bF)(p,{onClick:d.updateCookieExpires,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("更新到期时间")])),_:1},8,["onClick"]),(0,i.bF)(p,{onClick:d.updateCookieTask,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("更新任务信息")])),_:1},8,["onClick"]),(0,i.bF)(p,{divided:""})])),_:1})])),default:(0,i.k6)((()=>[(0,i.bF)(h,{type:"primary"},{default:(0,i.k6)((()=>[(0,i.eW)(" 更新功能  "),(0,i.bF)(k,null,{default:(0,i.k6)((()=>[((0,i.uX)(),(0,i.Wv)((0,i.$y)("el-icon-arrow-down-bold")))])),_:1})])),_:1})])),_:1}),(0,i.bF)(g,{trigger:"click",class:"m-r"},{dropdown:(0,i.k6)((()=>[(0,i.bF)(b,{class:"drop-common"},{default:(0,i.k6)((()=>[(0,i.bF)(p,{onClick:d.startLive,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("  开启直播  ")])),_:1},8,["onClick"]),(0,i.bF)(p,{onClick:d.sendGift,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("  发送礼物  ")])),_:1},8,["onClick"]),(0,i.bF)(p,{onClick:d.sendBulletScreen,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("  弹幕观看  ")])),_:1},8,["onClick"]),(0,i.bF)(p,{divided:""})])),_:1})])),default:(0,i.k6)((()=>[(0,i.bF)(h,{type:"primary"},{default:(0,i.k6)((()=>[(0,i.eW)(" 直播功能  "),(0,i.bF)(k,null,{default:(0,i.k6)((()=>[((0,i.uX)(),(0,i.Wv)((0,i.$y)("el-icon-arrow-down-bold")))])),_:1})])),_:1})])),_:1}),(0,i.bF)(g,{trigger:"click",class:"m-r"},{dropdown:(0,i.k6)((()=>[(0,i.bF)(b,{class:"drop-common"},{default:(0,i.k6)((()=>[(0,i.bF)(p,{onClick:d.receiveDaily,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("领取每日任务")])),_:1},8,["onClick"]),(0,i.bF)(p,{onClick:d.receiveNormal2,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("领取里程任务(20)")])),_:1},8,["onClick"]),(0,i.bF)(p,{divided:""})])),_:1})])),default:(0,i.k6)((()=>[(0,i.bF)(h,{type:"primary"},{default:(0,i.k6)((()=>[(0,i.eW)(" 领取奖励  "),(0,i.bF)(k,null,{default:(0,i.k6)((()=>[((0,i.uX)(),(0,i.Wv)((0,i.$y)("el-icon-arrow-down-bold")))])),_:1})])),_:1})])),_:1})]),(0,i.Lk)("div",l,[(0,i.Lk)("div",s,[(0,i.bF)(m,{modelValue:c.jObjectSearch.search,"onUpdate:modelValue":a[2]||(a[2]=e=>c.jObjectSearch.search=e),placeholder:"账号名称/直播号",clearable:""},null,8,["modelValue"]),(0,i.bF)(h,{type:"primary",icon:"el-icon-search",onClick:d.upsearch},{default:(0,i.k6)((()=>[(0,i.eW)(" 查询")])),_:1},8,["onClick"])])])])),_:1}),(0,i.bF)(j,{class:"nopadding"},{default:(0,i.k6)((()=>[(0,i.bF)(C,{ref:"table",apiObj:c.apiObj,border:"",params:{jObjectSearch:c.jObjectSearch},stripe:"",remoteSort:"",remoteFilter:""},{default:(0,i.k6)((()=>[(0,i.bF)(w,{type:"selection",width:"50"}),(0,i.bF)(w,{label:"功能",fixed:"left","header-align":"center",align:"left",width:"175"},{default:(0,i.k6)((e=>[(0,i.bF)(F,null,{default:(0,i.k6)((()=>["账号已到期"!=e.row.FStatusName?((0,i.uX)(),(0,i.Wv)(h,{key:0,text:"",type:"warning",size:"small",onClick:a=>d.showTask(e.row,e.$index)},{default:(0,i.k6)((()=>[(0,i.eW)("任务信息")])),_:2},1032,["onClick"])):(0,i.Q3)("",!0),"账号已到期"!=e.row.FStatusName?((0,i.uX)(),(0,i.Wv)(h,{key:1,text:"",size:"small",onClick:a=>d.showLive(e.row,e.$index),style:{color:"#3333FF"}},{default:(0,i.k6)((()=>[(0,i.eW)("直播配置")])),_:2},1032,["onClick"])):(0,i.Q3)("",!0)])),_:2},1024)])),_:1}),(0,i.bF)(w,{label:"序号",prop:"FSort",align:"center",width:"85",sortable:""}),(0,i.bF)(w,{label:"错误信息",prop:"FStatusName",align:"center",width:"115","show-overflow-tooltip":"",sortable:""}),(0,i.bF)(w,{label:"账号名称",prop:"FName",align:"center",width:"120","show-overflow-tooltip":""}),(0,i.bF)(w,{label:"直播间地址",prop:"FRoomNo",align:"center",width:"250","show-overflow-tooltip":""}),(0,i.bF)(w,{label:"准备领取",prop:"FTaskName","show-overflow-tooltip":"",align:"center","min-width":"200"}),(0,i.bF)(w,{label:"代理地址",prop:"FProxyAddress","show-overflow-tooltip":"",align:"center",width:"200"}),(0,i.bF)(w,{label:"鱼翅",prop:"FInfo",align:"center",width:"100"}),(0,i.bF)(w,{label:"账号到期时间",prop:"FExpirationTime",align:"center",width:"175",sortable:""}),(0,i.bF)(w,{label:"操作",fixed:"right","header-align":"center",align:"left",width:"170"},{default:(0,i.k6)((e=>[(0,i.bF)(F,null,{default:(0,i.k6)((()=>[(0,i.bF)(h,{text:"",type:"primary",size:"small",onClick:a=>d.editCookie(e.row,e.$index)},{default:(0,i.k6)((()=>[(0,i.eW)("编辑")])),_:2},1032,["onClick"]),(0,i.bF)(y,{title:"确定清空账号数据吗？",onConfirm:a=>d.emptyCookie(e.row,e.$index),width:"250"},{reference:(0,i.k6)((()=>[(0,i.bF)(h,{text:"",type:"danger",size:"small"},{default:(0,i.k6)((()=>[(0,i.eW)("重置")])),_:1})])),_:2},1032,["onConfirm"]),(0,i.bF)(y,{title:"确定删除账号数据吗？",onConfirm:a=>d.delCookie(e.row,e.$index),width:"250"},{reference:(0,i.k6)((()=>[(0,i.bF)(h,{text:"",type:"danger",size:"small"},{default:(0,i.k6)((()=>[(0,i.eW)("删除")])),_:1})])),_:2},1032,["onConfirm"])])),_:2},1024)])),_:1})])),_:1},8,["apiObj","params"])])),_:1})])),_:1})),[[S,c.updateLoading]]),c.dialog.cookies?((0,i.uX)(),(0,i.Wv)(_,{key:0,ref:"cookieDialog",onSuccess:d.upsearch,onClosed:a[3]||(a[3]=e=>c.dialog.cookies=!1)},null,8,["onSuccess"])):(0,i.Q3)("",!0),c.dialog.task?((0,i.uX)(),(0,i.Wv)($,{key:1,ref:"taskDialog",onSuccess:d.upsearch,onClosed:a[4]||(a[4]=e=>{c.dialog.task=!1,d.upsearch()})},null,8,["onSuccess"])):(0,i.Q3)("",!0),c.dialog.live?((0,i.uX)(),(0,i.Wv)(x,{key:2,ref:"liveDialog",onSuccess:d.upsearch,onClosed:a[5]||(a[5]=e=>c.dialog.live=!1)},null,8,["onSuccess"])):(0,i.Q3)("",!0)],64)}var c=t(1132),d=t(1448),n=t(2765),u=t(2826),h={name:"douyuCookies",components:{cookieDialog:d["default"],taskDialog:n["default"],liveDialog:u["default"]},data(){return{apiObj:null,updateLoading:!1,dialog:{cookies:!1,task:!1,live:!1},jObjectSearch:{search:"",areaId:""},area:[]}},async created(){let e=await this.$API.douyuArea.getAreaList.post({jObjectSearch:{}});0==e.code&&(this.area=e.data.rows,this.area.length>0&&(this.jObjectSearch.areaId=this.area[0].Fid)),this.apiObj=this.$API.douyuCookies.getCookiesList},methods:{addCookies(){this.$prompt("一次添加至少1个，做多10个","添加账号",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnPressEscape:!0,closeOnClickModal:!0,autofocus:!0,inputType:"number",inputPattern:/^[1-9]$|^10$/,inputErrorMessage:"至少1个，最多10个"}).then((async({value:e})=>{this.updateLoading=!0;let a=await this.$API.douyuCookies.addCookies.post({jObjectParam:{cookieNum:e}});0==a.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(a.message,"提示",{type:"error"}),this.updateLoading=!1}))},editCookie(e){this.dialog.cookies=!0,this.$nextTick((()=>{this.$refs.cookieDialog.open("edit").setData(e)}))},async delCookie(e){let a=await this.$API.douyuCookies.delCookie.post({jObjectParam:e});0==a.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(a.message,"提示",{type:"error"})},async emptyCookie(e){let a=await this.$API.douyuCookies.emptyCookie.post({jObjectParam:e});0==a.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(a.message,"提示",{type:"error"})},showTask(e){this.dialog.task=!0,this.$nextTick((()=>{this.$refs.taskDialog.open("show").setData({cookieId:e.Fid,areaId:this.jObjectSearch.areaId})}))},showLive(e){this.dialog.live=!0;let a=this.area.find((e=>e.Fid===this.jObjectSearch.areaId)).FName;this.$nextTick((()=>{this.$refs.liveDialog.open("show","直播配置（"+a+"）").setData({cookieId:e.Fid,areaId:this.jObjectSearch.areaId,areaName:a})}))},async execQuartz(e,a=0,t=""){const i=c.Ks.service({lock:!0,text:"执行中...",background:"rgba(0, 0, 0, 0.7)"});try{let i=this.$refs.table.getSelectionRows();if(0==i.length&&(i=(await this.apiObj.post({jObjectSearch:this.jObjectSearch})).data.rows),0==i.length)throw new Error("请添加账号！");let o=await this.$API.douyuQuartz.manualExecQuartz.post({jObjectParam:{array:i,areaId:this.jObjectSearch.areaId,jobName:e,delay:a,param:t}});if(0!=o.code)throw new Error(o.message);this.$message.success(o.message),this.upsearch()}catch(o){this.$alert(o.message,"提示",{type:"error"})}i.close()},async updateCookieExpires(){try{this.updateLoading=!0;let e=await this.$API.douyuCookies.updateCookieExpires.post({jObjectParam:{}});if(this.updateLoading=!1,0!=e.code)throw new Error(e.message);this.$message.success("更新成功！"),this.upsearch()}catch(e){this.$alert(e.message,"提示",{type:"error"})}},upsearch(){this.$refs.table.upData({jObjectSearch:this.jObjectSearch})},async startLive(){await this.execQuartz("开启直播")},async sendGift(){await this.execQuartz("发送礼物")},async sendBulletScreen(){await this.execQuartz("弹幕观看")},async receiveDaily(){await this.execQuartz("领取每日任务")},async receiveNormal2(){await this.execQuartz("领取里程任务",0,20)},async updateCookieTask(){await this.execQuartz("更新任务信息",1)}}},k=t(6262);const p=(0,k.A)(h,[["render",r]]);var b=p}}]);