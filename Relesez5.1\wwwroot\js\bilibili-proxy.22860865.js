"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[5664],{1342:function(e,t,a){a.r(t),a.d(t,{default:function(){return k}});var s=a(641),i=a(2644);const l={class:"custom-tree-node",style:{margin:"5px",color:"#000",padding:"5px"}},o={class:"custom-tree-node"},r={key:0},c={class:"left-panel"},n={class:"right-panel"},d={class:"right-panel-search"};function p(e,t,a,p,h,u){const g=(0,s.g2)("el-input"),b=(0,s.g2)("el-header"),m=(0,s.g2)("el-button"),y=(0,s.g2)("el-tree"),k=(0,s.g2)("el-main"),f=(0,s.g2)("el-footer"),F=(0,s.g2)("el-container"),x=(0,s.g2)("el-aside"),v=(0,s.g2)("sc-upload-file"),w=(0,s.g2)("el-popconfirm"),j=(0,s.g2)("el-table-column"),P=(0,s.g2)("el-switch"),$=(0,s.g2)("el-button-group"),S=(0,s.g2)("scTable"),_=(0,s.g2)("saveDialog"),C=(0,s.g2)("groupDialog"),O=(0,s.gN)("loading");return(0,s.uX)(),(0,s.CE)(s.FK,null,[(0,s.bo)(((0,s.uX)(),(0,s.Wv)(F,{"element-loading-text":"请稍等..."},{default:(0,s.k6)((()=>[(0,s.bF)(x,{width:"300px"},{default:(0,s.k6)((()=>[(0,s.bF)(F,null,{default:(0,s.k6)((()=>[(0,s.bF)(b,null,{default:(0,s.k6)((()=>[(0,s.bF)(g,{placeholder:"输入关键字进行过滤",modelValue:h.filterText,"onUpdate:modelValue":t[0]||(t[0]=e=>h.filterText=e),clearable:""},null,8,["modelValue"])])),_:1}),(0,s.bF)(k,{class:"nopadding"},{default:(0,s.k6)((()=>[(0,s.bo)(((0,s.uX)(),(0,s.Wv)(y,{ref:"tree",class:"custom-tree menu","node-key":"Fid",props:{label:"FName",value:"Fid"},data:h.treeData,"current-node-key":h.jObjectSearch.groupId,"check-on-click-node":!0,"show-checkbox":!1,"highlight-current":!0,"expand-on-click-node":!1,"filter-node-method":u.filterNode,onNodeClick:u.treeClick,"default-expand-all":!0},{default:(0,s.k6)((({data:e})=>[(0,s.Lk)("span",l,[(0,s.Lk)("span",o,[(0,s.Lk)("span",{style:(0,i.Tr)(0==e.FEnable?"color:red":"")},(0,i.v_)(e.FName),5),"所有分组"!=e.FName&&"默认分组"!=e.FName?((0,s.uX)(),(0,s.CE)("span",r,[1==e.FEnable?((0,s.uX)(),(0,s.Wv)(m,{key:0,class:"do",link:"",type:"warning",size:"small",onClick:t=>u.stopGroup(e)},{default:(0,s.k6)((()=>[(0,s.eW)("停用")])),_:2},1032,["onClick"])):(0,s.Q3)("",!0),0==e.FEnable?((0,s.uX)(),(0,s.Wv)(m,{key:1,class:"do",link:"",type:"success",size:"small",onClick:t=>u.startGroup(e)},{default:(0,s.k6)((()=>[(0,s.eW)("启用")])),_:2},1032,["onClick"])):(0,s.Q3)("",!0),(0,s.bF)(m,{class:"do",link:"",type:"primary",size:"small",onClick:t=>u.editGroup(e)},{default:(0,s.k6)((()=>[(0,s.eW)("编辑")])),_:2},1032,["onClick"]),(0,s.bF)(m,{class:"do",link:"",type:"danger",size:"small",style:{"margin-left":"10px"},onClick:t=>u.delGroup(e)},{default:(0,s.k6)((()=>[(0,s.eW)("删除")])),_:2},1032,["onClick"])])):(0,s.Q3)("",!0)])])])),_:1},8,["data","current-node-key","filter-node-method","onNodeClick"])),[[O,h.treeLoading]])])),_:1}),(0,s.bF)(f,{style:{height:"51px","text-align":"center"}},{default:(0,s.k6)((()=>[(0,s.bF)(m,{type:"primary",size:"small",icon:"el-icon-plus",style:{width:"90%"},onClick:t[1]||(t[1]=e=>u.addGroup())},{default:(0,s.k6)((()=>[(0,s.eW)("新增分组")])),_:1})])),_:1})])),_:1})])),_:1}),(0,s.bF)(F,null,{default:(0,s.k6)((()=>[(0,s.bo)(((0,s.uX)(),(0,s.Wv)(F,null,{default:(0,s.k6)((()=>[(0,s.bF)(b,null,{default:(0,s.k6)((()=>[(0,s.Lk)("div",c,[(0,s.bF)(m,{type:"primary",icon:"el-icon-plus",onClick:t[2]||(t[2]=e=>u.add())},{default:(0,s.k6)((()=>[(0,s.eW)("新增代理")])),_:1}),(0,s.bF)(m,{type:"primary",icon:"el-icon-document",onClick:t[3]||(t[3]=e=>u.download())},{default:(0,s.k6)((()=>[(0,s.eW)("下载模版")])),_:1}),(0,s.bF)(v,{multiple:!0,"show-file-list":!1,maxSize:10380902,onSuccess:u.onSuccess,params:{jObjectParam:{path:"Bili\\Other"}},style:{width:"131px","padding-left":"6px"}},{default:(0,s.k6)((()=>[(0,s.bF)(m,{type:"primary",icon:"el-icon-upload",disabled:h.isSaveing},{default:(0,s.k6)((()=>[(0,s.eW)("导入代理")])),_:1},8,["disabled"])])),_:1},8,["onSuccess"]),(0,s.bF)(m,{type:"primary",icon:"el-icon-download",onClick:t[4]||(t[4]=e=>u.exportProxy())},{default:(0,s.k6)((()=>[(0,s.eW)("导出代理")])),_:1}),(0,s.bF)(m,{type:"primary",icon:"el-icon-position",onClick:t[5]||(t[5]=e=>u.testAll())},{default:(0,s.k6)((()=>[(0,s.eW)("测试代理")])),_:1}),(0,s.bF)(m,{type:"primary",onClick:t[6]||(t[6]=e=>u.share())},{default:(0,s.k6)((()=>[(0,s.eW)("共享代理")])),_:1}),(0,s.bF)(w,{title:"确定取消共享代理吗？",onConfirm:t[7]||(t[7]=e=>u.cancel()),width:"200px"},{reference:(0,s.k6)((()=>[(0,s.bF)(m,{type:"primary"},{default:(0,s.k6)((()=>[(0,s.eW)("取消共享")])),_:1})])),_:1})]),(0,s.Lk)("div",n,[(0,s.Lk)("div",d,[(0,s.bF)(g,{modelValue:h.jObjectSearch.search,"onUpdate:modelValue":t[8]||(t[8]=e=>h.jObjectSearch.search=e),placeholder:"代理地址/代理账号",clearable:""},null,8,["modelValue"]),(0,s.bF)(m,{type:"primary",icon:"el-icon-search",onClick:u.upsearch},{default:(0,s.k6)((()=>[(0,s.eW)(" 查询")])),_:1},8,["onClick"])])])])),_:1}),(0,s.bF)(k,{class:"nopadding"},{default:(0,s.k6)((()=>[(0,s.bF)(S,{ref:"table",apiObj:h.apiObj,border:"",params:{jObjectSearch:h.jObjectSearch},stripe:"",remoteSort:"",remoteFilter:""},{default:(0,s.k6)((()=>[(0,s.bF)(j,{label:"序号",sortable:"",prop:"FSort",width:"80",align:"center"}),(0,s.bF)(j,{label:"代理地址",sortable:"",prop:"FAddress",align:"center",width:"230","show-overflow-tooltip":""}),(0,s.bF)(j,{label:"代理账号",prop:"FUserName",align:"center",width:"140","show-overflow-tooltip":""}),(0,s.bF)(j,{label:"代理密码",prop:"FPassword",align:"center",width:"140","show-overflow-tooltip":""}),(0,s.bF)(j,{label:"账号数量",sortable:"",width:"100",prop:"FCount",align:"center","show-overflow-tooltip":""}),(0,s.bF)(j,{label:"代理信息",sortable:"",prop:"FMsg","header-align":"center","show-overflow-tooltip":""}),(0,s.bF)(j,{label:"是否启用（禁用会使用本地网络）",prop:"FEnable",align:"center",width:"140"},{header:(0,s.k6)((()=>[(0,s.eW)(" 是否启用   "),(0,s.bF)(P,{modelValue:h.batch,"onUpdate:modelValue":t[9]||(t[9]=e=>h.batch=e),onChange:t[10]||(t[10]=e=>u.allSwitch()),"active-value":1,"inactive-value":0,style:{height:"23px","padding-bottom":"2px"}},null,8,["modelValue"])])),default:(0,s.k6)((e=>[(0,s.bF)(P,{modelValue:e.row.FEnable,"onUpdate:modelValue":t=>e.row.FEnable=t,onChange:t=>u.enableSwitch(t,e.row),loading:e.row.$enable,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue","onChange","loading"])])),_:1}),(0,s.bF)(j,{label:"操作",fixed:"right","header-align":"center",align:"center",width:"200"},{default:(0,s.k6)((e=>[(0,s.bF)($,null,{default:(0,s.k6)((()=>[(0,s.bF)(m,{text:"",type:"warning",size:"small",onClick:t=>u.test(e.row,e.$index)},{default:(0,s.k6)((()=>[(0,s.eW)("测试")])),_:2},1032,["onClick"]),(0,s.bF)(m,{text:"",type:"primary",size:"small",onClick:t=>u.edit(e.row,e.$index)},{default:(0,s.k6)((()=>[(0,s.eW)("编辑")])),_:2},1032,["onClick"]),(0,s.bF)(w,{title:"确定删除吗？",onConfirm:t=>u.del(e.row,e.$index)},{reference:(0,s.k6)((()=>[(0,s.bF)(m,{text:"",type:"danger",size:"small"},{default:(0,s.k6)((()=>[(0,s.eW)("删除")])),_:1})])),_:2},1032,["onConfirm"])])),_:2},1024)])),_:1})])),_:1},8,["apiObj","params"])])),_:1})])),_:1})),[[O,h.isSaveing]])])),_:1})])),_:1})),[[O,h.isSaveing]]),h.dialog.save?((0,s.uX)(),(0,s.Wv)(_,{key:0,ref:"saveDialog",onSuccess:u.upsearch,onClosed:t[11]||(t[11]=e=>h.dialog.save=!1)},null,8,["onSuccess"])):(0,s.Q3)("",!0),h.dialog.group?((0,s.uX)(),(0,s.Wv)(C,{key:1,ref:"groupDialog",onSuccess:u.upsearchTree,onClosed:t[12]||(t[12]=e=>h.dialog.group=!1)},null,8,["onSuccess"])):(0,s.Q3)("",!0)],64)}var h=a(3326),u=a(4097),g=a(3959),b={name:"biliProxy",components:{saveDialog:h["default"],groupDialog:u["default"]},data(){return{filterText:"",treeData:[],treeLoading:!0,apiObj:this.$API.biliProxy.getProxyList,isSaveing:!1,dialog:{save:!1},batch:1,jObjectSearch:{search:"",groupId:0}}},watch:{filterText(e){this.$refs.tree.filter(e)}},async created(){await this.upsearchTree()},async mounted(){},methods:{filterNode(e,t){return!e||-1!==t.FName.indexOf(e)},treeClick(){this.jObjectSearch.groupId=String(this.$refs.tree.getCurrentKey()),this.upsearch()},add(){this.dialog.save=!0,this.$nextTick((()=>{this.$refs.saveDialog.open("add").setData({Fid:0,FGroupId:this.jObjectSearch.groupId})}))},edit(e){this.dialog.save=!0,this.$nextTick((()=>{this.$refs.saveDialog.open("edit").setData(e)}))},async test(e){this.isSaveing=!0;let t=await this.$API.biliProxy.testProxy.post({jObjectParam:e});this.isSaveing=!1,0==t.code?this.$message.success("请看控制台"):this.$alert(t.message,"提示",{type:"error"})},async testAll(){this.isSaveing=!0,this.$message.success("请看控制台");let e=await this.$API.biliProxy.testProxy.post({jObjectParam:{type:"TestAll",groupId:this.jObjectSearch.groupId}});this.isSaveing=!1,0==e.code?this.upsearch():this.$alert(e.message,"提示",{type:"error"})},async del(e){let t=await this.$API.biliProxy.delProxy.post({jObjectParam:{Fid:e.Fid}});0==t.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(t.message,"提示",{type:"error"})},async enableSwitch(e,t){t.$enable=!0;let a=await this.$API.biliProxy.saveProxy.post({jObjectParam:t});0==a.code?this.$message.success("操作成功"):this.$alert(a.message,"提示",{type:"error"}),this.upsearch()},upsearch(){this.$refs.table.upData({jObjectSearch:this.jObjectSearch})},async upsearchTree(e=0){this.treeLoading=!0;let t=await this.$API.biliProxy.getProxyGroupList.post();this.treeLoading=!1,0==t.code&&(this.treeData=t.data,this.$nextTick((async()=>{this.jObjectSearch.groupId=e,await this.upsearch()})))},async allSwitch(){await this.$API.biliProxy.saveProxy.post({jObjectParam:{type:"all",FEnable:this.batch,groupId:this.jObjectSearch.groupId}}),this.upsearch()},async delGroup(e){this.$confirm("确认删除勾选分组？","提示",{type:"warning"}).then((async()=>{let t=await this.$API.biliProxy.delGroup.post({jObjectParam:{selected:e.Fid}});0==t.code?(this.$message.success("操作成功"),await this.upsearchTree()):this.$alert(t.message,"提示",{type:"error"})}))},addGroup(){this.dialog.group=!0,this.$nextTick((()=>{this.$refs.groupDialog.open("add")}))},editGroup(e){this.dialog.group=!0,this.$nextTick((()=>{this.$refs.groupDialog.open("edit").setData(e)}))},async onSuccess(e){0==e.code?(this.isSaveing=!0,e=await this.$API.biliProxy.importProxy.post({jObjectParam:{src:e.data.src,groupId:this.jObjectSearch.groupId}}),this.isSaveing=!1,0==e.code?(this.$message.success("导入成功！"),this.upsearch()):this.$alert(e.message,"提示",{type:"error"})):this.$alert(e.message,"提示",{type:"error"})},download(){const e=[["序号","代理地址"],["0","socks5://************:8088:账号:密码"]],t=g.Wp.aoa_to_sheet(e),a=g.Wp.book_new();g.Wp.book_append_sheet(a,t,"Sheet1"),g._h(a,"代理导入模版.xls")},async stopGroup(e){this.isSaveing=!0;var t=await this.$API.biliProxy.saveGroup.post({jObjectParam:{Fid:e.Fid,FEnable:0}});this.isSaveing=!1,0==t.code?this.upsearchTree(e.Fid):this.$alert(t.message,"提示",{type:"error"})},async startGroup(e){this.isSaveing=!0;var t=await this.$API.biliProxy.saveGroup.post({jObjectParam:{Fid:e.Fid,FEnable:1}});this.isSaveing=!1,0==t.code?this.upsearchTree(e.Fid):this.$alert(t.message,"提示",{type:"error"})},async exportProxy(){this.isSaveing=!0;let e=await this.$API.biliProxy.exportProxy.post({jObjectParam:this.jObjectSearch});if(this.isSaveing=!1,0==e.code){let t=document.createElement("a");t.style="display: none",t.target="_blank",t.download="Cookies",t.href=e.data,document.body.appendChild(t),t.click(),document.body.removeChild(t),this.upsearch()}else this.$alert(e.message,"提示",{type:"error"})},async share(){this.$prompt("请输入您的名称（用于展示）","确认共享当前分组的代理吗？",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnPressEscape:!0,closeOnClickModal:!0,autofocus:!0,inputPattern:/\S/,inputErrorMessage:"名称不能为空"}).then((async({value:e})=>{let t=await this.$API.biliProxy.share.post({jObjectParam:{groupId:this.jObjectSearch.groupId,name:e}});0==t.code?this.$message.success("共享成功！"):this.$alert(t.message,"提示",{type:"error"})}))},async cancel(){let e=await this.$API.biliProxy.cancel.post();0==e.code?this.$message.success("取消成功！"):this.$alert(e.message,"提示",{type:"error"})}}},m=a(6262);const y=(0,m.A)(b,[["render",p],["__scopeId","data-v-47e65e8c"]]);var k=y}}]);