"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[573,8734],{3499:function(e,t,a){a.r(t),a.d(t,{default:function(){return g}});var r=a(641),s=a(2644);const i={class:"custom-tree-node"},l={class:"left-panel"},o={class:"right-panel"};function n(e,t,a,n,u,d){const c=(0,r.g2)("el-input"),h=(0,r.g2)("el-header"),p=(0,r.g2)("el-button"),g=(0,r.g2)("el-button-group"),b=(0,r.g2)("el-tree"),f=(0,r.g2)("el-main"),m=(0,r.g2)("el-footer"),F=(0,r.g2)("el-container"),k=(0,r.g2)("el-aside"),w=(0,r.g2)("el-popconfirm"),y=(0,r.g2)("el-table-column"),$=(0,r.g2)("el-tag"),j=(0,r.g2)("el-switch"),C=(0,r.g2)("scTable"),_=(0,r.g2)("jobDialog"),S=(0,r.g2)("groupDialog"),z=(0,r.gN)("loading");return(0,r.uX)(),(0,r.CE)(r.FK,null,[(0,r.bo)(((0,r.uX)(),(0,r.Wv)(F,{"element-loading-text":"请稍等..."},{default:(0,r.k6)((()=>[(0,r.bF)(k,{width:"220px"},{default:(0,r.k6)((()=>[(0,r.bF)(F,null,{default:(0,r.k6)((()=>[(0,r.bF)(h,null,{default:(0,r.k6)((()=>[(0,r.bF)(c,{placeholder:"输入关键字进行过滤",modelValue:u.filterText,"onUpdate:modelValue":t[0]||(t[0]=e=>u.filterText=e),clearable:""},null,8,["modelValue"])])),_:1}),(0,r.bF)(f,{class:"nopadding"},{default:(0,r.k6)((()=>[(0,r.bo)(((0,r.uX)(),(0,r.Wv)(b,{ref:"tree",class:"custom-tree menu","node-key":"Fid",props:{label:"FName",value:"Fid"},data:u.treeData,"highlight-current":!0,"expand-on-click-node":!1,"filter-node-method":d.filterNode,onNodeClick:d.treeClick,"default-expand-all":!0},{default:(0,r.k6)((({data:e})=>[(0,r.Lk)("span",i,[(0,r.Lk)("span",{style:(0,s.Tr)("color:"+(1==e.FStatus?"green":"init"))},(0,s.v_)(e.FName),5),(0,r.Lk)("span",null,[(0,r.bF)(g,null,{default:(0,r.k6)((()=>[(0,r.bF)(p,{class:"do",link:"",type:"primary",size:"small",style:{"margin-left":"10px","margin-right":"10px"},onClick:t=>d.editQuartzGroup(e)},{default:(0,r.k6)((()=>[(0,r.eW)("编辑")])),_:2},1032,["onClick"])])),_:2},1024)])])])),_:1},8,["data","filter-node-method","onNodeClick"])),[[z,u.treeLoading]])])),_:1}),(0,r.bF)(m,{style:{height:"51px","text-align":"center"}},{default:(0,r.k6)((()=>[(0,r.bF)(p,{type:"primary",size:"small",icon:"el-icon-plus",style:{width:"45%"},onClick:t[1]||(t[1]=e=>d.addQuartzGroup())},{default:(0,r.k6)((()=>[(0,r.eW)("新增分组")])),_:1}),(0,r.bF)(p,{type:"danger",size:"small",icon:"el-icon-delete",style:{width:"45%"},onClick:t[2]||(t[2]=e=>d.delQuartzGroup())},{default:(0,r.k6)((()=>[(0,r.eW)("删除分组")])),_:1})])),_:1})])),_:1})])),_:1}),(0,r.bF)(F,null,{default:(0,r.k6)((()=>[(0,r.bF)(h,null,{default:(0,r.k6)((()=>[(0,r.Lk)("div",l,[(0,r.bF)(p,{type:"primary",icon:"el-icon-plus",onClick:t[3]||(t[3]=e=>d.addJob())},{default:(0,r.k6)((()=>[(0,r.eW)("新增任务")])),_:1}),1==u.status?((0,r.uX)(),(0,r.Wv)(w,{key:0,title:"确定关闭分组？",onConfirm:d.shutdownGroup},{reference:(0,r.k6)((()=>[(0,r.bF)(p,{type:"danger"},{default:(0,r.k6)((()=>[(0,r.eW)("关闭分组")])),_:1})])),_:1},8,["onConfirm"])):0==u.status?((0,r.uX)(),(0,r.Wv)(w,{key:1,title:"确定启动分组？",onConfirm:d.startGroup},{reference:(0,r.k6)((()=>[(0,r.bF)(p,{type:"success"},{default:(0,r.k6)((()=>[(0,r.eW)("启动分组")])),_:1})])),_:1},8,["onConfirm"])):(0,r.Q3)("",!0)]),(0,r.Lk)("div",o,[(0,r.bF)(c,{modelValue:u.jObjectSearch.search,"onUpdate:modelValue":t[4]||(t[4]=e=>u.jObjectSearch.search=e),placeholder:"任务名称",class:"input-with-select",onChange:d.upsearch},null,8,["modelValue","onChange"]),(0,r.bF)(p,{type:"primary",onClick:d.upsearch,icon:"el-icon-search"},{default:(0,r.k6)((()=>[(0,r.eW)("查询")])),_:1},8,["onClick"])])])),_:1}),(0,r.bF)(f,{class:"nopadding"},{default:(0,r.k6)((()=>[(0,r.bF)(C,{ref:"table",apiObj:u.taskApiObj,"highlight-current-row":"","row-key":"Fid",params:{jObjectSearch:u.jObjectSearch},border:""},{default:(0,r.k6)((()=>[(0,r.bF)(y,{label:"功能",fixed:"left",align:"center",width:"80"},{default:(0,r.k6)((e=>[(0,r.bF)(g,null,{default:(0,r.k6)((()=>["等待执行"==e.row.FStatus||"未启动"==e.row.FStatus?((0,r.uX)(),(0,r.Wv)(w,{key:0,title:"确认执行？",onConfirm:t=>d.execJob(e.row,e.$index)},{reference:(0,r.k6)((()=>[(0,r.bF)(p,{text:"",size:"small",style:{color:"#006600"}},{default:(0,r.k6)((()=>[(0,r.eW)("执行")])),_:1})])),_:2},1032,["onConfirm"])):(0,r.Q3)("",!0),"正在执行"==e.row.FStatus?((0,r.uX)(),(0,r.Wv)(w,{key:1,title:"确认停止？",onConfirm:t=>d.stopJob(e.row,e.$index)},{reference:(0,r.k6)((()=>[(0,r.bF)(p,{text:"",type:"warning",size:"small"},{default:(0,r.k6)((()=>[(0,r.eW)("停止")])),_:1})])),_:2},1032,["onConfirm"])):(0,r.Q3)("",!0),"未启动"==e.row.FStatus&&1==e.row.FGroupStatus?((0,r.uX)(),(0,r.Wv)(w,{key:2,title:"确认启动？",onConfirm:t=>d.startJob(e.row,e.$index)},{reference:(0,r.k6)((()=>[(0,r.bF)(p,{text:"",type:"success",size:"small"},{default:(0,r.k6)((()=>[(0,r.eW)("启动任务")])),_:1})])),_:2},1032,["onConfirm"])):(0,r.Q3)("",!0)])),_:2},1024)])),_:1}),(0,r.bF)(y,{label:"状态",prop:"FStatus",align:"center",width:"100"},{default:(0,r.k6)((e=>[(0,r.bF)($,{type:"等待执行"==e.row.FStatus?"":"正在执行"==e.row.FStatus?"success":"已停用"==e.row.FStatus?"danger":"warning"},{default:(0,r.k6)((()=>[(0,r.eW)((0,s.v_)(e.row.FStatus),1)])),_:2},1032,["type"])])),_:1}),(0,r.bF)(y,{label:"执行时间",prop:"FExecTime",align:"center",width:"80"}),(0,r.bF)(y,{label:"执行时长",prop:"FSecondsName",align:"center",width:"80"}),(0,r.bF)(y,{label:"随机时间",prop:"FDifferenceName",align:"center",width:"80"}),(0,r.bF)(y,{label:"执行类型",prop:"FType",align:"center",width:"80"}),(0,r.bF)(y,{label:"任务名称",prop:"FJobName",align:"center",width:"150"}),(0,r.bF)(y,{label:"分区名称",prop:"FAreaName",align:"center",width:"150"}),(0,r.bF)(y,{label:"账号名称",prop:"FCookieName","header-align":"center",align:"left","show-overflow-tooltip":""}),(0,r.bF)(y,{label:"参数标签",prop:"FJobLabel",align:"center",width:"80","show-overflow-tooltip":""}),(0,r.bF)(y,{label:"参数内容",prop:"FParam",align:"center",width:"150","show-overflow-tooltip":""}),(0,r.bF)(y,{label:"是否启用",prop:"FEnable",align:"center",width:"100"},{default:(0,r.k6)((e=>[(0,r.bF)(j,{modelValue:e.row.FEnable,"onUpdate:modelValue":t=>e.row.FEnable=t,onChange:t=>d.enableSwitch(t,e.row),loading:e.row.$enable,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue","onChange","loading"])])),_:1}),(0,r.bF)(y,{label:"操作",fixed:"right",align:"center",width:"130"},{default:(0,r.k6)((e=>[(0,r.bF)(g,null,{default:(0,r.k6)((()=>[(0,r.bF)(p,{text:"",type:"primary",size:"small",onClick:t=>d.editJob(e.row,e.$index)},{default:(0,r.k6)((()=>[(0,r.eW)("编辑")])),_:2},1032,["onClick"]),(0,r.bF)(w,{title:"确定删除吗？",onConfirm:t=>d.delJob(e.row,e.$index)},{reference:(0,r.k6)((()=>[(0,r.bF)(p,{text:"",type:"danger",size:"small"},{default:(0,r.k6)((()=>[(0,r.eW)("删除")])),_:1})])),_:2},1032,["onConfirm"])])),_:2},1024)])),_:1})])),_:1},8,["apiObj","params"])])),_:1})])),_:1})])),_:1})),[[z,u.loading]]),u.dialog.job?((0,r.uX)(),(0,r.Wv)(_,{key:0,ref:"jobDialog",onSuccess:d.upsearch,onClosed:t[5]||(t[5]=e=>u.dialog.job=!1)},null,8,["onSuccess"])):(0,r.Q3)("",!0),u.dialog.group?((0,r.uX)(),(0,r.Wv)(S,{key:1,ref:"groupDialog",onSuccess:d.upsearchTree,onClosed:t[6]||(t[6]=e=>u.dialog.group=!1)},null,8,["onSuccess"])):(0,r.Q3)("",!0)],64)}var u=a(8120),d=a(7493),c={name:"biliQuartz",components:{jobDialog:u["default"],groupDialog:d["default"]},data(){return{dialog:{job:!1,group:!1},filterText:"",treeData:[],defaultTreeValue:"",treeLoading:!0,taskApiObj:null,loading:!1,status:null,jObjectSearch:{search:"",groupId:0}}},watch:{filterText(e){this.$refs.tree.filter(e)}},async created(){},mounted(){this.getTree()},methods:{async getTree(){let e=await this.$API.biliQuartzGroup.getQuartzGroupList.post({jObjectSearch:{}});this.treeLoading=!1,this.treeData=e.data.rows;var t=this.treeData[0];t&&(this.$nextTick((()=>{this.$refs.tree.setCurrentKey(t.Fid)})),this.status=t.FStatus,this.jObjectSearch.groupId=t.Fid),this.taskApiObj=this.$API.biliQuartz.getQuartzList},filterNode(e,t){return!e||-1!==t.FName.indexOf(e)},addQuartzGroup(){this.dialog.group=!0,this.$nextTick((()=>{this.$refs.groupDialog.open("add")}))},editQuartzGroup(e){this.dialog.group=!0,this.$nextTick((()=>{this.$refs.groupDialog.open("edit").setData(e)}))},delQuartzGroup(){let e=this.$refs["tree"].getCurrentNode();e&&this.$confirm("确认删除 "+e.FName+" ？","提示",{type:"warning"}).then((async()=>{let t=await this.$API.biliQuartzGroup.delQuartzGroup.post({jObjectParam:{id:e.Fid}});0==t.code?(this.$message.success("操作成功"),await this.upsearchTree()):this.$alert(t.message,"提示",{type:"error"})}))},addJob(){let e=this.$refs["tree"].getCurrentNode();e?(this.dialog.job=!0,this.$nextTick((()=>{this.$refs.jobDialog.open("add").setData({FGroupId:e.Fid})}))):this.$alert("请选择任务分组！","提示",{type:"error"})},editJob(e){this.dialog.job=!0,this.$nextTick((()=>{this.$refs.jobDialog.open("edit").setData(e)}))},async delJob(e){this.loading=!0;let t=await this.$API.biliQuartz.delQuartz.post({jObjectParam:e});0==t.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(t.message,"提示",{type:"error"}),this.loading=!1},async enableSwitch(e,t){t.$enable=!0;let a=await this.$API.biliQuartz.enableSwitch.post({jObjectParam:t});0==a.code?this.$message.success("操作成功"):this.$alert(a.message,"提示",{type:"error"}),a.FEnable=a.data,1==a.data?1==this.status?t.FStatus="等待执行":t.FStatus="未启动":t.FStatus="已停用",delete t.$enable},async shutdownJob(e){this.loading=!0;let t=await this.$API.biliQuartz.shutdownQuartz.post({jObjectParam:e});0==t.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(t.message,"提示",{type:"error"}),this.loading=!1},async shutdownGroup(){this.loading=!0;let e=await this.$API.biliQuartzGroup.shutdownGroup.post({jObjectParam:{id:this.$refs["tree"].getCurrentKey()}});0==e.code?(this.$message.success("操作成功"),await this.upsearchTree()):this.$alert(e.message,"提示",{type:"error"}),this.loading=!1},async startGroup(){this.loading=!0;let e=await this.$API.biliQuartzGroup.startGroup.post({jObjectParam:{id:this.$refs["tree"].getCurrentKey()}});0==e.code?(this.$message.success("操作成功"),await this.upsearchTree()):this.$alert(e.message,"提示",{type:"error"}),this.loading=!1},async startJob(e){this.loading=!0;let t=await this.$API.biliQuartz.startQuartz.post({jObjectParam:e});0==t.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(t.message,"提示",{type:"error"}),this.loading=!1},async execJob(e){this.loading=!0;let t=await this.$API.biliQuartz.execQuartz.post({jObjectParam:e});0==t.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(t.message,"提示",{type:"error"}),this.loading=!1},async stopJob(e){this.loading=!0;let t=await this.$API.biliQuartz.stopQuartz.post({jObjectParam:e});0==t.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(t.message,"提示",{type:"error"}),this.loading=!1},treeClick(e){this.jObjectSearch.groupId=e.Fid,this.status=e.FStatus,this.upsearch()},upsearch(){this.$refs.table.upData({jObjectSearch:this.jObjectSearch})},async upsearchTree(){let e=this.$refs["tree"].getCurrentKey(),t=await this.$API.biliQuartzGroup.getQuartzGroupList.post({jObjectSearch:{}});this.treeLoading=!1,this.treeData=t.data.rows,void 0==this.treeData.find((t=>t.Fid==e))&&this.treeData.length>0&&(e=this.treeData[0].Fid,this.status=this.treeData[0].FStatus),this.$nextTick((()=>{this.$refs["tree"].setCurrentKey(e),this.status=this.$refs["tree"].getCurrentNode().FStatus})),this.jObjectSearch.groupId=e,this.upsearch()}}},h=a(6262);const p=(0,h.A)(c,[["render",n],["__scopeId","data-v-0f46db93"]]);var g=p}}]);