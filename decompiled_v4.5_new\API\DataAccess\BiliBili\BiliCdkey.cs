using System;
using System.CodeDom.Compiler;
using System.Data;
using System.Linq;
using System.Text.RegularExpressions;
using System.Text.RegularExpressions.Generated;
using API.Common;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.BiliBili;

public class BiliCdkey
{
	public static DataTable GetCdkeyList(string status, string areaId, string cookieId, string activityId, string search, string type, string taskId, string num, int userId, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		DataTable dataTable2;
		if (type == "预览")
		{
			string areaName = BiliArea.GetAreaName(areaId);
			string sSql = " SELECT * FROM TAreaTask WHERE  FAreaId=" + areaId + " AND FDaily=0 AND FEnable=1 ORDER BY FSort";
			DataTable dataTable = SQLHelper.BiliLocalDB.RunSqlDt(sSql);
			sSql = "";
			for (int i = 0; i < dataTable.Rows.Count; i++)
			{
				string text = dataTable.Rows[i]["FAwardId"].ToString() ?? throw new Exception("未找到FAwardId！");
				string text2 = dataTable.Rows[i]["FTaskName"].ToString() ?? throw new Exception("未找到FTaskName！");
				string text3 = dataTable.Rows[i]["FAwardName"].ToString() ?? throw new Exception("未找到FAwardName！");
				if (sSql == "")
				{
					sSql = " DECLARE @TT TABLE (Fid INT,FAreaName NVARCHAR(100),FCookieName NVARCHAR(100),FTaskName NVARCHAR(100),FName NVARCHAR(100),FCdkey  NVARCHAR(100),FDate DATETIME)";
				}
				sSql += " INSERT @TT";
				sSql += " SELECT TOP 1 T1.Fid,T2.FName AS 分区名称,T3.FName AS 账号名称,T4.FTaskName AS 任务名称, T1.FName AS Cdkey名称,T1.FCdkey AS Cdkey,T1.FDate AS 领取时间";
				sSql += " FROM TCdkey T1";
				sSql += " LEFT JOIN TArea T2 ON T2.Fid=T1.FAreaId";
				sSql += " LEFT JOIN TCookies T3 ON T3.Fid=T1.FCookieId";
				sSql += " LEFT JOIN TAreaTask T4 ON T4.FAreaId=T1.FAreaId AND T4.FAwardId=T1.FAwardId";
				sSql += " LEFT JOIN (SELECT FCookieId, COUNT(*) AS FCount FROM TCdkey WHERE FAwardId IN (SELECT FAwardId FROM TAreaTask WHERE FAreaId=662 AND FDaily=0)";
				sSql += "            AND FStatus=0 AND FAreaId=662 GROUP BY FCookieId) T5 ON T5.FCookieId=T1.FCookieId";
				sSql = sSql + " WHERE T1.FUserId=" + userId + " AND T1.FAreaId=" + areaId + " AND T1.FAwardId=" + text;
				sSql += " AND T4.FDaily=0 AND T1.FStatus=0 ORDER BY T5.FCount DESC";
				sSql += " INSERT @TT";
				sSql = sSql + " SELECT TOP (ABS(" + (i + 1) + " - (SELECT COUNT(*) FROM @TT))) 0,'" + areaName + "' AS 分区名称,'' AS 账号名称,'" + text2 + "' AS 任务名称,'" + text3 + "' AS Cdkey名称,'' AS Cdkey,NULL AS 领取时间";
			}
			sSql += " SELECT * FROM @TT";
			dataTable2 = SQLHelper.BiliLocalDB.RunSqlDt(sSql);
			int num2 = 0;
			for (int j = 0; j < dataTable2.Rows.Count; j++)
			{
				string input = dataTable2.Rows[j]["FName"].ToString() ?? "";
				string text4 = dataTable2.Rows[j]["FCdkey"].ToString() ?? "";
				MatchCollection matchCollection = MyRegex().Matches(input);
				if (matchCollection != null && matchCollection.Count > 0 && text4 != "")
				{
					num2 += ((int.Parse(matchCollection[0].Value) > 10) ? int.Parse(matchCollection[0].Value) : 0);
				}
			}
			DataRow dataRow = dataTable2.NewRow();
			dataRow["Fid"] = "0";
			dataRow["FAreaName"] = areaName;
			dataRow["FCookieName"] = "合计";
			dataRow["FName"] = num2;
			dataTable2.Rows.InsertAt(dataRow, 0);
		}
		else if (type == "自定义")
		{
			int.TryParse(num, out var result);
			string areaName2 = BiliArea.GetAreaName(areaId);
			string sSql = " SELECT * FROM TAreaTask WHERE  FAreaId=" + areaId + " AND Fid IN (" + taskId + ") AND FEnable=1 ORDER BY FSort";
			DataTable dataTable3 = SQLHelper.BiliLocalDB.RunSqlDt(sSql);
			sSql = "";
			for (int k = 0; k < dataTable3.Rows.Count; k++)
			{
				string text5 = dataTable3.Rows[k]["FAwardId"].ToString() ?? throw new Exception("未找到FAwardId！");
				string text6 = dataTable3.Rows[k]["FTaskName"].ToString() ?? throw new Exception("未找到FTaskName！");
				string text7 = dataTable3.Rows[k]["FAwardName"].ToString() ?? throw new Exception("未找到FAwardName！");
				if (sSql == "")
				{
					sSql = " DECLARE @TT TABLE (Fid INT,FAreaName NVARCHAR(100),FCookieName NVARCHAR(100),FTaskName NVARCHAR(100),FName NVARCHAR(100),FCdkey  NVARCHAR(100),FDate DATETIME)";
				}
				sSql += " INSERT @TT";
				sSql = sSql + " SELECT TOP " + result + " T1.Fid,T2.FName AS 分区名称,T3.FName AS 账号名称,T4.FTaskName AS 任务名称, T1.FName AS Cdkey名称,T1.FCdkey AS Cdkey,T1.FDate AS 领取时间";
				sSql += " FROM TCdkey T1";
				sSql += " LEFT JOIN TArea T2 ON T2.Fid=T1.FAreaId";
				sSql += " LEFT JOIN TCookies T3 ON T3.Fid=T1.FCookieId";
				sSql += " LEFT JOIN TAreaTask T4 ON T4.FAreaId=T1.FAreaId AND T4.FAwardId=T1.FAwardId";
				sSql = sSql + " WHERE T1.FUserId=" + userId + " AND T1.FAreaId=" + areaId + " AND T1.FAwardId=" + text5;
				sSql += " AND T1.FStatus=0";
				sSql += " INSERT @TT";
				sSql = sSql + " SELECT TOP (ABS(" + result + " * " + (k + 1) + " - (SELECT COUNT(*) FROM @TT))) 0,'" + areaName2 + "' AS 分区名称,'' AS 账号名称,'" + text6 + "' AS 任务名称,'" + text7 + "' AS Cdkey名称,'' AS Cdkey,NULL AS 领取时间 FROM TBulletSceen";
			}
			sSql += " SELECT * FROM @TT";
			dataTable2 = SQLHelper.BiliLocalDB.RunSqlDt(sSql, limit, offset, prop, order);
		}
		else
		{
			string sSql = " SELECT T1.Fid,T1.FName,T1.FCdkey,T1.FDate,CASE T1.FStatus WHEN 1 THEN '已导出' WHEN 0 THEN '' ELSE '状态不正确' END AS FStatusName ";
			sSql += " ,T2.FName AS FAreaName,T3.FName AS FCookieName,T4.FTaskName";
			sSql = sSql + SQLHelper.total + " FROM TCdkey T1";
			sSql += " LEFT JOIN TArea T2 ON T2.Fid=T1.FAreaId";
			sSql += " LEFT JOIN TCookies T3 ON T3.Fid=T1.FCookieId";
			sSql += " LEFT JOIN TAreaTask T4 ON T4.FAwardId=T1.FAwardId";
			sSql = sSql + "  WHERE T1.FUserId=" + userId;
			if (areaId != "")
			{
				sSql = sSql + " AND T1.FAreaId IN (" + areaId + ")";
			}
			if (cookieId != "")
			{
				sSql = sSql + " AND T1.FCookieId IN (" + cookieId + ")";
			}
			if (activityId != "")
			{
				sSql = sSql + " AND T1.FActivityId='" + activityId + "'";
			}
			if (search != "")
			{
				sSql = sSql + " AND (T1.FName LIKE '%" + search + "%' OR T1.FCdkey LIKE '%" + search + "%' OR T4.FTaskName LIKE '%" + search + "%' )";
			}
			if (status != "")
			{
				sSql = sSql + " AND T1.FStatus=" + status;
			}
			if (prop == "")
			{
				prop = "T1.FDate DESC,T3.FSort ASC,T4.FSort ";
				order = "ASC";
			}
			dataTable2 = SQLHelper.BiliLocalDB.RunSqlDt(sSql, limit, offset, prop, order);
		}
		return dataTable2;
	}

	public static void SaveCdkey(JObject jObject, int userId)
	{
		string jObject2 = Util.GetJObject(jObject, "areaId");
		string jObject3 = Util.GetJObject(jObject, "cookieId");
		string sSql = " SELECT FType FROM TArea WHERE Fid=" + jObject2;
		string text = SQLHelper.BiliLocalDB.RunSqlStr(sSql);
		JArray jArray = Util.GetJObject<JArray>(jObject, "rows") ?? new JArray();
		JArray jArray2 = new JArray();
		string text2 = "绑定账号";
		string text3 = "0";
		if (text == "方式一")
		{
			foreach (JToken item in jArray)
			{
				JToken jToken = item["extra_info"];
				if (jToken != null && jToken.Type != JTokenType.Null)
				{
					text2 = Util.GetJObject(jToken, "cdkey_content");
					text3 = Util.GetJObject(jToken, "cdkey_id");
				}
				long seconds = long.Parse(Util.GetJObject(item, "receive_time", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString()));
				string text4 = DateTimeOffset.FromUnixTimeSeconds(seconds).LocalDateTime.ToString("yyyy-MM-dd HH:mm:ss");
				jArray2.Add(new JObject
				{
					["FStatus"] = -1,
					["FUserId"] = userId,
					["FAreaId"] = jObject2,
					["FCookieId"] = jObject3,
					["FName"] = item["award_name"],
					["FCdkey"] = text2,
					["FDate"] = text4,
					["FAwardId"] = item["award_id"],
					["FActivityId"] = item["activity_id"],
					["FUniqueId"] = Util.GetJObject(item, "unique_id"),
					["FUniqueKey"] = Util.GetJObject(item, "unique_id"),
					["FCdkeyId"] = text3
				});
			}
		}
		else
		{
			foreach (JToken item2 in jArray)
			{
				JToken jToken2 = item2["extra_info"];
				if (jToken2 != null && jToken2.Type != JTokenType.Null)
				{
					text2 = Util.GetJObject(jToken2, "cdkey_content");
					text3 = Util.GetJObject(jToken2, "cdkey_id");
				}
				long seconds2 = long.Parse(Util.GetJObject(item2, "receive_time", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString()));
				string jObject4 = Util.GetJObject(item2, "unique_id");
				string text5 = DateTimeOffset.FromUnixTimeSeconds(seconds2).LocalDateTime.ToString("yyyy-MM-dd HH:mm:ss");
				jArray2.Add(new JObject
				{
					["FStatus"] = -1,
					["FUserId"] = userId,
					["FAreaId"] = jObject2,
					["FCookieId"] = jObject3,
					["FName"] = item2["award_name"],
					["FCdkey"] = text2,
					["FDate"] = text5,
					["FAwardId"] = item2["award_id"],
					["FActivityId"] = item2["activity_id"],
					["FUniqueId"] = jObject4,
					["FUniqueKey"] = jObject4.Split('-').Last(),
					["FCdkeyId"] = text3
				});
			}
		}
		DataTable dt = jArray2.ToObject<DataTable>();
		UpdateCdkeyList(dt, jObject3, jObject2, userId);
	}

	public static void UpdateCdkeyList(DataTable? dt, string cookieId, string areaId, int userId, bool updateTask = false)
	{
		if (dt == null || dt.Rows.Count <= 0)
		{
			return;
		}
		SqlConnection sqlConnection = SQLHelper.BiliLocalDB.InitCnn();
		SqlCommand sqlCommand = sqlConnection.CreateCommand();
		sqlCommand.Transaction = sqlConnection.BeginTransaction();
		try
		{
			SQLHelper.RunTableLockx("TCdkey", sqlCommand);
			SQLHelper.SqlBulkCopyByDataTable("TCdkey", dt, sqlCommand);
			string text = " DELETE TCdkey WHERE FUniqueId IN(";
			text = text + " SELECT FUniqueId FROM TCdkey WHERE FUserId=" + userId;
			if (cookieId != "")
			{
				text = text + " AND FCookieId IN (" + cookieId + ")";
			}
			if (areaId != "")
			{
				text = text + " AND FAreaId IN (" + areaId + ")";
			}
			text += " GROUP BY FUniqueId HAVING COUNT(*)>1";
			text += " ) AND FStatus=-1";
			if (updateTask)
			{
				text += " UPDATE TCookiesTask SET FReceiveStatus=3  FROM TCdkey T2 WHERE T2.FStatus=-1 AND TCookiesTask.FUserId=T2.FUserId ";
				text += " AND TCookiesTask.FCookieId=T2.FCookieId AND TCookiesTask.FAreaId=T2.FAreaId AND TCookiesTask.FAwardId=T2.FAwardId";
			}
			text += " UPDATE TCdkey SET FStatus=0 WHERE FStatus=-1";
			SQLHelper.RunSqlDt(text, sqlCommand);
			sqlCommand.Transaction.Commit();
		}
		catch (Exception ex)
		{
			sqlCommand?.Transaction?.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static string ExportCdkeyList(string id, string type, string areaId, int userId)
	{
		string sSql = "";
		string areaName = BiliArea.GetAreaName(areaId);
		switch (type)
		{
		case "勾选":
			sSql = " SELECT T1.Fid,T2.FName AS 分区名称,T3.FName AS 账号名称,T4.FTaskName AS 任务名称, T1.FName AS Cdkey名称,T1.FCdkey AS Cdkey,T1.FDate AS 领取时间";
			sSql += " FROM TCdkey T1";
			sSql += " LEFT JOIN TArea T2 ON T2.Fid=T1.FAreaId";
			sSql += " LEFT JOIN TCookies T3 ON T3.Fid=T1.FCookieId";
			sSql += " LEFT JOIN TAreaTask T4 ON T4.FAreaId=T1.FAreaId AND T4.FAwardId=T1.FAwardId";
			sSql = sSql + "  WHERE T1.FUserId=" + userId + " AND T1.FAreaId=" + areaId;
			if (id != "")
			{
				sSql = sSql + " AND T1.Fid IN (" + id + ")";
			}
			sSql += " ORDER BY T1.FDate DESC";
			break;
		case "整套":
		{
			sSql = " SELECT * FROM TAreaTask WHERE  FAreaId=" + areaId + " AND FDaily=0 AND FEnable=1 ORDER BY FSort";
			DataTable dataTable2 = SQLHelper.BiliLocalDB.RunSqlDt(sSql);
			sSql = "";
			for (int j = 0; j < dataTable2.Rows.Count; j++)
			{
				string text4 = dataTable2.Rows[j]["FAwardId"].ToString() ?? throw new Exception("未找到FAwardId！");
				string text5 = dataTable2.Rows[j]["FTaskName"].ToString() ?? throw new Exception("未找到FTaskName！");
				string text6 = dataTable2.Rows[j]["FAwardName"].ToString() ?? throw new Exception("未找到FAwardName！");
				if (sSql == "")
				{
					sSql = " DECLARE @TT TABLE (Fid INT,分区名称  NVARCHAR(100),账号名称  NVARCHAR(100),任务名称  NVARCHAR(100),Cdkey名称  NVARCHAR(100),Cdkey  NVARCHAR(100),领取时间 DATETIME)";
				}
				sSql += " INSERT @TT";
				sSql += " SELECT TOP 1 T1.Fid,T2.FName AS 分区名称,T3.FName AS 账号名称,T4.FTaskName AS 任务名称, T1.FName AS Cdkey名称,T1.FCdkey AS Cdkey,T1.FDate AS 领取时间";
				sSql += " FROM TCdkey T1";
				sSql += " LEFT JOIN TArea T2 ON T2.Fid=T1.FAreaId";
				sSql += " LEFT JOIN TCookies T3 ON T3.Fid=T1.FCookieId";
				sSql += " LEFT JOIN TAreaTask T4 ON T4.FAreaId=T1.FAreaId AND T4.FAwardId=T1.FAwardId";
				sSql += " LEFT JOIN (SELECT FCookieId, COUNT(*) AS FCount FROM TCdkey WHERE FAwardId IN (SELECT FAwardId FROM TAreaTask WHERE FAreaId=662 AND FDaily=0)";
				sSql += "            AND FStatus=0 AND FAreaId=662 GROUP BY FCookieId) T5 ON T5.FCookieId=T1.FCookieId";
				sSql = sSql + " WHERE T1.FUserId=" + userId + " AND T1.FAreaId=" + areaId + " AND T1.FAwardId=" + text4;
				sSql += " AND T4.FDaily=0 AND T1.FStatus=0 ORDER BY T5.FCount DESC";
				sSql += " INSERT @TT";
				sSql = sSql + " SELECT TOP (ABS(" + (j + 1) + " - (SELECT COUNT(*) FROM @TT))) 0,'" + areaName + "' AS 分区名称,'' AS 账号名称,'" + text5 + "' AS 任务名称,'" + text6 + "' AS Cdkey名称,'' AS Cdkey,NULL AS 领取时间";
			}
			sSql += " SELECT * FROM @TT";
			break;
		}
		case "每日":
		{
			sSql = " SELECT * FROM TAreaTask WHERE  FAreaId=" + areaId + " AND FDaily=1 AND FEnable=1 ORDER BY FSort";
			DataTable dataTable = SQLHelper.BiliLocalDB.RunSqlDt(sSql);
			sSql = "";
			for (int i = 0; i < dataTable.Rows.Count; i++)
			{
				string text = dataTable.Rows[i]["FAwardId"].ToString() ?? throw new Exception("未找到FAwardId！");
				string text2 = dataTable.Rows[i]["FTaskName"].ToString() ?? throw new Exception("未找到FTaskName！");
				string text3 = dataTable.Rows[i]["FAwardName"].ToString() ?? throw new Exception("未找到FAwardName！");
				if (sSql == "")
				{
					sSql = " DECLARE @TT TABLE (Fid INT,分区名称  NVARCHAR(100),账号名称  NVARCHAR(100),任务名称  NVARCHAR(100),Cdkey名称  NVARCHAR(100),Cdkey  NVARCHAR(100),领取时间 DATETIME)";
				}
				sSql += " INSERT @TT";
				sSql += " SELECT TOP 42 T1.Fid,T2.FName AS 分区名称,T3.FName AS 账号名称,T4.FTaskName AS 任务名称, T1.FName AS Cdkey名称,T1.FCdkey AS Cdkey,T1.FDate AS 领取时间";
				sSql += " FROM TCdkey T1";
				sSql += " LEFT JOIN TArea T2 ON T2.Fid=T1.FAreaId";
				sSql += " LEFT JOIN TCookies T3 ON T3.Fid=T1.FCookieId";
				sSql += " LEFT JOIN TAreaTask T4 ON T4.FAreaId=T1.FAreaId AND T4.FAwardId=T1.FAwardId";
				sSql = sSql + " WHERE T1.FUserId=" + userId + " AND T1.FAreaId=" + areaId + " AND T1.FAwardId=" + text;
				sSql += " AND T4.FDaily=1 AND T1.FStatus=0";
				sSql += " INSERT @TT";
				sSql = sSql + " SELECT TOP (ABS(42 * " + (i + 1) + " - (SELECT COUNT(*) FROM @TT))) 0,'" + areaName + "' AS 分区名称,'' AS 账号名称,'" + text2 + "' AS 任务名称,'" + text3 + "' AS Cdkey名称,'' AS Cdkey,NULL AS 领取时间 FROM TBulletSceen";
			}
			sSql += " SELECT * FROM @TT";
			break;
		}
		}
		DataTable dataTable3 = SQLHelper.BiliLocalDB.RunSqlDt(sSql);
		if (dataTable3.Rows.Count == 0)
		{
			throw new Exception("暂无数据！");
		}
		if (type == "整套")
		{
			int num = 0;
			for (int k = 0; k < dataTable3.Rows.Count; k++)
			{
				string input = dataTable3.Rows[k]["Cdkey名称"].ToString() ?? "";
				string text7 = dataTable3.Rows[k]["Cdkey"].ToString() ?? "";
				MatchCollection matchCollection = MyRegex().Matches(input);
				if (matchCollection != null && matchCollection.Count > 0 && text7 != "")
				{
					num += ((int.Parse(matchCollection[0].Value) > 10) ? int.Parse(matchCollection[0].Value) : 0);
				}
			}
			DataRow dataRow = dataTable3.NewRow();
			dataRow["Fid"] = "0";
			dataRow["分区名称"] = areaName;
			dataRow["账号名称"] = "合计";
			dataRow["Cdkey名称"] = num;
			dataTable3.Rows.InsertAt(dataRow, 0);
		}
		id = string.Join(',', (from row in dataTable3.Rows.OfType<DataRow>()
			select Convert.ToString(row["Fid"])).ToArray());
		Util.FileDownload(out string absolute, out string relative);
		ExcelHelper.X2003.TableToExcelForXLS(dataTable3, absolute);
		sSql = " UPDATE TCdkey SET FStatus=1 WHERE FUserId=" + userId + " AND Fid IN (" + id + ")";
		SQLHelper.BiliLocalDB.RunSqlText(sSql);
		return relative;
	}

	private static readonly Regex _myRegex = new Regex("\\d+");
	
	private static Regex MyRegex()
	{
		return _myRegex;
	}
}
