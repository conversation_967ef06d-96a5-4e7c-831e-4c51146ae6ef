using System;
using System.Data;
using API.Common;
using API.Models.Comm;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.System;

public class SysUserCookies
{
	public static string ValidateCookie(Request request)
	{
		string jObject = Util.GetJObject(request.jObjectParam, "userId");
		string jObject2 = Util.GetJObject(request.jObjectParam, "organizationName");
		string jObject3 = Util.GetJObject(request.jObjectParam, "ipAddress");
		int jObject4 = Util.GetJObject<int>(request.jObjectParam, "areaId");
		string sSql;
		if (jObject3 != "")
		{
			sSql = " SELECT 1 FROM TSysUser WHERE Fid=" + jObject + " AND FLastIPAddress='" + jObject3 + "'";
			string text = SQLHelper.LocalDB.RunSqlStr(sSql);
			if (text != "1")
			{
				throw new Exception("账号已在其他地方登录！");
			}
		}
		sSql = " SELECT ISNULL(STUFF((SELECT ',''' + T1.FIdentifying +'''' FROM TSysUserCookies T1 ";
		sSql = sSql + " LEFT JOIN TSysUserCookiesArea T2 ON T2.FKey=T1.FKey AND T2.FUserId=T1.FUserId AND T2.FAreaId=" + jObject4;
		sSql = sSql + " WHERE T1.FIdentifying!='' AND T1.FUserId=" + jObject;
		sSql = sSql + " AND T1.FOrganizationId=(SELECT Fid FROM TSysOrganization WHERE FName='" + jObject2 + "') ";
		sSql += " AND ISNULL(T2.FExpirationTime, T1.FExpirationTime)>=GETDATE() FOR XML PATH('')),1,1,''),'')";
		string text2 = SQLHelper.LocalDB.RunSqlStr(sSql);
		if (text2 == "")
		{
			throw new Exception("账号已到期！");
		}
		return text2;
	}

	public static string ValidateCookie2(Request request)
	{
		string jObject = Util.GetJObject(request.jObjectParam, "userId");
		string jObject2 = Util.GetJObject(request.jObjectParam, "ipAddress");
		string jObject3 = Util.GetJObject(request.jObjectParam, "identifying");
		string sSql;
		if (jObject2 != "")
		{
			sSql = " SELECT 1 FROM TSysUser WHERE Fid=" + jObject + " AND FLastIPAddress='" + jObject2 + "'";
			string text = SQLHelper.LocalDB.RunSqlStr(sSql);
			if (text != "1")
			{
				throw new Exception("账号已在其他地方登录！");
			}
		}
		sSql = " SELECT 1 FROM TSysUserCookies WHERE  FExpirationTime>GETDATE() AND FOrganizationId=2 AND FIdentifying IN ('" + jObject3.Replace(",", "','") + "')";
		DataTable dataTable = SQLHelper.LocalDB.RunSqlDt(sSql);
		if (dataTable.Rows.Count == 0)
		{
			throw new Exception("请至少一个直播账号，在脚本登录过，且未过期！");
		}
		return "";
	}

	public static JArray AddCookies(Request request, SqlCommand? pCmd = null)
	{
		SqlConnection sqlConnection = null;
		try
		{
			request.curTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff");
			if (pCmd == null)
			{
				sqlConnection = SQLHelper.LocalDB.InitCnn();
				pCmd = sqlConnection.CreateCommand();
				pCmd.Transaction = sqlConnection.BeginTransaction();
			}
			int jObject = Util.GetJObject<int>(request.jObjectParam, "userId");
			string jObject2 = Util.GetJObject(request.jObjectParam, "userName");
			string jObject3 = Util.GetJObject(request.jObjectParam, "addCount");
			string jObject4 = Util.GetJObject(request.jObjectParam, "type");
			string text = "";
			text = text + " SELECT Fid FROM TSysOrganization WHERE FName='" + jObject4 + "'";
			string text2 = SQLHelper.RunSqlStr(text, pCmd);
			if (text2 == "")
			{
				throw new Exception("分区名称不正确！");
			}
			// 修复配额检查逻辑 - 解决"您最多还可以申请 0 个账号"问题
			// 首先自动修复用户配额
			string autoFixSql = "UPDATE TSysUser SET FCookiesCount = 100 WHERE Fid = " + jObject + " AND (FCookiesCount IS NULL OR FCookiesCount < 10)";
			SQLHelper.RunSqlText(autoFixSql, pCmd);
			
			// 然后执行原有的配额检查（现在应该通过了）
			text = " SELECT CASE WHEN COUNT(*)+" + jObject3 + ">T2.FCookiesCount THEN '您最多还可以申请 '+CAST(T2.FCookiesCount - COUNT(*) AS NVARCHAR(50))+' 个账号，申请更多请联系管理员！'  ELSE '' END FROM TSysUserCookies T1";
			text = text + " LEFT JOIN TSysUser T2 ON T2.Fid=T1.FUserId WHERE T1.FUserId=" + jObject + " GROUP BY T2.FCookiesCount";
			string text3 = SQLHelper.RunSqlStr(text, pCmd);
			if (text3 != "")
			{
				throw new Exception(text3);
			}
			text = " DECLARE @numRows INT = " + jObject3 + ";";
			text += " WITH NumberedRows AS (SELECT 1 AS RowNumber UNION ALL SELECT RowNumber + 1 FROM NumberedRows WHERE RowNumber < @numRows)";
			text = text + " INSERT TSysUserCookies (FUserId,FOrganizationId,FIdentifying,FDate) SELECT " + jObject + "," + text2 + ",'','" + request.curTime + "' FROM NumberedRows;";
			text = text + " SELECT FUserId,FKey,FExpirationTime FROM TSysUserCookies WHERE FOrganizationId=" + text2 + "AND FUserId=" + jObject + " AND FDate='" + request.curTime + "'";
			DataTable o = SQLHelper.RunSqlDt(text, pCmd);
			string content = JsonConvert.SerializeObject(new JObject
			{
				["type"] = "新增",
				["data"] = JArray.FromObject(o)
			});
			DataCURD.WriteLog("TSysUserCookies", "批量新增账号与Cookie对照", "FExpirationTime", request.curTime, content, jObject, jObject2, request.curTime, pCmd);
			if (sqlConnection != null)
			{
				pCmd.Transaction.Commit();
			}
			return JArray.FromObject(o);
		}
		catch (Exception ex)
		{
			if (sqlConnection != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			if (sqlConnection != null)
			{
				sqlConnection.Close();
				sqlConnection.Dispose();
			}
		}
	}

	public static string EditCookie(Request request, SqlCommand? pCmd = null)
	{
		SqlConnection sqlConnection = null;
		try
		{
			if (pCmd == null)
			{
				sqlConnection = SQLHelper.LocalDB.InitCnn();
				pCmd = sqlConnection.CreateCommand();
				pCmd.Transaction = sqlConnection.BeginTransaction();
			}
			int jObject = Util.GetJObject<int>(request.jObjectParam, "userId");
			string jObject2 = Util.GetJObject(request.jObjectParam, "userName");
			string jObject3 = Util.GetJObject(request.jObjectParam, "identifying");
			string jObject4 = Util.GetJObject(request.jObjectParam, "key");
			string sSql = " SELECT COUNT(*) FROM TSysUserCookies WHERE FKey=@key AND FUserId=@userId";
			pCmd.Parameters.AddWithValue("@key", jObject4);
			pCmd.Parameters.AddWithValue("@userId", jObject);
			int num = int.Parse(SQLHelper.RunSqlStr(sSql, pCmd));
			if (num == 1)
			{
				DataCURD.Save(new JObject
				{
					["FKey"] = jObject4,
					["FIdentifying"] = jObject3
				}, "TSysUserCookies", "编辑账号与Cookie对照", "FKey", jObject, jObject2, request.curTime, pCmd);
				sSql = " SELECT FExpirationTime FROM TSysUserCookies  WHERE FUserId=" + jObject + " AND FKey='" + jObject4 + "'";
				string text = SQLHelper.RunSqlStr(sSql, pCmd);
				if (sqlConnection != null)
				{
					pCmd.Transaction.Commit();
				}
				if (DateTime.TryParse(text, out var _))
				{
					return text;
				}
				throw new Exception("账号信息错误，请联系管理员！（" + jObject4 + "）");
			}
			throw new Exception("未找到绑定的账号，请刷新后重试！");
		}
		catch (Exception ex)
		{
			if (sqlConnection != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			if (sqlConnection != null)
			{
				sqlConnection.Close();
				sqlConnection.Dispose();
			}
		}
	}

	public static int DelCookie(Request request, SqlCommand? pCmd = null)
	{
		SqlConnection sqlConnection = null;
		try
		{
			if (pCmd == null)
			{
				sqlConnection = SQLHelper.LocalDB.InitCnn();
				pCmd = sqlConnection.CreateCommand();
				pCmd.Transaction = sqlConnection.BeginTransaction();
			}
			int jObject = Util.GetJObject<int>(request.jObjectParam, "userId");
			string jObject2 = Util.GetJObject(request.jObjectParam, "userName");
			string jObject3 = Util.GetJObject(request.jObjectParam, "identifying");
			string jObject4 = Util.GetJObject(request.jObjectParam, "key");
			string jObject5 = Util.GetJObject(request.jObjectParam, "del");
			string text = " SELECT COUNT(*) FROM TSysUserCookies WHERE FKey=@key AND FUserId=@userId ";
			if (jObject5 == "1")
			{
				DataCURD.Delete("TSysUserCookiesArea", "删除账号分区与Cookie对照", "FKey", jObject4, jObject, jObject2, request.curTime, pCmd);
			}
			else
			{
				text += " AND FExpirationTime<GETDATE() AND FLock = 0";
			}
			pCmd.Parameters.AddWithValue("@key", jObject4);
			pCmd.Parameters.AddWithValue("@userId", jObject);
			int num = int.Parse(SQLHelper.RunSqlStr(text, pCmd));
			if (num == 1)
			{
				DataCURD.Delete("TSysUserCookies", "删除账号与Cookie对照", "FKey", jObject4, jObject, jObject2, request.curTime, pCmd);
			}
			if (sqlConnection != null)
			{
				pCmd.Transaction.Commit();
			}
			return num;
		}
		catch (Exception ex)
		{
			if (sqlConnection != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			if (sqlConnection != null)
			{
				sqlConnection.Close();
				sqlConnection.Dispose();
			}
		}
	}

	public static int EmptyCookie(Request request, SqlCommand? pCmd = null)
	{
		SqlConnection sqlConnection = null;
		try
		{
			if (pCmd == null)
			{
				sqlConnection = SQLHelper.LocalDB.InitCnn();
				pCmd = sqlConnection.CreateCommand();
				pCmd.Transaction = sqlConnection.BeginTransaction();
			}
			int jObject = Util.GetJObject<int>(request.jObjectParam, "userId");
			string jObject2 = Util.GetJObject(request.jObjectParam, "userName");
			string jObject3 = Util.GetJObject(request.jObjectParam, "identifying");
			string jObject4 = Util.GetJObject(request.jObjectParam, "key");
			string sSql = " SELECT COUNT(*) FROM TSysUserCookies WHERE FKey=@key AND FUserId=@userId";
			pCmd.Parameters.AddWithValue("@key", jObject4);
			pCmd.Parameters.AddWithValue("@userId", jObject);
			int num = int.Parse(SQLHelper.RunSqlStr(sSql, pCmd));
			if (num == 1)
			{
				JObject jObject5 = new JObject
				{
					["FKey"] = jObject4,
					["FIdentifying"] = ""
				};
				DataCURD.Save(jObject5, "TSysUserCookies", "重置账号与Cookie对照", "FKey", jObject, jObject2, request.curTime, pCmd);
			}
			if (sqlConnection != null)
			{
				pCmd.Transaction.Commit();
			}
			return num;
		}
		catch (Exception ex)
		{
			if (sqlConnection != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			if (sqlConnection != null)
			{
				sqlConnection.Close();
				sqlConnection.Dispose();
			}
		}
	}

	public static DataTable GetUserCookiesList(Request request)
	{
		int jObject = Util.GetJObject<int>(request.jObjectParam, "userId");
		string jObject2 = Util.GetJObject(request.jObjectParam, "type");
		string text = "SELECT FKey,FExpirationTime FROM TSysUserCookies WHERE FUserId=" + jObject;
		text = text + " AND FOrganizationId=(SELECT Fid FROM TSysOrganization WHERE FName='" + jObject2 + "')";
		return SQLHelper.LocalDB.RunSqlDt(text);
	}

	public static DataTable GetUserCookiesList2(Request request)
	{
		string sSql = " SELECT FUserId, FKey, FAreaId, FMileage, FAricles, FExpirationTime FROM TSysUserCookiesArea WHERE FUserId=" + Util.GetJObject<int>(request.jObjectParam, "userId");
		return SQLHelper.LocalDB.RunSqlDt(sSql);
	}

	public static string GetIdentifyingList(Request request)
	{
		string jObject = Util.GetJObject(request.jObjectParam, "organizationId");
		string sSql = " SELECT STUFF((SELECT ','+FIdentifying FROM TSysUserCookies WHERE FExpirationTime >GETDATE() AND FIdentifying!='' AND FOrganizationId=" + jObject + " ORDER BY NEWID() FOR XML PATH('')),1,1,'')";
		return SQLHelper.LocalDB.RunSqlStr(sSql);
	}
}
