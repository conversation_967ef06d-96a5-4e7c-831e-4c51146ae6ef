"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[9133],{4859:function(e,t,l){l.r(t),l.d(t,{default:function(){return xt}});var a=l(641),i=l(2644);const s={class:"adminui-header"},o={class:"adminui-header-left"},n={class:"logo-bar"},u=(0,a.Lk)("img",{class:"logo",src:"img/logo.png"},null,-1),r={key:0,class:"nav"},d=["onClick"],c={class:"adminui-header-right"},m={class:"aminui-wrapper"},h={key:0,class:"adminui-side-top"},p={class:"adminui-side-scroll"},k={class:"aminui-body el-container"},g={class:"adminui-main",id:"adminui-main"},f={class:"adminui-header"},b={class:"adminui-header-left"},v={class:"logo-bar"},y=(0,a.Lk)("img",{class:"logo",src:"img/logo.png"},null,-1),F={class:"adminui-header-right"},_={class:"aminui-wrapper"},L={class:"adminui-side-scroll"},C={class:"aminui-body el-container"},$={class:"adminui-main",id:"adminui-main"},w={class:"adminui-header"},E={class:"adminui-header-left"},x={class:"logo-bar"},T=(0,a.Lk)("img",{class:"logo",src:"img/logo.png"},null,-1),X={class:"adminui-header-right"},I={key:0,class:"adminui-header-menu"},M={class:"aminui-wrapper"},W={class:"aminui-body el-container"},V={class:"adminui-main",id:"adminui-main"},O={key:3,class:"aminui-wrapper"},A={key:0,class:"aminui-side-split"},P={class:"aminui-side-split-top"},N=["title"],Q={class:"adminui-side-split-scroll"},U=["onClick"],R={key:0,class:"adminui-side-top"},S={class:"adminui-side-scroll"},D={class:"aminui-body el-container"},z={class:"adminui-main",id:"adminui-main"};function G(e,t,l,G,K,B){const q=(0,a.g2)("el-icon"),j=(0,a.g2)("userbar"),H=(0,a.g2)("NavMenu"),Y=(0,a.g2)("el-menu"),J=(0,a.g2)("el-scrollbar"),Z=(0,a.g2)("el-icon-expand"),ee=(0,a.g2)("el-icon-fold"),te=(0,a.g2)("Side-m"),le=(0,a.g2)("Topbar"),ae=(0,a.g2)("Tags"),ie=(0,a.g2)("router-view"),se=(0,a.g2)("iframe-view"),oe=(0,a.g2)("router-link"),ne=(0,a.g2)("el-icon-close"),ue=((0,a.g2)("el-icon-brush-filled"),(0,a.g2)("setting")),re=(0,a.g2)("el-drawer"),de=(0,a.g2)("auto-exit");return(0,a.uX)(),(0,a.CE)(a.FK,null,["header"==B.layout?((0,a.uX)(),(0,a.CE)(a.FK,{key:0},[(0,a.Lk)("header",s,[(0,a.Lk)("div",o,[(0,a.Lk)("div",n,[u,(0,a.Lk)("span",null,(0,i.v_)(e.$CONFIG.APP_NAME),1)]),B.ismobile?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.CE)("ul",r,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(K.menu,(e=>((0,a.uX)(),(0,a.CE)("li",{key:e,class:(0,i.C4)(K.pmenu.path==e.path?"active":""),onClick:t=>B.showMenu(e)},[(0,a.bF)(q,null,{default:(0,a.k6)((()=>[((0,a.uX)(),(0,a.Wv)((0,a.$y)(e.meta.icon||"el-icon-menu")))])),_:2},1024),(0,a.Lk)("span",null,(0,i.v_)(e.meta.title),1)],10,d)))),128))]))]),(0,a.Lk)("div",c,[(0,a.bF)(j)])]),(0,a.Lk)("section",m,[!B.ismobile&&K.nextMenu.length>0||!K.pmenu.component?((0,a.uX)(),(0,a.CE)("div",{key:0,class:(0,i.C4)(B.menuIsCollapse?"aminui-side isCollapse":"aminui-side")},[B.menuIsCollapse?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.CE)("div",h,[(0,a.Lk)("h2",null,(0,i.v_)(K.pmenu.meta.title),1)])),(0,a.Lk)("div",p,[(0,a.bF)(J,null,{default:(0,a.k6)((()=>[(0,a.bF)(Y,{"default-active":K.active,router:"",collapse:B.menuIsCollapse,"unique-opened":e.$CONFIG.MENU_UNIQUE_OPENED},{default:(0,a.k6)((()=>[(0,a.bF)(H,{navMenus:K.nextMenu},null,8,["navMenus"])])),_:1},8,["default-active","collapse","unique-opened"])])),_:1})]),(0,a.Lk)("div",{class:"adminui-side-bottom",onClick:t[0]||(t[0]=t=>e.$store.commit("TOGGLE_menuIsCollapse"))},[(0,a.bF)(q,null,{default:(0,a.k6)((()=>[B.menuIsCollapse?((0,a.uX)(),(0,a.Wv)(Z,{key:0})):((0,a.uX)(),(0,a.Wv)(ee,{key:1}))])),_:1})])],2)):(0,a.Q3)("",!0),B.ismobile?((0,a.uX)(),(0,a.Wv)(te,{key:1})):(0,a.Q3)("",!0),(0,a.Lk)("div",k,[B.ismobile?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.Wv)(le,{key:0})),!B.ismobile&&B.layoutTags?((0,a.uX)(),(0,a.Wv)(ae,{key:1})):(0,a.Q3)("",!0),(0,a.Lk)("div",g,[(0,a.bF)(ie,null,{default:(0,a.k6)((({Component:t})=>[((0,a.uX)(),(0,a.Wv)(a.PR,{include:this.$store.state.keepAlive.keepLiveRoute},[e.$store.state.keepAlive.routeShow?((0,a.uX)(),(0,a.Wv)((0,a.$y)(t),{key:e.$route.fullPath})):(0,a.Q3)("",!0)],1032,["include"]))])),_:1}),(0,a.bF)(se)])])])],64)):"menu"==B.layout?((0,a.uX)(),(0,a.CE)(a.FK,{key:1},[(0,a.Lk)("header",f,[(0,a.Lk)("div",b,[(0,a.Lk)("div",v,[y,(0,a.Lk)("span",null,(0,i.v_)(e.$CONFIG.APP_NAME),1)])]),(0,a.Lk)("div",F,[(0,a.bF)(j)])]),(0,a.Lk)("section",_,[B.ismobile?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.CE)("div",{key:0,class:(0,i.C4)(B.menuIsCollapse?"aminui-side isCollapse":"aminui-side")},[(0,a.Lk)("div",L,[(0,a.bF)(J,null,{default:(0,a.k6)((()=>[(0,a.bF)(Y,{"default-active":K.active,router:"",collapse:B.menuIsCollapse,"unique-opened":e.$CONFIG.MENU_UNIQUE_OPENED},{default:(0,a.k6)((()=>[(0,a.bF)(H,{navMenus:K.menu},null,8,["navMenus"])])),_:1},8,["default-active","collapse","unique-opened"])])),_:1})]),(0,a.Lk)("div",{class:"adminui-side-bottom",onClick:t[1]||(t[1]=t=>e.$store.commit("TOGGLE_menuIsCollapse"))},[(0,a.bF)(q,null,{default:(0,a.k6)((()=>[B.menuIsCollapse?((0,a.uX)(),(0,a.Wv)(Z,{key:0})):((0,a.uX)(),(0,a.Wv)(ee,{key:1}))])),_:1})])],2)),B.ismobile?((0,a.uX)(),(0,a.Wv)(te,{key:1})):(0,a.Q3)("",!0),(0,a.Lk)("div",C,[B.ismobile?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.Wv)(le,{key:0})),!B.ismobile&&B.layoutTags?((0,a.uX)(),(0,a.Wv)(ae,{key:1})):(0,a.Q3)("",!0),(0,a.Lk)("div",$,[(0,a.bF)(ie,null,{default:(0,a.k6)((({Component:t})=>[((0,a.uX)(),(0,a.Wv)(a.PR,{include:this.$store.state.keepAlive.keepLiveRoute},[e.$store.state.keepAlive.routeShow?((0,a.uX)(),(0,a.Wv)((0,a.$y)(t),{key:e.$route.fullPath})):(0,a.Q3)("",!0)],1032,["include"]))])),_:1}),(0,a.bF)(se)])])])],64)):"dock"==B.layout?((0,a.uX)(),(0,a.CE)(a.FK,{key:2},[(0,a.Lk)("header",w,[(0,a.Lk)("div",E,[(0,a.Lk)("div",x,[T,(0,a.Lk)("span",null,(0,i.v_)(e.$CONFIG.APP_NAME),1)])]),(0,a.Lk)("div",X,[B.ismobile?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.CE)("div",I,[(0,a.bF)(Y,{mode:"horizontal","default-active":K.active,router:"","background-color":"#222b45","text-color":"#fff","active-text-color":"var(--el-color-primary)"},{default:(0,a.k6)((()=>[(0,a.bF)(H,{navMenus:K.menu},null,8,["navMenus"])])),_:1},8,["default-active"])])),B.ismobile?((0,a.uX)(),(0,a.Wv)(te,{key:1})):(0,a.Q3)("",!0),(0,a.bF)(j)])]),(0,a.Lk)("section",M,[(0,a.Lk)("div",W,[!B.ismobile&&B.layoutTags?((0,a.uX)(),(0,a.Wv)(ae,{key:0})):(0,a.Q3)("",!0),(0,a.Lk)("div",V,[(0,a.bF)(ie,null,{default:(0,a.k6)((({Component:t})=>[((0,a.uX)(),(0,a.Wv)(a.PR,{include:this.$store.state.keepAlive.keepLiveRoute},[e.$store.state.keepAlive.routeShow?((0,a.uX)(),(0,a.Wv)((0,a.$y)(t),{key:e.$route.fullPath})):(0,a.Q3)("",!0)],1032,["include"]))])),_:1}),(0,a.bF)(se)])])])],64)):((0,a.uX)(),(0,a.CE)("section",O,[B.ismobile?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.CE)("div",A,[(0,a.Lk)("div",P,[(0,a.bF)(oe,{to:e.$CONFIG.DASHBOARD_URL},{default:(0,a.k6)((()=>[(0,a.Lk)("img",{class:"logo",title:e.$CONFIG.APP_NAME,src:"img/logo-r.png"},null,8,N)])),_:1},8,["to"])]),(0,a.Lk)("div",Q,[(0,a.bF)(J,null,{default:(0,a.k6)((()=>[(0,a.Lk)("ul",null,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(K.menu,(t=>((0,a.uX)(),(0,a.CE)("li",{key:t,class:(0,i.C4)(K.pmenu.path==t.path?"active":""),onClick:e=>B.showMenu(t)},[(0,a.bF)(q,null,{default:(0,a.k6)((()=>[((0,a.uX)(),(0,a.Wv)((0,a.$y)(t.meta.icon||e.el-e.icon-K.menu)))])),_:2},1024),(0,a.Lk)("p",null,(0,i.v_)(t.meta.title),1)],10,U)))),128))])])),_:1})])])),!B.ismobile&&K.nextMenu.length>0||!K.pmenu.component?((0,a.uX)(),(0,a.CE)("div",{key:1,class:(0,i.C4)(B.menuIsCollapse?"aminui-side isCollapse":"aminui-side")},[B.menuIsCollapse?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.CE)("div",R,[(0,a.Lk)("h2",null,(0,i.v_)(K.pmenu.meta.title),1)])),(0,a.Lk)("div",S,[(0,a.bF)(J,null,{default:(0,a.k6)((()=>[(0,a.bF)(Y,{"default-active":K.active,router:"",collapse:B.menuIsCollapse,"unique-opened":e.$CONFIG.MENU_UNIQUE_OPENED},{default:(0,a.k6)((()=>[(0,a.bF)(H,{navMenus:K.nextMenu},null,8,["navMenus"])])),_:1},8,["default-active","collapse","unique-opened"])])),_:1})]),(0,a.Lk)("div",{class:"adminui-side-bottom",onClick:t[2]||(t[2]=t=>e.$store.commit("TOGGLE_menuIsCollapse"))},[(0,a.bF)(q,null,{default:(0,a.k6)((()=>[B.menuIsCollapse?((0,a.uX)(),(0,a.Wv)(Z,{key:0})):((0,a.uX)(),(0,a.Wv)(ee,{key:1}))])),_:1})])],2)):(0,a.Q3)("",!0),B.ismobile?((0,a.uX)(),(0,a.Wv)(te,{key:2})):(0,a.Q3)("",!0),(0,a.Lk)("div",D,[(0,a.bF)(le,null,{default:(0,a.k6)((()=>[(0,a.bF)(j)])),_:1}),!B.ismobile&&B.layoutTags?((0,a.uX)(),(0,a.Wv)(ae,{key:0})):(0,a.Q3)("",!0),(0,a.Lk)("div",z,[(0,a.bF)(ie,null,{default:(0,a.k6)((({Component:t})=>[((0,a.uX)(),(0,a.Wv)(a.PR,{include:this.$store.state.keepAlive.keepLiveRoute},[e.$store.state.keepAlive.routeShow?((0,a.uX)(),(0,a.Wv)((0,a.$y)(t),{key:e.$route.fullPath})):(0,a.Q3)("",!0)],1032,["include"]))])),_:1}),(0,a.bF)(se)])])])),(0,a.Lk)("div",{class:"main-maximize-exit",onClick:t[3]||(t[3]=(...e)=>B.exitMaximize&&B.exitMaximize(...e))},[(0,a.bF)(q,null,{default:(0,a.k6)((()=>[(0,a.bF)(ne)])),_:1})]),(0,a.Q3)("",!0),(0,a.bF)(re,{title:"布局实时演示",modelValue:K.settingDialog,"onUpdate:modelValue":t[5]||(t[5]=e=>K.settingDialog=e),size:400,"append-to-body":"","destroy-on-close":""},{default:(0,a.k6)((()=>[(0,a.bF)(ue)])),_:1},8,["modelValue"]),(0,a.bF)(de)],64)}l(8743);const K=e=>((0,a.Qi)("data-v-a483418e"),e=e(),(0,a.jt)(),e),B={class:"logo-bar"},q=K((()=>(0,a.Lk)("img",{class:"logo",src:"img/logo.png"},null,-1)));function j(e,t,l,s,o,n){const u=(0,a.g2)("el-icon-menu"),r=(0,a.g2)("el-icon"),d=(0,a.g2)("el-header"),c=(0,a.g2)("NavMenu"),m=(0,a.g2)("el-menu"),h=(0,a.g2)("el-scrollbar"),p=(0,a.g2)("el-main"),k=(0,a.g2)("el-container"),g=(0,a.g2)("el-drawer"),f=(0,a.gN)("drag");return(0,a.uX)(),(0,a.CE)(a.FK,null,[(0,a.bo)(((0,a.uX)(),(0,a.CE)("div",{ref:"",class:"mobile-nav-button",onClick:t[0]||(t[0]=e=>n.showMobileNav(e)),draggable:"false"},[(0,a.bF)(r,null,{default:(0,a.k6)((()=>[(0,a.bF)(u)])),_:1})])),[[f]]),(0,a.bF)(g,{ref:"mobileNavBox",title:"移动端菜单",size:240,modelValue:o.nav,"onUpdate:modelValue":t[1]||(t[1]=e=>o.nav=e),direction:"ltr","with-header":!1,"destroy-on-close":""},{default:(0,a.k6)((()=>[(0,a.bF)(k,{class:"mobile-nav"},{default:(0,a.k6)((()=>[(0,a.bF)(d,null,{default:(0,a.k6)((()=>[(0,a.Lk)("div",B,[q,(0,a.Lk)("span",null,(0,i.v_)(e.$CONFIG.APP_NAME),1)])])),_:1}),(0,a.bF)(p,null,{default:(0,a.k6)((()=>[(0,a.bF)(h,null,{default:(0,a.k6)((()=>[(0,a.bF)(m,{"default-active":e.$route.meta.active||e.$route.fullPath,onSelect:n.select,router:"","background-color":"#212d3d","text-color":"#fff","active-text-color":"#409EFF"},{default:(0,a.k6)((()=>[(0,a.bF)(c,{navMenus:o.menu},null,8,["navMenus"])])),_:1},8,["default-active","onSelect"])])),_:1})])),_:1})])),_:1})])),_:1},8,["modelValue"])],64)}var H=l(9322);const Y={key:0,style:{padding:"20px"}},J=["href"],Z={key:0,class:"menu-tag"},ee={key:1,class:"menu-tag"};function te(e,t,l,s,o,n){const u=(0,a.g2)("el-alert"),r=(0,a.g2)("el-icon"),d=(0,a.g2)("el-menu-item"),c=(0,a.g2)("NavMenu",!0),m=(0,a.g2)("el-sub-menu");return(0,a.uX)(),(0,a.CE)(a.FK,null,[l.navMenus.length<=0?((0,a.uX)(),(0,a.CE)("div",Y,[(0,a.bF)(u,{title:"无子集菜单",center:"",type:"info",closable:!1})])):(0,a.Q3)("",!0),((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(l.navMenus,(e=>((0,a.uX)(),(0,a.CE)(a.FK,{key:e},[n.hasChildren(e)?((0,a.uX)(),(0,a.Wv)(m,{key:1,index:e.path},{title:(0,a.k6)((()=>[e.meta&&e.meta.icon?((0,a.uX)(),(0,a.Wv)(r,{key:0,style:(0,i.Tr)(e.meta.color?{color:e.meta.color}:"")},{default:(0,a.k6)((()=>[((0,a.uX)(),(0,a.Wv)((0,a.$y)(e.meta.icon||"el-icon-menu")))])),_:2},1032,["style"])):(0,a.Q3)("",!0),(0,a.Lk)("span",{style:(0,i.Tr)(e.meta.color?{color:e.meta.color}:"")},(0,i.v_)(e.meta.title),5),e.meta.tag?((0,a.uX)(),(0,a.CE)("span",ee,(0,i.v_)(e.meta.tag),1)):(0,a.Q3)("",!0)])),default:(0,a.k6)((()=>[(0,a.bF)(c,{navMenus:e.children},null,8,["navMenus"])])),_:2},1032,["index"])):((0,a.uX)(),(0,a.Wv)(d,{key:0,index:e.path,style:(0,i.Tr)(e.meta.color?{color:e.meta.color}:"")},{title:(0,a.k6)((()=>[(0,a.Lk)("span",null,(0,i.v_)(e.meta.title),1),e.meta.tag?((0,a.uX)(),(0,a.CE)("span",Z,(0,i.v_)(e.meta.tag),1)):(0,a.Q3)("",!0)])),default:(0,a.k6)((()=>[e.meta&&"link"==e.meta.type?((0,a.uX)(),(0,a.CE)("a",{key:0,href:e.path,target:"_blank",onClick:t[0]||(t[0]=(0,H.D$)((()=>{}),["stop"]))},null,8,J)):(0,a.Q3)("",!0),e.meta&&e.meta.icon?((0,a.uX)(),(0,a.Wv)(r,{key:1},{default:(0,a.k6)((()=>[((0,a.uX)(),(0,a.Wv)((0,a.$y)(e.meta.icon||"el-icon-menu")))])),_:2},1024)):(0,a.Q3)("",!0)])),_:2},1032,["index","style"]))],64)))),128))],64)}var le={name:"NavMenu",props:["navMenus"],data(){return{}},methods:{hasChildren(e){return e.children&&!e.children.every((e=>e.meta.hidden))}}},ae=l(6262);const ie=(0,ae.A)(le,[["render",te]]);var se=ie,oe={components:{NavMenu:se},data(){return{nav:!1,menu:[]}},computed:{},created(){var e=this.$router.sc_getMenu();this.menu=this.filterUrl(e)},watch:{},methods:{showMobileNav(e){var t=e.currentTarget.getAttribute("drag-flag");if("true"==t)return!1;this.nav=!0},select(){this.$refs.mobileNavBox.handleClose()},filterUrl(e){var t=[];return e&&e.forEach((e=>{if(e.meta=e.meta?e.meta:{},e.meta.hidden||"button"==e.meta.type)return!1;"iframe"==e.meta.type&&(e.path=`/i/${e.name}`),e.children&&e.children.length>0&&(e.children=this.filterUrl(e.children)),t.push(e)})),t}},directives:{drag(e){let t=e,l="",a="";t.onmousedown=function(e){let i=e.clientX-t.offsetLeft,s=e.clientY-t.offsetTop;return document.onmousemove=function(e){t.setAttribute("drag-flag",!0),l=(new Date).getTime();let a=e.clientX-i,o=e.clientY-s;o>0&&o<document.body.clientHeight-50&&(t.style.top=o+"px"),a>0&&a<document.body.clientWidth-50&&(t.style.left=a+"px")},document.onmouseup=function(){a=(new Date).getTime(),a-l>200&&t.setAttribute("drag-flag",!1),document.onmousemove=null,document.onmouseup=null},!1}}}};const ne=(0,ae.A)(oe,[["render",j],["__scopeId","data-v-a483418e"]]);var ue=ne;const re=e=>((0,a.Qi)("data-v-d25726ee"),e=e(),(0,a.jt)(),e),de={class:"adminui-topbar"},ce={class:"left-panel"},me=re((()=>(0,a.Lk)("div",{class:"center-panel"},null,-1))),he={class:"right-panel"};function pe(e,t,l,s,o,n){const u=(0,a.g2)("el-icon"),r=(0,a.g2)("el-breadcrumb-item"),d=(0,a.g2)("el-breadcrumb");return(0,a.uX)(),(0,a.CE)("div",de,[(0,a.Lk)("div",ce,[(0,a.bF)(d,{"separator-icon":"el-icon-arrow-right",class:"hidden-sm-and-down"},{default:(0,a.k6)((()=>[(0,a.bF)(H.F,{name:"breadcrumb"},{default:(0,a.k6)((()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(o.breadList,(e=>((0,a.uX)(),(0,a.CE)(a.FK,{key:e.title},["/"==e.path||e.meta.hiddenBreadcrumb?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.Wv)(r,{key:e.meta.title},{default:(0,a.k6)((()=>[e.meta.icon?((0,a.uX)(),(0,a.Wv)(u,{key:0,class:"icon"},{default:(0,a.k6)((()=>[((0,a.uX)(),(0,a.Wv)((0,a.$y)(e.meta.icon)))])),_:2},1024)):(0,a.Q3)("",!0),(0,a.eW)((0,i.v_)(e.meta.title),1)])),_:2},1024))],64)))),128))])),_:1})])),_:1})]),me,(0,a.Lk)("div",he,[(0,a.RG)(e.$slots,"default",{},void 0,!0)])])}var ke={data(){return{breadList:[]}},created(){this.getBreadcrumb()},watch:{$route(){this.getBreadcrumb()}},methods:{getBreadcrumb(){let e=this.$route.meta.breadcrumb;this.breadList=e}}};const ge=(0,ae.A)(ke,[["render",pe],["__scopeId","data-v-d25726ee"]]);var fe=ge;const be={class:"adminui-tags"},ve={ref:"tags"},ye=["onContextmenu"],Fe=(0,a.Lk)("hr",null,null,-1),_e=(0,a.Lk)("hr",null,null,-1);function Le(e,t,l,s,o,n){const u=(0,a.g2)("el-icon-close"),r=(0,a.g2)("el-icon"),d=(0,a.g2)("router-link"),c=(0,a.g2)("el-icon-refresh"),m=(0,a.g2)("el-icon-folder-delete"),h=(0,a.g2)("el-icon-full-screen"),p=(0,a.g2)("el-icon-copy-document");return(0,a.uX)(),(0,a.CE)(a.FK,null,[(0,a.Lk)("div",be,[(0,a.Lk)("ul",ve,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(o.tagList,(e=>((0,a.uX)(),(0,a.CE)("li",{key:e,class:(0,i.C4)([n.isActive(e)?"active":"",e.meta.affix?"affix":""]),onContextmenu:(0,H.D$)((t=>n.openContextMenu(t,e)),["prevent"])},[(0,a.bF)(d,{to:e},{default:(0,a.k6)((()=>[(0,a.Lk)("span",null,(0,i.v_)(e.meta.title),1),e.meta.affix?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.Wv)(r,{key:0,onClick:(0,H.D$)((t=>n.closeSelectedTag(e)),["prevent","stop"])},{default:(0,a.k6)((()=>[(0,a.bF)(u)])),_:2},1032,["onClick"]))])),_:2},1032,["to"])],42,ye)))),128))],512)]),(0,a.bF)(H.eB,{name:"el-zoom-in-top"},{default:(0,a.k6)((()=>[o.contextMenuVisible?((0,a.uX)(),(0,a.CE)("ul",{key:0,style:(0,i.Tr)({left:o.left+"px",top:o.top+"px"}),class:"contextmenu",id:"contextmenu"},[(0,a.Lk)("li",{onClick:t[0]||(t[0]=e=>n.refreshTab())},[(0,a.bF)(r,null,{default:(0,a.k6)((()=>[(0,a.bF)(c)])),_:1}),(0,a.eW)("刷新")]),Fe,(0,a.Lk)("li",{onClick:t[1]||(t[1]=e=>n.closeTabs()),class:(0,i.C4)(o.contextMenuItem.meta.affix?"disabled":"")},[(0,a.bF)(r,null,{default:(0,a.k6)((()=>[(0,a.bF)(u)])),_:1}),(0,a.eW)("关闭标签")],2),(0,a.Lk)("li",{onClick:t[2]||(t[2]=e=>n.closeOtherTabs())},[(0,a.bF)(r,null,{default:(0,a.k6)((()=>[(0,a.bF)(m)])),_:1}),(0,a.eW)("关闭其他标签")]),_e,(0,a.Lk)("li",{onClick:t[3]||(t[3]=e=>n.maximize())},[(0,a.bF)(r,null,{default:(0,a.k6)((()=>[(0,a.bF)(h)])),_:1}),(0,a.eW)("最大化")]),(0,a.Lk)("li",{onClick:t[4]||(t[4]=e=>n.openWindow())},[(0,a.bF)(r,null,{default:(0,a.k6)((()=>[(0,a.bF)(p)])),_:1}),(0,a.eW)("在新的窗口中打开")])],4)):(0,a.Q3)("",!0)])),_:1})],64)}var Ce=l(246),$e={name:"tags",data(){return{contextMenuVisible:!1,contextMenuItem:null,left:0,top:0,tagList:this.$store.state.viewTags.viewTags,tipDisplayed:!1}},props:{},watch:{$route(e){this.addViewTags(e),this.$nextTick((()=>{const e=this.$refs.tags;if(e&&e.scrollWidth>e.clientWidth){let t=e.querySelector(".active");t.scrollIntoView(),this.tipDisplayed||(this.$msgbox({type:"warning",center:!0,title:"提示",message:"当前标签数量过多，可通过鼠标滚轴滚动标签栏。关闭标签数量可减少系统性能消耗。",confirmButtonText:"知道了"}),this.tipDisplayed=!0)}}))},contextMenuVisible(e){const t=e=>{const t=document.getElementById("contextmenu");t&&!t.contains(e.target)&&this.closeMenu()};e?document.body.addEventListener("click",(e=>t(e))):document.body.removeEventListener("click",(e=>t(e)))}},created(){var e=this.$router.sc_getMenu(),t=this.treeFind(e,(e=>e.path==this.$CONFIG.DASHBOARD_URL));t&&(t.fullPath=t.path,this.addViewTags(t),this.addViewTags(this.$route))},mounted(){this.tagDrop(),this.scrollInit()},methods:{treeFind(e,t){for(const l of e){if(t(l))return l;if(l.children){const e=this.treeFind(l.children,t);if(e)return e}}return null},tagDrop(){const e=this.$refs.tags;Ce.Ay.create(e,{draggable:"li",animation:300})},addViewTags(e){e.name&&!e.meta.fullpage&&(this.$store.commit("pushViewTags",e),this.$store.commit("pushKeepLive",e.name))},isActive(e){return e.fullPath===this.$route.fullPath},closeSelectedTag(e,t=!0){const l=this.tagList.findIndex((t=>t.fullPath==e.fullPath));if(this.$store.commit("removeViewTags",e),this.$store.commit("removeIframeList",e),this.$store.commit("removeKeepLive",e.name),t&&this.isActive(e)){const e=this.tagList[l-1];e?this.$router.push(e):this.$router.push("/")}},openContextMenu(e,t){this.contextMenuItem=t,this.contextMenuVisible=!0,this.left=e.clientX+1,this.top=e.clientY+1,this.$nextTick((()=>{let t=document.getElementById("contextmenu");document.body.offsetWidth-e.clientX<t.offsetWidth&&(this.left=document.body.offsetWidth-t.offsetWidth+1,this.top=e.clientY+1)}))},closeMenu(){this.contextMenuItem=null,this.contextMenuVisible=!1},refreshTab(){this.contextMenuVisible=!1;const e=this.contextMenuItem;this.$route.fullPath!==e.fullPath&&this.$router.push({path:e.fullPath,query:e.query}),this.$store.commit("refreshIframe",e),setTimeout((()=>{this.$store.commit("removeKeepLive",e.name),this.$store.commit("setRouteShow",!1),this.$nextTick((()=>{this.$store.commit("pushKeepLive",e.name),this.$store.commit("setRouteShow",!0)}))}),0)},closeTabs(){var e=this.contextMenuItem;e.meta.affix||(this.closeSelectedTag(e),this.contextMenuVisible=!1)},closeOtherTabs(){var e=this.contextMenuItem;this.$route.fullPath!=e.fullPath&&this.$router.push({path:e.fullPath,query:e.query});var t=[...this.tagList];t.forEach((t=>{if(t.meta&&t.meta.affix||e.fullPath==t.fullPath)return!0;this.closeSelectedTag(t,!1)})),this.contextMenuVisible=!1},maximize(){var e=this.contextMenuItem;this.contextMenuVisible=!1,this.$route.fullPath!=e.fullPath&&this.$router.push({path:e.fullPath,query:e.query}),document.getElementById("app").classList.add("main-maximize")},openWindow(){var e=this.contextMenuItem,t=e.href||"/";e.meta.affix||this.closeSelectedTag(e),window.open(t),this.contextMenuVisible=!1},scrollInit(){const e=this.$refs.tags;function t(t){const l=t.wheelDelta||t.detail,a=1,i=-1;let s=0;s=3==l||l<0&&-3!=l?50*a:50*i,e.scrollLeft+=s}e.addEventListener("mousewheel",t,!1)||e.addEventListener("DOMMouseScroll",t,!1)}}};const we=(0,ae.A)($e,[["render",Le]]);var Ee=we;const xe={class:"user-bar"},Te={class:"msg-list"},Xe=["href"],Ie={class:"msg-list__icon"},Me={class:"msg-list__main"},We={class:"msg-list__time"},Ve={class:"user-avatar"};function Oe(e,t,l,s,o,n){const u=(0,a.g2)("el-icon-search"),r=(0,a.g2)("el-icon"),d=(0,a.g2)("el-icon-full-screen"),c=(0,a.g2)("el-icon-sort"),m=(0,a.g2)("el-icon-chat-dot-round"),h=(0,a.g2)("el-badge"),p=(0,a.g2)("el-avatar"),k=(0,a.g2)("el-empty"),g=(0,a.g2)("el-scrollbar"),f=(0,a.g2)("el-main"),b=(0,a.g2)("el-button"),v=(0,a.g2)("el-footer"),y=(0,a.g2)("el-container"),F=(0,a.g2)("el-drawer"),_=(0,a.g2)("el-icon-arrow-down"),L=(0,a.g2)("el-dropdown-item"),C=(0,a.g2)("el-dropdown-menu"),$=(0,a.g2)("el-dropdown"),w=(0,a.g2)("search"),E=(0,a.g2)("el-dialog"),x=(0,a.g2)("tasks"),T=(0,a.g2)("password");return(0,a.uX)(),(0,a.CE)(a.FK,null,[(0,a.Lk)("div",xe,[(0,a.Lk)("div",{class:"panel-item hidden-sm-and-down",onClick:t[0]||(t[0]=(...e)=>n.search&&n.search(...e))},[(0,a.bF)(r,null,{default:(0,a.k6)((()=>[(0,a.bF)(u)])),_:1})]),(0,a.Lk)("div",{class:"screen panel-item hidden-sm-and-down",onClick:t[1]||(t[1]=(...e)=>n.screen&&n.screen(...e))},[(0,a.bF)(r,null,{default:(0,a.k6)((()=>[(0,a.bF)(d)])),_:1})]),(0,a.Lk)("div",{class:"tasks panel-item",onClick:t[2]||(t[2]=(...e)=>n.tasks&&n.tasks(...e))},[(0,a.bF)(r,null,{default:(0,a.k6)((()=>[(0,a.bF)(c)])),_:1})]),(0,a.Lk)("div",{class:"msg panel-item",onClick:t[4]||(t[4]=(...e)=>n.showMsg&&n.showMsg(...e))},[(0,a.bF)(h,{hidden:0==o.msgList.length,value:o.msgList.length,class:"badge",type:"danger"},{default:(0,a.k6)((()=>[(0,a.bF)(r,null,{default:(0,a.k6)((()=>[(0,a.bF)(m)])),_:1})])),_:1},8,["hidden","value"]),(0,a.bF)(F,{title:"新消息",modelValue:o.msg,"onUpdate:modelValue":t[3]||(t[3]=e=>o.msg=e),size:400,"append-to-body":"","destroy-on-close":""},{default:(0,a.k6)((()=>[(0,a.bF)(y,null,{default:(0,a.k6)((()=>[(0,a.bF)(f,{class:"nopadding"},{default:(0,a.k6)((()=>[(0,a.bF)(g,null,{default:(0,a.k6)((()=>[(0,a.Lk)("ul",Te,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(o.msgList,(e=>((0,a.uX)(),(0,a.CE)("li",{key:e.id},[(0,a.Lk)("a",{href:e.link,target:"_blank"},[(0,a.Lk)("div",Ie,[(0,a.bF)(h,{"is-dot":"",type:"danger"},{default:(0,a.k6)((()=>[(0,a.bF)(p,{size:40,src:e.avatar},null,8,["src"])])),_:2},1024)]),(0,a.Lk)("div",Me,[(0,a.Lk)("h2",null,(0,i.v_)(e.title),1),(0,a.Lk)("p",null,(0,i.v_)(e.describe),1)]),(0,a.Lk)("div",We,[(0,a.Lk)("p",null,(0,i.v_)(e.time),1)])],8,Xe)])))),128)),0==o.msgList.length?((0,a.uX)(),(0,a.Wv)(k,{key:0,description:"暂无新消息","image-size":100})):(0,a.Q3)("",!0)])])),_:1})])),_:1}),(0,a.bF)(v,null,{default:(0,a.k6)((()=>[(0,a.bF)(b,{type:"primary"},{default:(0,a.k6)((()=>[(0,a.eW)("消息中心")])),_:1}),(0,a.bF)(b,{onClick:n.markRead},{default:(0,a.k6)((()=>[(0,a.eW)("全部设为已读")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["modelValue"])]),(0,a.bF)($,{class:"user panel-item",trigger:"click",onCommand:n.handleUser},{dropdown:(0,a.k6)((()=>[(0,a.bF)(C,null,{default:(0,a.k6)((()=>[(0,a.bF)(L,{command:"pass"},{default:(0,a.k6)((()=>[(0,a.eW)("修改密码")])),_:1}),(0,a.bF)(L,{command:"clearCache"},{default:(0,a.k6)((()=>[(0,a.eW)("清除缓存")])),_:1}),(0,a.bF)(L,{divided:"",command:"outLogin"},{default:(0,a.k6)((()=>[(0,a.eW)("退出登录")])),_:1})])),_:1})])),default:(0,a.k6)((()=>[(0,a.Lk)("div",Ve,[(0,a.bF)(p,{size:30},{default:(0,a.k6)((()=>[(0,a.eW)((0,i.v_)(o.userNameF),1)])),_:1}),(0,a.Lk)("label",null,(0,i.v_)(o.userName),1),(0,a.bF)(r,{class:"el-icon--right"},{default:(0,a.k6)((()=>[(0,a.bF)(_)])),_:1})])])),_:1},8,["onCommand"])]),(0,a.bF)(E,{modelValue:o.searchVisible,"onUpdate:modelValue":t[6]||(t[6]=e=>o.searchVisible=e),width:700,title:"搜索",center:"","destroy-on-close":""},{default:(0,a.k6)((()=>[(0,a.bF)(w,{onSuccess:t[5]||(t[5]=e=>o.searchVisible=!1)})])),_:1},8,["modelValue"]),(0,a.bF)(F,{modelValue:o.tasksVisible,"onUpdate:modelValue":t[7]||(t[7]=e=>o.tasksVisible=e),size:450,title:"任务中心","destroy-on-close":""},{default:(0,a.k6)((()=>[(0,a.bF)(x)])),_:1},8,["modelValue"]),o.passwordVisible?((0,a.uX)(),(0,a.Wv)(T,{key:0,onSuccessed:n.login,ref:"password"},null,8,["onSuccessed"])):(0,a.Q3)("",!0)],64)}const Ae={class:"sc-search"},Pe={key:0,class:"sc-search-history"},Ne={class:"sc-search-result"},Qe={key:0,class:"sc-search-no-result"},Ue={key:1},Re=["onClick"],Se={class:"title"};function De(e,t,l,s,o,n){const u=(0,a.g2)("el-input"),r=(0,a.g2)("el-tag"),d=(0,a.g2)("el-icon"),c=(0,a.g2)("el-scrollbar");return(0,a.uX)(),(0,a.CE)("div",Ae,[(0,a.bF)(u,{ref:"input",modelValue:o.input,"onUpdate:modelValue":t[0]||(t[0]=e=>o.input=e),placeholder:"搜索",size:"large",clearable:"","prefix-icon":"el-icon-search","trigger-on-focus":!1,onInput:n.inputChange},null,8,["modelValue","onInput"]),o.history.length>0?((0,a.uX)(),(0,a.CE)("div",Pe,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(o.history,((e,t)=>((0,a.uX)(),(0,a.Wv)(r,{closable:"",effect:"dark",type:"info",key:e,onClick:t=>n.historyClick(e),onClose:e=>n.historyClose(t)},{default:(0,a.k6)((()=>[(0,a.eW)((0,i.v_)(e),1)])),_:2},1032,["onClick","onClose"])))),128))])):(0,a.Q3)("",!0),(0,a.Lk)("div",Ne,[o.result.length<=0?((0,a.uX)(),(0,a.CE)("div",Qe,"暂无搜索结果")):((0,a.uX)(),(0,a.CE)("ul",Ue,[(0,a.bF)(c,{"max-height":"366px"},{default:(0,a.k6)((()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(o.result,(e=>((0,a.uX)(),(0,a.CE)("li",{key:e.path,onClick:t=>n.to(e)},[(0,a.bF)(d,null,{default:(0,a.k6)((()=>[((0,a.uX)(),(0,a.Wv)((0,a.$y)(e.icon||"el-icon-menu")))])),_:2},1024),(0,a.Lk)("span",Se,(0,i.v_)(e.breadcrumb),1)],8,Re)))),128))])),_:1})]))])])}var ze={data(){return{input:"",menu:[],result:[],history:[]}},mounted(){var e=this.$TOOL.data.get("SEARCH_HISTORY")||[];this.history=e;var t=this.$TOOL.data.get("MENU");this.filterMenu(t),this.$refs.input.focus()},methods:{inputChange(e){this.result=e?this.menuFilter(e):[]},filterMenu(e){e.forEach((e=>{if(e.meta.hidden||"button"==e.meta.type)return!1;"iframe"==e.meta.type&&(e.path=`/i/${e.name}`),e.children&&e.children.length>0&&!e.component?this.filterMenu(e.children):this.menu.push(e)}))},menuFilter(e){var t=[],l=this.menu.filter((t=>t.meta.title.toLowerCase().indexOf(e.toLowerCase())>=0||(t.name.toLowerCase().indexOf(e.toLowerCase())>=0||void 0))),a=this.$router.getRoutes(),i=l.map((e=>"link"==e.meta.type?a.find((t=>t.path=="/"+e.path)):a.find((t=>t.path==e.path))));return i.forEach((e=>{t.push({name:e.name,type:e.meta.type,path:"link"==e.meta.type?e.path.slice(1):e.path,icon:e.meta.icon,title:e.meta.title,breadcrumb:e.meta.breadcrumb.map((e=>e.meta.title)).join(" - ")})})),t},to(e){this.history.includes(this.input)||(this.history.push(this.input),this.$TOOL.data.set("SEARCH_HISTORY",this.history)),"link"==e.type?setTimeout((()=>{let t=document.createElement("a");t.style="display: none",t.target="_blank",t.href=e.path,document.body.appendChild(t),t.click(),document.body.removeChild(t)}),10):this.$router.push({path:e.path}),this.$emit("success",!0)},historyClick(e){this.input=e,this.inputChange(e)},historyClose(e){this.history.splice(e,1),this.history.length<=0?this.$TOOL.data.remove("SEARCH_HISTORY"):this.$TOOL.data.set("SEARCH_HISTORY",this.history)}}};const Ge=(0,ae.A)(ze,[["render",De],["__scopeId","data-v-2bd43785"]]);var Ke=Ge;const Be=e=>((0,a.Qi)("data-v-49f38d9c"),e=e(),(0,a.jt)(),e),qe=Be((()=>(0,a.Lk)("h2",null,"没有正在执行的任务",-1))),je=Be((()=>(0,a.Lk)("p",{style:{"font-size":"14px",color:"#999","line-height":"1.5",margin:"0 40px"}}," 在处理耗时过久的任务时为了不阻碍正在处理的工作，可在任务中心进行异步执行。",-1))),He={class:"user-bar-tasks-item-body"},Ye={class:"taskIcon"},Je={class:"taskMain"},Ze={class:"title"},et={class:"bottom"},tt={class:"state"},lt={class:"handler"};function at(e,t,l,s,o,n){const u=(0,a.g2)("el-empty"),r=(0,a.g2)("el-avatar"),d=(0,a.g2)("el-tag"),c=(0,a.g2)("el-button"),m=(0,a.g2)("el-card"),h=(0,a.g2)("el-main"),p=(0,a.g2)("el-footer"),k=(0,a.g2)("el-container"),g=(0,a.gN)("time"),f=(0,a.gN)("loading");return(0,a.bo)(((0,a.uX)(),(0,a.Wv)(k,null,{default:(0,a.k6)((()=>[(0,a.bF)(h,null,{default:(0,a.k6)((()=>[0==o.tasks.length?((0,a.uX)(),(0,a.Wv)(u,{key:0,"image-size":120},{description:(0,a.k6)((()=>[qe])),default:(0,a.k6)((()=>[je])),_:1})):(0,a.Q3)("",!0),((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(o.tasks,(e=>((0,a.uX)(),(0,a.Wv)(m,{key:e.id,shadow:"hover",class:"user-bar-tasks-item"},{default:(0,a.k6)((()=>[(0,a.Lk)("div",He,[(0,a.Lk)("div",Ye,[(0,a.bF)(r,{style:{width:"60px",height:"60px","font-size":"18px"}},{default:(0,a.k6)((()=>[(0,a.eW)((0,i.v_)(e.group),1)])),_:2},1024)]),(0,a.Lk)("div",Je,[(0,a.Lk)("div",Ze,[(0,a.Lk)("h2",null,(0,i.v_)(e.taskName),1),(0,a.Lk)("p",null,[(0,a.bo)((0,a.Lk)("span",null,null,512),[[g,e.createDate,void 0,{tip:!0}]]),(0,a.eW)(" "+(0,i.v_)(e.time),1)])]),(0,a.Lk)("div",et,[(0,a.Lk)("div",tt,[e.state?((0,a.uX)(),(0,a.Wv)(d,{key:0,type:"success",size:"large"},{default:(0,a.k6)((()=>[(0,a.eW)("执行中")])),_:1})):(0,a.Q3)("",!0),e.state?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.Wv)(d,{key:1,size:"large"},{default:(0,a.k6)((()=>[(0,a.eW)("等待执行")])),_:1}))]),(0,a.Lk)("div",lt,[e.state?(0,a.Q3)("",!0):(0,a.bo)(((0,a.uX)(),(0,a.Wv)(c,{key:0,type:"primary",icon:"el-icon-bell",onClick:t=>n.exec(e)},{default:(0,a.k6)((()=>[(0,a.eW)("执行")])),_:2},1032,["onClick"])),[[f,e.loading]])])])])])])),_:2},1024)))),128))])),_:1}),(0,a.bF)(p,{style:{padding:"10px","text-align":"right"}},{default:(0,a.k6)((()=>[(0,a.bF)(c,{icon:"el-icon-refresh",onClick:n.refresh},{default:(0,a.k6)((()=>[(0,a.eW)("刷新")])),_:1},8,["onClick"])])),_:1})])),_:1})),[[f,o.loading]])}var it={data(){return{loading:!1,tasks:[]}},mounted(){this.getData()},methods:{async getData(){this.loading=!0;var e=await this.$API.home.getQuatzList.post();this.tasks=e.data,this.loading=!1},refresh(){this.getData()},async exec(e){let t;if(e.loading=!0,"BiliBili"==e.group)t=await this.$API.biliQuartz.execQuartz.post({jObjectParam:{Fid:e.id,FJobName:e.taskName}});else{if("斗鱼"!=e.group)return void this.$alert("无此类型的任务！","提示",{type:"error"});t=await this.$API.douyuQuartz.execQuartz.post({jObjectParam:{Fid:e.id,FJobName:e.taskName}})}e.loading=!1,0==t.code?(this.$message.success("操作成功"),await this.getData()):this.$alert(t.message,"提示",{type:"error"})}}};const st=(0,ae.A)(it,[["render",at],["__scopeId","data-v-49f38d9c"]]);var ot=st;function nt(e,t,l,i,s,o){const n=(0,a.g2)("el-input"),u=(0,a.g2)("el-form-item"),r=(0,a.g2)("el-col"),d=(0,a.g2)("el-row"),c=(0,a.g2)("el-form"),m=(0,a.g2)("el-button"),h=(0,a.g2)("el-dialog");return(0,a.uX)(),(0,a.Wv)(h,{modelValue:s.visible,"onUpdate:modelValue":t[4]||(t[4]=e=>s.visible=e),title:"修改密码",width:"500"},{footer:(0,a.k6)((()=>[(0,a.bF)(m,{onClick:t[3]||(t[3]=e=>s.visible=!1)},{default:(0,a.k6)((()=>[(0,a.eW)("取 消")])),_:1}),(0,a.bF)(m,{type:"primary",loading:s.isSaveing,onClick:o.save},{default:(0,a.k6)((()=>[(0,a.eW)("提交")])),_:1},8,["loading","onClick"])])),default:(0,a.k6)((()=>[(0,a.bF)(c,{ref:"form",model:s.form,rules:s.rules,"label-width":120},{default:(0,a.k6)((()=>[(0,a.bF)(d,null,{default:(0,a.k6)((()=>[(0,a.bF)(r,null,{default:(0,a.k6)((()=>[(0,a.bF)(u,{label:"旧密码",prop:"oldpw1"},{default:(0,a.k6)((()=>[(0,a.bF)(n,{modelValue:s.form.oldpw1,"onUpdate:modelValue":t[0]||(t[0]=e=>s.form.oldpw1=e),"show-password":"",placeholder:"请输入旧密码"},null,8,["modelValue"])])),_:1})])),_:1}),(0,a.bF)(r,null,{default:(0,a.k6)((()=>[(0,a.bF)(u,{label:"新密码",prop:"newpw1"},{default:(0,a.k6)((()=>[(0,a.bF)(n,{modelValue:s.form.newpw1,"onUpdate:modelValue":t[1]||(t[1]=e=>s.form.newpw1=e),"show-password":"",placeholder:"请输入新密码"},null,8,["modelValue"])])),_:1})])),_:1}),(0,a.bF)(r,null,{default:(0,a.k6)((()=>[(0,a.bF)(u,{label:"确认新密码",prop:"newpw2"},{default:(0,a.k6)((()=>[(0,a.bF)(n,{modelValue:s.form.newpw2,"onUpdate:modelValue":t[2]||(t[2]=e=>s.form.newpw2=e),"show-password":"",placeholder:"请再一次输入新密码"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue"])}var ut={data(){return{form:{},visible:!0,isSaveing:!1,rules:{oldpw1:[{required:!0,message:"请输入旧密码"}],newpw1:[{required:!0,message:"请输入新的密码"}],newpw2:[{required:!0,message:"请再次输入新的密码"},{validator:(e,t,l)=>{this.form.newpw1!=this.form.newpw2?l(new Error("两次输入密码不一致")):l()}}]}}},mounted(){},methods:{open(){this.visible=!0},async save(){this.$refs.form.validate((async e=>{if(!e)return!1;this.isSaveing=!0;var t=await this.$API.sysUser.editPWD.post({jObjectParam:this.form});this.isSaveing=!1,0==t.code?(this.visible=!1,this.$alert(t.message,"提示",{type:"success"}),this.$emit("successed")):this.$alert(t.message,"提示",{type:"error"})}))},backLogin(){this.$router.push({path:"/login"})}}};const rt=(0,ae.A)(ut,[["render",nt]]);var dt=rt,ct={components:{search:Ke,tasks:ot,password:dt},data(){return{userName:"",userNameF:"",passwordVisible:!1,searchVisible:!1,tasksVisible:!1,msg:!1,msgList:[]}},created(){var e=this.$TOOL.data.get("USER_INFO");this.userName=e.userName,this.userNameF=this.userName.substring(0,1)},methods:{login(){this.$router.replace({path:"/login"})},handleUser(e){"pass"==e&&(this.passwordVisible=!0,this.$nextTick((()=>{this.$refs.password.open()}))),"cmd"==e&&this.$router.push({path:"/cmd"}),"clearCache"==e&&this.$confirm("清除缓存会将系统初始化，包括登录状态、主题、语言设置等，是否继续？","提示",{type:"info"}).then((()=>{const e=this.$loading();this.$TOOL.data.clear(),this.$router.replace({path:"/login"}),setTimeout((()=>{e.close(),location.reload()}),1e3)})).catch((()=>{})),"outLogin"==e&&this.$confirm("确认是否退出当前用户？","提示",{type:"warning",confirmButtonText:"退出",confirmButtonClass:"el-button--danger"}).then((()=>{this.$router.replace({path:"/login"}),this.$TOOL.data.set("REMEMBER_ME",!1)})).catch((()=>{}))},screen(){var e=document.documentElement;this.$TOOL.screen(e)},showMsg(){this.msg=!0},markRead(){this.msgList=[]},search(){this.searchVisible=!0},tasks(){this.tasksVisible=!0}}};const mt=(0,ae.A)(ct,[["render",Oe],["__scopeId","data-v-5e35ab42"]]);var ht=mt;function pt(e,t,l,i,s,o){const n=(0,a.g2)("el-alert"),u=(0,a.g2)("el-divider"),r=(0,a.g2)("el-switch"),d=(0,a.g2)("el-form-item"),c=(0,a.g2)("el-option"),m=(0,a.g2)("el-select"),h=(0,a.g2)("el-color-picker"),p=(0,a.g2)("el-form");return(0,a.uX)(),(0,a.Wv)(p,{ref:"form","label-width":"120px","label-position":"left",style:{padding:"0 20px"}},{default:(0,a.k6)((()=>[(0,a.bF)(n,{title:"以下配置可实时预览，开发者可在 config/index.js 中配置默认值，非常不建议在生产环境下开放布局设置",type:"error",closable:!1}),(0,a.bF)(u),(0,a.bF)(d,{label:e.$t("user.nightmode")},{default:(0,a.k6)((()=>[(0,a.bF)(r,{modelValue:s.dark,"onUpdate:modelValue":t[0]||(t[0]=e=>s.dark=e)},null,8,["modelValue"])])),_:1},8,["label"]),(0,a.bF)(d,{label:e.$t("user.language")},{default:(0,a.k6)((()=>[(0,a.bF)(m,{modelValue:s.lang,"onUpdate:modelValue":t[1]||(t[1]=e=>s.lang=e)},{default:(0,a.k6)((()=>[(0,a.bF)(c,{label:"简体中文",value:"zh-cn"}),(0,a.bF)(c,{label:"English",value:"en"})])),_:1},8,["modelValue"])])),_:1},8,["label"]),(0,a.bF)(u),(0,a.bF)(d,{label:"主题颜色"},{default:(0,a.k6)((()=>[(0,a.bF)(h,{modelValue:s.colorPrimary,"onUpdate:modelValue":t[2]||(t[2]=e=>s.colorPrimary=e),predefine:s.colorList},{default:(0,a.k6)((()=>[(0,a.eW)(">")])),_:1},8,["modelValue","predefine"])])),_:1}),(0,a.bF)(u),(0,a.bF)(d,{label:"框架布局"},{default:(0,a.k6)((()=>[(0,a.bF)(m,{modelValue:s.layout,"onUpdate:modelValue":t[3]||(t[3]=e=>s.layout=e),placeholder:"请选择"},{default:(0,a.k6)((()=>[(0,a.bF)(c,{label:"默认",value:"default"}),(0,a.bF)(c,{label:"通栏",value:"header"}),(0,a.bF)(c,{label:"经典",value:"menu"}),(0,a.bF)(c,{label:"功能坞",value:"dock"})])),_:1},8,["modelValue"])])),_:1}),(0,a.bF)(d,{label:"折叠菜单"},{default:(0,a.k6)((()=>[(0,a.bF)(r,{modelValue:s.menuIsCollapse,"onUpdate:modelValue":t[4]||(t[4]=e=>s.menuIsCollapse=e)},null,8,["modelValue"])])),_:1}),(0,a.bF)(d,{label:"标签栏"},{default:(0,a.k6)((()=>[(0,a.bF)(r,{modelValue:s.layoutTags,"onUpdate:modelValue":t[5]||(t[5]=e=>s.layoutTags=e)},null,8,["modelValue"])])),_:1}),(0,a.bF)(u)])),_:1},512)}var kt=l(3058),gt={data(){return{layout:this.$store.state.global.layout,menuIsCollapse:this.$store.state.global.menuIsCollapse,layoutTags:this.$store.state.global.layoutTags,lang:this.$TOOL.data.get("APP_LANG")||this.$CONFIG.LANG,dark:this.$TOOL.data.get("APP_DARK")||!1,colorList:["#409EFF","#009688","#536dfe","#ff5c93","#c62f2f","#fd726d"],colorPrimary:this.$TOOL.data.get("APP_COLOR")||this.$CONFIG.COLOR||"#409EFF"}},watch:{layout(e){this.$store.commit("SET_layout",e)},menuIsCollapse(){this.$store.commit("TOGGLE_menuIsCollapse")},layoutTags(){this.$store.commit("TOGGLE_layoutTags")},dark(e){e?(document.documentElement.classList.add("dark"),this.$TOOL.data.set("APP_DARK",e)):(document.documentElement.classList.remove("dark"),this.$TOOL.data.remove("APP_DARK"))},lang(e){this.$i18n.locale=e,this.$TOOL.data.set("APP_LANG",e)},colorPrimary(e){e||(e="#409EFF",this.colorPrimary="#409EFF"),document.documentElement.style.setProperty("--el-color-primary",e);for(let t=1;t<=9;t++)document.documentElement.style.setProperty(`--el-color-primary-light-${t}`,kt.A.lighten(e,t/10));for(let t=1;t<=9;t++)document.documentElement.style.setProperty(`--el-color-primary-dark-${t}`,kt.A.darken(e,t/10));this.$TOOL.data.set("APP_COLOR",e)}}};const ft=(0,ae.A)(gt,[["render",pt]]);var bt=ft;const vt={class:"iframe-pages"},yt=["src"];function Ft(e,t,l,i,s,o){return(0,a.bo)(((0,a.uX)(),(0,a.CE)("div",vt,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(o.iframeList,(t=>(0,a.bo)(((0,a.uX)(),(0,a.CE)("iframe",{key:t.meta.url,src:t.meta.url,frameborder:"0"},null,8,yt)),[[H.aG,e.$route.meta.url==t.meta.url]]))),128))],512)),[[H.aG,"iframe"==e.$route.meta.type]])}var _t={data(){return{}},watch:{$route(e){this.push(e)}},created(){this.push(this.$route)},computed:{iframeList(){return this.$store.state.iframe.iframeList},ismobile(){return this.$store.state.global.ismobile},layoutTags(){return this.$store.state.global.layoutTags}},mounted(){},methods:{push(e){"iframe"==e.meta.type?this.ismobile||!this.layoutTags?this.$store.commit("setIframeList",e):this.$store.commit("pushIframeList",e):!this.ismobile&&this.layoutTags||this.$store.commit("clearIframeList")}}};const Lt=(0,ae.A)(_t,[["render",Ft],["__scopeId","data-v-27bcc55e"]]);var Ct=Lt,$t={render(){},data(){return{logoutCount:this.$TOOL.data.get("AUTO_EXIT")}},mounted(){this.logoutCount&&(this.setNewAutoExitTime(),document.onclick=()=>{this.setNewAutoExitTime()},document.onmousemove=()=>{this.setNewAutoExitTime()},document.onkeydown=()=>{this.setNewAutoExitTime()},document.onscroll=()=>{this.setNewAutoExitTime()},window.autoExitTimer=window.setInterval(this.autoExitfun,1e3))},unmounted(){this.logoutCount&&(clearInterval(window.autoExitTimer),window.autoExitTimer=null)},methods:{setNewAutoExitTime(){window.autoExitTime=(new Date).getTime()},autoExitfun(){(new Date).getTime()-window.autoExitTime>60*this.logoutCount*1e3&&(clearInterval(window.autoExitTimer),window.autoExitTimer=null,this.$router.replace({path:"/login"}),this.$alert("用户长时间无操作，为保证账户安全，系统已自动登出。","提示",{type:"warning",center:!0,roundButton:!0}))}}},wt={name:"index",components:{SideM:ue,Topbar:fe,Tags:Ee,NavMenu:se,userbar:ht,setting:bt,iframeView:Ct,autoExit:$t},data(){return{settingDialog:!1,menu:[],nextMenu:[],pmenu:{},active:""}},computed:{ismobile(){return this.$store.state.global.ismobile},layout(){return this.$store.state.global.layout},layoutTags(){return this.$store.state.global.layoutTags},menuIsCollapse(){return this.$store.state.global.menuIsCollapse}},created(){this.onLayoutResize(),window.addEventListener("resize",this.onLayoutResize);var e=this.$router.sc_getMenu();this.menu=this.filterUrl(e),this.showThis()},watch:{$route(){this.showThis()},layout:{handler(e){document.body.setAttribute("data-layout",e)},immediate:!0}},methods:{openSetting(){this.settingDialog=!0},onLayoutResize(){this.$store.commit("SET_ismobile",document.body.clientWidth<992)},showThis(){this.pmenu=this.$route.meta.breadcrumb?this.$route.meta.breadcrumb[0]:{},this.nextMenu=this.filterUrl(this.pmenu.children),this.$nextTick((()=>{this.active=this.$route.meta.active||this.$route.fullPath}))},showMenu(e){this.pmenu=e,this.nextMenu=this.filterUrl(e.children),e.children&&0!=e.children.length||!e.component||this.$router.push({path:e.path})},filterUrl(e){var t=[];return e&&e.forEach((e=>{if(e.meta=e.meta?e.meta:{},e.meta.hidden||"button"==e.meta.type)return!1;"iframe"==e.meta.type&&(e.path=`/i/${e.name}`),e.children&&e.children.length>0&&(e.children=this.filterUrl(e.children)),t.push(e)})),t},exitMaximize(){document.getElementById("app").classList.remove("main-maximize")}}};const Et=(0,ae.A)(wt,[["render",G]]);var xt=Et}}]);