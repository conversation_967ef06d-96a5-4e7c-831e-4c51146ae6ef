"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[9958],{3307:function(e,a,t){t.r(a),t.d(a,{default:function(){return i}});var l=t(641);const r={class:"left-panel"};function c(e,a,t,c,o,p){const d=(0,l.g2)("el-date-picker"),i=(0,l.g2)("sc-select"),h=(0,l.g2)("el-header"),n=(0,l.g2)("el-table-column"),b=(0,l.g2)("scTable"),s=(0,l.g2)("el-main"),u=(0,l.g2)("el-container"),j=(0,l.gN)("loading");return(0,l.bo)(((0,l.uX)(),(0,l.Wv)(u,null,{default:(0,l.k6)((()=>[(0,l.bF)(h,null,{default:(0,l.k6)((()=>[(0,l.Lk)("div",r,[(0,l.bF)(d,{modelValue:o.jObjectSearch.date,"onUpdate:modelValue":a[0]||(a[0]=e=>o.jObjectSearch.date=e),type:"date",onChange:p.upsearchDate,style:{width:"140px","margin-right":"10px"}},null,8,["modelValue","onChange"]),((0,l.uX)(),(0,l.Wv)(i,{clearable:"",modelValue:o.jObjectSearch.p1,"onUpdate:modelValue":a[1]||(a[1]=e=>o.jObjectSearch.p1=e),key:o.jObjectSearch.date,params:{jObjectSearch:{date:this.$TOOL.dateFormat(o.jObjectSearch.date,"yyyy-MM-dd")}},apiObj:e.$API.biliLog.getPathList,prop:{label:"FName",value:"Fid"},placeholder:"功能名称",style:{width:"200px","padding-right":"10px"},onChange:p.upsearchP1},null,8,["modelValue","params","apiObj","onChange"])),((0,l.uX)(),(0,l.Wv)(i,{clearable:"",modelValue:o.jObjectSearch.p2,"onUpdate:modelValue":a[2]||(a[2]=e=>o.jObjectSearch.p2=e),key:o.jObjectSearch.p1,params:{jObjectSearch:{date:this.$TOOL.dateFormat(o.jObjectSearch.date,"yyyy-MM-dd"),path:o.jObjectSearch.p1}},apiObj:e.$API.biliLog.getPathList,prop:{label:"FName",value:"Fid"},placeholder:"账号名称",style:{width:"200px","padding-right":"10px"},onChange:p.upsearch},null,8,["modelValue","params","apiObj","onChange"]))])])),_:1}),(0,l.bF)(s,{class:"nopadding"},{default:(0,l.k6)((()=>[(0,l.bF)(b,{ref:"table","row-Style":p.rowStyle,apiObj:o.apiObj,border:"",params:{jObjectSearch:o.jObjectSearch},stripe:"",remoteSort:"",remoteFilter:""},{default:(0,l.k6)((()=>[(0,l.bF)(n,{label:"时间",prop:"FDate",align:"center",width:"180"}),(0,l.bF)(n,{label:"任务",prop:"FTask",align:"center",width:"150"}),(0,l.bF)(n,{label:"状态",prop:"FStatus",align:"center",width:"80"}),(0,l.bF)(n,{label:"内容",prop:"FContent","show-overflow-tooltip":"","header-align":"center"})])),_:1},8,["row-Style","apiObj","params"])])),_:1})])),_:1})),[[j,o.updateLoading]])}var o={name:"biliHelpLog",components:{},data(){return{apiObj:this.$API.biliLog.getLogList,updateLoading:!1,dialog:{save:!1},jObjectSearch:{date:this.$TOOL.dateFormat(new Date,"yyyy-MM-dd")}}},async created(){},methods:{upsearch(){this.$refs.table.upData({jObjectSearch:{path:this.jObjectSearch.p2}})},upsearchDate(){this.jObjectSearch.p1="",this.jObjectSearch.p2=""},upsearchP1(){this.jObjectSearch.p2=""},rowStyle(e){return"成功"==e.row.FStatus?"color:#006600":"失败"==e.row.FStatus?"color:#CC0000":"警告"==e.row.FStatus?"color:#CC6600":void 0}}},p=t(6262);const d=(0,p.A)(o,[["render",c]]);var i=d}}]);