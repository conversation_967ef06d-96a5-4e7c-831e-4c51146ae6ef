"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[7790],{7137:function(e,a,t){t.r(a),t.d(a,{default:function(){return h}});var l=t(641);const i={class:"left-panel"},r={class:"right-panel"},d={class:"right-panel-search"};function o(e,a,t,o,s,c){const n=(0,l.g2)("el-option"),h=(0,l.g2)("el-select"),u=(0,l.g2)("el-button"),p=(0,l.g2)("el-input"),b=(0,l.g2)("el-header"),g=(0,l.g2)("el-table-column"),m=(0,l.g2)("el-table"),y=(0,l.g2)("el-main"),j=(0,l.g2)("el-container"),k=(0,l.gN)("loading");return(0,l.uX)(),(0,l.Wv)(j,null,{default:(0,l.k6)((()=>[(0,l.bF)(b,null,{default:(0,l.k6)((()=>[(0,l.Lk)("div",i,[(0,l.bF)(h,{modelValue:s.jObjectSearch.areaId,"onUpdate:modelValue":a[0]||(a[0]=e=>s.jObjectSearch.areaId=e),filterable:"",onChange:c.upsearch,style:{"padding-right":"10px",width:"180px"}},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(s.area,(e=>((0,l.uX)(),(0,l.Wv)(n,{key:e.Fid,label:e.FName,value:e.Fid},null,8,["label","value"])))),128))])),_:1},8,["modelValue","onChange"]),(0,l.bF)(u,{type:"primary",onClick:c.receive,disabled:s.loading},{default:(0,l.k6)((()=>[(0,l.eW)(" 领取详情（任务信息统计）")])),_:1},8,["onClick","disabled"]),(0,l.bF)(u,{type:"primary",onClick:c.date,disabled:s.loading},{default:(0,l.k6)((()=>[(0,l.eW)(" 领取时间（Cdkey领取时间统计）")])),_:1},8,["onClick","disabled"]),(0,l.bF)(u,{type:"primary",onClick:c.daily,disabled:s.loading},{default:(0,l.k6)((()=>[(0,l.eW)(" 每日任务完成情况（接口）")])),_:1},8,["onClick","disabled"]),(0,l.bF)(u,{type:"primary",icon:"el-icon-download",onClick:c.exports,disabled:s.loading},{default:(0,l.k6)((()=>[(0,l.eW)(" 导出报表")])),_:1},8,["onClick","disabled"])]),(0,l.Lk)("div",r,[(0,l.Lk)("div",d,[(0,l.bF)(p,{modelValue:s.jObjectSearch.search,"onUpdate:modelValue":a[1]||(a[1]=e=>s.jObjectSearch.search=e),placeholder:"账号名称",clearable:""},null,8,["modelValue"]),(0,l.bF)(u,{type:"primary",icon:"el-icon-search",onClick:c.upsearch,disabled:s.loading},{default:(0,l.k6)((()=>[(0,l.eW)(" 查询")])),_:1},8,["onClick","disabled"])])])])),_:1}),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(y,{class:"nopadding"},{default:(0,l.k6)((()=>[(0,l.bF)(m,{"cell-style":c.cellStyle,ref:"table",data:s.data,border:"",stripe:"",remoteSort:"",remoteFilter:""},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(s.columns,((e,a)=>((0,l.uX)(),(0,l.Wv)(g,{key:e,label:e,prop:e,align:"center",fixed:0==a,width:0==a?s.headers:s.width,formatter:c.formatter,"show-overflow-tooltip":""},null,8,["label","prop","fixed","width","formatter"])))),128))])),_:1},8,["cell-style","data"])])),_:1})),[[k,s.loading]])])),_:1})}var s={name:"biliReport",components:{},data(){return{loading:!0,dialog:{},jObjectSearch:{search:"",type:"receive"},area:[],data:[],columns:[],width:180,headers:180}},async created(){let e=await this.$API.biliArea.getAreaList.post({jObjectSearch:{}});0==e.code&&(this.area=e.data.rows,this.area.length>0&&(this.jObjectSearch.areaId=this.area[0].Fid)),e=await this.$API.biliReport.getReportList.post({jObjectSearch:this.jObjectSearch}),0==e.code&&(this.data=e.data.rows,this.columns=e.data.columns),this.loading=!1},methods:{async upsearch(){this.loading=!0;let e=await this.$API.biliReport.getReportList.post({jObjectSearch:this.jObjectSearch});this.loading=!1,0==e.code&&(this.data=e.data.rows,this.columns=e.data.columns)},async receive(){this.data=[],this.columns=[],this.width=180,this.headers=180,this.jObjectSearch.type="receive",await this.upsearch()},async date(){this.data=[],this.columns=[],this.width=75,this.headers=350,this.jObjectSearch.type="date",await this.upsearch()},async daily(){this.loading=!0,this.data=[],this.columns=[],this.width=0,this.headers=180,this.jObjectSearch.type="daily",await this.upsearch()},formatter(e,a,t){if(t)return t},async exports(){this.loading=!0;let e=await this.$API.biliReport.getReportList.post({jObjectSearch:{export:1,...this.jObjectSearch}});if(this.loading=!1,0==e.code){let a=document.createElement("a");a.style="display: none",a.target="_blank",a.download="统计报表",a.href=e.data,document.body.appendChild(a),a.click(),document.body.removeChild(a)}else this.$alert(e.message,"提示",{type:"error"})},cellStyle(e){return"已领取"==e.row[e.column.property]?{color:"green"}:"已完成"==e.row[e.column.property]?{color:"#e6a23c"}:"未更新"==e.row[e.column.property]?{color:"#f56c6c"}:void 0}}},c=t(6262);const n=(0,c.A)(s,[["render",o]]);var h=n}}]);