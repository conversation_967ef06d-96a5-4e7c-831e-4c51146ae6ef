"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[1170,711,4207,8194,6519,2356,2809,6027],{6912:function(t,e,s){s.r(e),s.d(e,{default:function(){return T}});s(8743);var i=s(641),l=s(2644);const a=t=>((0,i.Qi)("data-v-c1fce518"),t=t(),(0,i.jt)(),t),n={class:"widgets-content"},o={class:"widgets-top"},d=a((()=>(0,i.Lk)("div",{class:"widgets-top-title"}," 控制台 ",-1))),u={class:"widgets-top-actions"},c={class:"widgets",ref:"widgets"},r={class:"widgets-wrapper"},g={key:0,class:"no-widgets"},k={class:"widgets-item"},m={key:0,class:"customize-overlay"},p={key:0,class:"widgets-aside"},f={class:"widgets-aside-title"},L={class:"selectLayout"},h=a((()=>(0,i.Lk)("span",null,null,-1))),y=a((()=>(0,i.Lk)("span",null,null,-1))),v=a((()=>(0,i.Lk)("span",null,null,-1))),b=a((()=>(0,i.Lk)("span",null,null,-1))),C=a((()=>(0,i.Lk)("span",null,null,-1))),F=a((()=>(0,i.Lk)("span",null,null,-1))),_=a((()=>(0,i.Lk)("span",null,null,-1))),w=a((()=>(0,i.Lk)("span",null,null,-1))),$=a((()=>(0,i.Lk)("span",null,null,-1))),O=a((()=>(0,i.Lk)("span",null,null,-1))),z=a((()=>(0,i.Lk)("span",null,null,-1))),W=a((()=>(0,i.Lk)("span",null,null,-1))),X={class:"widgets-list"},I={key:0,class:"widgets-list-nodata"},D={class:"item-logo"},G={class:"item-info"},j={class:"item-actions"};function E(t,e,s,a,E,P){const R=(0,i.g2)("el-button"),N=(0,i.g2)("el-empty"),S=(0,i.g2)("el-icon"),A=(0,i.g2)("draggable"),J=(0,i.g2)("el-col"),T=(0,i.g2)("el-row"),Q=(0,i.g2)("el-icon-circle-plus-filled"),x=(0,i.g2)("el-icon-close"),V=(0,i.g2)("el-header"),U=(0,i.g2)("el-main"),B=(0,i.g2)("el-footer"),K=(0,i.g2)("el-container");return(0,i.uX)(),(0,i.CE)("div",{class:(0,l.C4)(["widgets-home",E.customizing?"customizing":""]),ref:"main"},[(0,i.Lk)("div",n,[(0,i.Lk)("div",o,[d,(0,i.Lk)("div",u,[E.customizing?((0,i.uX)(),(0,i.Wv)(R,{key:0,type:"primary",icon:"el-icon-check",round:"",onClick:P.save},{default:(0,i.k6)((()=>[(0,i.eW)("完成")])),_:1},8,["onClick"])):((0,i.uX)(),(0,i.Wv)(R,{key:1,type:"primary",icon:"el-icon-edit",round:"",onClick:P.custom},{default:(0,i.k6)((()=>[(0,i.eW)("自定义")])),_:1},8,["onClick"]))])]),(0,i.Lk)("div",c,[(0,i.Lk)("div",r,[P.nowCompsList.length<=0?((0,i.uX)(),(0,i.CE)("div",g,[(0,i.bF)(N,{image:"img/no-widgets.svg",description:"没有部件啦","image-size":280})])):(0,i.Q3)("",!0),(0,i.bF)(T,{gutter:15},{default:(0,i.k6)((()=>[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(E.grid.layout,((t,e)=>((0,i.uX)(),(0,i.Wv)(J,{key:e,md:t,xs:24},{default:(0,i.k6)((()=>[(0,i.bF)(A,{modelValue:E.grid.copmsList[e],"onUpdate:modelValue":t=>E.grid.copmsList[e]=t,animation:"200",handle:".customize-overlay",group:"people","item-key":"com",dragClass:"aaaaa","force-fallback":"",fallbackOnBody:"",class:"draggable-box"},{item:(0,i.k6)((({element:t})=>[(0,i.Lk)("div",k,[((0,i.uX)(),(0,i.Wv)((0,i.$y)(E.allComps[t]))),E.customizing?((0,i.uX)(),(0,i.CE)("div",m,[(0,i.bF)(R,{class:"close",type:"danger",plain:"",icon:"el-icon-close",size:"small",onClick:e=>P.remove(t)},null,8,["onClick"]),(0,i.Lk)("label",null,[(0,i.bF)(S,null,{default:(0,i.k6)((()=>[((0,i.uX)(),(0,i.Wv)((0,i.$y)(E.allComps[t].icon)))])),_:2},1024),(0,i.eW)((0,l.v_)(E.allComps[t].title),1)])])):(0,i.Q3)("",!0)])])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:2},1032,["md"])))),128))])),_:1})])],512)]),E.customizing?((0,i.uX)(),(0,i.CE)("div",p,[(0,i.bF)(K,null,{default:(0,i.k6)((()=>[(0,i.bF)(V,null,{default:(0,i.k6)((()=>[(0,i.Lk)("div",f,[(0,i.bF)(S,null,{default:(0,i.k6)((()=>[(0,i.bF)(Q)])),_:1}),(0,i.eW)("添加部件")]),(0,i.Lk)("div",{class:"widgets-aside-close",onClick:e[0]||(e[0]=t=>P.close())},[(0,i.bF)(S,null,{default:(0,i.k6)((()=>[(0,i.bF)(x)])),_:1})])])),_:1}),(0,i.bF)(V,{style:{height:"auto"}},{default:(0,i.k6)((()=>[(0,i.Lk)("div",L,[(0,i.Lk)("div",{class:(0,l.C4)(["selectLayout-item item01",{active:"12,6,6"==E.grid.layout.join(",")}]),onClick:e[1]||(e[1]=t=>P.setLayout([12,6,6]))},[(0,i.bF)(T,{gutter:2},{default:(0,i.k6)((()=>[(0,i.bF)(J,{span:12},{default:(0,i.k6)((()=>[h])),_:1}),(0,i.bF)(J,{span:6},{default:(0,i.k6)((()=>[y])),_:1}),(0,i.bF)(J,{span:6},{default:(0,i.k6)((()=>[v])),_:1})])),_:1})],2),(0,i.Lk)("div",{class:(0,l.C4)(["selectLayout-item item02",{active:"24,16,8"==E.grid.layout.join(",")}]),onClick:e[2]||(e[2]=t=>P.setLayout([24,16,8]))},[(0,i.bF)(T,{gutter:2},{default:(0,i.k6)((()=>[(0,i.bF)(J,{span:24},{default:(0,i.k6)((()=>[b])),_:1}),(0,i.bF)(J,{span:16},{default:(0,i.k6)((()=>[C])),_:1}),(0,i.bF)(J,{span:8},{default:(0,i.k6)((()=>[F])),_:1})])),_:1})],2),(0,i.Lk)("div",{class:(0,l.C4)(["selectLayout-item item04",{active:"16,8,24"==E.grid.layout.join(",")}]),onClick:e[3]||(e[3]=t=>P.setLayout([16,8,24]))},[(0,i.bF)(T,{gutter:2},{default:(0,i.k6)((()=>[(0,i.bF)(J,{span:16},{default:(0,i.k6)((()=>[_])),_:1}),(0,i.bF)(J,{span:8},{default:(0,i.k6)((()=>[w])),_:1}),(0,i.bF)(J,{span:24},{default:(0,i.k6)((()=>[$])),_:1})])),_:1})],2),(0,i.Lk)("div",{class:(0,l.C4)(["selectLayout-item item03",{active:"24"==E.grid.layout.join(",")}]),onClick:e[4]||(e[4]=t=>P.setLayout([24]))},[(0,i.bF)(T,{gutter:2},{default:(0,i.k6)((()=>[(0,i.bF)(J,{span:24},{default:(0,i.k6)((()=>[O])),_:1}),(0,i.bF)(J,{span:24},{default:(0,i.k6)((()=>[z])),_:1}),(0,i.bF)(J,{span:24},{default:(0,i.k6)((()=>[W])),_:1})])),_:1})],2)])])),_:1}),(0,i.bF)(U,{class:"nopadding"},{default:(0,i.k6)((()=>[(0,i.Lk)("div",X,[P.myCompsList.length<=0?((0,i.uX)(),(0,i.CE)("div",I,[(0,i.bF)(N,{description:"没有部件啦","image-size":60})])):(0,i.Q3)("",!0),((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(P.myCompsList,(t=>((0,i.uX)(),(0,i.CE)("div",{key:t.title,class:"widgets-list-item"},[(0,i.Lk)("div",D,[(0,i.bF)(S,null,{default:(0,i.k6)((()=>[((0,i.uX)(),(0,i.Wv)((0,i.$y)(t.icon)))])),_:2},1024)]),(0,i.Lk)("div",G,[(0,i.Lk)("h2",null,(0,l.v_)(t.title),1),(0,i.Lk)("p",null,(0,l.v_)(t.description),1)]),(0,i.Lk)("div",j,[(0,i.bF)(R,{type:"primary",icon:"el-icon-plus",size:"small",onClick:e=>P.push(t)},null,8,["onClick"])])])))),128))])])),_:1}),(0,i.bF)(B,{style:{height:"51px"}},{default:(0,i.k6)((()=>[(0,i.bF)(R,{size:"small",onClick:e[5]||(e[5]=t=>P.backDefaul())},{default:(0,i.k6)((()=>[(0,i.eW)("恢复默认")])),_:1})])),_:1})])),_:1})])):(0,i.Q3)("",!0)],2)}var P=s(432),R=s.n(P),N=s(6511),S={components:{draggable:R()},data(){return{customizing:!1,allComps:N["default"],selectLayout:[],defaultGrid:this.$CONFIG.DEFAULT_GRID,grid:[]}},created(){this.grid=this.$TOOL.data.get("GRID")||JSON.parse(JSON.stringify(this.defaultGrid))},mounted(){this.$emit("on-mounted")},computed:{allCompsList(){var t=[];for(var e in this.allComps)t.push({key:e,title:N["default"][e].title,icon:N["default"][e].icon,description:N["default"][e].description});var s=this.grid.copmsList.reduce((function(t,e){return t.concat(e)}));for(let i of t){const t=s.find((t=>t===i.key));t&&(i.disabled=!0)}return t},myCompsList(){var t=this.$TOOL.data.get("DASHBOARDGRID");return this.allCompsList.filter((e=>!e.disabled&&t.includes(e.key)))},nowCompsList(){return this.grid.copmsList.reduce((function(t,e){return t.concat(e)}))}},methods:{custom(){this.customizing=!0;const t=this.$refs.widgets.offsetWidth;this.$nextTick((()=>{const e=this.$refs.widgets.offsetWidth/t;this.$refs.widgets.style.setProperty("transform",`scale(${e})`)}))},setLayout(t){this.grid.layout=t,"24"==t.join(",")&&(this.grid.copmsList[0]=[...this.grid.copmsList[0],...this.grid.copmsList[1],...this.grid.copmsList[2]],this.grid.copmsList[1]=[],this.grid.copmsList[2]=[])},push(t){let e=this.grid.copmsList[0];e.push(t.key)},remove(t){var e=this.grid.copmsList;e.forEach(((s,i)=>{var l=s.filter((e=>e!=t));e[i]=l}))},async save(){this.customizing=!1,this.$refs.widgets.style.removeProperty("transform");let t=await this.$API.home.saveWidgetsConfig.post({jObjectParam:{key:"GRID",val:JSON.stringify(this.grid)}});0!=t.code&&this.$alert(t.message,"提示",{type:"error"}),this.$TOOL.data.set("GRID",this.grid)},backDefaul(){this.customizing=!1,this.$refs.widgets.style.removeProperty("transform"),this.grid=JSON.parse(JSON.stringify(this.defaultGrid)),this.$API.home.saveWidgetsConfig.post({jObjectParam:{key:"GRID",val:JSON.stringify(this.grid)}}),this.$TOOL.data.set("GRID",this.grid)},close(){this.customizing=!1,this.$refs.widgets.style.removeProperty("transform")}}},A=s(6262);const J=(0,A.A)(S,[["render",E],["__scopeId","data-v-c1fce518"]]);var T=J}}]);