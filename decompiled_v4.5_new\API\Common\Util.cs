using System;
using System.CodeDom.Compiler;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Text.RegularExpressions.Generated;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using API.BusService.System;
using API.Models.Comm;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SixLabors.Fonts;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Drawing.Processing;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Processing;

namespace API.Common;

public class Util
{
	public enum FileExtension
	{
		JPG = 255216,
		GIF = 7173,
		PNG = 13780,
		SWF = 6787,
		RAR = 8297,
		ZIP = 8075,
		_7Z = 55122,
		VALIDFILE = 9999999
	}

	public static Dictionary<string, string> SplitCookie(string cookie)
	{
		Dictionary<string, string> dictionary = new Dictionary<string, string>();
		if (!string.IsNullOrEmpty(cookie))
		{
			string[] array = cookie.Split(';');
			string[] array2 = array;
			foreach (string text in array2)
			{
				string[] array3 = text.Trim().Split('=');
				if (array3.Length == 2)
				{
					dictionary[array3[0]] = array3[1];
				}
			}
		}
		return dictionary;
	}

	public static string DictionaryToCookieString(Dictionary<string, string> dictionary)
	{
		StringBuilder stringBuilder = new StringBuilder();
		foreach (KeyValuePair<string, string> item in dictionary)
		{
			StringBuilder stringBuilder2 = stringBuilder;
			StringBuilder.AppendInterpolatedStringHandler handler = new StringBuilder.AppendInterpolatedStringHandler(3, 2, stringBuilder2);
			handler.AppendFormatted(item.Key);
			handler.AppendLiteral("=");
			handler.AppendFormatted(item.Value);
			handler.AppendLiteral("; ");
			stringBuilder2.Append(ref handler);
		}
		if (stringBuilder.Length > 0)
		{
			stringBuilder.Length -= 2;
		}
		return stringBuilder.ToString();
	}

	public static string Signature(Dictionary<string, string> @params, string salt)
	{
		StringBuilder stringBuilder = new StringBuilder();
		Dictionary<string, string> dictionary = @params.OrderBy<KeyValuePair<string, string>, string>((KeyValuePair<string, string> o) => o.Key).ToDictionary((KeyValuePair<string, string> o) => o.Key, (KeyValuePair<string, string> p) => p.Value);
		foreach (KeyValuePair<string, string> item in dictionary)
		{
			stringBuilder.Append(item.Key + "=" + WebUtility.UrlDecode(item.Value) + "&");
		}
		return CalcMD5(stringBuilder.ToString() + salt);
	}

	public static FileExtension CheckFileExtension(byte[] btype)
	{
		string text = btype[0].ToString();
		text += btype[1];
		try
		{
			return (FileExtension)Enum.Parse(typeof(FileExtension), text);
		}
		catch
		{
			return FileExtension.VALIDFILE;
		}
	}

	public static string ImageToBase64(string filename)
	{
		using FileStream fileStream = File.OpenRead(filename);
		byte[] array = new byte[fileStream.Length];
		fileStream.Read(array, 0, (int)fileStream.Length);
		return HttpUtility.UrlEncode(CheckFileExtension(new byte[2]
		{
			array[0],
			array[1]
		}) switch
		{
			FileExtension.JPG => "data:image/jpeg;base64,", 
			FileExtension.GIF => "data:image/gif;base64,", 
			FileExtension.PNG => "data:image/png;base64,", 
			_ => "data:image/png;base64,", 
		} + Convert.ToBase64String(array), Encoding.UTF8);
	}

	public static void FileDelete(string path)
	{
		if (File.Exists(path))
		{
			File.Delete(path);
		}
	}

	public static bool FileExists(string path)
	{
		return File.Exists(path);
	}

	public static void CopyAndRenameFolder(string sourceFolderPath, string destinationFolderPath, bool delete = true)
	{
		if (Directory.Exists(destinationFolderPath) && delete)
		{
			Directory.Delete(destinationFolderPath, recursive: true);
		}
		Directory.CreateDirectory(destinationFolderPath);
		string[] files = Directory.GetFiles(sourceFolderPath);
		foreach (string text in files)
		{
			string fileName = Path.GetFileName(text);
			string destFileName = Path.Combine(destinationFolderPath, fileName);
			File.Copy(text, destFileName, overwrite: true);
		}
		string[] directories = Directory.GetDirectories(sourceFolderPath);
		foreach (string text2 in directories)
		{
			string fileName2 = Path.GetFileName(text2);
			string destinationFolderPath2 = Path.Combine(destinationFolderPath, fileName2);
			CopyAndRenameFolder(text2, destinationFolderPath2);
		}
	}

	public static string GetCookieByKey(string cookies, string key, string @default = "")
	{
		string pattern = $"{key}\\s*=\\s*(?<value>[^;]+)";
		Regex regex = new Regex(pattern, RegexOptions.Compiled);
		Match match = regex.Match(cookies);
		if (match.Success)
		{
			return match.Groups["value"].Value;
		}
		return @default;
	}

	public static Process? ProcessStart(string filePath, string arguments = "", bool WindowStyle = true)
	{
		filePath = Directory.GetCurrentDirectory() + "\\Util" + filePath;
		ProcessStartInfo startInfo = new ProcessStartInfo
		{
			FileName = (filePath ?? ""),
			Arguments = arguments,
			UseShellExecute = true,
			WindowStyle = ((!WindowStyle) ? ProcessWindowStyle.Hidden : ProcessWindowStyle.Normal)
		};
		return Process.Start(startInfo);
	}

	public static string ProcessExist(string filePath)
	{
		string text = Directory.GetCurrentDirectory() + "\\Util" + filePath;
		Process[] processesByName = Process.GetProcessesByName(Path.GetFileNameWithoutExtension(filePath));
		bool flag = false;
		Process[] array = processesByName;
		foreach (Process process in array)
		{
			try
			{
				if (process.MainModule?.FileName == text)
				{
					flag = true;
				}
			}
			catch
			{
			}
		}
		if (!flag)
		{
			Process process2 = ProcessStart(filePath, "", WindowStyle: false);
			Thread.Sleep(2000);
			if (process2 == null || process2.HasExited)
			{
				return "未检测到或未启动";
			}
		}
		return "";
	}

	public static async Task<JArray> Upload(IFormFileCollection Files, string path, string subfolders, string format)
	{
		string wwwRootPath = Directory.GetCurrentDirectory() + "\\wwwroot\\";
		JArray jArray = new JArray();
		for (int i = 0; i < Files.Count; i++)
		{
			IFormFile oFile = Files[i];
			if (oFile.Length <= 0)
			{
				continue;
			}
			string oldValue = "." + oFile.FileName.Substring(oFile.FileName.LastIndexOf('.') + 1, oFile.FileName.Length - oFile.FileName.LastIndexOf('.') - 1).ToLower();
			string text = oFile.FileName.ToLower().Replace(oldValue, "");
			string text2 = Path.GetExtension(oFile.FileName).ToLower();
			string text3 = oFile.OpenReadStream().ReadByte().ToString() + oFile.OpenReadStream().ReadByte();
			if (text2 == ".txt")
			{
				text3 = "*";
			}
			switch (text3 + text2)
			{
			default:
				throw new Exception("文件格式错误，请联系管理员！");
			case "137137.png":
			case "208208.ppt":
			case "208208.xls":
			case "208208.doc":
			case "255255.jpg":
			case "7171.gif":
			case "6666.bmp":
			case "3737.pdf":
			case "8080.docx":
			case "8080.pptx":
			case "8080.xlsx":
			case "00.mp4":
			case "*.txt":
			{
				string relative = "files\\" + path.Trim('\\') + "\\";
				if (subfolders == "1")
				{
					relative = relative + DateTime.Now.Year + "\\" + DateTime.Now.Month + "\\" + DateTime.Now.Day + "\\";
				}
				string absolute = wwwRootPath + relative;
				string text4 = text + text2;
				if (format == "1")
				{
					if (!Directory.Exists(absolute))
					{
						Directory.CreateDirectory(absolute);
					}
					DateTime dateTime = DateTime.Now;
					while (dateTime < DateTime.Now.AddDays(100.0))
					{
						text4 = dateTime.ToString("yyyy-MM-dd") + text2;
						string tempRelative = relative + text4;
						string tempAbsolute = absolute + text4;
						if (!File.Exists(tempAbsolute))
						{
							using (FileStream stream = new FileStream(tempAbsolute, FileMode.Create))
							{
								await oFile.CopyToAsync(stream);
							}
							jArray.Add(new JObject
							{
								["src"] = tempRelative,
								["fileName"] = Path.GetFileName(tempAbsolute)
							});
							break;
						}
						dateTime = dateTime.AddDays(1.0);
					}
					break;
				}
				if (!Directory.Exists(absolute))
				{
					Directory.CreateDirectory(absolute);
					relative += text4;
					absolute += text4;
					using (FileStream stream = new FileStream(absolute, FileMode.Create))
					{
						await oFile.CopyToAsync(stream);
					}
					jArray.Add(new JObject
					{
						["src"] = relative,
						["fileName"] = Path.GetFileName(absolute),
						["absolute"] = absolute
					});
					break;
				}
				for (int j = 0; j < 100; j++)
				{
					text4 = text + ((j == 0) ? "" : ("(" + j + ")")) + text2;
					string tempAbsolute = relative + text4;
					string tempRelative = absolute + text4;
					if (!File.Exists(tempRelative))
					{
						using (FileStream stream = new FileStream(tempRelative, FileMode.Create))
						{
							await oFile.CopyToAsync(stream);
						}
						string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(tempRelative);
						jArray.Add(new JObject
						{
							["src"] = tempAbsolute,
							["fileName"] = fileNameWithoutExtension,
							["size"] = oFile.Length,
							["absolute"] = tempRelative
						});
						break;
					}
				}
				break;
			}
			}
		}
		return jArray;
	}

	public static void WriteLog(string typeName, string name, string jobName, string content, ConsoleColor? color = null, bool writeLog = true)
	{
		try
		{
			string text = "正常";
			switch (color)
			{
			case ConsoleColor.Green:
				text = "成功";
				break;
			case ConsoleColor.Red:
				text = "失败";
				break;
			case ConsoleColor.Yellow:
				text = "警告";
				break;
			case ConsoleColor.Blue:
				text = "信息";
				break;
			default:
				color = ConsoleColor.White;
				break;
			}
			Console.ForegroundColor = color.Value;
			Console.WriteLine(">>" + DateTime.Now.ToString("HH:mm:ss") + "\t" + name + "\t" + jobName + "\t" + content);
			Console.ForegroundColor = ConsoleColor.White;
			if (writeLog)
			{
				char[] invalidFileNameChars = Path.GetInvalidFileNameChars();
				string text2 = Directory.GetCurrentDirectory() + "\\log\\" + typeName + "\\" + DateTime.Now.ToString("yyyyMM") + "\\" + DateTime.Now.Day + "\\" + jobName + "\\";
				string path = text2 + string.Join("", name.Split(invalidFileNameChars, StringSplitOptions.RemoveEmptyEntries)) + ".log";
				if (!Directory.Exists(text2))
				{
					Directory.CreateDirectory(text2);
				}
				using StreamWriter streamWriter = File.AppendText(path);
				streamWriter.Write(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss ") + "\t" + jobName + "\t" + content + "\t" + text + "\n");
				return;
			}
		}
		catch
		{
		}
	}

	public static async Task<object?> Request<T>(string path, T model, HttpContext? httpContext = null)
	{
		string val = AppSettings.GetVal("ServerUrl");
		if (val == "")
		{
			return null;
		}
		Dictionary<string, string> dictionary = new Dictionary<string, string>
		{
			{ "Content-Type", "application/json" },
			{
				"Cookie",
				BusSysUser.Instance.User.Cookie
			},
			{ "NetCore", "8.0.0" }
		};
		string text = JsonConvert.SerializeObject(model);
		if (text.Contains("jObjectParam"))
		{
			string text2 = AppSettings.GetVal("TokenPrefix") + Token.GetTokenValue(BusSysUser.Instance.User.Id.ToString());
			string text3 = Guid.NewGuid().ToString();
			string text4 = Token.GetUTC_TimeLen().ToString();
			string value = Token.CreateValidateSign(text4, text2, text3, text);
			dictionary.Add("Authorization", text2);
			dictionary.Add("Nonce", text3);
			dictionary.Add("Timestamp", text4);
			dictionary.Add("Signature", value);
		}
		HttpClientFactory httpClientFactory = new HttpClientFactory("User", dictionary);
		JObject jObject = ((httpContext == null || !(httpContext.Request.Method == "GET")) ? (await httpClientFactory.Post(val + path, text, null, 10.0)) : (await httpClientFactory.Get(val + path)));
		JToken jToken = jObject["responseHeaders"];
		Response response = jObject.ToObject<Response>();
		if (response == null)
		{
			return null;
		}
		if (response.code == 0)
		{
			if (httpContext != null && jToken != null && jToken["Set-Cookie"] != null)
			{
				string[] array = (jToken["Set-Cookie"]?.ToString() ?? "").Split(';');
				string[] array2 = array;
				foreach (string text5 in array2)
				{
					if (text5.Contains("bulid3="))
					{
						string value2 = RsaEncrypt.RSADecrypt(text5.Replace("bulid3=", "").Trim());
						httpContext.Session.SetString("verifyCode", value2);
					}
					else if (text5.Contains("token="))
					{
						BusSysUser.Instance.User.Cookie = text5;
					}
				}
			}
			return response.data;
		}
		if (response.code == -100)
		{
			return null;
		}
		throw new Exception(response.message);
	}

	public static async Task<object?> MySelfRequest<T>(string apiUrl, string path, T model, HttpContext? httpContext = null)
	{
		Dictionary<string, string> dictionary = new Dictionary<string, string>
		{
			{ "Content-Type", "application/json" },
			{
				"Cookie",
				BusSysUser.Instance.User.Cookie
			},
			{ "NetCore", "8.0.0" }
		};
		string text = JsonConvert.SerializeObject(model);
		if (text.Contains("jObjectParam"))
		{
			string text2 = AppSettings.GetVal("TokenPrefix") + Token.GetTokenValue(BusSysUser.Instance.User.Id.ToString());
			string text3 = Guid.NewGuid().ToString();
			string text4 = Token.GetUTC_TimeLen().ToString();
			string value = Token.CreateValidateSign(text4, text2, text3, text);
			dictionary.Add("Authorization", text2);
			dictionary.Add("Nonce", text3);
			dictionary.Add("Timestamp", text4);
			dictionary.Add("Signature", value);
		}
		HttpClientFactory httpClientFactory = new HttpClientFactory("User", dictionary);
		JObject jObject = ((httpContext == null || !(httpContext.Request.Method == "GET")) ? (await httpClientFactory.Post(apiUrl + path, text, null, 10.0)) : (await httpClientFactory.Get(apiUrl + path)));
		JToken jToken = jObject["responseHeaders"];
		Response response = jObject.ToObject<Response>();
		if (response == null)
		{
			return null;
		}
		if (response.code == 0)
		{
			if (httpContext != null && jToken != null && jToken["Set-Cookie"] != null)
			{
				string[] array = (jToken["Set-Cookie"]?.ToString() ?? "").Split(';');
				string[] array2 = array;
				foreach (string text5 in array2)
				{
					if (text5.Contains("bulid3="))
					{
						string value2 = RsaEncrypt.RSADecrypt(text5.Replace("bulid3=", "").Trim());
						httpContext.Session.SetString("verifyCode", value2);
					}
					else if (text5.Contains("token="))
					{
						BusSysUser.Instance.User.Cookie = text5;
					}
				}
			}
			return response.data;
		}
		if (response.code == -100)
		{
			return null;
		}
		throw new Exception(response.message);
	}

	public static void FileDownload(out string absolute, out string relative, string suffix = ".xls", string path = "", string root = "\\files\\download\\")
	{
		if (path.Replace("\\", "") == "")
		{
			path = "common";
		}
		string text = Guid.NewGuid().ToString("N") + suffix;
		string text2 = Directory.GetCurrentDirectory() + "\\wwwroot";
		relative = root + path + "\\" + text;
		absolute = text2 + relative;
		if (!Directory.Exists(Path.GetDirectoryName(absolute)))
		{
			Directory.CreateDirectory(Path.GetDirectoryName(absolute) ?? "");
		}
	}

	public static bool PasswordStrong(string pwd)
	{
		Regex regex = PassRegex();
		return regex.IsMatch(pwd);
	}

	private static readonly Regex _passRegex = new Regex("(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=([\\x21-\\x7e]+)[^a-zA-Z0-9]).{8,18}", RegexOptions.Multiline | RegexOptions.IgnorePatternWhitespace);
	
	private static Regex PassRegex()
	{
		return _passRegex;
	}

	public static string Regex(string str, string s, string e)
	{
		Regex regex = new Regex("(?<=(" + s + "))[.\\s\\S]*?(?=(" + e + "))", RegexOptions.Multiline | RegexOptions.Singleline);
		return regex.Match(str).Value;
	}

	public static string CalcMD5(string str)
	{
		byte[] bytes = Encoding.UTF8.GetBytes(str);
		return CalcMD5(bytes);
	}

	public static string CalcMD5(Stream stream)
	{
		using MD5 mD = MD5.Create();
		byte[] md5Bytes = mD.ComputeHash(stream);
		return BytesToString(md5Bytes);
	}

	private static string BytesToString(byte[] md5Bytes)
	{
		StringBuilder stringBuilder = new StringBuilder();
		for (int i = 0; i < md5Bytes.Length; i++)
		{
			stringBuilder.Append(md5Bytes[i].ToString("X2"));
		}
		return stringBuilder.ToString();
	}

	public static string CalcMD5(byte[] buffer)
	{
		byte[] md5Bytes = MD5.HashData(buffer);
		return BytesToString(md5Bytes);
	}

	public static JObject GetTableResponse(DataTable dt)
	{
		int result = dt.Rows.Count;
		if (dt.Columns["total"] != null && result != 0 && !int.TryParse(dt.Rows[0]["total"].ToString(), out result))
		{
			result = dt.Rows.Count;
		}
		JArray value = JArray.Parse(JsonConvert.SerializeObject(dt, Formatting.Indented, new DateTimeConvert()));
		return new JObject
		{
			["total"] = result,
			["rows"] = value
		};
	}

	public static string GetJObject(JToken? jToken, string key, string def = "")
	{
		if (jToken == null || jToken[key] == null)
		{
			return def;
		}
		string text = (jToken[key] ?? ((JToken)def)).ToString().Trim();
		if (!(text == ""))
		{
			return text;
		}
		return def;
	}

	public static string GetJObject(DataRow? dr, string key, string def = "")
	{
		if (dr == null || !dr.Table.Columns.Contains(key))
		{
			return def;
		}
		return dr[key].ToString() ?? def;
	}

	public static T? GetJObject<T>(JToken? jToken, string key)
	{
		JToken jToken2 = jToken?[key];
		try
		{
			if (jToken2 != null)
			{
				return jToken2.ToObject<T>();
			}
			return default(T);
		}
		catch
		{
			return default(T);
		}
	}

	public static string ExceptionMessage(string errorMessage)
	{
		string result = errorMessage;
		if (errorMessage.Contains("22007") && errorMessage.Contains("timestamp"))
		{
			result = "参数格式不正确！";
		}
		else if (errorMessage.Contains("Invalid") && errorMessage.Contains("position"))
		{
			result = "JSON字符串格式不正确！";
		}
		else if (errorMessage.Contains("42601") && errorMessage.Contains("input"))
		{
			result = "请求的参数不正确！";
		}
		else if (errorMessage == "在位置 0 处没有任何行。")
		{
			result = "未找到记录！";
		}
		else if (errorMessage.Contains("关键字") && errorMessage.Contains("附近有语法错误"))
		{
			result = "请求的参数不正确！";
		}
		return result;
	}

	public static string VerifyCode(string[] verifyCode)
	{
		int width = verifyCode.Length * 32;
		int height = 36;
		using Image source = new Image<Rgba32>(width, height);
		source.Mutate(delegate(IImageProcessingContext x)
		{
			x.DrawLine(Pens.DashDot(Color.White, width), new PointF
			{
				X = 0f,
				Y = 0f
			}, new PointF
			{
				X = width,
				Y = height
			});
		});
		FontCollection fontCollection = new FontCollection();
		Font font = (font = SystemFonts.CreateFont(SystemFonts.Families.First().Name, 20f, FontStyle.Bold));
		PointF startPointF = new PointF(5f, 5f);
		Random random = new Random();
		Color[] colors = new Color[12]
		{
			Color.Red,
			Color.Blue,
			Color.Green,
			Color.Purple,
			Color.Peru,
			Color.LightSeaGreen,
			Color.Lime,
			Color.Magenta,
			Color.Maroon,
			Color.MediumBlue,
			Color.MidnightBlue,
			Color.Navy
		};
		int i;
		for (i = 0; i < verifyCode.Length; i++)
		{
			source.Mutate(delegate(IImageProcessingContext x)
			{
				x.DrawText(verifyCode[i].ToString(), font, colors[random.Next(colors.Length)], startPointF);
			});
			startPointF.X += (width - 10) / verifyCode.Length;
			startPointF.Y = random.Next(5, 10);
		}
		PatternPen pen = Pens.DashDot(Color.Silver, 1f);
		for (int num = 0; num < 8; num++)
		{
			PointF[] points = new PointF[2]
			{
				new PointF(random.Next(width), random.Next(height)),
				new PointF(random.Next(width), random.Next(height))
			};
			source.Mutate(delegate(IImageProcessingContext x)
			{
				x.DrawLine(pen, points);
			});
		}
		using MemoryStream memoryStream = new MemoryStream();
		source.Save(memoryStream, JpegFormat.Instance);
		return "data:image/png;base64," + Convert.ToBase64String(memoryStream.ToArray());
	}
}
