using System;
using System.Data;
using API.Common;
using API.DataAccess.System;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json.Linq;

namespace API.BusService;

public class BusLogin
{
	public static JObject CheckLogin(DataTable dt)
	{
		JObject jObject = new JObject();
		string text = dt.Rows[0]["Fid"].ToString() ?? "";
		string text2 = dt.Rows[0]["FUserName"].ToString() ?? "";
		string id = dt.Rows[0]["FOrganizationId"].ToString() ?? "";
		string text3 = dt.Rows[0]["FLastIPAddress"].ToString() ?? "";
		string text4 = dt.Rows[0]["FLastCookie"].ToString() ?? "";
		jObject["userId"] = text;
		jObject["userName"] = text2;
		jObject["organization"] = SysOrganization.GetMyOrganization(id);
		jObject["address"] = text3;
		jObject["cookie"] = text4;
		jObject["dashboard"] = "0";
		jObject["userHear"] = ((dt.Rows[0]["FImage"].ToString() == "") ? "img/default_head.jpg" : dt.Rows[0]["FImage"].ToString());
		jObject["dashboard"] = "0";
		jObject["role"] = new JArray();
		string rights;
		JObject menuList = SysMenu.GetMenuList(text, out rights);
		jObject["rights"] = rights;
		menuList["grid"] = ((dt.Rows[0]["FGrid"].ToString() != "") ? JObject.Parse(dt.Rows[0]["FGrid"].ToString() ?? "{}") : ((JToken)"{}"));
		return new JObject
		{
			["userInfo"] = jObject,
			["menuInfo"] = menuList
		};
	}

	public static string CreateVerifyCode(HttpContext httpContext)
	{
		string[] array = new string[4]
		{
			new Random().Next(0, 9).ToString(),
			new Random().Next(0, 9).ToString(),
			new Random().Next(0, 9).ToString(),
			new Random().Next(0, 9).ToString()
		};
		string text = string.Join("", array) + "," + DateTime.Now;
		httpContext.Response.Headers.Append("set-cookie", "bulid3=" + RsaEncrypt.RSAEncrypt(text));
		httpContext.Session.SetString("verifyCode", text);
		return Util.VerifyCode(array);
	}

	public static string CheckVerifyCode(HttpContext httpContext, string verifyCode, bool rememberMe)
	{
		// 风控对抗项目：检查配置是否绕过验证码
		string bypassCaptcha = AppSettings.GetVal("BypassCaptcha");
		if (bypassCaptcha.ToLower() == "true")
		{
			// 绕过验证码检查，直接返回成功
			httpContext.Session.SetString("verifyCode", "");
			return verifyCode ?? "bypassed";
		}

		string text = httpContext.Session.GetString("verifyCode");
		if (text == null || text == "")
		{
			throw new Exception("验证码无效！");
		}
		string[] array = text.Split(",");
		if (rememberMe)
		{
			verifyCode = array[0];
		}
		if (verifyCode == "")
		{
			throw new Exception("请输入验证码！");
		}
		if (DateTime.Parse(array[1]).AddMinutes(1.0) < DateTime.Now)
		{
			throw new Exception("验证码已过期！");
		}
		if (array[0] != verifyCode)
		{
			throw new Exception("验证码不正确！");
		}
		httpContext.Session.SetString("verifyCode", "");
		return verifyCode;
	}
}