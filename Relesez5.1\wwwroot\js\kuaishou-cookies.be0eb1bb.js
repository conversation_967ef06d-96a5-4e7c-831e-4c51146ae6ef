"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[3094,4912,7104,1580],{8118:function(e,t,a){a.r(t),a.d(t,{default:function(){return b}});var i=a(641);const o={class:"left-panel"},s={class:"right-panel"},l={class:"right-panel-search"},r={class:"dialog-footer"};function n(e,t,a,n,d,c){const h=(0,i.g2)("el-button"),p=(0,i.g2)("el-icon"),u=(0,i.g2)("el-dropdown-item"),k=(0,i.g2)("el-dropdown-menu"),g=(0,i.g2)("el-dropdown"),b=(0,i.g2)("el-input"),m=(0,i.g2)("el-header"),f=(0,i.g2)("el-table-column"),w=(0,i.g2)("el-button-group"),F=(0,i.g2)("el-popconfirm"),y=(0,i.g2)("scTable"),C=(0,i.g2)("el-main"),j=(0,i.g2)("el-container"),$=(0,i.g2)("el-table"),x=(0,i.g2)("el-dialog"),v=(0,i.g2)("cookieDialog"),S=(0,i.g2)("taskDialog"),_=(0,i.g2)("expirationDialog"),O=(0,i.gN)("loading");return(0,i.uX)(),(0,i.CE)(i.FK,null,[(0,i.bo)(((0,i.uX)(),(0,i.Wv)(j,{"element-loading-text":"执行中..."},{default:(0,i.k6)((()=>[(0,i.bF)(m,null,{default:(0,i.k6)((()=>[(0,i.Lk)("div",o,[(0,i.bF)(h,{type:"primary",icon:"el-icon-plus",onClick:t[0]||(t[0]=e=>c.addCookies())},{default:(0,i.k6)((()=>[(0,i.eW)("新增账号")])),_:1}),(0,i.bF)(g,{trigger:"click",class:"m-r"},{dropdown:(0,i.k6)((()=>[(0,i.bF)(k,{class:"drop-common"},{default:(0,i.k6)((()=>[(0,i.bF)(u,{onClick:c.updateSystem,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("更新系统数据")])),_:1},8,["onClick"]),(0,i.bF)(u,{onClick:c.addCookiesExpirationTime,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("充值账号时长")])),_:1},8,["onClick"]),(0,i.bF)(u,{onClick:c.updateCookieExpires,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("更新到期时间")])),_:1},8,["onClick"]),(0,i.bF)(u,{divided:""})])),_:1})])),default:(0,i.k6)((()=>[(0,i.bF)(h,{type:"primary"},{default:(0,i.k6)((()=>[(0,i.eW)("续费功能  "),(0,i.bF)(p,null,{default:(0,i.k6)((()=>[((0,i.uX)(),(0,i.Wv)((0,i.$y)("el-icon-arrow-down-bold")))])),_:1})])),_:1})])),_:1}),(0,i.bF)(h,{type:"primary",icon:"el-icon-refresh",onClick:t[1]||(t[1]=e=>c.updateCookieTask())},{default:(0,i.k6)((()=>[(0,i.eW)("更新任务")])),_:1}),(0,i.bF)(h,{type:"primary",icon:"el-icon-download",onClick:t[2]||(t[2]=e=>c.exportCdkey())},{default:(0,i.k6)((()=>[(0,i.eW)("导出Ckdey")])),_:1}),(0,i.bF)(h,{type:"primary",icon:"el-icon-grid",onClick:t[3]||(t[3]=e=>c.receiceTask())},{default:(0,i.k6)((()=>[(0,i.eW)("领取任务")])),_:1})]),(0,i.Lk)("div",s,[(0,i.Lk)("div",l,[(0,i.bF)(b,{modelValue:d.jObjectSearch.search,"onUpdate:modelValue":t[4]||(t[4]=e=>d.jObjectSearch.search=e),placeholder:"账号名称",clearable:""},null,8,["modelValue"]),(0,i.bF)(h,{type:"primary",icon:"el-icon-search",onClick:c.upsearch},{default:(0,i.k6)((()=>[(0,i.eW)(" 查询")])),_:1},8,["onClick"])])])])),_:1}),(0,i.bF)(C,{class:"nopadding"},{default:(0,i.k6)((()=>[(0,i.bF)(y,{ref:"table",apiObj:d.apiObj,border:"",params:{jObjectSearch:d.jObjectSearch},stripe:"",remoteSort:"",remoteFilter:""},{default:(0,i.k6)((()=>[(0,i.bF)(f,{type:"selection",width:"50"}),(0,i.bF)(f,{label:"功能",fixed:"left","header-align":"center",align:"left",width:"100"},{default:(0,i.k6)((e=>[(0,i.bF)(w,null,{default:(0,i.k6)((()=>["账号已到期"!=e.row.FStatusName?((0,i.uX)(),(0,i.Wv)(h,{key:0,text:"",type:"warning",size:"small",onClick:t=>c.showTask(e.row,e.$index)},{default:(0,i.k6)((()=>[(0,i.eW)("任务信息")])),_:2},1032,["onClick"])):(0,i.Q3)("",!0)])),_:2},1024)])),_:1}),(0,i.bF)(f,{label:"序号",prop:"FSort",align:"center",width:"85",sortable:""}),(0,i.bF)(f,{label:"账号名称",prop:"FName",align:"center",width:"120","show-overflow-tooltip":""}),(0,i.bF)(f,{label:"手机Cookie",prop:"FC1",align:"center",width:"120","show-overflow-tooltip":""}),(0,i.bF)(f,{label:"网页Cookie",prop:"FC2",align:"center",width:"120","show-overflow-tooltip":""}),(0,i.bF)(f,{label:"准备领取","min-width":"120","header-align":"center",prop:"FTaskName","show-overflow-tooltip":"",align:"left"}),(0,i.bF)(f,{label:"错误信息",prop:"FStatusName",align:"center","min-width":"120","show-overflow-tooltip":"",sortable:""}),(0,i.bF)(f,{label:"操作",fixed:"right","header-align":"center",align:"left",width:"170"},{default:(0,i.k6)((e=>[(0,i.bF)(w,null,{default:(0,i.k6)((()=>[(0,i.bF)(h,{text:"",type:"primary",size:"small",onClick:t=>c.editCookie(e.row,e.$index)},{default:(0,i.k6)((()=>[(0,i.eW)("编辑")])),_:2},1032,["onClick"]),(0,i.bF)(F,{title:"确定删除账号数据吗？",onConfirm:t=>c.delCookie(e.row,e.$index),width:"250"},{reference:(0,i.k6)((()=>[(0,i.bF)(h,{text:"",type:"danger",size:"small"},{default:(0,i.k6)((()=>[(0,i.eW)("删除")])),_:1})])),_:2},1032,["onConfirm"])])),_:2},1024)])),_:1})])),_:1},8,["apiObj","params"])])),_:1})])),_:1})),[[O,d.updateLoading]]),(0,i.bF)(x,{modelValue:d.dialog.taskList,"onUpdate:modelValue":t[6]||(t[6]=e=>d.dialog.taskList=e),title:"领取任务",width:"1200px"},{footer:(0,i.k6)((()=>[(0,i.Lk)("div",r,[(0,i.bF)(h,{onClick:t[5]||(t[5]=e=>d.dialog.taskList=!1)},{default:(0,i.k6)((()=>[(0,i.eW)("关闭")])),_:1})])])),default:(0,i.k6)((()=>[(0,i.bo)(((0,i.uX)(),(0,i.Wv)($,{data:d.tableData,border:"",style:{width:"100%"},height:"400"},{default:(0,i.k6)((()=>[(0,i.bF)(f,{label:"操作",fixed:"left","header-align":"center",align:"center",width:"100"},{default:(0,i.k6)((e=>[(0,i.bF)(w,null,{default:(0,i.k6)((()=>[(0,i.bF)(F,{title:"确定领取任务吗？",onConfirm:t=>c.confirm(e.row,e.$index),width:"200"},{reference:(0,i.k6)((()=>[(0,i.bF)(h,{text:"",type:"primary",size:"small"},{default:(0,i.k6)((()=>[(0,i.eW)("领取任务")])),_:1})])),_:2},1032,["onConfirm"])])),_:2},1024)])),_:1}),(0,i.bF)(f,{prop:"title",label:"分区名称","header-align":"center",width:"250","show-overflow-tooltip":""}),(0,i.bF)(f,{prop:"description",label:"奖励内容","header-align":"center","show-overflow-tooltip":""})])),_:1},8,["data"])),[[O,0==d.tableData.length]])])),_:1},8,["modelValue"]),d.dialog.cookies?((0,i.uX)(),(0,i.Wv)(v,{key:0,ref:"cookieDialog",onSuccess:c.upsearch,onClosed:t[7]||(t[7]=e=>d.dialog.cookies=!1)},null,8,["onSuccess"])):(0,i.Q3)("",!0),d.dialog.task?((0,i.uX)(),(0,i.Wv)(S,{key:1,ref:"taskDialog",onSuccess:c.upsearch,onClosed:t[8]||(t[8]=e=>{d.dialog.task=!1,c.upsearch()})},null,8,["onSuccess"])):(0,i.Q3)("",!0),d.dialog.expiration?((0,i.uX)(),(0,i.Wv)(_,{key:2,ref:"expirationDialog",onSuccess:c.upsearch,onClosed:t[9]||(t[9]=e=>d.dialog.expiration=!1)},null,8,["onSuccess"])):(0,i.Q3)("",!0)],64)}a(8743);var d=a(1132),c=a(4988),h=a(2595),p=a(5514),u={name:"ksCookies",components:{cookieDialog:c["default"],taskDialog:h["default"],expirationDialog:p["default"]},data(){return{apiObj:this.$API.ksCookies.getCookiesList,updateLoading:!1,dialog:{cookies:!1,task:!1,live:!1,expiration:!1},jObjectSearch:{search:"",areaId:""},area:[],tableData:[]}},methods:{async receiceTask(){let e=this.$refs.table.getSelectionRows();if(0==e.length)return void this.$alert("请选择账号！","提示",{type:"error"});this.dialog.taskList=!0;let t=await this.$API.ksCookiesTask.getReceiveTaskList.post();0==t.code?this.tableData=t.data:this.$alert(t.message,"提示",{type:"error"})},async confirm(e){let t=this.$refs.table.getSelectionRows();if(0==t.length)return void this.$alert("请选择账号！","提示",{type:"error"});const a=t.reduce(((e,t)=>e+t.Fid+","),"");let i=await this.$API.ksCookiesTask.receiveTask.post({jObjectParam:{cookieId:String(a).substring(0,a.length-1),value:e.taskId}});0==i.code?this.$alert(i.message,"提示",{type:"success"}):this.$alert(i.message,"提示",{type:"error"})},addCookies(){this.$prompt("一次添加至少1个，做多10个","添加账号",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnPressEscape:!0,closeOnClickModal:!0,autofocus:!0,inputType:"number",inputPattern:/^[1-9]$|^10$/,inputErrorMessage:"至少1个，最多10个"}).then((async({value:e})=>{this.updateLoading=!0;let t=await this.$API.ksCookies.addCookies.post({jObjectParam:{cookieNum:e}});0==t.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(t.message,"提示",{type:"error"}),this.updateLoading=!1}))},editCookie(e){this.dialog.cookies=!0,this.$nextTick((()=>{this.$refs.cookieDialog.open("edit").setData(e)}))},async delCookie(e){let t=await this.$API.ksCookies.delCookie.post({jObjectParam:{Fid:e.Fid,FIdentifying:e.FIdentifying,FKey:e.FKey}});0==t.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(t.message,"提示",{type:"error"})},showTask(e){this.dialog.task=!0,this.$nextTick((()=>{this.$refs.taskDialog.open("show").setData({cookieId:e.Fid})}))},addCookiesExpirationTime(){let e=this.$refs.table.getSelectionRows();if(0==t.length)return void this.$alert("请选择账号","提示",{type:"error"});let t=[];for(let a in e)t.push({Fid:e[a].Fid,FName:e[a].FName,FKey:e[a].FKey,FExpirationTime:e[a].FExpirationTime});this.dialog.expiration=!0,this.$nextTick((()=>{this.$refs.expirationDialog.open("show").setData({array:t})}))},async updateCookieExpires(){try{this.updateLoading=!0;let e=await this.$API.ksCookies.updateCookieExpires.post({jObjectParam:{}});if(this.updateLoading=!1,0!=e.code)throw new Error(e.message);this.$message.success("更新成功！"),this.upsearch()}catch(e){this.$alert(e.message,"提示",{type:"error"})}},async updateCookieTask(){this.updateLoading=!0;let e=this.$refs.table.getSelectionRows();if(0==e.length&&(e=(await this.apiObj.post({jObjectSearch:this.jObjectSearch})).data.rows),0==e.length)throw new Error("请添加账号！");const t=e.reduce(((e,t)=>e+t.Fid+", "),"");let a=await this.$API.ksCookiesTask.updateCookiesTask.post({jObjectParam:{cookieId:String(t).substring(0,t.length-2)}});0==a.code?(this.$message.success("更新成功！"),this.upsearch()):this.$alert(a.message,"提示",{type:"error"}),this.updateLoading=!1},async updateSystem(){try{this.updateLoading=!0;let e=await this.$API.ksCookies.updateSystem.post();if(this.updateLoading=!1,0!=e.code)throw new Error(e.message);this.$message.success("操作成功！"),this.upsearch()}catch(e){this.$alert(e.message,"提示",{type:"error"})}},upsearch(){this.$refs.table.upData({jObjectSearch:this.jObjectSearch})},async exportCdkey(){let e=this.$refs.table.getSelectionRows();if(0==e.length&&(e=(await this.apiObj.post({jObjectSearch:this.jObjectSearch})).data.rows),0==e.length)throw new Error("请添加账号！");const t=e.reduce(((e,t)=>e+t.Fid+", "),"");this.updateLoading=!0;let a=await this.$API.ksCookiesTask.exportCdkeyList.post({jObjectParam:{cookieId:String(t).substring(0,t.length-2)}});this.updateLoading=!1;let i=document.createElement("a");i.style="display: none",i.target="_blank",i.download="cdkey",i.href=a.data,document.body.appendChild(i),i.click(),document.body.removeChild(i),this.upsearch()},async execQuartz(e,t=0){const a=d.Ks.service({lock:!0,text:"执行中...",background:"rgba(0, 0, 0, 0.7)"});try{let a=this.$refs.table.getSelectionRows();if(0==a.length&&(a=(await this.apiObj.post({jObjectSearch:this.jObjectSearch})).data.rows),0==a.length)throw new Error("请添加账号！");let i=[];for(let e in a)new Date(a[e].FExpirationTime)>new Date&&i.push({Fid:a[e].Fid,FName:a[e].FName,FKey:a[e].FKey,FExpirationTime:a[e].FExpirationTime});let o=await this.$API.biliQuartz.manualExecQuartz.post({jObjectParam:{array:i,areaId:this.jObjectSearch.areaId,jobName:e,delay:t}});if(0!=o.code)throw new Error(o.message);this.$message.success(o.message),this.upsearch()}catch(i){this.$alert(i.message,"提示",{type:"error"})}a.close()}}},k=a(6262);const g=(0,k.A)(u,[["render",n],["__scopeId","data-v-72799e86"]]);var b=g}}]);