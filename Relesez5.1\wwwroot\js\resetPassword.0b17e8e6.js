"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[75],{6103:function(e,l,t){t.r(l),t.d(l,{default:function(){return f}});var a=t(641),s=t(2644);const o=(0,a.Lk)("div",{class:"el-form-item-msg"},"请输入注册时填写的登录账号",-1),r={class:"yzm"},i={key:0},u=(0,a.Lk)("div",{class:"el-form-item-msg"},"请输入包含英文、数字的8位以上密码",-1);function d(e,l,t,d,m,n){const p=(0,a.g2)("el-step"),c=(0,a.g2)("el-steps"),f=(0,a.g2)("el-input"),b=(0,a.g2)("el-form-item"),h=(0,a.g2)("el-button"),w=(0,a.g2)("el-form"),k=(0,a.g2)("el-result"),v=(0,a.g2)("common-page");return(0,a.uX)(),(0,a.Wv)(v,{title:"重置密码"},{default:(0,a.k6)((()=>[(0,a.bF)(c,{active:m.stepActive,simple:"","finish-status":"success"},{default:(0,a.k6)((()=>[(0,a.bF)(p,{title:"填写新密码"}),(0,a.bF)(p,{title:"完成重置"})])),_:1},8,["active"]),0==m.stepActive?((0,a.uX)(),(0,a.Wv)(w,{key:0,ref:"form",model:m.form,rules:m.rules,"label-width":120},{default:(0,a.k6)((()=>[(0,a.bF)(b,{label:"登录账号",prop:"user"},{default:(0,a.k6)((()=>[(0,a.bF)(f,{modelValue:m.form.user,"onUpdate:modelValue":l[0]||(l[0]=e=>m.form.user=e),placeholder:"请输入登录账号"},null,8,["modelValue"]),o])),_:1}),(0,a.bF)(b,{label:"手机号码",prop:"phone"},{default:(0,a.k6)((()=>[(0,a.bF)(f,{modelValue:m.form.phone,"onUpdate:modelValue":l[1]||(l[1]=e=>m.form.phone=e),placeholder:"请输入手机号码"},null,8,["modelValue"])])),_:1}),(0,a.bF)(b,{label:"短信验证码",prop:"yzm"},{default:(0,a.k6)((()=>[(0,a.Lk)("div",r,[(0,a.bF)(f,{modelValue:m.form.yzm,"onUpdate:modelValue":l[2]||(l[2]=e=>m.form.yzm=e),placeholder:"请输入6位短信验证码"},null,8,["modelValue"]),(0,a.bF)(h,{onClick:n.getYzm,disabled:m.disabled},{default:(0,a.k6)((()=>[(0,a.eW)("获取验证码"),m.disabled?((0,a.uX)(),(0,a.CE)("span",i," ("+(0,s.v_)(m.time)+")",1)):(0,a.Q3)("",!0)])),_:1},8,["onClick","disabled"])])])),_:1}),(0,a.bF)(b,{label:"新密码",prop:"newpw"},{default:(0,a.k6)((()=>[(0,a.bF)(f,{modelValue:m.form.newpw,"onUpdate:modelValue":l[3]||(l[3]=e=>m.form.newpw=e),"show-password":"",placeholder:"请输入新密码"},null,8,["modelValue"]),u])),_:1}),(0,a.bF)(b,{label:"确认新密码",prop:"newpw2"},{default:(0,a.k6)((()=>[(0,a.bF)(f,{modelValue:m.form.newpw2,"onUpdate:modelValue":l[4]||(l[4]=e=>m.form.newpw2=e),"show-password":"",placeholder:"请再一次输入新密码"},null,8,["modelValue"])])),_:1}),(0,a.bF)(b,null,{default:(0,a.k6)((()=>[(0,a.bF)(h,{type:"primary",onClick:n.save},{default:(0,a.k6)((()=>[(0,a.eW)("提交")])),_:1},8,["onClick"])])),_:1})])),_:1},8,["model","rules"])):(0,a.Q3)("",!0),1==m.stepActive?((0,a.uX)(),(0,a.Wv)(k,{key:1,icon:"success",title:"密码重置成功","sub-title":"请牢记自己的新密码,返回登录后使用新密码登录"},{extra:(0,a.k6)((()=>[(0,a.bF)(h,{type:"primary",onClick:n.backLogin},{default:(0,a.k6)((()=>[(0,a.eW)("返回登录")])),_:1},8,["onClick"])])),_:1})):(0,a.Q3)("",!0)])),_:1})}t(8743);var m=t(9499),n={components:{commonPage:m["default"]},data(){return{stepActive:0,form:{user:"",phone:"",yzm:"",newpw:"",newpw2:""},rules:{user:[{required:!0,message:"请输入登录账号"}],phone:[{required:!0,message:"请输入手机号"}],yzm:[{required:!0,message:"请输入短信验证码"}],newpw:[{required:!0,message:"请输入新的密码"}],newpw2:[{required:!0,message:"请再次输入新的密码"},{validator:(e,l,t)=>{l!==this.form.newpw?t(new Error("两次输入密码不一致")):t()}}]},disabled:!1,time:0}},mounted(){},methods:{async getYzm(){var e=await this.$refs.form.validateField("phone").catch((()=>{}));if(!e)return!1;this.$message.success("已发送短信至手机号码"),this.disabled=!0,this.time=60;var l=setInterval((()=>{this.time-=1,this.time<1&&(clearInterval(l),this.disabled=!1,this.time=0)}),1e3)},async save(){var e=await this.$refs.form.validate().catch((()=>{}));if(!e)return!1;this.stepActive=1},backLogin(){this.$router.push({path:"/login"})}}},p=t(6262);const c=(0,p.A)(n,[["render",d]]);var f=c}}]);