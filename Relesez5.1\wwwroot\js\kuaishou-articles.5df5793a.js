"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[4776],{5910:function(e,t,a){a.r(t),a.d(t,{default:function(){return h}});var s=a(641),l=a(2644);const i={class:"left-panel"},o={style:{float:"left"}},r={style:{float:"right",color:"var(--el-text-color-secondary)","font-size":"13px"}};function c(e,t,a,c,n,u){const d=(0,s.g2)("el-button"),p=(0,s.g2)("el-option"),h=(0,s.g2)("el-select"),m=(0,s.g2)("el-header"),k=(0,s.g2)("sc-file-select"),g=(0,s.g2)("el-card"),f=(0,s.g2)("el-main"),b=(0,s.g2)("el-container"),j=(0,s.gN)("loading");return(0,s.uX)(),(0,s.Wv)(b,null,{default:(0,s.k6)((()=>[(0,s.bF)(m,null,{default:(0,s.k6)((()=>[(0,s.Lk)("div",i,[(0,s.bF)(d,{type:"primary",onClick:t[0]||(t[0]=e=>u.edit())},{default:(0,s.k6)((()=>[(0,s.eW)("编辑tag")])),_:1}),(0,s.bF)(h,{modelValue:n.jObjectSearch.areaName,"onUpdate:modelValue":t[1]||(t[1]=e=>n.jObjectSearch.areaName=e),filterable:"",onChange:u.upsearch,style:{"padding-left":"10px",width:"800px"}},{default:(0,s.k6)((()=>[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(n.area,(e=>((0,s.uX)(),(0,s.Wv)(p,{key:e.FName,label:e.FName,value:e.FName},{default:(0,s.k6)((()=>[(0,s.Lk)("span",o,(0,l.v_)(e.FName),1),(0,s.Lk)("span",r,(0,l.v_)(e.FTag),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","onChange"])])])),_:1}),(0,s.bo)(((0,s.uX)(),(0,s.Wv)(f,null,{default:(0,s.k6)((()=>[(0,s.bo)(((0,s.uX)(),(0,s.Wv)(g,{shadow:"never"},{default:(0,s.k6)((()=>[((0,s.uX)(),(0,s.Wv)(k,{key:n.i,ref:"file",modelValue:n.file,"onUpdate:modelValue":t[4]||(t[4]=e=>n.file=e),listApiObj:e.$API.ksArticles.getFileList,menuApiObj:e.$API.ksArticles.getUserList,path:"ks\\articles\\"+n.jObjectSearch.areaName,multiple:n.multiple,max:99,onSubmit:u.submit},{do:(0,s.k6)((()=>[(0,s.bF)(d,{type:"primary",icon:"el-icon-folder",onClick:t[2]||(t[2]=e=>u.openFolder())},{default:(0,s.k6)((()=>[(0,s.eW)("打开文件夹")])),_:1}),(0,s.bF)(d,{icon:"el-icon-switch",type:"primary",onClick:t[3]||(t[3]=e=>n.multiple=!n.multiple)},{default:(0,s.k6)((()=>[(0,s.eW)("切换多选")])),_:1})])),_:1},8,["modelValue","listApiObj","menuApiObj","path","multiple","onSubmit"]))])),_:1})),[[j,e.isSaveing]])])),_:1})),[[j,n.loading]])])),_:1})}a(8743);var n=a(695),u={name:"ksArticles",components:{scFileSelect:n.A},data(){return{loading:!1,jObjectSearch:{search:"",areaName:this.$TOOL.data.get("KSTASKART")},file:[],area:[],i:0,multiple:!1}},async created(){let e=await this.$API.ksCookiesTask.getTaskList.post();0==e.code&&(this.area=e.data.rows)},methods:{edit(){this.$prompt("请输入当前任务投稿需要Tag，注意#、注意空格","编辑Tag",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnPressEscape:!0,closeOnClickModal:!0,autofocus:!0}).then((async({value:e})=>{let t=await this.$API.ksCookiesTask.editTag.post({jObjectParam:{task:this.jObjectSearch.areaName,tag:e}});0==t.code?(this.$message.success("操作成功"),t=await this.$API.ksCookiesTask.getTaskList.post(),0==t.code&&(this.area=t.data.rows)):this.$alert(t.message,"提示",{type:"error"})}))},submit(e){let t=[];"string"==typeof e?t.push(e):t=e,this.$confirm("确定删除选中的 "+t.length+" 项吗？","提示",{type:"warning"}).then((async()=>{this.isSaveing=!0;let e=await this.$API.ksArticles.delFile.post({jObjectParam:{array:t}});this.isSaveing=!1,0==e.code?this.$message.success("操作成功"):this.$alert(e.message,"提示",{type:"error"}),this.$refs.file.getData()})).catch((()=>{}))},async openFolder(){await this.$API.ksArticles.openFolder.post({jObjectParam:{path:this.$refs.file.getPath()}})},upsearch(){this.i++,this.$TOOL.data.set("KSTASKART",this.jObjectSearch.areaName)}}},d=a(6262);const p=(0,d.A)(u,[["render",c]]);var h=p}}]);