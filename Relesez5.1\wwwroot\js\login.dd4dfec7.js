"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[6966],{5529:function(e,t,l){l.r(t),l.d(t,{default:function(){return P}});var a=l(641),o=l(2644);const i=e=>((0,a.Qi)("data-v-03443634"),e=e(),(0,a.jt)(),e),n={class:"login_bg"},s={class:"login_adv",style:{"background-image":"url(img/auth_banner.jpg)"}},c={class:"login_adv__title"},d=i((()=>(0,a.Lk)("div",{class:"login_adv__mask"},null,-1))),g={class:"login_adv__bottom"},r={class:"login_main"},u={class:"login_config"},m=i((()=>(0,a.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink","aria-hidden":"true",role:"img",width:"1em",height:"1em",preserveAspectRatio:"xMidYMid meet",viewBox:"0 0 512 512"},[(0,a.Lk)("path",{d:"M478.33 433.6l-90-218a22 22 0 0 0-40.67 0l-90 218a22 22 0 1 0 40.67 16.79L316.66 406h102.67l18.33 44.39A22 22 0 0 0 458 464a22 22 0 0 0 20.32-30.4zM334.83 362L368 281.65L401.17 362z",fill:"currentColor"}),(0,a.Lk)("path",{d:"M267.84 342.92a22 22 0 0 0-4.89-30.7c-.2-.15-15-11.13-36.49-34.73c39.65-53.68 62.11-114.75 71.27-143.49H330a22 22 0 0 0 0-44H214V70a22 22 0 0 0-44 0v20H54a22 22 0 0 0 0 44h197.25c-9.52 26.95-27.05 69.5-53.79 108.36c-31.41-41.68-43.08-68.65-43.17-68.87a22 22 0 0 0-40.58 17c.58 1.38 14.55 34.23 52.86 83.93c.92 1.19 1.83 2.35 2.74 3.51c-39.24 44.35-77.74 71.86-93.85 80.74a22 22 0 1 0 21.07 38.63c2.16-1.18 48.6-26.89 101.63-85.59c22.52 24.08 38 35.44 38.93 36.1a22 22 0 0 0 30.75-4.9z",fill:"currentColor"})],-1))),L={class:"login-form"},h={class:"login-header"},k={class:"logo"},v=["alt"],_={class:"qrCodeLogin"},f={class:"msg"},O=i((()=>(0,a.Lk)("br",null,null,-1))),w={key:0,class:"qrCodeLogin-result"};function $(e,t,l,i,$,p){const b=(0,a.g2)("el-button"),A=(0,a.g2)("el-dropdown-item"),C=(0,a.g2)("el-dropdown-menu"),F=(0,a.g2)("el-dropdown"),P=(0,a.g2)("password-form"),N=(0,a.g2)("el-tab-pane"),R=(0,a.g2)("el-tabs"),T=(0,a.g2)("sc-qr-code"),E=(0,a.g2)("el-result"),I=(0,a.g2)("el-dialog");return(0,a.uX)(),(0,a.CE)(a.FK,null,[(0,a.Lk)("div",n,[(0,a.Lk)("div",s,[(0,a.Lk)("div",c,[(0,a.Lk)("h4",null,(0,o.v_)(e.$t("login.slogan")),1),(0,a.Lk)("p",null,(0,o.v_)(e.$t("login.describe")),1)]),d,(0,a.Lk)("div",g," © "+(0,o.v_)(e.$CONFIG.APP_NAME)+" "+(0,o.v_)(e.$CONFIG.APP_VER),1)]),(0,a.Lk)("div",r,[(0,a.Lk)("div",u,[(0,a.bF)(b,{icon:$.config.dark?"el-icon-sunny":"el-icon-moon",circle:"",type:"info",onClick:p.configDark},null,8,["icon","onClick"]),(0,a.bF)(F,{trigger:"click",placement:"bottom-end",onCommand:p.configLang},{dropdown:(0,a.k6)((()=>[(0,a.bF)(C,null,{default:(0,a.k6)((()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)($.lang,(e=>((0,a.uX)(),(0,a.Wv)(A,{key:e.value,command:e,class:(0,o.C4)({selected:$.config.lang==e.value})},{default:(0,a.k6)((()=>[(0,a.eW)((0,o.v_)(e.name),1)])),_:2},1032,["command","class"])))),128))])),_:1})])),default:(0,a.k6)((()=>[(0,a.bF)(b,{circle:""},{default:(0,a.k6)((()=>[m])),_:1})])),_:1},8,["onCommand"])]),(0,a.Lk)("div",L,[(0,a.Lk)("div",h,[(0,a.Lk)("div",k,[(0,a.Lk)("img",{alt:e.$CONFIG.APP_NAME,src:"img/logo.png"},null,8,v),(0,a.Lk)("label",null,(0,o.v_)(e.$CONFIG.APP_NAME),1)])]),(0,a.bF)(R,null,{default:(0,a.k6)((()=>[(0,a.bF)(N,{label:e.$t("login.accountLogin"),lazy:""},{default:(0,a.k6)((()=>[(0,a.bF)(P)])),_:1},8,["label"])])),_:1})])])]),(0,a.bF)(I,{modelValue:$.showWechatLogin,"onUpdate:modelValue":t[0]||(t[0]=e=>$.showWechatLogin=e),title:e.$t("login.wechatLoginTitle"),width:400,"destroy-on-close":""},{default:(0,a.k6)((()=>[(0,a.Lk)("div",_,[(0,a.bF)(T,{class:"qrCode",text:$.WechatLoginCode,size:200},null,8,["text"]),(0,a.Lk)("p",f,[(0,a.eW)((0,o.v_)(e.$tc("login.wechatLoginMsg",1)),1),O,(0,a.eW)((0,o.v_)(e.$tc("login.wechatLoginMsg",2)),1)]),$.isWechatLoginResult?((0,a.uX)(),(0,a.CE)("div",w,[(0,a.bF)(E,{icon:"success",title:e.$tc("login.wechatLoginResult",1),"sub-title":e.$tc("login.wechatLoginResult",2)},null,8,["title","sub-title"])])):(0,a.Q3)("",!0)])])),_:1},8,["modelValue","title"])],64)}var p=l(5874),b=l(6028),A={components:{passwordForm:p["default"],phoneForm:b["default"]},data(){return{config:{lang:this.$TOOL.data.get("APP_LANG")||this.$CONFIG.LANG,dark:this.$TOOL.data.get("APP_DARK")||!1},lang:[{name:"简体中文",value:"zh-cn"},{name:"English",value:"en"}],WechatLoginCode:"",showWechatLogin:!1,isWechatLoginResult:!1}},watch:{"config.dark"(e){e?(document.documentElement.classList.add("dark"),this.$TOOL.data.set("APP_DARK",e)):(document.documentElement.classList.remove("dark"),this.$TOOL.data.remove("APP_DARK"))},"config.lang"(e){this.$i18n.locale=e,this.$TOOL.data.set("APP_LANG",e)}},created:function(){this.$TOOL.cookie.remove("authorization"),this.$TOOL.cookie.remove("token"),this.$TOOL.data.remove("USER_INFO"),this.$TOOL.data.remove("MENU"),this.$TOOL.data.remove("PERMISSIONS"),this.$TOOL.data.remove("DASHBOARDGRID"),this.$TOOL.data.remove("GRID"),this.$store.commit("clearViewTags"),this.$store.commit("clearKeepLive"),this.$store.commit("clearIframeList")},methods:{configDark(){this.config.dark=!this.config.dark},configLang(e){this.config.lang=e.value}}},C=l(6262);const F=(0,C.A)(A,[["render",$],["__scopeId","data-v-03443634"]]);var P=F}}]);