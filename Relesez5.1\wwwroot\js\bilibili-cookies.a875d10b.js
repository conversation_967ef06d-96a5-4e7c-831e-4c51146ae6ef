"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[9677,9509,6460,6505,8305],{6834:function(e,t,a){a.r(t),a.d(t,{default:function(){return C}});var i=a(641),o=a(2644);const r={class:"left-panel"},s={style:{float:"left",width:"150px"}},l={style:{float:"right",color:"var(--el-text-color-secondary)"}},d={class:"right-panel"},c={class:"right-panel-search"},n={key:1},h={class:"dialog-footer"};function p(e,t,a,p,u,m){const k=(0,i.g2)("el-option"),b=(0,i.g2)("el-select"),g=(0,i.g2)("el-button"),F=(0,i.g2)("el-icon"),f=(0,i.g2)("el-dropdown-item"),y=(0,i.g2)("sc-upload-file"),w=(0,i.g2)("el-tooltip"),C=(0,i.g2)("el-dropdown-menu"),x=(0,i.g2)("el-dropdown"),j=(0,i.g2)("el-input"),$=(0,i.g2)("el-header"),v=(0,i.g2)("el-table-column"),_=(0,i.g2)("el-button-group"),O=(0,i.g2)("el-link"),E=(0,i.g2)("el-popconfirm"),P=(0,i.g2)("scTable"),S=(0,i.g2)("el-main"),I=(0,i.g2)("el-container"),T=(0,i.g2)("cookieDialog"),W=(0,i.g2)("taskDialog"),D=(0,i.g2)("liveDialog"),L=(0,i.g2)("expirationDialog"),N=(0,i.g2)("sc-select"),A=(0,i.g2)("el-form-item"),K=(0,i.g2)("el-form"),R=(0,i.g2)("el-dialog"),z=(0,i.g2)("sc-qr-code"),Q=(0,i.gN)("loading");return(0,i.uX)(),(0,i.CE)(i.FK,null,[(0,i.bo)(((0,i.uX)(),(0,i.Wv)(I,{"element-loading-text":"执行中..."},{default:(0,i.k6)((()=>[(0,i.bF)($,null,{default:(0,i.k6)((()=>[(0,i.Lk)("div",r,[(0,i.bF)(b,{modelValue:u.jObjectSearch.areaId,"onUpdate:modelValue":t[0]||(t[0]=e=>u.jObjectSearch.areaId=e),filterable:"",onChange:m.upsearch,style:{"padding-right":"10px",width:"180px"},clearable:""},{default:(0,i.k6)((()=>[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(u.area,(e=>((0,i.uX)(),(0,i.Wv)(k,{key:e.Fid,label:e.FName,value:e.Fid},{default:(0,i.k6)((()=>[(0,i.Lk)("span",s,(0,o.v_)(e.FName),1),(0,i.Lk)("span",l,(0,o.v_)(e.FMsg),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","onChange"]),(0,i.bF)(g,{type:"primary",icon:"el-icon-suitcase",onClick:t[1]||(t[1]=e=>m.clean())},{default:(0,i.k6)((()=>[(0,i.eW)("清理信息")])),_:1}),(0,i.bF)(x,{trigger:"click","split-button":"",type:"primary",onClick:t[2]||(t[2]=e=>m.addCookies()),style:{"padding-right":"10px"}},{dropdown:(0,i.k6)((()=>[(0,i.bF)(C,null,{default:(0,i.k6)((()=>[(0,i.bF)(f,{onClick:m.group,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("分组管理")])),_:1},8,["onClick"]),(0,i.bF)(f,{onClick:m.download,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("导入模版")])),_:1},8,["onClick"]),(0,i.bF)(w,{class:"box-item",effect:"dark",content:"仅支持Excel，请下载导入模版",placement:"right"},{default:(0,i.k6)((()=>[(0,i.bF)(y,{ref:"uploadRef",multiple:!0,"show-file-list":!1,accept:".xls,.xlsx","on-success":m.fileSuccess,params:{jObjectParam:{path:"Bili\\Cookies"}},class:"upload-demo"},{default:(0,i.k6)((()=>[(0,i.bF)(f,{divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("导入账号")])),_:1})])),_:1},8,["on-success"])])),_:1}),(0,i.bF)(f,{onClick:m.exportCookie,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("导出账号")])),_:1},8,["onClick"]),(0,i.bF)(f,{divided:""})])),_:1})])),default:(0,i.k6)((()=>[(0,i.bF)(F,null,{default:(0,i.k6)((()=>[((0,i.uX)(),(0,i.Wv)((0,i.$y)("el-icon-plus")))])),_:1}),(0,i.eW)("   新增账号 ")])),_:1}),(0,i.bF)(x,{trigger:"click",class:"m-r"},{dropdown:(0,i.k6)((()=>[(0,i.bF)(C,{class:"drop-common"},{default:(0,i.k6)((()=>[(0,i.bF)(f,{onClick:m.updateSystem,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("更新系统数据")])),_:1},8,["onClick"]),(0,i.bF)(f,{onClick:m.addCookiesExpirationTime,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("充值账号时长")])),_:1},8,["onClick"]),(0,i.bF)(f,{onClick:m.updateCookieExpires,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("更新到期时间")])),_:1},8,["onClick"]),(0,i.bF)(f,{divided:""})])),_:1})])),default:(0,i.k6)((()=>[(0,i.bF)(g,{type:"primary"},{default:(0,i.k6)((()=>[(0,i.eW)(" 续费功能  "),(0,i.bF)(F,null,{default:(0,i.k6)((()=>[((0,i.uX)(),(0,i.Wv)((0,i.$y)("el-icon-arrow-down-bold")))])),_:1})])),_:1})])),_:1}),(0,i.bF)(x,{trigger:"click",class:"m-r"},{dropdown:(0,i.k6)((()=>[(0,i.bF)(C,{class:"drop-common"},{default:(0,i.k6)((()=>[(0,i.bF)(f,{onClick:m.updateCookieTask,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("更新任务信息")])),_:1},8,["onClick"]),(0,i.bF)(f,{onClick:m.updateCookieTaskPlus,divided:""},{default:(0,i.k6)((()=>[(0,i.bF)(w,{class:"box-item",effect:"dark",content:"快速更新任务状态和天数，无法获取剩余库存",placement:"top-start"},{default:(0,i.k6)((()=>[(0,i.eW)(" 更新任务信息Plus ")])),_:1})])),_:1},8,["onClick"]),(0,i.bF)(f,{onClick:m.updateTaskDay,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("更新直播天数")])),_:1},8,["onClick"]),(0,i.bF)(f,{divided:""})])),_:1})])),default:(0,i.k6)((()=>[(0,i.bF)(g,{type:"primary"},{default:(0,i.k6)((()=>[(0,i.eW)(" 任务功能  "),(0,i.bF)(F,null,{default:(0,i.k6)((()=>[((0,i.uX)(),(0,i.Wv)((0,i.$y)("el-icon-arrow-down-bold")))])),_:1})])),_:1})])),_:1}),(0,i.bF)(x,{trigger:"click",class:"m-r"},{dropdown:(0,i.k6)((()=>[(0,i.bF)(C,{class:"drop-common"},{default:(0,i.k6)((()=>[(0,i.bF)(f,{onClick:m.startLive,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("  开启直播  ")])),_:1},8,["onClick"]),(0,i.bF)(f,{onClick:m.liveTask,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("  弹幕礼物观看  ")])),_:1},8,["onClick"]),(0,i.bF)(f,{onClick:m.help,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("  任务帮助  ")])),_:1},8,["onClick"]),(0,i.bF)(f,{onClick:m.bulletScreen,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("  辅助弹幕  ")])),_:1},8,["onClick"]),(0,i.bF)(f,{onClick:m.watch,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("  辅助观看  ")])),_:1},8,["onClick"]),(0,i.bF)(f,{divided:""})])),_:1})])),default:(0,i.k6)((()=>[(0,i.bF)(g,{type:"primary"},{default:(0,i.k6)((()=>[(0,i.eW)(" 直播功能  "),(0,i.bF)(F,null,{default:(0,i.k6)((()=>[((0,i.uX)(),(0,i.Wv)((0,i.$y)("el-icon-arrow-down-bold")))])),_:1})])),_:1})])),_:1}),(0,i.bF)(x,{trigger:"click",class:"m-r"},{dropdown:(0,i.k6)((()=>[(0,i.bF)(C,{class:"drop-common"},{default:(0,i.k6)((()=>[(0,i.bF)(f,{onClick:m.receiveNormal,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("领取里程投稿")])),_:1},8,["onClick"]),(0,i.bF)(f,{onClick:m.receiveNormalPlus,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("领取里程投稿Plus")])),_:1},8,["onClick"]),(0,i.bF)(f,{onClick:m.receiveDaily,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("领取每日任务")])),_:1},8,["onClick"]),(0,i.bF)(f,{onClick:m.receiveCompulsory,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("领取强制任务")])),_:1},8,["onClick"]),(0,i.bF)(f,{onClick:m.lotteryDraw,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("抽奖")])),_:1},8,["onClick"]),(0,i.bF)(f,{divided:""})])),_:1})])),default:(0,i.k6)((()=>[(0,i.bF)(g,{type:"primary"},{default:(0,i.k6)((()=>[(0,i.eW)(" 领取奖励  "),(0,i.bF)(F,null,{default:(0,i.k6)((()=>[((0,i.uX)(),(0,i.Wv)((0,i.$y)("el-icon-arrow-down-bold")))])),_:1})])),_:1})])),_:1}),(0,i.bF)(x,{trigger:"click",class:"m-r"},{dropdown:(0,i.k6)((()=>[(0,i.bF)(C,{class:"drop-common"},{default:(0,i.k6)((()=>[(0,i.bF)(f,{onClick:m.supplement,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("批量补全Cookie")])),_:1},8,["onClick"]),(0,i.bF)(f,{onClick:m.updateUserAgent,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("批量变更UA")])),_:1},8,["onClick"]),(0,i.bF)(f,{onClick:m.updateProxy,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("批量变更代理")])),_:1},8,["onClick"]),(0,i.bF)(f,{onClick:m.updatePower,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("批量更新等级/电池")])),_:1},8,["onClick"]),(0,i.bF)(f,{onClick:m.recharge,divided:""},{default:(0,i.k6)((()=>[(0,i.eW)("充值电池（单选）")])),_:1},8,["onClick"]),(0,i.bF)(f,{onClick:m.delPlus,divided:""},{default:(0,i.k6)((()=>[(0,i.bF)(w,{class:"box-item",effect:"dark",content:"同时删除服务器信息，无法还原,请谨慎操作！",placement:"right"},{default:(0,i.k6)((()=>[(0,i.eW)(" 批量删除账号 ")])),_:1})])),_:1},8,["onClick"]),(0,i.bF)(f,{divided:""})])),_:1})])),default:(0,i.k6)((()=>[(0,i.bF)(g,{type:"primary"},{default:(0,i.k6)((()=>[(0,i.eW)(" 高级功能  "),(0,i.bF)(F,null,{default:(0,i.k6)((()=>[((0,i.uX)(),(0,i.Wv)((0,i.$y)("el-icon-arrow-down-bold")))])),_:1})])),_:1})])),_:1})]),(0,i.Lk)("div",d,[(0,i.Lk)("div",c,[(0,i.bF)(j,{modelValue:u.jObjectSearch.search,"onUpdate:modelValue":t[3]||(t[3]=e=>u.jObjectSearch.search=e),placeholder:"账号名称/直播号",clearable:""},null,8,["modelValue"]),(0,i.bF)(g,{type:"primary",icon:"el-icon-search",onClick:m.upsearch},{default:(0,i.k6)((()=>[(0,i.eW)(" 查询")])),_:1},8,["onClick"])])])])),_:1}),(0,i.bF)(S,{class:"nopadding"},{default:(0,i.k6)((()=>[(0,i.bF)(P,{ref:"table","cell-style":m.cellStyle,apiObj:u.apiObj,border:"",params:{jObjectSearch:u.jObjectSearch},stripe:"",remoteSort:"",remoteFilter:""},{default:(0,i.k6)((()=>[(0,i.bF)(v,{type:"selection",width:"50",align:"center"}),(0,i.bF)(v,{label:"功能",fixed:"left","header-align":"center",align:"left",width:"170"},{default:(0,i.k6)((e=>[(0,i.bF)(_,null,{default:(0,i.k6)((()=>["账号已到期"!=e.row.FStatusName?((0,i.uX)(),(0,i.Wv)(g,{key:0,text:"",type:"warning",size:"small",onClick:t=>m.showTask(e.row,e.$index)},{default:(0,i.k6)((()=>[(0,i.eW)("任务信息")])),_:2},1032,["onClick"])):(0,i.Q3)("",!0),(0,i.bF)(g,{text:"",size:"small",onClick:t=>m.showLive(e.row,e.$index),style:{color:"#3333FF"}},{default:(0,i.k6)((()=>[(0,i.eW)("直播配置")])),_:2},1032,["onClick"])])),_:2},1024)])),_:1}),(0,i.bF)(v,{label:"序号",prop:"FSort",align:"center",width:"85",sortable:""}),(0,i.bF)(v,{label:"错误信息",prop:"FStatusName","header-align":"center","min-width":"120","show-overflow-tooltip":"",sortable:""}),(0,i.bF)(v,{label:"代理地址",prop:"FProxyAddress","show-overflow-tooltip":"","header-align":"center",width:"200",sortable:""}),(0,i.bF)(v,{label:"账号名称",prop:"FName","header-align":"center",width:"150",sortable:"","show-overflow-tooltip":""}),(0,i.bF)(v,{label:"直播间地址",prop:"FRoomNo","header-align":"center",width:"140","show-overflow-tooltip":""},{default:(0,i.k6)((e=>[0!=e.row.FRoomId?((0,i.uX)(),(0,i.Wv)(O,{key:0,type:"primary",onClick:t=>m.open(e.row.FRoomNo)},{default:(0,i.k6)((()=>[(0,i.eW)((0,o.v_)(e.row.FRoomId),1)])),_:2},1032,["onClick"])):((0,i.uX)(),(0,i.CE)("span",n))])),_:1}),(0,i.bF)(v,{label:"天数",prop:"FCompleteName","show-overflow-tooltip":"",align:"center",sortable:"",width:"75"}),(0,i.bF)(v,{label:"准备领取",prop:"FAwardName2","show-overflow-tooltip":"","header-align":"center",sortable:"","min-width":"150"}),(0,i.bF)(v,{label:"等级/电池",prop:"FInfo",align:"center",width:"85"}),(0,i.bF)(v,{label:"账号到期时间",prop:"FExpirationTime",align:"center",width:"155",sortable:""}),(0,i.bF)(v,{label:"操作",fixed:"right","header-align":"center",align:"center",width:"130"},{default:(0,i.k6)((e=>[(0,i.bF)(_,null,{default:(0,i.k6)((()=>[(0,i.bF)(g,{text:"",type:"primary",size:"small",onClick:t=>m.editCookie(e.row,e.$index)},{default:(0,i.k6)((()=>[(0,i.eW)("编辑")])),_:2},1032,["onClick"]),(0,i.bF)(E,{title:"确定删除账号数据吗？(更新到期时间会还原)",onConfirm:t=>m.delCookie(e.row,e.$index),width:"190"},{reference:(0,i.k6)((()=>[(0,i.bF)(g,{text:"",type:"danger",size:"small"},{default:(0,i.k6)((()=>[(0,i.eW)("删除")])),_:1})])),_:2},1032,["onConfirm"])])),_:2},1024)])),_:1})])),_:1},8,["cell-style","apiObj","params"])])),_:1})])),_:1})),[[Q,u.updateLoading]]),u.dialog.cookies?((0,i.uX)(),(0,i.Wv)(T,{key:0,ref:"cookieDialog",onSuccess:m.upsearch,onClosed:t[4]||(t[4]=e=>u.dialog.cookies=!1)},null,8,["onSuccess"])):(0,i.Q3)("",!0),u.dialog.task?((0,i.uX)(),(0,i.Wv)(W,{key:1,ref:"taskDialog",onSuccess:m.upsearch,onClosed:t[5]||(t[5]=e=>{u.dialog.task=!1,m.upsearch()})},null,8,["onSuccess"])):(0,i.Q3)("",!0),u.dialog.live?((0,i.uX)(),(0,i.Wv)(D,{key:2,ref:"liveDialog",onSuccess:m.upsearch,onClosed:t[6]||(t[6]=e=>u.dialog.live=!1)},null,8,["onSuccess"])):(0,i.Q3)("",!0),u.dialog.expiration?((0,i.uX)(),(0,i.Wv)(L,{key:3,ref:"expirationDialog",onSuccess:m.upsearch,onClosed:t[7]||(t[7]=e=>u.dialog.expiration=!1)},null,8,["onSuccess"])):(0,i.Q3)("",!0),(0,i.bF)(R,{modelValue:u.dialog.proxy,"onUpdate:modelValue":t[10]||(t[10]=e=>u.dialog.proxy=e),title:"变更代理",width:"500px"},{footer:(0,i.k6)((()=>[(0,i.Lk)("span",h,[(0,i.bF)(g,{onClick:t[9]||(t[9]=e=>{u.dialog.proxy=!1,u.form.FProxyId=""})},{default:(0,i.k6)((()=>[(0,i.eW)("取消")])),_:1}),(0,i.bF)(g,{type:"primary",onClick:m.saveProxy},{default:(0,i.k6)((()=>[(0,i.eW)(" 确认 ")])),_:1},8,["onClick"])])])),default:(0,i.k6)((()=>[(0,i.bF)(K,{model:u.form},{default:(0,i.k6)((()=>[(0,i.bF)(A,{label:"代理地址",prop:"FProxyId"},{default:(0,i.k6)((()=>[(0,i.bF)(N,{clearable:"",modelValue:u.form.FProxyId,"onUpdate:modelValue":t[8]||(t[8]=e=>u.form.FProxyId=e),style:{width:"100%"},params:{jObjectSearch:{groupId:0,enable:1}},apiObj:e.$API.biliProxy.getProxyList,prop:{label:"FAddressName",value:"Fid"},placeholder:"不选代理，即可取消账号代理"},null,8,["modelValue","apiObj"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),(0,i.bF)(R,{modelValue:u.dialog.qr,"onUpdate:modelValue":t[11]||(t[11]=e=>u.dialog.qr=e),title:u.qrTitle,class:"qrDialog",width:"300px"},{default:(0,i.k6)((()=>[(0,i.bF)(z,{text:e.qrcode,size:300},null,8,["text"])])),_:1},8,["modelValue","title"])],64)}a(8743);var u=a(1132),m=a(2845),k=a(8505),b=a(906),g=a(4220),F=a(3959),f={name:"biliCookies",components:{cookieDialog:m["default"],taskDialog:k["default"],liveDialog:b["default"],expirationDialog:g["default"]},data(){return{apiObj:null,updateLoading:!1,dialog:{cookies:!1,task:!1,live:!1,expiration:!1,proxy:!1},jObjectSearch:{search:"",areaId:""},area:[],qrCode:"",qrTitle:"",form:{}}},async created(){let e=await this.$API.biliArea.getAreaList.post({jObjectSearch:{}});0==e.code&&(this.area=e.data.rows,this.area.length>0&&(this.jObjectSearch.areaId=this.area[0].Fid)),this.apiObj=this.$API.biliCookies.getCookiesList},methods:{open(e){window.open(e,"_blank")},group(){this.$router.push({path:"/bilibili/cookies/group"})},addCookies(){this.$prompt("一次添加至少1个，做多10个","添加账号",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnPressEscape:!0,closeOnClickModal:!0,autofocus:!0,inputType:"number",inputPattern:/^[1-9]$|^10$/,inputErrorMessage:"至少1个，最多10个"}).then((async({value:e})=>{this.updateLoading=!0;let t=await this.$API.biliCookies.addCookies.post({jObjectParam:{cookieNum:e}});0==t.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(t.message,"提示",{type:"error"}),this.updateLoading=!1}))},editCookie(e){this.dialog.cookies=!0,this.$nextTick((()=>{this.$refs.cookieDialog.open("edit").setData(e)}))},async delCookie(e){let t=await this.$API.biliCookies.delCookie.post({jObjectParam:e});0==t.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(t.message,"提示",{type:"error"})},async emptyCookie(e){let t=await this.$API.biliCookies.emptyCookie.post({jObjectParam:e});0==t.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(t.message,"提示",{type:"error"})},showTask(e){this.dialog.task=!0,this.$nextTick((()=>{this.$refs.taskDialog.open("show").setData({cookieId:e.Fid,areaId:this.jObjectSearch.areaId})}))},showLive(e){if(this.dialog.live=!0,this.jObjectSearch.areaId){let t=this.area.find((e=>e.Fid===this.jObjectSearch.areaId)).FName;this.$nextTick((()=>{this.$refs.liveDialog.open("show","直播配置（"+t+"）").setData({cookieId:e.Fid,areaId:this.jObjectSearch.areaId,areaName:t})}))}else this.$alert("请先选择分区","提示",{type:"error"})},addCookiesExpirationTime(){let e=this.$refs.table.getSelectionRows(),t=[];for(let a in e)t.push({Fid:e[a].Fid,FName:e[a].FName,FKey:e[a].FKey,FExpirationTime:e[a].FExpirationTime});0!=t.length?(this.dialog.expiration=!0,this.$nextTick((()=>{this.$refs.expirationDialog.open("show").setData({array:t})}))):this.$alert("请选择账号","提示",{type:"error"})},async updateCookieExpires(){try{this.updateLoading=!0;let e=await this.$API.biliCookies.updateCookieExpires.post({jObjectParam:{}});if(this.updateLoading=!1,0!=e.code)throw new Error(e.message);this.$message.success("更新成功！"),this.upsearch()}catch(e){this.$alert(e.message,"提示",{type:"error"})}},async lotteryDraw(){if(""!=this.jObjectSearch.areaId)try{let e=this.$refs.table.getSelectionRows();if(0==e.length&&(e=(await this.apiObj.post({jObjectSearch:this.jObjectSearch})).data.rows),0==e.length)throw new Error("请添加账号！");let t=[];for(let i in e)new Date(e[i].FExpirationTime)>new Date&&t.push({Fid:e[i].Fid,FName:e[i].FName,FKey:e[i].FKey,FExpirationTime:e[i].FExpirationTime});this.updateLoading=!0;let a=await this.$API.biliCookies.lotteryDraw.post({jObjectParam:{array:t,areaId:this.jObjectSearch.areaId}});if(this.updateLoading=!1,0!=a.code)throw new Error(a.message);this.$message.success("操作成功,等待控制台出结果！"),this.upsearch()}catch(e){this.$alert(e.message,"提示",{type:"error"})}else this.$alert("请先选择分区","提示",{type:"error"})},async updateUserAgent(){this.$prompt("合法UA","请输入UA",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnPressEscape:!0,closeOnClickModal:!0,autofocus:!0}).then((async({value:e})=>{try{let t=this.$refs.table.getSelectionRows();if(0==t.length&&(t=(await this.apiObj.post({jObjectSearch:this.jObjectSearch})).data.rows),0==t.length)throw new Error("请添加账号！");let a=[];for(let e in t)new Date(t[e].FExpirationTime)>new Date&&a.push({Fid:t[e].Fid,FName:t[e].FName,FKey:t[e].FKey,FExpirationTime:t[e].FExpirationTime});this.updateLoading=!0;let i=await this.$API.biliCookies.updateUserAgent.post({jObjectParam:{array:a,userAgent:e}});if(this.updateLoading=!1,0!=i.code)throw new Error(i.message);this.$message.success("操作成功！"),this.upsearch()}catch(t){this.$alert(t.message,"提示",{type:"error"})}}))},updateProxy(){try{let e=this.$refs.table.getSelectionRows();if(0==e.length)throw new Error("请选择账号！");this.dialog.proxy=!0}catch(e){this.$alert(e.message,"提示",{type:"error"})}},async updatePower(){try{let e=this.$refs.table.getSelectionRows();if(0==e.length&&(e=(await this.apiObj.post({jObjectSearch:this.jObjectSearch})).data.rows),0==e.length)throw new Error("请添加账号！");let t=[];for(let i in e)t.push({Fid:e[i].Fid,FName:e[i].FName,FKey:e[i].FKey,FExpirationTime:e[i].FExpirationTime});this.updateLoading=!0;let a=await this.$API.biliCookies.updatePower.post({jObjectParam:{array:t,areaId:this.jObjectSearch.areaId}});if(this.updateLoading=!1,0!=a.code)throw new Error(a.message);this.$message.success("操作成功"),this.upsearch()}catch(e){this.$alert(e.message,"提示",{type:"error"})}},async saveProxy(){try{let e=this.$refs.table.getSelectionRows();if(0==e.length&&(e=(await this.apiObj.post({jObjectSearch:this.jObjectSearch})).data.rows),0==e.length)throw new Error("请添加账号！");let t=[];for(let i in e)new Date(e[i].FExpirationTime)>new Date&&t.push({Fid:e[i].Fid,FName:e[i].FName,FKey:e[i].FKey,FExpirationTime:e[i].FExpirationTime});this.updateLoading=!0;let a=await this.$API.biliCookies.updateProxy.post({jObjectParam:{array:t,proxyId:this.form.FProxyId}});if(this.updateLoading=!1,0!=a.code)throw new Error(a.message);this.$message.success("操作成功！"),this.dialog.proxy=!1,this.form.FProxyId="",this.upsearch()}catch(e){this.$alert(e.message,"提示",{type:"error"})}},async updateSystem(){try{this.updateLoading=!0;let e=await this.$API.biliCookies.updateSystem.post();if(this.updateLoading=!1,0!=e.code)throw new Error(e.message);this.$message.success("操作成功！"),this.upsearch()}catch(e){this.$alert(e.message,"提示",{type:"error"})}},upsearch(){this.$refs.table.upData({jObjectSearch:this.jObjectSearch})},async updateTaskDay(){if(""==this.jObjectSearch.areaId)return void this.$alert("请先选择分区","提示",{type:"error"});const e=u.Ks.service({lock:!0,text:"执行中...",background:"rgba(0, 0, 0, 0.7)"});try{let e="0",t=await this.$API.biliAreaTask.getAreaTaskList.post({jObjectSearch:this.jObjectSearch});if(0!=t.code)throw new Error(o.message);for(let r in t.data.rows)1==t.data.rows[r].FComplete&&(e+=","+t.data.rows[r].Fid);if("0"==e)throw new Error("无法刷新天数！");let a=this.$refs.table.getSelectionRows();if(0==a.length&&(a=(await this.apiObj.post({jObjectSearch:this.jObjectSearch})).data.rows),0==a.length)throw new Error("请添加账号！");let i=[];for(let r in a)new Date(a[r].FExpirationTime)>new Date&&i.push({Fid:a[r].Fid,FName:a[r].FName,FKey:a[r].FKey,FExpirationTime:a[r].FExpirationTime});let o=await this.$API.biliQuartz.manualExecQuartz.post({jObjectParam:{array:i,areaId:this.jObjectSearch.areaId,jobName:"更新任务信息",type:this.$TOOL.data.get("EXECMODE"),param:e,delay:1}});if(0!=o.code)throw new Error(o.message);this.$message.success(o.message),this.upsearch()}catch(t){this.$alert(t.message,"提示",{type:"error"})}e.close()},async execQuartz(e,t=0,a=!0,i=""){if(""==this.jObjectSearch.areaId)return void this.$alert("请先选择分区","提示",{type:"error"});const o=u.Ks.service({lock:!0,text:"执行中...",background:"rgba(0, 0, 0, 0.7)"});try{let o=this.$refs.table.getSelectionRows();if(0==o.length&&(o=(await this.apiObj.post({jObjectSearch:this.jObjectSearch})).data.rows),0==o.length)throw new Error("请添加账号！");let r=[];if(a)for(let e in o)new Date(o[e].FExpirationTime)>new Date&&r.push({Fid:o[e].Fid,FName:o[e].FName,FKey:o[e].FKey,FExpirationTime:o[e].FExpirationTime});else for(let e in o)r.push({Fid:o[e].Fid,FName:o[e].FName,FKey:o[e].FKey,FExpirationTime:o[e].FExpirationTime});let s=await this.$API.biliQuartz.manualExecQuartz.post({jObjectParam:{array:r,areaId:this.jObjectSearch.areaId,jobName:e,type:this.$TOOL.data.get("EXECMODE"),delay:t,param:i}});if(0!=s.code)throw new Error(s.message);this.$message.success(s.message),this.upsearch()}catch(r){this.$alert(r.message,"提示",{type:"error"})}o.close()},async startLive(){await this.execQuartz("开启直播")},async receiveCompulsory(){await this.execQuartz("领取强制任务")},async receiveDaily(){await this.execQuartz("领取每日任务")},async receiveNormal(){await this.execQuartz("领取里程投稿")},async receiveNormalPlus(){await this.execQuartz("领取里程投稿Plus")},async liveTask(){await this.execQuartz("弹幕礼物观看",0,!1)},async updateCookieTask(){await this.execQuartz("更新任务信息",1)},async updateCookieTaskPlus(){await this.execQuartz("更新任务信息Plus",1)},async bulletScreen(){let e=this.$refs.table.getSelectionRows();if(0==e.length)return void this.$alert("请选择账号！","提示",{type:"error"});let t=[];for(let a in e)new Date(e[a].FExpirationTime)>new Date&&t.push({Fid:e[a].Fid,FName:e[a].FName,FKey:e[a].FKey,FRoomId:e[a].FRoomId,FExpirationTime:e[a].FExpirationTime});this.$prompt("请输入发送的消息","辅助弹幕",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnPressEscape:!0,closeOnClickModal:!0,autofocus:!0}).then((async({value:e})=>{this.updateLoading=!0;let a=t.map((e=>e.FRoomId)).join(","),i=await this.$API.biliLive.bulletScreen.post({jObjectParam:{roomId:a,msg:e}});0==i.code?(this.$message.success(i.data),this.upsearch()):this.$alert(i.message,"提示",{type:"error"}),this.updateLoading=!1}))},async watch(){let e=this.$refs.table.getSelectionRows();if(0==e.length)return void this.$alert("请选择账号！","提示",{type:"error"});let t=[];for(let a in e)new Date(e[a].FExpirationTime)>new Date&&t.push({Fid:e[a].Fid,FName:e[a].FName,FKey:e[a].FKey,FRoomId:e[a].FRoomId,FExpirationTime:e[a].FExpirationTime});this.$prompt("请输入观看时长，单位分钟","辅助观看",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnPressEscape:!0,closeOnClickModal:!0,autofocus:!0}).then((async({value:e})=>{this.updateLoading=!0;let a=t.map((e=>e.FRoomId)).join(","),i=await this.$API.biliLive.watchLive.post({jObjectParam:{roomId:a,watchMinutes:e,areaId:this.jObjectSearch.areaId}});0==i.code?(this.$message.success(i.data),this.upsearch()):this.$alert(i.message,"提示",{type:"error"}),this.updateLoading=!1}))},async fileSuccess(e){if(this.updateLoading=!0,0==e.code){let t=await this.$API.biliCookies.importCookies.post({jObjectParam:e.data});0==t.code?(this.$message.success(t.message),this.upsearch()):this.$alert(t.message,"提示",{type:"error"})}else this.$alert(e.message,"提示",{type:"error"});this.updateLoading=!1},help(){let e=this.$refs.table.getSelectionRows();0!=e.length?this.$confirm("确认要执行任务帮助吗？","提示",{type:"warning"}).then((async()=>{await this.execQuartz("任务帮助",1,!0,"否")})):this.$alert("请选择账号！","提示",{type:"error"})},async delPlus(){this.$confirm("确认删除服务器未到期的账号！（该操作不可逆）","提示",{type:"warning"}).then((async()=>{try{let e=this.$refs.table.getSelectionRows();if(0==e.length)throw new Error("请选择账号！");let t=[];for(let i in e)t.push({Fid:e[i].Fid,FIdentifying:e[i].FIdentifying,FKey:e[i].FKey});this.updateLoading=!0;let a=await this.$API.biliCookies.delCookiePlus.post({jObjectParam:{array:t}});if(this.updateLoading=!1,0!=a.code)throw new Error(a.message);this.$message.success(a.message),this.upsearch()}catch(e){this.$alert(e.message,"提示",{type:"error"})}}))},cellStyle(e){return"暂无抢领任务！"==e.row[e.column.property]?{color:"green"}:"账号即将到期"==e.row[e.column.property]?{color:"#e6a23c"}:"请更新任务！"==e.row[e.column.property]||"账号已到期"==e.row[e.column.property]?{color:"#f56c6c"}:void 0},async exportCookie(){let e=this.$refs.table.getSelectionRows();if(0==e.length)return void this.$alert("请选择账号！","提示",{type:"error"});let t=[];for(let i in e)new Date(e[i].FExpirationTime)>new Date&&t.push({Fid:e[i].Fid,FName:e[i].FName,FKey:e[i].FKey,FRoomId:e[i].FRoomId,FExpirationTime:e[i].FExpirationTime});this.updateLoading=!0;let a=await this.$API.biliCookies.exportCookie.post({jObjectParam:{array:t}});if(this.updateLoading=!1,0==a.code){let e=document.createElement("a");e.style="display: none",e.target="_blank",e.download="Cookies",e.href=a.data,document.body.appendChild(e),e.click(),document.body.removeChild(e),this.upsearch()}else this.$alert(a.message,"提示",{type:"error"})},download(){const e=[["Key","序号","账号名称","Cookie","代理地址"],["唯一(编辑账号，右上),可替换当前账号（可不填）","0（纯数字）","bili(不填自动获取)","buid3=xxxx; buid4=xxxxx","http://192.168.0.1:8080:账号:密码"]],t=F.Wp.aoa_to_sheet(e);t["!cols"]=[{wch:40},{wch:10},{wch:30},{wch:50},{wch:50}];const a=F.Wp.book_new();F.Wp.book_append_sheet(a,t,"Sheet1"),F._h(a,"账号导入模版.xls")},async clean(){await this.$API.biliCookies.cleanMessage.post(),this.upsearch()},supplement(){this.$confirm("确认补全缺失的Cookie信息？（该操作不可逆）","提示",{type:"warning"}).then((async()=>{try{let e=this.$refs.table.getSelectionRows();if(0==e.length)throw new Error("请选择账号！");let t=[];for(let i in e)t.push({Fid:e[i].Fid,FIdentifying:e[i].FIdentifying,FKey:e[i].FKey});this.updateLoading=!0;let a=await this.$API.biliCookies.supplement.post({jObjectParam:{array:t}});if(this.updateLoading=!1,0!=a.code)throw new Error(a.message);this.$message.success(a.message),this.upsearch()}catch(e){this.$alert(e.message,"提示",{type:"error"})}}))},recharge(){try{let e=this.$refs.table.getSelectionRows();if(1!=e.length)throw new Error("请选择一个账号！");this.$prompt("请输入充值金额（元）","充值电池",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnPressEscape:!0,closeOnClickModal:!0,autofocus:!0,inputType:"number",inputPattern:/^[1-9]\d*$/,inputErrorMessage:"请输入正整数"}).then((async({value:t})=>{let a=await this.$API.biliCookies.recharge.post({jObjectParam:{id:e[0].Fid,money:t}});0==a.code?(this.qrcode=a.data,this.dialog.qr=!0,this.qrTitle="充值电池（"+e[0].FName+"）"):this.$alert(a.message,"提示",{type:"error"})}))}catch(e){this.$alert(e.message,"提示",{type:"error"})}}}},y=a(6262);const w=(0,y.A)(f,[["render",p],["__scopeId","data-v-50488c0a"]]);var C=w}}]);