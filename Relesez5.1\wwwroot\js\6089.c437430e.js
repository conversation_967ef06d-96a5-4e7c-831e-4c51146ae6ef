"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[6089],{9235:function(__unused_webpack_module,__webpack_exports__){__webpack_exports__.A={name:"uploadRender",props:{modelValue:[String,Number,Boolean,Date,Object,Array],item:{type:Object,default:()=>{}}},data(){return{value:this.modelValue,apiObj:this.getApiObj()}},watch:{value(e){this.$emit("update:modelValue",e)}},mounted(){},methods:{getApiObj(){return eval("this."+this.item.options.apiObj)}}}},6089:function(e,t,l){l.r(t),l.d(t,{default:function(){return n}});var u=l(641);function a(e,t,l,a,p,i){const o=(0,u.g2)("el-table-column"),n=(0,u.g2)("sc-table-select");return(0,u.uX)(),(0,u.Wv)(n,{modelValue:p.value,"onUpdate:modelValue":t[0]||(t[0]=e=>p.value=e),apiObj:p.apiObj,"table-width":600,multiple:l.item.options.multiple,props:l.item.options.props,style:{width:"100%"}},{default:(0,u.k6)((()=>[((0,u.uX)(!0),(0,u.CE)(u.FK,null,(0,u.pI)(l.item.options.column,((e,t)=>((0,u.uX)(),(0,u.Wv)(o,{key:t,prop:e.prop,label:e.label,width:e.width},null,8,["prop","label","width"])))),128))])),_:1},8,["modelValue","apiObj","multiple","props"])}var p=l(9235),i=l(6262);const o=(0,i.A)(p.A,[["render",a]]);var n=o}}]);