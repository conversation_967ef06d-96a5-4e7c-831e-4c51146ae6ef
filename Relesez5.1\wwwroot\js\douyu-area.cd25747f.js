"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[6527],{7595:function(e,a,t){t.r(a),t.d(a,{default:function(){return g}});var l=t(641),r=t(2644);const i={class:"custom-tree-node"},s={class:"left-panel"},o={class:"right-panel"};function n(e,a,t,n,d,c){const u=(0,l.g2)("el-input"),h=(0,l.g2)("el-header"),p=(0,l.g2)("el-button"),g=(0,l.g2)("el-tree"),b=(0,l.g2)("el-main"),k=(0,l.g2)("el-footer"),F=(0,l.g2)("el-container"),f=(0,l.g2)("el-aside"),m=(0,l.g2)("el-table-column"),y=(0,l.g2)("el-switch"),w=(0,l.g2)("el-popconfirm"),j=(0,l.g2)("el-button-group"),C=(0,l.g2)("scTable"),$=(0,l.g2)("areaDialog"),A=(0,l.g2)("taskDialog"),T=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)(l.FK,null,[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(F,{"element-loading-text":"请稍等..."},{default:(0,l.k6)((()=>[(0,l.bF)(f,{width:"220px"},{default:(0,l.k6)((()=>[(0,l.bF)(F,null,{default:(0,l.k6)((()=>[(0,l.bF)(h,null,{default:(0,l.k6)((()=>[(0,l.bF)(u,{placeholder:"输入关键字进行过滤",modelValue:d.filterText,"onUpdate:modelValue":a[0]||(a[0]=e=>d.filterText=e),clearable:""},null,8,["modelValue"])])),_:1}),(0,l.bF)(b,{class:"nopadding"},{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(g,{ref:"tree",class:"custom-tree menu","node-key":"Fid",props:{label:"FName",value:"Fid"},data:d.treeData,"current-node-key":d.jObjectSearch.areaId,"highlight-current":!0,"expand-on-click-node":!1,"filter-node-method":c.filterNode,onNodeClick:c.treeClick,"default-expand-all":!0},{default:(0,l.k6)((({data:e})=>[(0,l.Lk)("span",i,[(0,l.Lk)("span",null,(0,r.v_)(e.FName),1),(0,l.Lk)("span",null,[(0,l.bF)(p,{class:"do",link:"",type:"primary",size:"small",style:{"margin-left":"10px","margin-right":"10px"},onClick:a=>c.editArea(e)},{default:(0,l.k6)((()=>[(0,l.eW)("编辑")])),_:2},1032,["onClick"])])])])),_:1},8,["data","current-node-key","filter-node-method","onNodeClick"])),[[T,d.treeLoading]])])),_:1}),(0,l.bF)(k,{style:{height:"51px","text-align":"center"}},{default:(0,l.k6)((()=>[(0,l.bF)(p,{type:"primary",size:"small",icon:"el-icon-plus",style:{width:"45%"},onClick:a[1]||(a[1]=e=>c.addArea())},{default:(0,l.k6)((()=>[(0,l.eW)("新增分区")])),_:1}),(0,l.bF)(p,{type:"danger",size:"small",icon:"el-icon-delete",style:{width:"45%"},onClick:a[2]||(a[2]=e=>c.delArea())},{default:(0,l.k6)((()=>[(0,l.eW)("删除分区")])),_:1})])),_:1})])),_:1})])),_:1}),(0,l.bF)(F,null,{default:(0,l.k6)((()=>[(0,l.bF)(h,null,{default:(0,l.k6)((()=>[(0,l.Lk)("div",s,[(0,l.bF)(p,{type:"primary",icon:"el-icon-plus",onClick:a[3]||(a[3]=e=>c.addTask())},{default:(0,l.k6)((()=>[(0,l.eW)("新增信息")])),_:1})]),(0,l.Lk)("div",o,[(0,l.bF)(u,{modelValue:d.jObjectSearch.search,"onUpdate:modelValue":a[4]||(a[4]=e=>d.jObjectSearch.search=e),placeholder:"任务Key / 任务名称 / 奖励名称",class:"input-with-select",onChange:c.upsearch,style:{width:"250px"}},null,8,["modelValue","onChange"]),(0,l.bF)(p,{type:"primary",onClick:c.upsearch,icon:"el-icon-search"},{default:(0,l.k6)((()=>[(0,l.eW)("查询")])),_:1},8,["onClick"])])])),_:1}),(0,l.bF)(b,{class:"nopadding"},{default:(0,l.k6)((()=>[(0,l.bF)(C,{ref:"table",apiObj:d.taskApiObj,"highlight-current-row":"","row-key":"Fid",params:{jObjectSearch:d.jObjectSearch},"row-Style":c.rowStyle,border:""},{default:(0,l.k6)((()=>[(0,l.bF)(m,{label:"排序",prop:"FSort",align:"center",width:"70"}),(0,l.bF)(m,{label:"分区名称",prop:"FAreaName",align:"center",width:"170"}),(0,l.bF)(m,{label:"任务标识",prop:"FTaskId",align:"center",width:"120"}),(0,l.bF)(m,{label:"任务名称",prop:"FTaskName",align:"center","show-overflow-tooltip":""}),(0,l.bF)(m,{label:"奖励名称",prop:"FPrizeName",align:"center","show-overflow-tooltip":""}),(0,l.bF)(m,{label:"库存数量",prop:"FRemainDesc",align:"center",width:"100"}),(0,l.bF)(m,{label:"单价",prop:"FPrice",align:"center",width:"70"}),(0,l.bF)(m,{label:"显示天数",prop:"FCompleteName",align:"center",width:"90"}),(0,l.bF)(m,{label:"每日任务",prop:"FDailyName",align:"center",width:"90"}),(0,l.bF)(m,{label:"是否启用",prop:"FEnable",align:"center",width:"100"},{default:(0,l.k6)((e=>[(0,l.bF)(y,{modelValue:e.row.FEnable,"onUpdate:modelValue":a=>e.row.FEnable=a,onChange:a=>c.enableSwitch(a,e.row),loading:e.row.$enable,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue","onChange","loading"])])),_:1}),(0,l.bF)(m,{label:"操作",fixed:"right",align:"center",width:"140"},{default:(0,l.k6)((e=>[(0,l.bF)(j,null,{default:(0,l.k6)((()=>[(0,l.bF)(p,{text:"",type:"primary",size:"small",onClick:a=>c.editTask(e.row,e.$index)},{default:(0,l.k6)((()=>[(0,l.eW)("编辑")])),_:2},1032,["onClick"]),(0,l.bF)(w,{title:"确定删除吗？",onConfirm:a=>c.delTask(e.row,e.$index)},{reference:(0,l.k6)((()=>[(0,l.bF)(p,{text:"",type:"danger",size:"small"},{default:(0,l.k6)((()=>[(0,l.eW)("删除")])),_:1})])),_:2},1032,["onConfirm"])])),_:2},1024)])),_:1})])),_:1},8,["apiObj","params","row-Style"])])),_:1})])),_:1})])),_:1})),[[T,d.updateLoading]]),d.dialog.area?((0,l.uX)(),(0,l.Wv)($,{key:0,ref:"areaDialog",onSuccess:c.upsearchTree,onClosed:a[5]||(a[5]=e=>d.dialog.area=!1)},null,8,["onSuccess"])):(0,l.Q3)("",!0),d.dialog.task?((0,l.uX)(),(0,l.Wv)(A,{key:1,ref:"taskDialog",onSuccess:c.upsearch,onClosed:a[6]||(a[6]=e=>d.dialog.task=!1)},null,8,["onSuccess"])):(0,l.Q3)("",!0)],64)}var d=t(1570),c=t(6790),u={name:"douyuArea",components:{areaDialog:d["default"],taskDialog:c["default"]},data(){return{dialog:{area:!1,task:!1},filterText:"",treeData:[],defaultTreeValue:"",treeLoading:!0,taskApiObj:null,updateLoading:!1,jObjectSearch:{search:"",areaId:0}}},watch:{filterText(e){this.$refs.tree.filter(e)}},async created(){let e=await this.$API.douyuArea.getAreaList.post({jObjectSearch:{}});this.treeLoading=!1,this.treeData=e.data.rows,e.data.rows.length>0&&(this.jObjectSearch.areaId=e.data.rows[0].Fid),this.taskApiObj=this.$API.douyuAreaTask.getAreaTaskList},async mounted(){},methods:{filterNode(e,a){return!e||-1!==a.FName.indexOf(e)},addArea(){this.dialog.area=!0,this.$nextTick((()=>{this.$refs.areaDialog.open("add")}))},editArea(e){this.dialog.area=!0,this.$nextTick((()=>{this.$refs.areaDialog.open("edit").setData(e)}))},delArea(){let e=this.$refs["tree"].getCurrentNode();e&&this.$confirm("确认删除 "+e.FName+" ？","提示",{type:"warning"}).then((async()=>{let a=await this.$API.douyuArea.delArea.post({jObjectParam:{id:e.Fid}});0==a.code?(this.$message.success("操作成功"),this.upsearchTree()):this.$alert(a.message,"提示",{type:"error"})}))},addTask(){let e=this.$refs["tree"].getCurrentNode();e?(this.dialog.task=!0,this.$nextTick((()=>{this.$refs.taskDialog.open("add").setData({FAreaId:e.Fid})}))):this.$alert("请先选择游戏分区！","提示",{type:"error"})},editTask(e){this.dialog.task=!0,this.$nextTick((()=>{this.$refs.taskDialog.open("add").setData(e)}))},async delTask(e){let a=await this.$API.douyuAreaTask.delAreaTask.post({jObjectParam:{id:e.Fid}});0==a.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(a.message,"提示",{type:"error"})},async enableSwitch(e,a){a.$enable=!0;let t=await this.$API.douyuAreaTask.enableSwitch.post({jObjectParam:a});0==t.code?this.$message.success("操作成功"):this.$alert(t.message,"提示",{type:"error"}),a.FEnable=t.data,0==a.FEnable&&(a.FCompulsory=0,a.FCompulsoryName=""),delete a.$enable},treeClick(e){this.jObjectSearch.areaId=e.Fid,this.upsearch()},upsearch(){this.$refs.table.upData({jObjectSearch:this.jObjectSearch})},async upsearchTree(){let e=this.$refs["tree"].getCurrentKey(),a=await this.$API.douyuArea.getAreaList.post({jObjectSearch:{}});this.treeLoading=!1,this.treeData=a.data.rows,void 0==this.treeData.find((a=>a.Fid==e))&&this.treeData.length>0&&(e=this.treeData[0].Fid),this.$nextTick((()=>{this.$refs["tree"].setCurrentKey(e),this.jObjectSearch.areaId=e})),this.upsearch()},rowStyle(e){return 1==e.row.FCompulsory?"color:#6600FF":0==e.row.FEnable?"color:#CC0000":1==e.row.FDaily?"color:#CC6600":void 0}}},h=t(6262);const p=(0,h.A)(u,[["render",n],["__scopeId","data-v-7ccf0b28"]]);var g=p}}]);