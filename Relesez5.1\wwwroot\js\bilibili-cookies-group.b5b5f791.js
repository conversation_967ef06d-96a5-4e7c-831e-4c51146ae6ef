"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[1051],{3337:function(e,a,t){t.r(a),t.d(a,{default:function(){return p}});var l=t(641);const i={class:"left-panel"},s={class:"right-panel"},r={class:"right-panel-search"};function o(e,a,t,o,n,c){const d=(0,l.g2)("el-option"),p=(0,l.g2)("el-select"),h=(0,l.g2)("el-button"),u=(0,l.g2)("el-switch"),b=(0,l.g2)("el-input"),g=(0,l.g2)("el-header"),m=(0,l.g2)("el-table-column"),w=(0,l.g2)("scTable"),F=(0,l.g2)("el-main"),v=(0,l.g2)("el-container"),f=(0,l.gN)("auths"),j=(0,l.gN)("loading");return(0,l.bo)(((0,l.uX)(),(0,l.Wv)(v,null,{default:(0,l.k6)((()=>[(0,l.bF)(g,null,{default:(0,l.k6)((()=>[(0,l.Lk)("div",i,[(0,l.bF)(p,{modelValue:n.jObjectSearch.areaId,"onUpdate:modelValue":a[0]||(a[0]=e=>n.jObjectSearch.areaId=e),filterable:"",onChange:c.upsearch,style:{"padding-right":"10px",width:"180px"}},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(n.area,(e=>((0,l.uX)(),(0,l.Wv)(d,{key:e.Fid,label:e.FName,value:e.Fid},null,8,["label","value"])))),128))])),_:1},8,["modelValue","onChange"]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(h,{type:"danger",onClick:a[1]||(a[1]=e=>c.del())},{default:(0,l.k6)((()=>[(0,l.eW)("删除配置")])),_:1})),[[f,["vip"]]]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(h,{type:"primary",onClick:a[2]||(a[2]=e=>c.complete())},{default:(0,l.k6)((()=>[(0,l.eW)("新增配置")])),_:1})),[[f,["vip"]]]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(h,{type:"primary",onClick:a[3]||(a[3]=e=>c.matching())},{default:(0,l.k6)((()=>[(0,l.eW)("智能匹配")])),_:1})),[[f,["vip"]]]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(h,{type:"primary",onClick:a[4]||(a[4]=e=>c.setting())},{default:(0,l.k6)((()=>[(0,l.eW)("设置勾选账号")])),_:1})),[[f,["vip"]]]),(0,l.bo)((0,l.bF)(u,{modelValue:n.mileage,"onUpdate:modelValue":a[5]||(a[5]=e=>n.mileage=e),"active-text":"直播",style:{"padding-left":"10px"}},null,8,["modelValue"]),[[f,["vip"]]]),(0,l.bo)((0,l.bF)(u,{modelValue:n.aricles,"onUpdate:modelValue":a[6]||(a[6]=e=>n.aricles=e),"active-text":"投稿",style:{"padding-left":"10px"}},null,8,["modelValue"]),[[f,["vip"]]])]),(0,l.Lk)("div",s,[(0,l.Lk)("div",r,[(0,l.bF)(b,{modelValue:n.jObjectSearch.search,"onUpdate:modelValue":a[7]||(a[7]=e=>n.jObjectSearch.search=e),placeholder:"账号名称",clearable:""},null,8,["modelValue"]),(0,l.bF)(h,{type:"primary",icon:"el-icon-search",onClick:c.upsearch},{default:(0,l.k6)((()=>[(0,l.eW)(" 查询")])),_:1},8,["onClick"])])])])),_:1}),(0,l.bF)(F,{class:"nopadding"},{default:(0,l.k6)((()=>[(0,l.bF)(w,{ref:"table",apiObj:n.apiObj,border:"",params:{jObjectSearch:n.jObjectSearch},stripe:"",remoteSort:"",remoteFilter:""},{default:(0,l.k6)((()=>[(0,l.bF)(m,{type:"selection",width:"50",align:"center"}),(0,l.bF)(m,{label:"序号",prop:"FSort",align:"center",width:"85",sortable:""}),(0,l.bF)(m,{label:"账号名称",prop:"FName","header-align":"center",width:"140",sortable:"","show-overflow-tooltip":""}),(0,l.bF)(m,{label:"分区名称",prop:"FAreaName",align:"center",width:"140","show-overflow-tooltip":""}),(0,l.bF)(m,{label:"到期时间",prop:"FExpirationTime",align:"center",width:"155",sortable:""}),(0,l.bF)(m,{label:"直播相关",prop:"FMileage",align:"center",width:"140"},{default:(0,l.k6)((e=>[(0,l.bF)(u,{modelValue:e.row.FMileage,"onUpdate:modelValue":a=>e.row.FMileage=a,onChange:a=>c.enableSwitch(a,e.row),disabled:c.disable(),loading:e.row.$enable,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue","onChange","disabled","loading"])])),_:1}),(0,l.bF)(m,{label:"投稿相关",prop:"FAricles",align:"center",width:"140"},{default:(0,l.k6)((e=>[(0,l.bF)(u,{modelValue:e.row.FAricles,"onUpdate:modelValue":a=>e.row.FAricles=a,onChange:a=>c.enableSwitch(a,e.row),disabled:c.disable(),loading:e.row.$enable,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue","onChange","disabled","loading"])])),_:1}),(0,l.bF)(m,{label:"其他信息",prop:"FMessage","header-align":"center",sortable:"","show-overflow-tooltip":""})])),_:1},8,["apiObj","params"])])),_:1})])),_:1})),[[j,n.updateLoading]])}var n={name:"biliProxy",components:{},data(){return{apiObj:"",area:[],updateLoading:!1,aricles:!0,mileage:!0,jObjectSearch:{search:""}}},async created(){let e=await this.$API.biliArea.getAreaList.post({jObjectSearch:{}});0==e.code&&(this.area=e.data.rows,this.area.length>0&&(this.jObjectSearch.areaId=this.area[0].Fid)),this.apiObj=this.$API.biliGroup.getCookiesList},methods:{async complete(){this.updateLoading=!0;let e=await this.$API.biliGroup.complete.post();this.updateLoading=!1,0==e.code?(this.$message.success("操作完成！"),this.upsearch()):this.$alert(e.message,"提示",{type:"error"})},matching(){this.$confirm("根据当前任务信息的完成情况，自动分配对应功能！","提示",{type:"warning"}).then((async()=>{this.updateLoading=!0;let e=await this.$API.biliGroup.matching.post();this.updateLoading=!1,0==e.code?(this.$message.success("操作完成！"),this.upsearch()):this.$alert(e.message,"提示",{type:"error"})})).catch((()=>{}))},del(){this.$confirm("确认删除全部信息吗？","提示",{type:"warning"}).then((async()=>{this.updateLoading=!0;let e=await this.$API.biliGroup.del.post();this.updateLoading=!1,0==e.code?(this.$message.success("操作完成！"),this.upsearch()):this.$alert(e.message,"提示",{type:"error"})})).catch((()=>{}))},async enableSwitch(e,a){a.$enable=!0;let t=await this.$API.biliGroup.setOption.post({jObjectParam:{id:a.Fid,mileage:a.FMileage,aricles:a.FAricles}});a.$enable=!1,0==t.code?this.$message.success("操作成功"):this.$alert(t.message,"提示",{type:"error"})},async setting(){let e=this.$refs.table.getSelectionRows();0!=e.length?this.$confirm("确认设置？","提示",{type:"warning"}).then((async()=>{let a="";for(let l=0;l<e.length;l++)a+=""==a?e[l].Fid:","+e[l].Fid;let t=await this.$API.biliGroup.setOption.post({jObjectParam:{id:a,mileage:this.mileage?1:0,aricles:this.aricles?1:0}});0==t.code?(this.$message.success("操作完成！"),this.upsearch()):this.$alert(t.message,"提示",{type:"error"})})).catch((()=>{})):this.$alert("请至少选择一条数据","提示",{type:"warning"})},upsearch(){this.$refs.table.upData({jObjectSearch:this.jObjectSearch})},disable(){let e=this.$TOOL.data.get("PERMISSIONS");if(!e.some((e=>"vip"===e)))return!0}}},c=t(6262);const d=(0,c.A)(n,[["render",o]]);var p=d}}]);