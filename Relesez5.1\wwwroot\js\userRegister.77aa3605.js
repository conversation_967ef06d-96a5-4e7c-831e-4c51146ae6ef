"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[5201],{2659:function(e,l,t){t.r(l),t.d(l,{default:function(){return y}});var s=t(641);const r=(0,s.Lk)("div",{class:"el-form-item-msg"},"登录账号将作为登录时的唯一凭证",-1),a=(0,s.Lk)("div",{class:"el-form-item-msg"},"密码必须包含数字、大小写字母、特殊符号、至少8个字符，最多18个字符",-1),o={style:{display:"flex","align-items":"center","justify-content":"space-between",width:"100%"}},d={style:{display:"flex","align-items":"center"}},i=["src"],u={key:1};function n(e,l,t,n,p,c){const m=(0,s.g2)("el-step"),f=(0,s.g2)("el-steps"),h=(0,s.g2)("el-input"),g=(0,s.g2)("el-form-item"),v=(0,s.g2)("sc-password-strength"),b=(0,s.g2)("sc-select"),k=(0,s.g2)("el-checkbox"),w=(0,s.g2)("el-form"),y=(0,s.g2)("el-button"),V=(0,s.g2)("el-result"),C=(0,s.g2)("el-dialog"),F=(0,s.g2)("common-page");return(0,s.uX)(),(0,s.Wv)(F,{title:"注册新账号"},{default:(0,s.k6)((()=>[(0,s.bF)(f,{active:p.stepActive,simple:"","finish-status":"success"},{default:(0,s.k6)((()=>[(0,s.bF)(m,{title:"基础信息"}),(0,s.bF)(m,{title:"完成注册"})])),_:1},8,["active"]),0==p.stepActive?((0,s.uX)(),(0,s.Wv)(w,{key:0,ref:"stepForm_0",model:p.form,rules:p.rules,"label-width":120},{default:(0,s.k6)((()=>[(0,s.bF)(g,{label:"登录账号",prop:"user"},{default:(0,s.k6)((()=>[(0,s.bF)(h,{modelValue:p.form.user,"onUpdate:modelValue":l[0]||(l[0]=e=>p.form.user=e),placeholder:"请输入登录账号"},null,8,["modelValue"]),r])),_:1}),(0,s.bF)(g,{label:"登录密码",prop:"password"},{default:(0,s.k6)((()=>[(0,s.bF)(h,{modelValue:p.form.password,"onUpdate:modelValue":l[1]||(l[1]=e=>p.form.password=e),type:"password","show-password":"",placeholder:"请输入登录密码"},null,8,["modelValue"]),(0,s.bF)(v,{modelValue:p.form.password,"onUpdate:modelValue":l[2]||(l[2]=e=>p.form.password=e)},null,8,["modelValue"]),a])),_:1}),(0,s.bF)(g,{label:"确认密码",prop:"password2"},{default:(0,s.k6)((()=>[(0,s.bF)(h,{modelValue:p.form.password2,"onUpdate:modelValue":l[3]||(l[3]=e=>p.form.password2=e),type:"password","show-password":"",placeholder:"请再一次输入登录密码"},null,8,["modelValue"])])),_:1}),(0,s.bF)(g,{rules:[{required:!0,message:"请选择平台名称！"}],label:"平台名称",prop:"role",required:""},{default:(0,s.k6)((()=>[(0,s.bF)(b,{multiple:"",modelValue:p.form.role,"onUpdate:modelValue":l[4]||(l[4]=e=>p.form.role=e),style:{width:"100%"},apiObj:[{label:"哔哩哔哩",value:"12"},{label:"斗鱼",value:"14",disabled:!0},{label:"快手",value:"15",disabled:!0},{label:"虎牙",value:"虎牙",disabled:!0}],placeholder:"平台分区"},null,8,["modelValue"])])),_:1}),(0,s.bF)(g,{rules:[{required:!0,message:"请输入验证码"}],label:"验证码",prop:"verifyCode",required:""},{default:(0,s.k6)((()=>[(0,s.Lk)("div",o,[(0,s.Lk)("div",null,[(0,s.bF)(h,{modelValue:p.form.verifyCode,"onUpdate:modelValue":l[5]||(l[5]=e=>p.form.verifyCode=e),clearable:"",placeholder:"验证码"},null,8,["modelValue"])]),(0,s.Lk)("div",d,[(0,s.Lk)("img",{onClick:l[6]||(l[6]=(...e)=>c.changeverifyCode&&c.changeverifyCode(...e)),style:{cursor:"pointer",height:"33px"},src:p.verifyCode,fit:"cover"},null,8,i)])])])),_:1}),(0,s.bF)(g,{label:"",prop:"agree"},{default:(0,s.k6)((()=>[(0,s.bF)(k,{modelValue:p.form.agree,"onUpdate:modelValue":l[7]||(l[7]=e=>p.form.agree=e),label:""},{default:(0,s.k6)((()=>[(0,s.eW)("已阅读并同意")])),_:1},8,["modelValue"]),(0,s.Lk)("span",{class:"link",onClick:l[8]||(l[8]=e=>p.showAgree=!0)},"《平台服务协议》")])),_:1})])),_:1},8,["model","rules"])):(0,s.Q3)("",!0),1==p.stepActive?((0,s.uX)(),(0,s.CE)("div",u,[(0,s.bF)(V,{icon:"success",title:"注册成功","sub-title":""},{extra:(0,s.k6)((()=>[(0,s.bF)(y,{type:"primary",onClick:c.goLogin},{default:(0,s.k6)((()=>[(0,s.eW)("前去登录")])),_:1},8,["onClick"])])),_:1})])):(0,s.Q3)("",!0),(0,s.bF)(w,{style:{"text-align":"center"}},{default:(0,s.k6)((()=>[0==p.stepActive?((0,s.uX)(),(0,s.Wv)(y,{key:0,type:"primary",loading:p.isSaveing,onClick:c.save},{default:(0,s.k6)((()=>[(0,s.eW)("提交")])),_:1},8,["loading","onClick"])):(0,s.Q3)("",!0)])),_:1}),(0,s.bF)(C,{modelValue:p.showAgree,"onUpdate:modelValue":l[11]||(l[11]=e=>p.showAgree=e),title:"平台服务协议",width:800,"destroy-on-close":""},{footer:(0,s.k6)((()=>[(0,s.bF)(y,{onClick:l[9]||(l[9]=e=>p.showAgree=!1)},{default:(0,s.k6)((()=>[(0,s.eW)("取消")])),_:1}),(0,s.bF)(y,{type:"primary",onClick:l[10]||(l[10]=e=>{p.showAgree=!1,p.form.agree=!0})},{default:(0,s.k6)((()=>[(0,s.eW)("我已阅读并同意")])),_:1})])),default:(0,s.k6)((()=>[(0,s.eW)(" 自己看着用吧 ")])),_:1},8,["modelValue"])])),_:1})}t(8743);var p=t(2644);const c={class:"sc-password-strength"};function m(e,l,t,r,a,o){return(0,s.uX)(),(0,s.CE)("div",c,[(0,s.Lk)("div",{class:(0,p.C4)(["sc-password-strength-bar",`sc-password-strength-level-${a.level}`])},null,2)])}var f={props:{modelValue:{type:String,default:""}},data(){return{level:0}},watch:{modelValue(){this.strength(this.modelValue)}},mounted(){this.strength(this.modelValue)},methods:{strength(e){var l=0,t=e.length>=6,s=/\d/.test(e),r=/[a-z]/.test(e),a=/[A-Z]/.test(e),o=!/(\w)\1{2}/.test(e),d=/[`~!@#$%^&*()_+<>?:"{},./;'[\]]/.test(e);return e.length<=0?(l=0,this.level=l,!1):t?(s&&(l+=1),r&&(l+=1),a&&(l+=1),o&&(l+=1),d&&(l+=1),void(this.level=l)):(l=1,this.level=l,!1)}}},h=t(6262);const g=(0,h.A)(f,[["render",m],["__scopeId","data-v-c391cecc"]]);var v=g,b=t(9499),k={components:{commonPage:b["default"],scPasswordStrength:v},data(){return{stepActive:0,showAgree:!1,verifyCode:"",isSaveing:!1,form:{user:"",password:"",password2:"",agree:!1,role:["12"]},rules:{user:[{required:!0,message:"请输入账号名"}],password:[{required:!0,message:"请输入密码"}],password2:[{required:!0,message:"请再次输入密码"},{validator:(e,l,t)=>{l!==this.form.password?t(new Error("两次输入密码不一致")):t()}}],agree:[{validator:(e,l,t)=>{l?t():t(new Error("请阅读并同意协议"))}}]}}},mounted(){this.changeverifyCode()},methods:{pre(){this.stepActive-=1},next(){const e=`stepForm_${this.stepActive}`;this.$refs[e].validate((e=>{if(!e)return!1;this.stepActive+=1}))},save(){const e=`stepForm_${this.stepActive}`;this.$refs[e].validate((async e=>{if(!e)return!1;this.isSaveing=!0;var l=await this.$API.auth.register.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?this.stepActive+=1:(this.$alert(l.message,"提示",{type:"error"}),this.changeverifyCode())}))},async changeverifyCode(){let e=await this.$API.auth.verifyCode.get();0==e.code&&(this.verifyCode=e.data)},goLogin(){this.$router.push({path:"/login"})}}};const w=(0,h.A)(k,[["render",n]]);var y=w}}]);