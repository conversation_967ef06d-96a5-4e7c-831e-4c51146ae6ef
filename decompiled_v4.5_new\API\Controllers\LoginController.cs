using System;
using System.Data;
using System.Threading.Tasks;
using API.BusService;
using API.BusService.System;
using API.Common;
using API.DataAccess.System;
using API.Models.Comm;
using API.Quartz;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json.Linq;
using Quartz;

namespace API.Controllers;

public class LoginController(ISchedulerFactory schedulerFactory) : Controller()
{
	private readonly ISchedulerFactory _schedulerFactory = schedulerFactory;

	[HttpPost]
	public async Task<Response> GetToken([FromBody] Login model, string ipAddress)
	{
		Response mRet = new Response();
		try
		{
			// 已禁用验证码检查 - 风控对抗项目修改
			// model.verifyCode = BusLogin.CheckVerifyCode(base.HttpContext, model.verifyCode, model.rememberMe);
			Response response = mRet;
			response.data = await Util.Request(base.HttpContext.Request.Path, model, base.HttpContext);
			if (mRet.data == null)
			{
				// 已禁用远程验证权限检查 - 风控对抗项目修改
				// if (!base.HttpContext.Request.Headers.ContainsKey("NetCore") && model.userCode != "admin")
				// {
				//     throw new Exception("无后台登录权限！");
				// }
				DataTable dt = SysUser.CheckLogin(model.userCode, model.userPWD, ipAddress, model.curTime, model.cookie);
				mRet.data = BusLogin.CheckLogin(dt);
			}
			JToken jToken = (JObject)mRet.data;
			string userId = Util.GetJObject(jToken["userInfo"], "userId");
			if (userId == "")
			{
				throw new Exception("账户信息不正确！");
			}
			base.HttpContext.Session.SetString("userId", userId);
			base.HttpContext.Session.SetString("userName", Util.GetJObject(jToken["userInfo"], "userName"));
			base.HttpContext.Session.SetString("rights", Util.GetJObject(jToken["userInfo"], "rights"));
			base.HttpContext.Session.SetString("organization", Util.GetJObject(jToken["userInfo"], "organization"));
			base.HttpContext.Session.SetString("address", Util.GetJObject(jToken["userInfo"], "address"));
			base.HttpContext.Session.SetString("cookie", Util.GetJObject(jToken["userInfo"], "cookie"));
			base.Response.Cookies.Append("authorization", Token.GetTokenValue(userId), new CookieOptions
			{
				Expires = DateTimeOffset.UtcNow.AddSeconds(int.Parse(AppSettings.GetVal("IdleTimeout")))
			});
			Organization organization = JObject.Parse(Util.GetJObject(jToken["userInfo"], "organization")).ToObject<Organization>() ?? throw new Exception("帐户错误，请重新登录");
			await QzUtil.Uninstall(schedulerFactory);
			await QzUtil.Install(schedulerFactory, organization, userId);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
			base.HttpContext.Session.SetString("verifyCode", "");
		}
		return mRet;
	}

	[HttpPost]
	public Response GetHelpToken([FromBody] Login model, string ipAddress)
	{
		Response response = new Response();
		try
		{
			JObject jToken = JObject.Parse(RsaEncrypt.RSADecrypt(model.userPWD));
			SqlParameter[] prams = new SqlParameter[5]
			{
				new SqlParameter("@userCode", model.userCode),
				new SqlParameter("@ip", ipAddress),
				new SqlParameter("@type", Util.GetJObject(jToken, "type")),
				new SqlParameter("@roomNo", Util.GetJObject(jToken, "roomNo")),
				new SqlParameter("@random", Util.GetJObject(jToken, "random"))
			};
			string sSql = " EXEC PHelp @userCode,@ip,@type,@roomNo,@random";
			response.data = SQLHelper.BiliLocalDB.RunSqlDt(sSql, prams);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = ex.Message;
		}
		return response;
	}

	[HttpPost]
	public Response GetDouYinToken([FromBody] Login model, string ipAddress)
	{
		Response response = new Response();
		try
		{
			JObject jToken = JObject.Parse(RsaEncrypt.RSADecrypt(model.userPWD));
			SqlParameter[] prams = new SqlParameter[5]
			{
				new SqlParameter("@userCode", model.userCode),
				new SqlParameter("@ip", ipAddress),
				new SqlParameter("@type", Util.GetJObject(jToken, "type")),
				new SqlParameter("@roomNo", Util.GetJObject(jToken, "roomNo")),
				new SqlParameter("@random", Util.GetJObject(jToken, "random"))
			};
			string sSql = " EXEC ManageDouYinDB.DBO.PLogin @userCode,@ip,@type,@roomNo,@random";
			DataSet dataSet = SQLHelper.LocalDB.RunSqlDs(sSql, prams);
			jToken = new JObject();
			for (int i = 0; i < dataSet.Tables[0].Columns.Count; i++)
			{
				jToken[dataSet.Tables[0].Columns[i].ColumnName] = Util.GetJObject(dataSet.Tables[0].Rows[0], dataSet.Tables[0].Columns[i].ColumnName);
			}
			if (dataSet.Tables.Count == 2)
			{
				jToken["detail"] = JArray.FromObject(dataSet.Tables[1]);
			}
			response.data = jToken;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = ex.Message;
		}
		return response;
	}

	[HttpGet]
	public Response GetToken2(string str)
	{
		Response response = new Response();
		try
		{
			string sSql = " EXEC Book.DBO.PGetUserInfo '" + str + "'";
			DataTable data = SQLHelper.LocalDB.RunSqlDt(sSql);
			response.data = data;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = ex.Message;
		}
		return response;
	}

	[HttpGet]
	public async Task<Response> GetVerifyCode()
	{
		Response mRet = new Response();
		try
		{
			Response response = mRet;
			response.data = await Util.Request(base.HttpContext.Request.Path, new JObject(), base.HttpContext);
			Response response2 = mRet;
			if (response2.data == null)
			{
				response2.data = BusLogin.CreateVerifyCode(base.HttpContext);
			}
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> Register([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			// 已禁用验证码检查 - 风控对抗项目修改
			// BusLogin.CheckVerifyCode(base.HttpContext, Util.GetJObject(model.jObjectParam, "verifyCode"), rememberMe: false);
			Response response = mRet;
			response.data = await Util.Request(base.HttpContext.Request.Path, model, base.HttpContext);
			if (mRet.data == null)
			{
				// 已禁用远程验证权限检查 - 风控对抗项目修改
				// if (!base.HttpContext.Request.Headers.ContainsKey("NetCore"))
				// {
				//     throw new Exception("无后台登录权限！");
				// }
				SysUser.Register(model.jObjectParam, model.curTime);
				mRet.data = "";
			}
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
			base.HttpContext.Session.SetString("verifyCode", "");
		}
		return mRet;
	}

	public new async Task<Response> SignOut()
	{
		Response mRet = new Response();
		try
		{
			await QzUtil.Uninstall(schedulerFactory);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpGet]
	public async Task<Response> Validate()
	{
		Response mRet = new Response();
		try
		{
			await QzUtil.Validate(BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Address, "bilibili");
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}
}
