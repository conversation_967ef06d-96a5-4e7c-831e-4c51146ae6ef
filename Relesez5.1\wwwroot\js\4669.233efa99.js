"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[4669],{4669:function(e,t,s){s.r(t),s.d(t,{default:function(){return f}});var r=s(641);const p=e=>((0,r.Qi)("data-v-5a089473"),e=e(),(0,r.jt)(),e),i={class:"sc-cropper"},a={class:"sc-cropper__img"},o=["src"],c={class:"sc-cropper__preview"},n=p((()=>(0,r.Lk)("h4",null,"图像预览",-1))),l={class:"sc-cropper__preview__img",ref:"preview"};function u(e,t,s,p,u,d){return(0,r.uX)(),(0,r.CE)("div",i,[(0,r.Lk)("div",a,[(0,r.Lk)("img",{src:s.src,ref:"img"},null,8,o)]),(0,r.Lk)("div",c,[n,(0,r.Lk)("div",l,null,512)])])}var d=s(5643),g=s.n(d),v={props:{src:{type:String,default:""},compress:{type:Number,default:1},aspectRatio:{type:Number,default:NaN}},data(){return{crop:null}},watch:{aspectRatio(e){this.crop.setAspectRatio(e)}},mounted(){this.init()},methods:{init(){this.crop=new(g())(this.$refs.img,{viewMode:2,dragMode:"move",responsive:!1,aspectRatio:this.aspectRatio,preview:this.$refs.preview})},setAspectRatio(e){this.crop.setAspectRatio(e)},getCropData(e,t="image/jpeg"){e(this.crop.getCroppedCanvas().toDataURL(t,this.compress))},getCropBlob(e,t="image/jpeg"){this.crop.getCroppedCanvas().toBlob((t=>{e(t)}),t,this.compress)},getCropFile(e,t="fileName.jpg",s="image/jpeg"){this.crop.getCroppedCanvas().toBlob((r=>{let p=new File([r],t,{type:s});e(p)}),s,this.compress)}}},h=s(6262);const m=(0,h.A)(v,[["render",u],["__scopeId","data-v-5a089473"]]);var f=m}}]);