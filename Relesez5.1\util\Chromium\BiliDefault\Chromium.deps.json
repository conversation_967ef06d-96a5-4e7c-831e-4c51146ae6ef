{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"Chromium/1.0.0": {"dependencies": {"CefSharp.H264.Core.x64": "109.1.110", "CefSharp.H264.Core.x86": "109.1.110", "CefSharp.WinForms.NETCore": "109.1.110", "CommandLineParser": "2.9.1", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Http": "8.0.0", "Newtonsoft.Json": "13.0.3", "chromiumembeddedframework.runtime.win-arm64": "109.1.11", "chromiumembeddedframework.runtime.win-x64": "109.1.11", "chromiumembeddedframework.runtime.win-x86": "109.1.11"}, "runtime": {"Chromium.dll": {}}}, "CefSharp.Common.NETCore/109.1.110": {"dependencies": {"chromiumembeddedframework.runtime": "109.1.11"}, "runtimeTargets": {"runtimes/win-arm64/lib/netcoreapp3.1/CefSharp.Core.Runtime.dll": {"rid": "win-arm64", "assetType": "runtime", "assemblyVersion": "***********", "fileVersion": "***********"}, "runtimes/win-arm64/lib/netcoreapp3.1/CefSharp.Core.dll": {"rid": "win-arm64", "assetType": "runtime", "assemblyVersion": "***********", "fileVersion": "***********"}, "runtimes/win-arm64/lib/netcoreapp3.1/CefSharp.dll": {"rid": "win-arm64", "assetType": "runtime", "assemblyVersion": "***********", "fileVersion": "***********"}, "runtimes/win-arm64/lib/netcoreapp3.1/Ijwhost.dll": {"rid": "win-arm64", "assetType": "runtime", "fileVersion": "3.100.3022.47609"}, "runtimes/win-x64/lib/netcoreapp3.1/CefSharp.Core.Runtime.dll": {"rid": "win-x64", "assetType": "runtime", "assemblyVersion": "***********", "fileVersion": "***********"}, "runtimes/win-x64/lib/netcoreapp3.1/CefSharp.Core.dll": {"rid": "win-x64", "assetType": "runtime", "assemblyVersion": "***********", "fileVersion": "***********"}, "runtimes/win-x64/lib/netcoreapp3.1/CefSharp.dll": {"rid": "win-x64", "assetType": "runtime", "assemblyVersion": "***********", "fileVersion": "***********"}, "runtimes/win-x64/lib/netcoreapp3.1/Ijwhost.dll": {"rid": "win-x64", "assetType": "runtime", "fileVersion": "3.100.3022.47609"}, "runtimes/win-x86/lib/netcoreapp3.1/CefSharp.Core.Runtime.dll": {"rid": "win-x86", "assetType": "runtime", "assemblyVersion": "***********", "fileVersion": "***********"}, "runtimes/win-x86/lib/netcoreapp3.1/CefSharp.Core.dll": {"rid": "win-x86", "assetType": "runtime", "assemblyVersion": "***********", "fileVersion": "***********"}, "runtimes/win-x86/lib/netcoreapp3.1/CefSharp.dll": {"rid": "win-x86", "assetType": "runtime", "assemblyVersion": "***********", "fileVersion": "***********"}, "runtimes/win-x86/lib/netcoreapp3.1/Ijwhost.dll": {"rid": "win-x86", "assetType": "runtime", "fileVersion": "3.100.3022.47609"}, "runtimes/win-arm64/native/CefSharp.BrowserSubprocess.Core.dll": {"rid": "win-arm64", "assetType": "native", "assemblyVersion": "***********", "fileVersion": "***********"}, "runtimes/win-arm64/native/CefSharp.BrowserSubprocess.dll": {"rid": "win-arm64", "assetType": "native", "assemblyVersion": "***********", "fileVersion": "***********"}, "runtimes/win-arm64/native/CefSharp.BrowserSubprocess.exe": {"rid": "win-arm64", "assetType": "native", "fileVersion": "***********"}, "runtimes/win-arm64/native/CefSharp.BrowserSubprocess.runtimeconfig.json": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/CefSharp.BrowserSubprocess.Core.dll": {"rid": "win-x64", "assetType": "native", "assemblyVersion": "***********", "fileVersion": "***********"}, "runtimes/win-x64/native/CefSharp.BrowserSubprocess.dll": {"rid": "win-x64", "assetType": "native", "assemblyVersion": "***********", "fileVersion": "***********"}, "runtimes/win-x64/native/CefSharp.BrowserSubprocess.exe": {"rid": "win-x64", "assetType": "native", "fileVersion": "***********"}, "runtimes/win-x64/native/CefSharp.BrowserSubprocess.runtimeconfig.json": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/CefSharp.BrowserSubprocess.Core.dll": {"rid": "win-x86", "assetType": "native", "assemblyVersion": "***********", "fileVersion": "***********"}, "runtimes/win-x86/native/CefSharp.BrowserSubprocess.dll": {"rid": "win-x86", "assetType": "native", "assemblyVersion": "***********", "fileVersion": "***********"}, "runtimes/win-x86/native/CefSharp.BrowserSubprocess.exe": {"rid": "win-x86", "assetType": "native", "fileVersion": "***********"}, "runtimes/win-x86/native/CefSharp.BrowserSubprocess.runtimeconfig.json": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "CefSharp.H264.Core.x64/109.1.110": {}, "CefSharp.H264.Core.x86/109.1.110": {}, "CefSharp.WinForms.NETCore/109.1.110": {"dependencies": {"CefSharp.Common.NETCore": "109.1.110"}, "runtime": {"lib/netcoreapp3.1/CefSharp.WinForms.dll": {"assemblyVersion": "***********", "fileVersion": "***********"}}}, "chromiumembeddedframework.runtime/109.1.11": {}, "chromiumembeddedframework.runtime.win-arm64/109.1.11": {"runtimeTargets": {"runtimes/win-arm64/native/chrome_100_percent.pak": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/chrome_200_percent.pak": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/chrome_elf.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "109.0.5414.87"}, "runtimes/win-arm64/native/icudtl.dat": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/libEGL.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "2.1.19909.0"}, "runtimes/win-arm64/native/libGLESv2.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "2.1.19909.0"}, "runtimes/win-arm64/native/libcef.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "109.1.11.0"}, "runtimes/win-arm64/native/resources.pak": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/snapshot_blob.bin": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/v8_context_snapshot.bin": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/vk_swiftshader.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "5.0.0.1"}, "runtimes/win-arm64/native/vk_swiftshader_icd.json": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/vulkan-1.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.0.1111.2222"}}}, "chromiumembeddedframework.runtime.win-x64/109.1.11": {"runtimeTargets": {"runtimes/win-x64/native/chrome_100_percent.pak": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/chrome_200_percent.pak": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/chrome_elf.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "109.0.5414.87"}, "runtimes/win-x64/native/d3dcompiler_47.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "10.0.20348.1"}, "runtimes/win-x64/native/icudtl.dat": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libEGL.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "2.1.19909.0"}, "runtimes/win-x64/native/libGLESv2.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "2.1.19909.0"}, "runtimes/win-x64/native/libcef.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "109.1.11.0"}, "runtimes/win-x64/native/resources.pak": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/snapshot_blob.bin": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/v8_context_snapshot.bin": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/vk_swiftshader.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "5.0.0.1"}, "runtimes/win-x64/native/vk_swiftshader_icd.json": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/vulkan-1.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.0.1111.2222"}}}, "chromiumembeddedframework.runtime.win-x86/109.1.11": {"runtimeTargets": {"runtimes/win-x86/native/chrome_100_percent.pak": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/chrome_200_percent.pak": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/chrome_elf.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "109.0.5414.87"}, "runtimes/win-x86/native/d3dcompiler_47.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "10.0.20348.1"}, "runtimes/win-x86/native/icudtl.dat": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libEGL.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "2.1.19909.0"}, "runtimes/win-x86/native/libGLESv2.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "2.1.19909.0"}, "runtimes/win-x86/native/libcef.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "109.1.11.0"}, "runtimes/win-x86/native/resources.pak": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/snapshot_blob.bin": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/v8_context_snapshot.bin": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/vk_swiftshader.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "5.0.0.1"}, "runtimes/win-x86/native/vk_swiftshader_icd.json": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/vulkan-1.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.0.1111.2222"}}}, "CommandLineParser/2.9.1": {"runtime": {"lib/netstandard2.0/CommandLine.dll": {"assemblyVersion": "2.9.1.0", "fileVersion": "2.9.1.0"}}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Diagnostics/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Http/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Primitives/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "System.Diagnostics.DiagnosticSource/8.0.0": {}}}, "libraries": {"Chromium/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CefSharp.Common.NETCore/109.1.110": {"type": "package", "serviceable": true, "sha512": "sha512-o1vDiVB1uAv2eWQEphgze8b/InIyifDJDAZ8nZdNarYT01+yOnwYxFbLYEpfmLAUUlduU0i4kXUv6tgQPykZjg==", "path": "cefsharp.common.netcore/109.1.110", "hashPath": "cefsharp.common.netcore.109.1.110.nupkg.sha512"}, "CefSharp.H264.Core.x64/109.1.110": {"type": "package", "serviceable": true, "sha512": "sha512-RlSt+olziY/U4PStNvaQtbXYQdDkE9qFxL85sW1pb91OKt8eSfL+aNZTefww5/UqVpziGwTmZBuEfl4sNbVyug==", "path": "cefsharp.h264.core.x64/109.1.110", "hashPath": "cefsharp.h264.core.x64.109.1.110.nupkg.sha512"}, "CefSharp.H264.Core.x86/109.1.110": {"type": "package", "serviceable": true, "sha512": "sha512-Vfeq4sIyi55wBZmKSSaBcsqRba1rBzI3sniKC7bWME/wEckyK3Ej8n5R+p0GYI5hxbUX9oaVC0o9caXF0WmlpA==", "path": "cefsharp.h264.core.x86/109.1.110", "hashPath": "cefsharp.h264.core.x86.109.1.110.nupkg.sha512"}, "CefSharp.WinForms.NETCore/109.1.110": {"type": "package", "serviceable": true, "sha512": "sha512-I9KVpMMmOmiC+1AFOUe6pSaOlFJE16mpL09xrzGVUY1HRATh1qsW7rRGeNd4lDN4GN6U4v+LosOrgUQmjOhyPA==", "path": "cefsharp.winforms.netcore/109.1.110", "hashPath": "cefsharp.winforms.netcore.109.1.110.nupkg.sha512"}, "chromiumembeddedframework.runtime/109.1.11": {"type": "package", "serviceable": true, "sha512": "sha512-e9h+vpj9eCp4GTqDnrDMBQZhFZc0RLNDh71hAxbm9JveOb/Bk1u0I/0Okze9iZj9nKRmNXT5zffLEFizuw/Sng==", "path": "chromiumembeddedframework.runtime/109.1.11", "hashPath": "chromiumembeddedframework.runtime.109.1.11.nupkg.sha512"}, "chromiumembeddedframework.runtime.win-arm64/109.1.11": {"type": "package", "serviceable": true, "sha512": "sha512-6Ly6piVBsMtMzjEZna6tvhyB7BeBX4Wmp1KZk50RYTXlMKn1zijD/Jb5kNjhiG9j+xylgD/MjZvhuW0HiMnlQg==", "path": "chromiumembeddedframework.runtime.win-arm64/109.1.11", "hashPath": "chromiumembeddedframework.runtime.win-arm64.109.1.11.nupkg.sha512"}, "chromiumembeddedframework.runtime.win-x64/109.1.11": {"type": "package", "serviceable": true, "sha512": "sha512-jgBt3HVJ9ZHBSClk902jdIKlDrYQtBHmwc0eEKHQ7hq6MLezxSOpFPuVq6seCt65N+Wvyob9Bcn6ZZZBuOUWAw==", "path": "chromiumembeddedframework.runtime.win-x64/109.1.11", "hashPath": "chromiumembeddedframework.runtime.win-x64.109.1.11.nupkg.sha512"}, "chromiumembeddedframework.runtime.win-x86/109.1.11": {"type": "package", "serviceable": true, "sha512": "sha512-jthtXvyhLfe4ED90zRMbVICRnflcXRJmboWi3ALupe0ShU9bbxo4TxYZVBjSMDc5uk4nOg+Dh4JWXR6J0O4kHw==", "path": "chromiumembeddedframework.runtime.win-x86/109.1.11", "hashPath": "chromiumembeddedframework.runtime.win-x86.109.1.11.nupkg.sha512"}, "CommandLineParser/2.9.1": {"type": "package", "serviceable": true, "sha512": "sha512-OE0sl1/sQ37bjVsPKKtwQlWDgqaxWgtme3xZz7JssWUzg5JpMIyHgCTY9MVMxOg48fJ1AgGT3tgdH5m/kQ5xhA==", "path": "commandlineparser/2.9.1", "hashPath": "commandlineparser.2.9.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "path": "microsoft.extensions.diagnostics/8.0.0", "hashPath": "microsoft.extensions.diagnostics.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cWz4caHwvx0emoYe7NkHPxII/KkTI8R/LC9qdqJqnKv2poTJ4e2qqPGQqvRoQ5kaSA4FU5IV3qFAuLuOhoqULQ==", "path": "microsoft.extensions.http/8.0.0", "hashPath": "microsoft.extensions.http.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "path": "system.diagnostics.diagnosticsource/8.0.0", "hashPath": "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512"}}}