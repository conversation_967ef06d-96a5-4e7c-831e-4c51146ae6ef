"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[6584,1604],{1323:function(e,a,l){l.r(a),l.d(a,{default:function(){return p}});var i=l(641);const s={class:"left-panel"},t={class:"right-panel"},o={class:"right-panel-search"},n=(0,i.Lk)("span",{class:"el-radio__label"},null,-1);function r(e,a,l,r,d,c){const u=(0,i.g2)("el-button"),h=(0,i.g2)("el-input"),g=(0,i.g2)("el-header"),b=(0,i.g2)("el-radio"),p=(0,i.g2)("el-table-column"),m=(0,i.g2)("el-switch"),f=(0,i.g2)("el-popconfirm"),k=(0,i.g2)("scTable"),v=(0,i.g2)("el-main"),y=(0,i.g2)("el-container"),F=(0,i.g2)("save-dialog"),C=(0,i.g2)("menu-dialog"),w=(0,i.g2)("user-dialog"),S=(0,i.gN)("auth");return(0,i.uX)(),(0,i.CE)(i.FK,null,[(0,i.bF)(y,null,{default:(0,i.k6)((()=>[(0,i.bF)(g,null,{default:(0,i.k6)((()=>[(0,i.Lk)("div",s,[(0,i.bo)(((0,i.uX)(),(0,i.Wv)(u,{type:"primary",icon:"el-icon-plus",onClick:c.add},{default:(0,i.k6)((()=>[(0,i.eW)(" 新增")])),_:1},8,["onClick"])),[[S,"sysRole.add"]]),(0,i.bo)(((0,i.uX)(),(0,i.Wv)(u,{type:"primary",plain:"",onClick:c.permission},{default:(0,i.k6)((()=>[(0,i.eW)("权限设置")])),_:1},8,["onClick"])),[[S,"sysRole.setMenu"]]),(0,i.bo)(((0,i.uX)(),(0,i.Wv)(u,{type:"primary",plain:"",onClick:c.account},{default:(0,i.k6)((()=>[(0,i.eW)("人员分配")])),_:1},8,["onClick"])),[[S,"sysRole.switchUser"]])]),(0,i.Lk)("div",t,[(0,i.Lk)("div",o,[(0,i.bF)(h,{modelValue:d.jObjectSearch.search,"onUpdate:modelValue":a[0]||(a[0]=e=>d.jObjectSearch.search=e),placeholder:"角色名称",clearable:""},null,8,["modelValue"]),(0,i.bF)(u,{type:"primary",icon:"el-icon-search",onClick:c.upsearch},{default:(0,i.k6)((()=>[(0,i.eW)(" 查询")])),_:1},8,["onClick"])])])])),_:1}),(0,i.bF)(v,{class:"nopadding"},{default:(0,i.k6)((()=>[(0,i.bF)(k,{ref:"table",apiObj:d.apiObj,onSelectionChange:c.selectionChange,border:"",params:{jObjectSearch:d.jObjectSearch},onCurrentChange:c.handleSelectionChange,"page-size":d.pageSize,stripe:"",remoteSort:"",remoteFilter:""},{default:(0,i.k6)((()=>[(0,i.bF)(p,{width:"55",align:"center"},{default:(0,i.k6)((e=>[(0,i.bF)(b,{modelValue:d.radioId,"onUpdate:modelValue":a[1]||(a[1]=e=>d.radioId=e),label:e.row.Fid},{default:(0,i.k6)((()=>[n])),_:2},1032,["modelValue","label"])])),_:1}),(0,i.bF)(p,{label:"#",type:"index",width:"50",align:"center"}),(0,i.bF)(p,{label:"角色",prop:"FName",align:"center",width:"250"}),(0,i.bF)(p,{label:"排序",align:"center",prop:"FSort",width:"100"}),(0,i.bF)(p,{label:"备注",prop:"FDescribe",align:"center"}),(0,i.bo)(((0,i.uX)(),(0,i.Wv)(p,{label:"是否启用",align:"center",prop:"FEnable",width:"100"},{default:(0,i.k6)((e=>[(0,i.bF)(m,{modelValue:e.row.FEnable,"onUpdate:modelValue":a=>e.row.FEnable=a,onChange:a=>c.changeSwitch(a,e.row),loading:e.row.$switch_fenable,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue","onChange","loading"])])),_:1})),[[S,"sysRole.edit"]]),(0,i.bF)(p,{label:"操作",align:"center",width:"100"},{default:(0,i.k6)((e=>[(0,i.bo)(((0,i.uX)(),(0,i.Wv)(u,{type:"primary",link:"",size:"small",onClick:a=>c.table_edit(e.row)},{default:(0,i.k6)((()=>[(0,i.eW)("编辑")])),_:2},1032,["onClick"])),[[S,"sysRole.edit"]]),(0,i.bF)(f,{title:"确定删除吗？",onConfirm:a=>c.table_del(e.row)},{reference:(0,i.k6)((()=>[(0,i.bo)(((0,i.uX)(),(0,i.Wv)(u,{type:"danger",link:"",size:"small"},{default:(0,i.k6)((()=>[(0,i.eW)("删除")])),_:1})),[[S,"sysRole.del"]])])),_:2},1032,["onConfirm"])])),_:1})])),_:1},8,["apiObj","onSelectionChange","params","onCurrentChange","page-size"])])),_:1})])),_:1}),d.dialog.save?((0,i.uX)(),(0,i.Wv)(F,{key:0,ref:"saveDialog",onSuccess:c.handleSaveSuccess,onClosed:a[2]||(a[2]=e=>d.dialog.save=!1)},null,8,["onSuccess"])):(0,i.Q3)("",!0),d.dialog.menu?((0,i.uX)(),(0,i.Wv)(C,{key:1,ref:"menuDialog",onClosed:a[3]||(a[3]=e=>d.dialog.menu=!1)},null,512)):(0,i.Q3)("",!0),d.dialog.user?((0,i.uX)(),(0,i.Wv)(w,{key:2,ref:"userDialog",onClosed:a[4]||(a[4]=e=>d.dialog.user=!1)},null,512)):(0,i.Q3)("",!0)],64)}var d=l(3653),c=l(4855),u=l(9572),h={name:"sysRole",components:{saveDialog:d["default"],menuDialog:c["default"],userDialog:u["default"]},data(){return{dialog:{save:!1,menu:!1,user:!1},apiObj:this.$API.sysRole.getSysRoleList,selection:[],jObjectSearch:{search:"",enable:""},pageSize:10,radioId:null}},methods:{add(){this.dialog.save=!0,this.$nextTick((()=>{this.$refs.saveDialog.open()}))},table_edit(e){this.dialog.save=!0,this.$nextTick((()=>{this.$refs.saveDialog.open("edit").setData(e)}))},table_show(e){this.dialog.save=!0,this.$nextTick((()=>{this.$refs.saveDialog.open("show").setData(e)}))},permission(){this.radioId>0?(this.dialog.menu=!0,this.$nextTick((()=>{this.$refs.menuDialog.open(this.radioId)}))):this.$alert("请选择一行进行权限设置","提示",{type:"error"})},account(){this.radioId>0?(this.dialog.user=!0,this.$nextTick((()=>{this.$refs.userDialog.open(this.radioId)}))):this.$alert("请选择一行进行权限设置","提示",{type:"error"})},async table_del(e){var a={jObjectParam:{id:e.Fid}},l=await this.$API.sysRole.delSysRole.post(a);0==l.code?(this.$refs.table.refresh(),this.$message.success("删除成功")):this.$alert(l.message,"提示",{type:"error"})},changeSwitch(e,a){a.FEnable="1"==a.FEnable?"0":"1",a.$switch_fenable=!0,setTimeout((async()=>{var l=await this.$API.sysRole.enableSysRole.post({jObjectParam:{Fid:a.Fid,FEnable:e}});0==l.code?(delete a.$switch_fenable,a.FEnable=e,this.$message.success("操作成功")):this.$alert(l.message,"提示",{type:"error"})}),500)},selectionChange(e){this.selection=e},upsearch(){this.$refs.table.upData({jObjectSearch:this.jObjectSearch})},handleSaveSuccess(){this.upsearch()},handleSelectionChange(e){e&&(this.radioId=e.Fid)}}},g=l(6262);const b=(0,g.A)(h,[["render",r]]);var p=b}}]);