"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[9875],{9100:function(e,a,l){l.r(a),l.d(a,{default:function(){return v}});var t=l(641);const i=e=>((0,t.Qi)("data-v-2e16d846"),e=e(),(0,t.jt)(),e),s={class:"left-panel"},r={class:"slider-demo-block",style:{"padding-left":"5px","margin-right":"5px"}},n=i((()=>(0,t.Lk)("span",{class:"demonstration"},"代理线程：",-1))),c={class:"slider-demo-block",style:{"padding-left":"5px","margin-right":"5px"}},d=i((()=>(0,t.Lk)("span",{class:"demonstration"},"每批稿件数：",-1))),o={class:"slider-demo-block",style:{"padding-left":"5px","margin-right":"5px"}},p=i((()=>(0,t.Lk)("span",{class:"demonstration"},"刷播类型：",-1))),u={class:"slider-demo-block",style:{"padding-left":"5px","margin-right":"5px"}},h=i((()=>(0,t.Lk)("span",{class:"demonstration"},"观看比例：",-1))),b=i((()=>(0,t.Lk)("br",null,null,-1))),m=i((()=>(0,t.Lk)("br",null,null,-1))),g=i((()=>(0,t.Lk)("br",null,null,-1))),F={class:"right-panel"},y={class:"right-panel-search"};function k(e,a,l,i,k,j){const f=(0,t.g2)("el-option"),O=(0,t.g2)("el-select"),S=(0,t.g2)("el-tooltip"),v=(0,t.g2)("el-slider"),P=(0,t.g2)("el-button"),w=(0,t.g2)("sc-upload-file"),L=(0,t.g2)("el-input"),x=(0,t.g2)("el-header"),C=(0,t.g2)("el-table-column"),N=(0,t.g2)("scTable"),$=(0,t.g2)("el-main"),_=(0,t.g2)("el-container"),V=(0,t.g2)("saveDialog"),A=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)(t.FK,null,[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(_,{"element-loading-text":"正在执行，请耐心等待！"},{default:(0,t.k6)((()=>[(0,t.bF)(x,null,{default:(0,t.k6)((()=>[(0,t.Lk)("div",s,[(0,t.bF)(S,{class:"box-item",effect:"dark",content:"每个代理会创建X个线程同时刷播！",placement:"top"},{default:(0,t.k6)((()=>[(0,t.Lk)("div",r,[n,(0,t.bF)(O,{modelValue:k.jObjectSearch.num,"onUpdate:modelValue":a[0]||(a[0]=e=>k.jObjectSearch.num=e),placeholder:"线程数",filterable:"",onChange:a[1]||(a[1]=()=>{this.$TOOL.data.set("articlesPlayNum",k.jObjectSearch.num)}),style:{width:"60px","padding-right":"10px"}},{default:(0,t.k6)((()=>[((0,t.uX)(),(0,t.CE)(t.FK,null,(0,t.pI)([{FName:1,Fid:1},{FName:2,Fid:2},{FName:3,Fid:3},{FName:4,Fid:4},{FName:5,Fid:5}],(e=>(0,t.bF)(f,{key:e.Fid,label:e.FName,value:e.Fid},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])])),_:1}),(0,t.bF)(S,{class:"box-item",effect:"dark",content:"分批次刷，每批刷多少稿件，动态代理10分钟请选20-25！",placement:"top"},{default:(0,t.k6)((()=>[(0,t.Lk)("div",c,[d,(0,t.bF)(O,{modelValue:k.jObjectSearch.group,"onUpdate:modelValue":a[2]||(a[2]=e=>k.jObjectSearch.group=e),placeholder:"每组多少稿件",filterable:"",onChange:a[3]||(a[3]=()=>{this.$TOOL.data.set("articlesPlayGroup",k.jObjectSearch.group)}),style:{width:"80px","padding-right":"10px"}},{default:(0,t.k6)((()=>[((0,t.uX)(),(0,t.CE)(t.FK,null,(0,t.pI)([{FName:20,Fid:20},{FName:25,Fid:25},{FName:30,Fid:30},{FName:35,Fid:35},{FName:40,Fid:40},{FName:45,Fid:45},{FName:50,Fid:50}],(e=>(0,t.bF)(f,{key:e.Fid,label:e.FName,value:e.Fid},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])])),_:1}),(0,t.bF)(S,{class:"box-item",effect:"dark",content:"集中：暂用内存小，速度慢；分散：占用内存大，每个代理占用60M内存（配置不够慎用）",placement:"top"},{default:(0,t.k6)((()=>[(0,t.Lk)("div",o,[p,(0,t.bF)(O,{modelValue:k.jObjectSearch.type,"onUpdate:modelValue":a[4]||(a[4]=e=>k.jObjectSearch.type=e),filterable:"",onChange:a[5]||(a[5]=()=>{this.$TOOL.data.set("articlesPlayType",k.jObjectSearch.type)}),style:{width:"90px","padding-right":"10px"}},{default:(0,t.k6)((()=>[((0,t.uX)(),(0,t.CE)(t.FK,null,(0,t.pI)([{FName:"集中",Fid:"集中"},{FName:"分散",Fid:"分散"}],(e=>(0,t.bF)(f,{key:e.Fid,label:e.FName,value:e.Fid},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])])),_:1}),(0,t.Lk)("div",u,[h,(0,t.bF)(v,{modelValue:k.play,"onUpdate:modelValue":a[6]||(a[6]=e=>k.play=e),min:0,onChange:j.sliderChange,style:{width:"80px"}},null,8,["modelValue","onChange"])]),(0,t.bF)(w,{multiple:!0,"show-file-list":!1,maxSize:10380902,onSuccess:j.onSuccess,params:{jObjectParam:{path:"Bili\\Other"}},style:{width:"100px",float:"left"}},{default:(0,t.k6)((()=>[(0,t.bF)(S,{class:"box-item",effect:"dark",content:"稿件管理 → 高级功能 → 导出bv号（刷播）！",placement:"top"},{default:(0,t.k6)((()=>[(0,t.bF)(P,{type:"primary",disabled:e.isSaveing,style:{"margin-left":"12px"}},{default:(0,t.k6)((()=>[(0,t.eW)("开始刷播")])),_:1},8,["disabled"])])),_:1})])),_:1},8,["onSuccess"]),(0,t.bF)(P,{type:"primary",disabled:e.isSaveing,style:{"margin-left":"12px"},onClick:j.continuePlayer},{default:(0,t.k6)((()=>[(0,t.eW)("继续刷播")])),_:1},8,["disabled","onClick"]),(0,t.bF)(P,{type:"warning",disabled:e.isSaveing,style:{"margin-left":"12px"},onClick:j.breakPlayer},{default:(0,t.k6)((()=>[(0,t.eW)("停止刷播")])),_:1},8,["disabled","onClick"]),(0,t.bF)(S,{placement:"top"},{content:(0,t.k6)((()=>[(0,t.eW)("1.刷播的分区，当天请投2个，一个刷，一个不刷 "),b,(0,t.eW)("2.刷播会用到一个CK管理排序最小的CK "),m,(0,t.eW)("3.刷播禁止时间：17.30-18.10，23.30-2.10 "),g,(0,t.eW)("4.点击按钮跳转动态IP购买(记得买流量计费的) ")])),default:(0,t.k6)((()=>[(0,t.bF)(P,{onClick:j.open,icon:"el-icon-Warning",style:{"margin-left":"10px"}},{default:(0,t.k6)((()=>[(0,t.eW)("*注意事项*")])),_:1},8,["onClick"])])),_:1})]),(0,t.Lk)("div",F,[(0,t.Lk)("div",y,[(0,t.bF)(L,{modelValue:k.jObjectSearch.search,"onUpdate:modelValue":a[7]||(a[7]=e=>k.jObjectSearch.search=e),placeholder:"账号名称 / 视频BV号",clearable:""},null,8,["modelValue"]),(0,t.bF)(P,{type:"primary",icon:"el-icon-search",onClick:j.upsearch},{default:(0,t.k6)((()=>[(0,t.eW)(" 查询")])),_:1},8,["onClick"])])])])),_:1}),(0,t.bF)($,{class:"nopadding"},{default:(0,t.k6)((()=>[(0,t.bF)(N,{ref:"table",apiObj:k.apiObj,border:"",params:{jObjectSearch:k.jObjectSearch},stripe:"",remoteSort:"",remoteFilter:""},{default:(0,t.k6)((()=>[(0,t.bF)(C,{type:"selection",width:"50"}),(0,t.bF)(C,{label:"账号名称",prop:"FCookieName","header-align":"center",width:"180","show-overflow-tooltip":""}),(0,t.bF)(C,{label:"分区名称",prop:"FAreaName",align:"center",width:"150"}),(0,t.bF)(C,{label:"刷播批次",prop:"FShare",align:"center",width:"120"}),(0,t.bF)(C,{label:"视频BV号",prop:"FBvid",align:"center",width:"150"}),(0,t.bF)(C,{label:"开始时间",prop:"FDate",align:"center",width:"200"}),(0,t.bF)(C,{label:"预计播放量",prop:"FStart",align:"center",width:"120"}),(0,t.bF)(C,{label:"播放比例",prop:"FPlayer",align:"center",width:"120"}),(0,t.bF)(C,{label:"状态",prop:"FShareSource",align:"center",width:"130"})])),_:1},8,["apiObj","params"])])),_:1})])),_:1})),[[A,k.updateLoading]]),k.dialog.save?((0,t.uX)(),(0,t.Wv)(V,{key:0,ref:"saveDialog",onSuccess:j.upsearch,onClosed:a[8]||(a[8]=e=>k.dialog.save=!1)},null,8,["onSuccess"])):(0,t.Q3)("",!0)],64)}var j=l(6448),f={name:"articlesPlay",components:{saveDialog:j["default"]},data(){return{apiObj:"",updateLoading:!1,area:[],dialog:{save:!1},play:this.$TOOL.data.get("articlesPlayPlay",30),jObjectSearch:{search:"",areaId:"",num:this.$TOOL.data.get("articlesPlayNum",1),group:this.$TOOL.data.get("articlesPlayGroup",30),type:this.$TOOL.data.get("articlesPlayType","集中")}}},async created(){this.apiObj=this.$API.biliArticlesPlay.getArticlesPlayList},methods:{async onSuccess(e){0==e.code?(this.updateLoading=!0,e=await this.$API.biliArticlesPlay.importArticlesPlay.post({jObjectParam:{src:e.data.src,player:this.play,type:this.jObjectSearch.type,num:this.jObjectSearch.num,group:this.jObjectSearch.group}}),this.updateLoading=!1,0==e.code?(this.$message.success("开始执行！"),this.upsearch()):this.$alert(e.message,"提示",{type:"error"})):this.$alert(e.message,"提示",{type:"error"})},breakPlayer(){this.$confirm("确认停止刷播?","提示",{type:"warning"}).then((async()=>{let e=await this.$API.biliArticlesPlay.breakPlayer.post();0==e.code?(this.$alert("当前批次刷完后停止！","提示",{type:"error"}),this.upsearch()):this.$alert(e.message,"提示",{type:"error"})})).catch((()=>!1))},continuePlayer(){this.$confirm("确认恢复刷播?","提示",{type:"warning"}).then((async()=>{let e=await this.$API.biliArticlesPlay.continuePlayer.post({jObjectParam:{player:this.play,type:this.jObjectSearch.type,num:this.jObjectSearch.num,group:this.jObjectSearch.group}});0==e.code?(this.$message.success("正在恢复刷播！"),this.upsearch()):this.$alert(e.message,"提示",{type:"error"})})).catch((()=>!1))},upsearch(){this.$refs.table.upData({jObjectSearch:this.jObjectSearch})},sliderChange(e){this.$TOOL.data.set("articlesPlayPlay",e)},open(){window.open("https://www.hailiangip.com/","_blank")}}},O=l(6262);const S=(0,O.A)(f,[["render",k],["__scopeId","data-v-2e16d846"]]);var v=S}}]);