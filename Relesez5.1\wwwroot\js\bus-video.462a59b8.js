"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[5829],{6711:function(e,t,a){a.r(t),a.d(t,{default:function(){return b}});var l=a(641);const i=e=>((0,l.Qi)("data-v-3bcdf009"),e=e(),(0,l.jt)(),e),o=i((()=>(0,l.Lk)("div",{class:"left-panel"},null,-1))),s={class:"right-panel"},d={class:"right-panel-search"},n={class:"item"};function c(e,t,a,i,c,r){const p=(0,l.g2)("el-input"),u=(0,l.g2)("el-button"),h=(0,l.g2)("el-header"),b=(0,l.g2)("el-image"),g=(0,l.g2)("el-aside"),k=(0,l.g2)("sc-video"),f=(0,l.g2)("el-main"),y=(0,l.g2)("el-container"),v=(0,l.gN)("loading");return(0,l.uX)(),(0,l.Wv)(y,null,{default:(0,l.k6)((()=>[(0,l.bF)(h,null,{default:(0,l.k6)((()=>[o,(0,l.Lk)("div",s,[(0,l.Lk)("div",d,[(0,l.bF)(p,{modelValue:c.jObjectSearch.search,"onUpdate:modelValue":t[0]||(t[0]=e=>c.jObjectSearch.search=e),placeholder:"模糊查询",clearable:""},null,8,["modelValue"]),(0,l.bF)(u,{disabled:c.updateLoading,type:"primary",icon:"el-icon-search",onClick:r.upsearch},{default:(0,l.k6)((()=>[(0,l.eW)(" 查询")])),_:1},8,["disabled","onClick"])])])])),_:1}),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(f,{class:"nopadding","element-loading-text":"执行中..."},{default:(0,l.k6)((()=>[c.list.length>0?((0,l.uX)(),(0,l.Wv)(y,{key:0},{default:(0,l.k6)((()=>[(0,l.bF)(g,{width:"1080px",style:{background:"#fff"}},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(c.list,(e=>((0,l.uX)(),(0,l.CE)("div",{key:e},[(0,l.Lk)("div",n,[(0,l.bF)(b,{style:{width:"200px",height:"300px",cursor:"pointer"},src:e.photo.coverUrl,fit:"cover",onClick:t=>c.playUrl=e.photo.photoH265Url},null,8,["src","onClick"])])])))),128))])),_:1}),(0,l.bF)(f,null,{default:(0,l.k6)((()=>[(0,l.bF)(k,{src:c.playUrl,options:e.options},null,8,["src","options"]),(0,l.bF)(u,{type:"primary",icon:"el-icon-plus",onClick:t[1]||(t[1]=e=>r.downLoad()),style:{"margin-top":"30px"},disabled:c.updateLoading},{default:(0,l.k6)((()=>[(0,l.eW)("下载当前视频")])),_:1},8,["disabled"])])),_:1})])),_:1})):(0,l.Q3)("",!0)])),_:1})),[[v,c.updateLoading]])])),_:1})}a(2838);var r=a(8573),p={name:"videos",components:{scVideo:r.A},data(){return{updateLoading:!1,area:[],playUrl:"",list:[],jObjectSearch:{search:""}}},async created(){},methods:{async upsearch(){if(!this.jObjectSearch.search)return void this.$alert("请输入查询内容！","提示",{type:"error"});this.playUrl="",this.updateLoading=!0;let e=await this.$API.biliArticles.getVideoList.post({jObjectSearch:this.jObjectSearch});this.updateLoading=!1,console.log(e),0==e.code?this.list=e.data:this.$alert(e.message,"提示",{type:"error"})},downLoad(){this.updateLoading=!0;var e=this.jObjectSearch.search,t=this.playUrl,a=new XMLHttpRequest;a.open("GET",t,!0),a.responseType="blob",a.onload=()=>{if(4===a.readyState&&200===a.status){let t=a.response,l=window.URL.createObjectURL(new Blob([t],{type:"video/mp4"})),i=document.createElement("a");i.download=e,i.href=l,i.style.display="none",document.body.appendChild(i),i.click(),i.remove()}},a.send(),this.updateLoading=!1}}},u=a(6262);const h=(0,u.A)(p,[["render",c],["__scopeId","data-v-3bcdf009"]]);var b=h}}]);