(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[7311],{7216:function(e,t,n){!function(t,r){e.exports=r(n(5614))}(0,(function(e){"use strict";e=e&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e;var t={VISIBILITY_CHANGE:"VISIBILITY_CHANGE"},n={SEEK:"SEEK"},r={LADER_START:"LOADER_START",LOADER_DATALOADED:"LOADER_DATALOADED",LOADER_COMPLETE:"LOADER_COMPLETE",LOADER_RESPONSE_HEADERS:"LOADER_RESPONSE_HEADERS",LOADER_ERROR:"LOADER_ERROR",LOADER_RETRY:"LOADER_RETRY",LOADER_TTFB:"LOADER_TTFB"},i={DEMUX_START:"DEMUX_START",DEMUX_COMPLETE:"DEMUX_COMPLETE",DEMUX_ERROR:"DEMUX_ERROR",METADATA_PARSED:"METADATA_PARSED",SEI_PARSED:"SEI_PARSED",VIDEO_METADATA_CHANGE:"VIDEO_METADATA_CHANGE",AUDIO_METADATA_CHANGE:"AUDIO_METADATA_CHANGE",MEDIA_INFO:"MEDIA_INFO",ISKEYFRAME:"ISKEYFRAME"},a={REMUX_METADATA:"REMUX_METADATA",REMUX_MEDIA:"REMUX_MEDIA",MEDIA_SEGMENT:"MEDIA_SEGMENT",REMUX_ERROR:"REMUX_ERROR",INIT_SEGMENT:"INIT_SEGMENT",DETECT_CHANGE_STREAM:"DETECT_CHANGE_STREAM",DETECT_CHANGE_STREAM_DISCONTINUE:"DETECT_CHANGE_STREAM_DISCONTINUE",DETECT_AUDIO_GAP:"DETECT_AUDIO_GAP",DETECT_LARGE_GAP:"DETECT_LARGE_GAP",DETECT_AUDIO_OVERLAP:"DETECT_AUDIO_OVERLAP",RANDOM_ACCESS_POINT:"RANDOM_ACCESS_POINT",DETECT_FRAG_ID_DISCONTINUE:"DETECT_FRAG_ID_DISCONTINUE"},o={SOURCE_UPDATE_END:"SOURCE_UPDATE_END",MSE_ERROR:"MSE_ERROR",MSE_WRONG_TRACK_ADD:"MSE_WRONG_TRACK_ADD"},s={RETRY_TIME_EXCEEDED:"RETRY_TIME_EXCEEDED"},l=Object.assign({},r,i,a,o,s,n,t),u=[],c=[];for(var d in l)l.hasOwnProperty(d)&&u.push(l[d]);for(var p in l)l.hasOwnProperty(p)&&c.push(l[p]);var f={ALLEVENTS:l,HLS_EVENTS:s,REMUX_EVENTS:a,DEMUX_EVENTS:i,MSE_EVENTS:o,LOADER_EVENTS:r,FlvAllowedEvents:u,HlsAllowedEvents:c,CRYPTO_EVENTS:{START_DECRYPTOO:"START_DECRYPTO",DECRYPTED:"DECRYPTED"},PLAYER_EVENTS:n,BROWSER_EVENTS:t},h=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),g=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._baseURL="",this._list={},this._ts={},this.version=0,this.sequence=-1,this.targetduration=0,this.duration=0,this.fragLength=0,this._lastget=void 0,this.end=!1,this.autoclear=t.autoclear||!1,this.logger=t.logger,this.downloadedUrls=[],this._avgSegmentDuration=4}return h(e,[{key:"push",value:function(e,t,n,r,i,a){this._ts[e]||(this._ts[e]={duration:t,downloaded:!1,downloading:!1,start:this.duration,discontinue:!!n,id:r,cc:i,isLast:a||!1},this._list[this.duration]=e,this.duration+=t,this.fragLength+=1)}},{key:"deleteFrag",value:function(e){this._ts[e]&&(this._ts[e].start>this._lastget.time&&(this._lastget={duration:this._ts[e].duration,time:this._ts[e].start,downloaded:!1,downloading:!1,url:e,id:this._ts[e].id}),delete this._list[this._ts[e].start],delete this._ts[e],this.fragLength-=1)}},{key:"_calcAvgFrgmentDuration",value:function(e){if(!e.frags)return e.targetduration;var t=e.frags.length;return Math.floor(e.duration/t/1e3)}},{key:"pushM3U8",value:function(e,t){if(!e)throw new Error("No m3u8 data received.");if(this.version=e.version,this.targetduration=e.targetduration,this._avgSegmentDuration=Math.min(this.targetduration,this._calcAvgFrgmentDuration(e)),e.encrypt&&!this.encrypt&&(this.encrypt=e.encrypt),this.end=e.end||!1,e.sequence||(e.sequence=0),!(e.sequence>this.sequence))throw new Error("Old m3u8 file received, "+e.sequence);var n=e.frags.length;this.logger&&this.logger.log("PLAYLIST","new playlist ["+e.sequence+", "+(e.sequence+n-1)+"]"),this.sequence=e.sequence;for(var r=[],i=0;i<n;i++){var a=e.frags[i];!this._ts[a.url]&&this.downloadedUrls.indexOf(a.url)<0&&(r.push(a.url),this.push(a.url,a.duration,a.discontinue,a.id,a.cc,a.isLast))}if(r.length<1)throw new Error("Can not read ts file list.");if(t)for(var o=this.getTsList(),s=0;s<o.length;s++)r.indexOf(o[s])<0&&this.deleteFrag(o[s])}},{key:"getTsList",value:function(){return Object.keys(this._ts)}},{key:"downloaded",value:function(e,t){var n=this._ts[e];n&&(n.downloaded=t)}},{key:"downloading",value:function(e,t){var n=this._ts[e];n&&(n.downloading=t)}},{key:"getTsByName",value:function(e){return this._ts[e]}},{key:"getTs",value:function(e){var t=Object.keys(this._list),n=void 0;if(void 0===e&&(e=this._lastget?this._lastget.time+this._lastget.duration:0),!(t.length<1||e>=this.duration)){t=t.sort((function(e,t){return parseFloat(e)-parseFloat(t)}));for(var r=0;r<t.length&&e>=parseInt(t[r]);r++){var i=this._list[t[r]];n={url:i,downloaded:this._ts[i].downloaded,downloading:this._ts[i].downloading,time:parseInt(t[r]),duration:parseInt(this._ts[i].duration),id:this._ts[i].id,cc:this._ts[i].cc,isLast:this._ts[i].isLast},this.autoclear&&this._lastget&&(delete this._ts[this._lastget.url],delete this._list[this._lastget.time]),this._lastget=n}return n&&this.downloadedUrls.push(n.url),n}}},{key:"getLastDownloadedTs",value:function(){for(var e=Object.keys(this._list).sort((function(e,t){return Number(e)-Number(t)})),t=void 0,n=0;n<e.length;n++){var r=this._list[e[n]],i=this._ts[r].downloaded,a=this._ts[r].downloading;if(!i)break;t={url:r,downloaded:i,downloading:a,time:parseInt(e[n]),duration:parseInt(this._ts[r].duration)}}return t}},{key:"clear",value:function(){this._baseURL="",this._list={},this._ts={},this.version=0,this.sequence=-1,this.targetduration=0,this.duration=0}},{key:"clearDownloaded",value:function(){for(var e=Object.keys(this._ts),t=0,n=e.length;t<n;t++){var r=this._ts[e[t]];r.downloaded=!1,r.downloading=!1}}},{key:"destroy",value:function(){this._baseURL="",this._list={},this._ts={},this.version=0,this.sequence=-1,this.targetduration=0,this.duration=0,this.fragLength=0,this._lastget=void 0,this.autoclear=!1}},{key:"resetSequence",value:function(){this.sequence=-1}},{key:"list",get:function(){return this._list}},{key:"baseURL",set:function(e){this.baseURL!==e&&(this.clear(),this._baseURL=e)},get:function(){return this._baseURL}},{key:"avgSegmentDuration",get:function(){return this._avgSegmentDuration}}]),e}(),y=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();function v(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var m=function e(){v(this,e),this.mimetype="",this.init=null,this.data=[],this.bufferDuration=0},x=function(){function e(){v(this,e),this.sources={}}return y(e,[{key:"getSource",value:function(e){return this.sources[e]}},{key:"createSource",value:function(e){return this.sources[e]=new m,this.sources[e]}},{key:"clear",value:function(){this.sources={}}},{key:"destroy",value:function(){this.clear()}}]),e}(),b=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),_=function(){function e(t){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),!(t instanceof ArrayBuffer))throw new Error("data is invalid");this.buffer=t,this.dataview=new DataView(t),this.dataview.position=0}return b(e,[{key:"back",value:function(e){this.position-=e}},{key:"getUint8",value:function(e){return this.dataview.getUint8(e)}},{key:"skip",value:function(t){for(var n=Math.floor(t/4),r=t%4,i=0;i<n;i++)e.readByte(this.dataview,4);r>0&&e.readByte(this.dataview,r)}},{key:"readUint8",value:function(){return e.readByte(this.dataview,1)}},{key:"readUint16",value:function(){return e.readByte(this.dataview,2)}},{key:"readUint24",value:function(){return e.readByte(this.dataview,3)}},{key:"readUint32",value:function(){return e.readByte(this.dataview,4)}},{key:"readUint64",value:function(){return e.readByte(this.dataview,8)}},{key:"readInt8",value:function(){return e.readByte(this.dataview,1,!0)}},{key:"readInt16",value:function(){return e.readByte(this.dataview,2,!0)}},{key:"readInt32",value:function(){return e.readByte(this.dataview,4,!0)}},{key:"writeUint32",value:function(e){return new Uint8Array([e>>>24&255,e>>>16&255,e>>>8&255,255&e])}},{key:"length",get:function(){return this.buffer.byteLength}},{key:"position",set:function(e){this.dataview.position=e},get:function(){return this.dataview.position}}],[{key:"readByte",value:function(e,t,n){var r=void 0;switch(t){case 1:r=n?e.getInt8(e.position):e.getUint8(e.position);break;case 2:r=n?e.getInt16(e.position):e.getUint16(e.position);break;case 3:if(n)throw new Error("not supported for readByte 3");r=e.getUint8(e.position)<<16,r|=e.getUint8(e.position+1)<<8,r|=e.getUint8(e.position+2);break;case 4:r=n?e.getInt32(e.position):e.getUint32(e.position);break;case 8:if(n)throw new Error("not supported for readBody 8");r=e.getUint32(e.position)<<32,r|=e.getUint32(e.position+4);break;default:r=""}return e.position+=t,r}}]),e}(),k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},E=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();function w(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==(void 0===t?"undefined":k(t))&&"function"!=typeof t?e:t}function T(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+(void 0===t?"undefined":k(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function S(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var C=function(){function e(){S(this,e),this.id=-1,this.sequenceNumber=0,this.samples=[],this.droppedSamples=[],this.length=0}return E(e,[{key:"reset",value:function(){this.sequenceNumber=0,this.samples=[],this.length=0}},{key:"destroy",value:function(){this.reset(),this.id=-1}}]),e}(),O=function(e){function t(){S(this,t);var e=w(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.TAG="AudioTrack",e.type="audio",e}return T(t,e),t}(C),A=function(e){function t(){S(this,t);var e=w(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.TAG="VideoTrack",e.type="video",e.dropped=0,e.sequenceNumber=0,e}return T(t,e),E(t,[{key:"reset",value:function(){this.sequenceNumber=0,this.samples=[],this.length=0,this.dropped=0}}]),t}(C),R=(function(){function e(){S(this,e),this.audioTrack=null,this.videoTrack=null}E(e,[{key:"destroy",value:function(){this.audioTrack=null,this.videoTrack=null}}])}(),function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}()),D=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.length=t||0,this.historyLen=t||0,this.array=[],this.offset=0}return R(e,[{key:"push",value:function(e){this.array.push(e),this.length+=e.byteLength,this.historyLen+=e.byteLength}},{key:"shift",value:function(e){if(this.array.length<1)return new Uint8Array(0);if(void 0===e)return this._shiftBuffer();if(this.offset+e===this.array[0].length){var t=this.array[0].slice(this.offset,this.offset+e);return this.offset=0,this.array.shift(),this.length-=e,t}if(this.offset+e<this.array[0].length){var n=this.array[0].slice(this.offset,this.offset+e);return this.offset+=e,this.length-=e,n}for(var r=new Uint8Array(e),i=0;this.array.length>0&&e>0;){if(this.offset+e<this.array[0].length){var a=this.array[0].slice(this.offset,this.offset+e);r.set(a,i),this.offset+=e,this.length-=e,e=0;break}var o=this.array[0].length-this.offset;r.set(this.array[0].slice(this.offset,this.array[0].length),i),this.array.shift(),this.offset=0,i+=o,this.length-=o,e-=o}return r}},{key:"clear",value:function(){this.array=[],this.length=0,this.offset=0}},{key:"destroy",value:function(){this.clear(),this.historyLen=0}},{key:"_shiftBuffer",value:function(){return this.length-=this.array[0].length,this.offset=0,this.array.shift()}},{key:"toInt",value:function(e,t){for(var n=0,r=this.offset+e;r<this.offset+t+e;)r<this.array[0].length?n=256*n+this.array[0][r]:this.array[1]&&(n=256*n+this.array[1][r-this.array[0].length]),r++;return n}}]),e}(),L=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();function P(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var M=function(){function e(t){P(this,e);var n={sampleRate:48e3,channelCount:2,codec:"mp4a.40.2",config:[41,401,136,0],duration:0,id:2,refSampleDuration:21,sampleRateIndex:3,timescale:1e3,type:"audio"};return t?Object.assign({},n,t):n}return L(e,[{key:"destroy",value:function(){this.init=null}}]),e}(),I=function(){function e(t){P(this,e);var n={avcc:null,sps:new Uint8Array(0),pps:new Uint8Array(0),chromaFormat:420,codec:"avc1.640020",codecHeight:720,codecWidth:1280,duration:0,frameRate:{fixed:!0,fps:25,fps_num:25e3,fps_den:1e3},id:1,level:"3.2",presentHeight:720,presentWidth:1280,profile:"High",refSampleDuration:40,parRatio:{height:1,width:1},timescale:1e3,type:"video"};return t?Object.assign({},n,t):n}return L(e,[{key:"destroy",value:function(){this.init=null,this.sps=null,this.pps=null}}]),e}(),B=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();function U(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var j=function(){function e(t){U(this,e);var n=e.getDefault();return t?Object.assign({},n,t):n}return B(e,null,[{key:"getDefault",value:function(){return{dts:-1,pts:-1,originDts:-1,data:new Uint8Array}}}]),e}(),N=function(){function e(t){U(this,e);var n=e.getDefault();return t?Object.assign({},n,t):n}return B(e,null,[{key:"getDefault",value:function(){return{dts:-1,pts:void 0,isKeyframe:!1,originDts:-1,data:new Uint8Array,nals:[]}}}]),e}(),F=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),z=function(e){for(var t in e)if(e.hasOwnProperty(t)&&null===e[t])return!1;return!0},V=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.mimeType=null,this.duration=null,this.hasVideo=!1,this.video={codec:null,width:null,height:null,profile:null,level:null,frameRate:{fixed:!0,fps:25,fps_num:25e3,fps_den:1e3},chromaFormat:null,parRatio:{width:1,height:1}},this.hasAudio=!1,this.audio={codec:null,sampleRate:null,sampleRateIndex:null,channelCount:null}}return F(e,[{key:"isComplete",value:function(){return e.isBaseInfoReady(this)&&e.isVideoReady(this)&&e.isAudioReady(this)}}],[{key:"isBaseInfoReady",value:function(e){return z(e)}},{key:"isVideoReady",value:function(e){return!e.hasVideo||z(e.video)}},{key:"isAudioReady",value:function(e){return!e.hasAudio||z(e.video)}}]),e}(),G=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),H=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);var n=Object.assign({},e.getDefault(),t);this.id=n.id,this.url=n.url,this.start=n.start,this.duration=n.duration,this.discontinue=n.discontinue,this.cc=n.cc}return G(e,null,[{key:"getDefault",value:function(){return{id:-1,url:"",start:-1,duration:-1,discontinue:!1,cc:-1}}}]),e}();function q(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function W(e,t){return e(t={exports:{}},t.exports),t.exports}var Y=W((function(e){var t=Object.prototype.hasOwnProperty,n="~";function r(){}function i(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function a(e,t,r,a,o){if("function"!=typeof r)throw new TypeError("The listener must be a function");var s=new i(r,a||e,o),l=n?n+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],s]:e._events[l].push(s):(e._events[l]=s,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new r:delete e._events[t]}function s(){this._events=new r,this._eventsCount=0}Object.create&&(r.prototype=Object.create(null),(new r).__proto__||(n=!1)),s.prototype.eventNames=function(){var e,r,i=[];if(0===this._eventsCount)return i;for(r in e=this._events)t.call(e,r)&&i.push(n?r.slice(1):r);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},s.prototype.listeners=function(e){var t=n?n+e:e,r=this._events[t];if(!r)return[];if(r.fn)return[r.fn];for(var i=0,a=r.length,o=new Array(a);i<a;i++)o[i]=r[i].fn;return o},s.prototype.listenerCount=function(e){var t=n?n+e:e,r=this._events[t];return r?r.fn?1:r.length:0},s.prototype.emit=function(e,t,r,i,a,o){var s=n?n+e:e;if(!this._events[s])return!1;var l,u,c=this._events[s],d=arguments.length;if(c.fn){switch(c.once&&this.removeListener(e,c.fn,void 0,!0),d){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,t),!0;case 3:return c.fn.call(c.context,t,r),!0;case 4:return c.fn.call(c.context,t,r,i),!0;case 5:return c.fn.call(c.context,t,r,i,a),!0;case 6:return c.fn.call(c.context,t,r,i,a,o),!0}for(u=1,l=new Array(d-1);u<d;u++)l[u-1]=arguments[u];c.fn.apply(c.context,l)}else{var p,f=c.length;for(u=0;u<f;u++)switch(c[u].once&&this.removeListener(e,c[u].fn,void 0,!0),d){case 1:c[u].fn.call(c[u].context);break;case 2:c[u].fn.call(c[u].context,t);break;case 3:c[u].fn.call(c[u].context,t,r);break;case 4:c[u].fn.call(c[u].context,t,r,i);break;default:if(!l)for(p=1,l=new Array(d-1);p<d;p++)l[p-1]=arguments[p];c[u].fn.apply(c[u].context,l)}}return!0},s.prototype.on=function(e,t,n){return a(this,e,t,n,!1)},s.prototype.once=function(e,t,n){return a(this,e,t,n,!0)},s.prototype.removeListener=function(e,t,r,i){var a=n?n+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var s=this._events[a];if(s.fn)s.fn!==t||i&&!s.once||r&&s.context!==r||o(this,a);else{for(var l=0,u=[],c=s.length;l<c;l++)(s[l].fn!==t||i&&!s[l].once||r&&s[l].context!==r)&&u.push(s[l]);u.length?this._events[a]=1===u.length?u[0]:u:o(this,a)}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&o(this,t)):(this._events=new r,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=n,s.EventEmitter=s,e.exports=s})),X="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},K=function e(t,n,r){null===t&&(t=Function.prototype);var i=Object.getOwnPropertyDescriptor(t,n);if(void 0===i){var a=Object.getPrototypeOf(t);return null===a?void 0:e(a,n,r)}if("value"in i)return i.value;var o=i.get;return void 0!==o?o.call(r):void 0},$=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();function Z(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var J="__TO__",Q=function(){function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];Z(this,e),this._emitter=new Y,this._emitter.off||(this._emitter.off=this._emitter.removeListener),this.mediaInfo=new V,this._instanceMap={},this._clsMap={},this._inited=!1,this.allowedEvents=r,this._configs=n,this._player=t,this._hooks={}}return $(e,[{key:"getInstance",value:function(e){var t=this._instanceMap[e];return t||null}},{key:"initInstance",value:function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var i=n[0],a=n[1],o=n[2],s=n[3];if(this._clsMap[e]){var l=new this._clsMap[e](i,a,o,s);return this._instanceMap[e]=l,l.init&&l.init(),l}throw new Error(e+"未在context中注册")}},{key:"init",value:function(e){if(!this._inited){for(var t in this._clsMap)this._clsMap.hasOwnProperty(t)&&!this._instanceMap[t]&&this.initInstance(t,e);this._inited=!0}}},{key:"registry",value:function(e,t){var n=this,r=this._emitter,i=this._isMessageNameValid.bind(this),a=this,o=function(t){function n(t,r,i){Z(this,n);var o=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==(void 0===t?"undefined":X(t))&&"function"!=typeof t?e:t}(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,t,r,i));return o.listeners={},o.onceListeners={},o.TAG=e,o._context=a,o}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+(void 0===t?"undefined":X(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(n,t),$(n,[{key:"on",value:function(t,n){return i(t),this.listeners[t]?this.listeners[t].push(n):this.listeners[t]=[n],r.on(""+t+J+e,n),r.on(t,n)}},{key:"before",value:function(e,t){i(e),a._hooks[e]?a._hooks[e].push(t):a._hooks[e]=[t]}},{key:"once",value:function(t,n){return i(t),this.onceListeners[t]?this.onceListeners[t].push(n):this.onceListeners[t]=[n],r.once(""+t+J+e,n),r.once(t,n)}},{key:"emit",value:function(e){i(e);for(var t=a._hooks?a._hooks[e]:null,n=arguments.length,o=Array(n>1?n-1:0),s=1;s<n;s++)o[s-1]=arguments[s];if(t)for(var l=0,u=t.length;l<u;l++){var c=t[l];c.apply(void 0,o)}return r.emit.apply(r,[e].concat(o))}},{key:"emitTo",value:function(e,t){i(t);for(var n=arguments.length,a=Array(n>2?n-2:0),o=2;o<n;o++)a[o-2]=arguments[o];return r.emit.apply(r,[""+t+J+e].concat(a))}},{key:"off",value:function(e,t){return i(e),r.off(e,t)}},{key:"removeListeners",value:function(){var t=Object.prototype.hasOwnProperty.bind(this.listeners);for(var n in this.listeners)if(t(n))for(var i=this.listeners[n]||[],a=0;a<i.length;a++){var o=i[a];r.off(n,o),r.off(""+n+J+e,o)}for(var s in this.onceListeners)if(t(s))for(var l=this.onceListeners[s]||[],u=0;u<l.length;u++){var c=l[u];r.off(s,c),r.off(""+s+J+e,c)}}},{key:"destroy",value:function(){if(this.removeListeners(),this.listeners={},delete a._instanceMap[e],K(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"destroy",this))return K(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"destroy",this).call(this);this._context=null}},{key:"_player",get:function(){return this._context?this._context._player:null},set:function(e){this._context&&(this._context._player=e)}},{key:"_pluginConfig",get:function(){return this._context?this._context._configs:null}}]),n}(t);return this._clsMap[e]=o,function(){for(var t=arguments.length,r=Array(t),i=0;i<t;i++)r[i]=arguments[i];return n.initInstance.apply(n,[e].concat(r))}}},{key:"seek",value:function(e){this._emitter.emit(f.PLAYER_EVENTS.SEEK,e)}},{key:"destroyInstances",value:function(){var e=this;Object.keys(this._instanceMap).forEach((function(t){e._instanceMap[t].destroy&&e._instanceMap[t].destroy()}))}},{key:"destroy",value:function(){this.destroyInstances(),this._emitter&&this._emitter.removeAllListeners(),this._emitter=null,this.allowedEvents=[],this._clsMap=null,this._hooks=null,this._player=null,this._configs=null}},{key:"_isMessageNameValid",value:function(e){if(!this.allowedEvents.indexOf(e)<0)throw new Error("unregistered message name: "+e)}}]),e}(),ee=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),te=f.CRYPTO_EVENTS,ne=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.inputBuffer=t.inputbuffer,this.outputBuffer=t.outputbuffer,this.key=t.key,this.iv=t.iv,this.method=t.method,this.crypto=window.crypto||window.msCrypto}return ee(e,[{key:"init",value:function(){this.on(te.START_DECRYPTO,this.decrypto.bind(this))}},{key:"decrypto",value:function(){var e=this;this.aeskey?this.decryptoData():this.crypto.subtle.importKey("raw",this.key.buffer,{name:"AES-CBC"},!1,["encrypt","decrypt"]).then((function(t){e.aeskey=t,e.decryptoData()}))}},{key:"decryptoData",value:function(){var e=this,t=this._context.getInstance(this.inputBuffer),n=this._context.getInstance(this.outputBuffer),r=t.shift();r&&this.crypto.subtle.decrypt({name:"AES-CBC",iv:this.iv.buffer},this.aeskey,r).then((function(t){n.push(new Uint8Array(t)),e.emit(te.DECRYPTED),e.decryptoData(r)}))}}]),e}(),re=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),ie=f.MSE_EVENTS,ae=function(){function e(t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),n&&(this._context=n,this.emit=n._emitter.emit.bind(n._emitter)),this.TAG="MSE",this.configs=Object.assign({},t),this.container=this.configs.container,this.format=this.configs.format,this.mediaSource=null,this.sourceBuffers={},this.preloadTime=this.configs.preloadTime||1,this.onSourceOpen=this.onSourceOpen.bind(this),this.onTimeUpdate=this.onTimeUpdate.bind(this),this.onUpdateEnd=this.onUpdateEnd.bind(this),this.onWaiting=this.onWaiting.bind(this),this.opened=!1}return re(e,[{key:"init",value:function(){this.mediaSource=new self.MediaSource,this.mediaSource.addEventListener("sourceopen",this.onSourceOpen),this._url=null,this.container.addEventListener("timeupdate",this.onTimeUpdate),this.container.addEventListener("waiting",this.onWaiting)}},{key:"resetContext",value:function(t,n){if(this._context=t,this.emit=t._emitter.emit.bind(t._emitter),!n)for(var r=0;r<Object.keys(this.sourceBuffers).length;r++){var i=this.sourceBuffers[Object.keys(this.sourceBuffers)[r]];i.updating||e.clearBuffer(i)}}},{key:"onTimeUpdate",value:function(){this.emit("TIME_UPDATE",this.container)}},{key:"onWaiting",value:function(){this.emit("WAITING",this.container)}},{key:"onSourceOpen",value:function(){this.opened=!0,this.addSourceBuffers()}},{key:"onUpdateEnd",value:function(){this.emit(ie.SOURCE_UPDATE_END),this.doAppend()}},{key:"addSourceBuffers",value:function(){if(this.mediaSource&&"open"===this.mediaSource.readyState&&this.opened){var e=this._context.getInstance("PRE_SOURCE_BUFFER"),t=this._context.getInstance("TRACKS"),n=void 0;if(e&&t){e=e.sources;for(var r=!1,i=0,a=Object.keys(e).length;i<a;i++){var o=Object.keys(e)[i];r=!1,"audio"===o?n=t.audioTrack:"video"===o&&(n=t.videoTrack),n&&null!==e[o].init&&e[o].data.length&&(r=!0)}if(r){if(Object.keys(this.sourceBuffers).length>1)return;for(var s=0,l=Object.keys(e).length;s<l;s++){var u=Object.keys(e)[s];if(!this.sourceBuffers[u]){var c=e[u],d="video"===u?"video/mp4;codecs="+c.mimetype:"audio/mp4;codecs="+c.mimetype;try{var p=this.mediaSource.addSourceBuffer(d);this.sourceBuffers[u]=p,p.addEventListener("updateend",this.onUpdateEnd)}catch(e){if(22===e.code&&Object.keys(this.sourceBuffers).length>0)return this.emit(ie.MSE_WRONG_TRACK_ADD,u);this.emit(ie.MSE_ERROR,this.TAG,new Error(e.message))}}}Object.keys(this.sourceBuffers).length===this.sourceBufferLen&&this.doAppend()}}}}},{key:"doAppend",value:function(){if(this.mediaSource&&"closed"!==this.mediaSource.readyState){this._doCleanupSourceBuffer();var e=this._context.getInstance("PRE_SOURCE_BUFFER");if(e&&!(Object.keys(this.sourceBuffers).length<this.sourceBufferLen))for(var t=0;t<Object.keys(this.sourceBuffers).length;t++){var n=Object.keys(this.sourceBuffers)[t],r=this.sourceBuffers[n];if(!r.updating){var i=e.sources[n];if(this["no"+n])i.data=[],i.init.buffer=null;else if(i&&!i.inited)try{r.appendBuffer(i.init.buffer.buffer),i.inited=!0}catch(e){}else if(i){var a=i.data.shift();if(a)try{r.appendBuffer(a.buffer.buffer)}catch(e){i.data.unshift(a)}}}}}}},{key:"endOfStream",value:function(){try{"open"===this.mediaSource.readyState&&this.mediaSource.endOfStream()}catch(e){}}},{key:"remove",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;try{for(var n=0;n<Object.keys(this.sourceBuffers).length;n++){var r=this.sourceBuffers[Object.keys(this.sourceBuffers)[n]];r.updating||e>t&&r.remove(t,e)}}catch(e){}}},{key:"_doCleanupSourceBuffer",value:function(){for(var e=this.container.currentTime,t={video:[],audio:[]},n=0;n<Object.keys(this.sourceBuffers).length;n++){for(var r=Object.keys(this.sourceBuffers)[n],i=this.sourceBuffers[r],a=i.buffered,o=!1,s=0;s<a.length;s++){var l=a.start(s),u=a.end(s);if(l<=e&&e<u+3){if(e-l>=180){o=!0;var c=e-180;t[r].push({start:l,end:c})}}else u<e&&e-l>=180&&(o=!0,t[r].push({start:l,end:u}))}o&&!i.updating&&this._doRemoveRanges(t)}}},{key:"_doRemoveRanges",value:function(e){for(var t in e)if(this.sourceBuffers[t]&&!this.sourceBuffers[t].updating)for(var n=this.sourceBuffers[t],r=e[t];r.length&&!n.updating;){var i=r.shift();try{i&&i.end>i.start&&n.remove(i.start,i.end)}catch(e){}}}},{key:"cleanBuffers",value:function(){for(var t=this,n=[],r=function(r){var i=t.sourceBuffers[Object.keys(t.sourceBuffers)[r]],a=void 0;a=i.updating?new Promise((function(t){i.addEventListener("updateend",(function n(){var r=3;setTimeout((function n(){i.updating?r>0?(setTimeout(n,200),r--):t():(e.clearBuffer(i),i.addEventListener("updateend",(function(){t()})))}),200),i.removeEventListener("updateend",n)}))})):new Promise((function(t){e.clearBuffer(i),i.addEventListener("updateend",(function(){t()}))})),n.push(a)},i=0;i<Object.keys(this.sourceBuffers).length;i++)r(i);return Promise.all(n)}},{key:"removeBuffers",value:function(){for(var t=this,n=[],r=function(r){var i=t.sourceBuffers[Object.keys(t.sourceBuffers)[r]];i.removeEventListener("updateend",t.onUpdateEnd);var a=void 0;a=i.updating?new Promise((function(t){i.addEventListener("updateend",(function n(){var r=3;setTimeout((function n(){i.updating?r>0?(setTimeout(n,200),r--):t():(e.clearBuffer(i),i.addEventListener("updateend",(function(){t()})))}),200),i.removeEventListener("updateend",n)}))})):new Promise((function(t){e.clearBuffer(i),i.addEventListener("updateend",(function(){t()}))})),n.push(a)},i=0;i<Object.keys(this.sourceBuffers).length;i++)r(i);return Promise.all(n)}},{key:"destroy",value:function(){var e=this;return this.container?(this.container.removeEventListener("timeupdate",this.onTimeUpdate),this.container.removeEventListener("waiting",this.onWaiting),this.mediaSource.removeEventListener("sourceopen",this.onSourceOpen),this.removeBuffers().then((function(){for(var t=Object.keys(e.sourceBuffers),n=0;n<t.length;n++){var r=e.sourceBuffers[t[n]];delete e.sourceBuffers[t[n]],"open"===e.mediaSource.readyState&&e.mediaSource.removeSourceBuffer(r)}e.endOfStream();try{window.URL.revokeObjectURL(e.url)}catch(e){}e.url=null,e.configs={},e.container=null,e.mediaSource=null,e.sourceBuffers={},e.preloadTime=1,e.onSourceOpen=null,e.onTimeUpdate=null,e.onUpdateEnd=null,e.onWaiting=null,e.noaudio=void 0,e.novideo=void 0}))):Promise.resolve()}},{key:"sourceBufferLen",get:function(){return this._context.mediaInfo?(!!this._context.mediaInfo.hasVideo&&!this.novideo)+(!!this._context.mediaInfo.hasAudio&&!this.noaudio):this.noaudio||this.novideo?1:2}},{key:"url",set:function(e){this._url=e},get:function(){if(!this._url)try{this._url=window.URL.createObjectURL(this.mediaSource)}catch(e){}return this._url}}],[{key:"clearBuffer",value:function(e){try{for(var t=e.buffered,n=.1,r=0,i=t.length;r<i;r++)n=t.end(r);e.remove(0,n)}catch(e){}}}]),e}(),oe=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),se=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._firstCheckpoint=0,this._lastCheckpoint=0,this._intervalBytes=0,this._lastSamplingBytes=0,this._now=Date.now}return oe(e,[{key:"reset",value:function(){this._firstCheckpoint=this._lastCheckpoint=0,this._intervalBytes=0,this._lastSamplingBytes=0}},{key:"addBytes",value:function(e){var t=this._now()-this._lastCheckpoint;0===this._firstCheckpoint?(this._firstCheckpoint=this._now(),this._lastCheckpoint=this._firstCheckpoint,this._intervalBytes+=e):t<5e3?this._intervalBytes+=e:(this._lastSamplingBytes=this._intervalBytes/(t/1e3),this._intervalBytes=e,this._lastCheckpoint=this._now())}},{key:"currentKBps",get:function(){this.addBytes(0);var e=(this._now()-this._lastCheckpoint)/1e3;return 0===e&&(e=1),this._intervalBytes/e/1024}},{key:"lastSamplingKBps",get:function(){return this.addBytes(0),0!==this._lastSamplingBytes?this._lastSamplingBytes/1024:this._now()-this._lastCheckpoint>=500?this.currentKBps:0}}]),e}(),le="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ue="function"==typeof Symbol&&"symbol"===le(Symbol.iterator)?function(e){return void 0===e?"undefined":le(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":void 0===e?"undefined":le(e)},ce=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),de=f.LOADER_EVENTS,pe=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.configs=Object.assign({},t),this.url=null,this.status=0,this.error=null,this._reader=null,this._canceled=!1,this._destroyed=!1,this.readtype=this.configs.readtype,this.buffer=this.configs.buffer||"LOADER_BUFFER",this._loaderTaskNo=0,this._ttfb=0,this._speed=new se,window.AbortController&&(this.abortControllerEnabled=!0)}return ce(e,[{key:"init",value:function(){this.on(de.LADER_START,this.load.bind(this))}},{key:"fetch",value:function(e){function t(t,n,r){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}((function(e,t,n){var r=this,i=null;this.abortControllerEnabled&&(this.abortController=new window.AbortController),Object.assign(t,{signal:this.abortController?this.abortController.signal:void 0});var a=(new Date).getTime();return Promise.race([fetch(e,t),new Promise((function(e,t){i=setTimeout((function(){t({code:999,message:"fetch timeout"}),r.abortController&&r.abortController.abort()}),n||1e4)}))]).then((function(e){i&&(clearTimeout(i),i=null);var t=(new Date).getTime();return r.emit(de.LOADER_TTFB,{start:a,end:t,elapsed:t-a}),e}))}))},{key:"internalLoad",value:function(e,t,n,r){var i=this,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,o=arguments[5];if(!this._destroyed)return this.loading=!0,this.fetch(this.url,t,o).then((function(s){if(!i._destroyed&&i.emit(de.LOADER_RESPONSE_HEADERS,i.TAG,s.headers),s.ok)return i.status=s.status,Promise.resolve().then((function(){i._onFetchResponse(s)})),Promise.resolve(s);n-- >0?i._retryTimer=setTimeout((function(){return i.emit(de.LOADER_RETRY,i.TAG,{response:s,reason:"response not ok",retryTime:r-n}),i.internalLoad(e,t,n,r,a,o)}),a):(i.loading=!1,i.emit(de.LOADER_ERROR,i.TAG,{code:s.status||21,message:s.status+" ("+s.statusText+")"}))})).catch((function(s){if(i._destroyed)i.loading=!1;else if(n-- >0)i._retryTimer=setTimeout((function(){return i.emit(de.LOADER_RETRY,i.TAG,{error:s,reason:"fetch error",retryTime:r-n}),i.internalLoad(e,t,n,r,a,o)}),a);else{if(i.loading=!1,s&&"AbortError"===s.name)return;i.emit(de.LOADER_ERROR,i.TAG,Object.assign({code:21},s))}}))}},{key:"load",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.retryCount,i=n.retryDelay,a=n.loadTimeout;r=void 0===r?3:r,this.url=e,this._canceled=!1;var o=this.getParams(t);return this.internalLoad(e,o,r,r,i,a)}},{key:"_onFetchResponse",value:function(e){var t=this,n=this,r=this._context.getInstance(this.buffer);this._loaderTaskNo++;var i=this._loaderTaskNo;if(!0===e.ok)switch(this.readtype){case 2:e.json().then((function(e){n.loading=!1,n._canceled||n._destroyed||(r?(r.push(e),n.emit(de.LOADER_COMPLETE,r)):n.emit(de.LOADER_COMPLETE,e))}));break;case 1:e.text().then((function(e){n.loading=!1,n._canceled||n._destroyed||(r?(r.push(e),n.emit(de.LOADER_COMPLETE,r)):n.emit(de.LOADER_COMPLETE,e))}));break;case 3:e.arrayBuffer().then((function(e){n.loading=!1,n._canceled||n._destroyed||(r?(r.push(new Uint8Array(e)),t._speed.addBytes(e.byteLength),n.emit(de.LOADER_COMPLETE,r)):n.emit(de.LOADER_COMPLETE,e))})).catch((function(){}));break;case 0:default:return this._onReader(e.body.getReader(),i)}}},{key:"_onReader",value:function(e,t){var n=this,r=this._context.getInstance(this.buffer);if(!r&&this._reader||this._destroyed)try{this._reader.cancel()}catch(e){}this._reader=e,!1!==this.loading&&(this._noDataTimer=setTimeout((function(){!1!==n.loading&&n.emit(de.LOADER_COMPLETE)}),3e3),this._reader&&this._reader.read().then((function(i){if(clearTimeout(n._noDataTimer),!n._canceled&&!n._destroyed)return i.done?(n.loading=!1,n.status=0,void Promise.resolve().then((function(){n.emit(de.LOADER_COMPLETE,r)}))):(r.push(i.value),n._speed.addBytes(i.value.byteLength),Promise.resolve().then((function(){n.emit(de.LOADER_DATALOADED,r)})),n._onReader(e,t));if(n._reader)try{n._reader.cancel()}catch(e){}})).catch((function(e){clearTimeout(n._noDataTimer),n.loading=!1,e&&"AbortError"===e.name||n.emit(de.LOADER_ERROR,n.TAG,e)})))}},{key:"getParams",value:function(e){var t=Object.assign({},e),n=new Headers,r={method:"GET",headers:n,mode:"cors",cache:"default"};if("object"===ue(this.configs.headers)){var i=this.configs.headers;for(var a in i)i.hasOwnProperty(a)&&n.append(a,i[a])}if("object"===ue(t.headers)){var o=t.headers;for(var s in o)o.hasOwnProperty(s)&&n.append(s,o[s])}return!1===t.cors&&(r.mode="same-origin"),t.withCredentials&&(r.credentials="include"),r}},{key:"cancel",value:function(){if(this._reader){try{this._reader.cancel()}catch(e){}this._reader=null,this.loading=!1}clearTimeout(this._noDataTimer),this._canceled=!0,this.abortController&&this.abortController.abort()}},{key:"destroy",value:function(){this._destroyed=!0,clearTimeout(this._retryTimer),clearTimeout(this._noDataTimer),this.cancel(),this._speed.reset()}},{key:"currentSpeed",get:function(){return this._speed.lastSamplingKBps}}],[{key:"isSupported",value:function(){return!!window.fetch}},{key:"type",get:function(){return"loader"}}]),e}(),fe=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),he=new(function(){function e(){var t=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);try{var n=/xgd=(\d)/.exec(document.cookie);this._status=!!n,this._level=n&&n[1]}catch(e){this._status=!1}["group","groupEnd","log","warn","error"].forEach((function(e){t[e]=function(n,r,i,a,o,s,l,u,c,d){var p;if(t._status){var f=n,h=[r,i,a,o,s,l,u,c,d].filter((function(e){return void 0!==e}));(p=console)[e].apply(p,["["+f+"]:"].concat(function(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}(h)))}}}))}return fe(e,[{key:"enable",get:function(){return this._status}},{key:"long",get:function(){return"2"===this._level}}]),e}()),ge=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),ye=f.LOADER_EVENTS,ve=f.REMUX_EVENTS,me=f.DEMUX_EVENTS,xe=f.HLS_EVENTS,be=f.CRYTO_EVENTS,_e=f.MSE_EVENTS,ke=/#EXT-X-STREAM-INF:([^\n\r]*)[\r\n]+([^\r\n]+)/g,Ee=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.configs=Object.assign({},t),this.url="",this.sequence=0,this._playlist=null,this.retrytimes=this.configs.retrytimes||3,this.preloadTime=this.configs.preloadTime,this.container=this.configs.container,this._m3u8lasttime=0,this._timmer=setInterval(this._checkStatus.bind(this),300),this._lastCheck=0,this.m3u8Text=null}return ge(e,[{key:"init",value:function(){var e=this._player.hlsOps,t=e.XgBuffer,n=e.Tracks,r=e.Playlist,i=e.RemuxedBufferManager,a=e.Compatibility,o=e.FetchLoader,s=e.TsDemuxer,l=e.Mp4Remuxer,u=e.Mse;this._context.registry("M3U8_BUFFER",t),this._context.registry("TS_BUFFER",t),this._context.registry("TRACKS",n),this._playlist=this._context.registry("PLAYLIST",r)({autoclear:!0}),this._context.registry("PRE_SOURCE_BUFFER",i),this._context.registry("COMPATIBILITY",a),this._m3u8loader=this._context.registry("M3U8_LOADER",o)({buffer:"M3U8_BUFFER",readtype:1}),this._tsloader=this._context.registry("TS_LOADER",o)({buffer:"TS_BUFFER",readtype:3}),this._context.registry("TS_DEMUXER",s)({inputbuffer:"TS_BUFFER"}),this._context.registry("MP4_REMUXER",l),this.mse=this._context.registry("MSE",u)({container:this.container}),this.initEvents()}},{key:"initEvents",value:function(){this.on(ye.LOADER_COMPLETE,this._onLoadComplete.bind(this)),this.on(ye.LOADER_RETRY,this._handleFetchRetry.bind(this)),this.on(ve.INIT_SEGMENT,this.mse.addSourceBuffers.bind(this.mse)),this.on(ve.MEDIA_SEGMENT,this.mse.doAppend.bind(this.mse)),this.on(me.METADATA_PARSED,this._onMetadataParsed.bind(this)),this.on(me.SEI_PARSED,this._handleSEIParsed.bind(this)),this.on(me.DEMUX_COMPLETE,this._onDemuxComplete.bind(this)),this.on(ye.LOADER_ERROR,this._onLoadError.bind(this)),this.on(me.DEMUX_ERROR,this._onDemuxError.bind(this)),this.on(ve.REMUX_ERROR,this._onRemuxError.bind(this)),this.on(_e.MSE_ERROR,this._handleMseError.bind(this))}},{key:"_onError",value:function(e,t,n,r){var i={code:n.code,errorType:e,errorDetails:"["+t+"]: "+(n?n.message:""),errorFatal:r};this._player.emit("HLS_ERROR",i)}},{key:"_onDemuxComplete",value:function(){this.emit(ve.REMUX_MEDIA,"ts")}},{key:"_onMetadataParsed",value:function(e){"video"===e&&(this._context.mediaInfo.hasVideo=!0),"audio"===e&&(this._context.mediaInfo.hasAudio=!0),this.emit(ve.REMUX_METADATA,e)}},{key:"_onLoadError",value:function(e,t){!this._tsloader.loading&&!this._m3u8loader.loading&&this.retrytimes>=1?(this.retrytimes--,this._onError(ye.LOADER_ERROR,e,t,!1)):this.retrytimes<=1&&(this._player.emit("error",{code:t.code,errorType:"network",ex:"["+e+"]: "+t.message,errd:{}}),this._onError(ye.LOADER_ERROR,e,t,!0),this.emit(xe.RETRY_TIME_EXCEEDED),this.mse.endOfStream())}},{key:"_onDemuxError",value:function(e,t,n){void 0===n&&(n=!0),this._player.emit("error",{code:t.code,errorType:"network",ex:"["+e+"]: "+(t?t.message:""),errd:{}}),this._onError(ye.LOADER_ERROR,e,t,n)}},{key:"_onRemuxError",value:function(e,t,n){void 0===n&&(n=!0),this._player.emit("error",{errorType:"parse",ex:"["+e+"]: "+t.message,errd:{}}),this._onError(ve.REMUX_ERROR,e,t,n)}},{key:"_handleMseError",value:function(e,t,n){void 0===n&&(n=!1),this._player.emit("error",{errorType:"format",ex:"["+e+"]: "+t.message,errd:{}}),this._onError(_e.MSE_ERROR,e,t,n)}},{key:"_handleSEIParsed",value:function(e){this._player.emit("SEI_PARSED",e)}},{key:"_onLoadComplete",value:function(e){if("M3U8_BUFFER"===e.TAG){var t=void 0;try{this.m3u8Text=e.shift();var n=ke.exec(this.m3u8Text);n&&n[2]?this.load(n[2]):t=this._player.hlsOps.M3U8Parser.parse(this.m3u8Text,this.url)}catch(e){this._onError("M3U8_PARSER_ERROR","M3U8_PARSER",e,!1)}if(!t)return void(this.retrytimes>0?(this.retrytimes--,this._preload()):(this.emit(xe.RETRY_TIME_EXCEEDED),this.mse.endOfStream()));try{this._playlist.pushM3U8(t,!0)}catch(e){this._onError("M3U8_PARSER_ERROR","PLAYLIST",e,!1)}if(this._playlist.encrypt&&this._playlist.encrypt.uri&&!this._playlist.encrypt.key){var r=this._player.hlsOps.XgBuffer;this._context.registry("DECRYPT_BUFFER",r)(),this._context.registry("KEY_BUFFER",r)(),this._tsloader.buffer="DECRYPT_BUFFER",this._keyLoader=this._context.registry("KEY_LOADER",pe)({buffer:"KEY_BUFFER",readtype:3});var i=this._player.config.retry||{},a=i.count,o=i.delay;this.emitTo("KEY_LOADER",ye.LADER_START,this._playlist.encrypt.uri,{},a,o)}else this._m3u8Loaded(t)}else"TS_BUFFER"===e.TAG?(this.retrytimes=this.configs.retrytimes||3,this._playlist.downloaded(this._tsloader.url,!0),this.emit(me.DEMUX_START)):"DECRYPT_BUFFER"===e.TAG?(this.retrytimes=this.configs.retrytimes||3,this._playlist.downloaded(this._tsloader.url,!0),this.emitTo("CRYPTO",be.START_DECRYPT)):"KEY_BUFFER"===e.TAG&&(this.retrytimes=this.configs.retrytimes||3,this._playlist.encrypt.key=e.shift(),this._crypto=this._context.registry("CRYPTO",ne)({key:this._playlist.encrypt.key,iv:this._playlist.encrypt.ivb,method:this._playlist.encrypt.method,inputbuffer:"DECRYPT_BUFFER",outputbuffer:"TS_BUFFER"}),this._crypto.on(be.DECRYPTED,this._onDcripted.bind(this)))}},{key:"_handleFetchRetry",value:function(e,t){this._player.emit("retry",Object.assign({tag:e},t))}},{key:"_onDcripted",value:function(){this.emit(me.DEMUX_START)}},{key:"_m3u8Loaded",value:function(e){this.preloadTime||(this.preloadTime=this._playlist.targetduration?this._playlist.targetduration:5),this._playlist.fragLength>0&&this._playlist.sequence<e.sequence?this.retrytimes=this.configs.retrytimes||3:this.retrytimes>0?(this.retrytimes--,this._preload()):(this.emit(xe.RETRY_TIME_EXCEEDED),this.mse.endOfStream())}},{key:"_checkStatus",value:function(){if(!(this.retrytimes<1&&Date.now()-this._lastCheck<4e3))if(this.retrytimes<1&&clearInterval(this._timmer),this._lastCheck=(new Date).getTime(),this.container.buffered.length<1)this._preload();else{var e=this.container.currentTime,t=this.container.buffered.start(this.container.buffered.length-1);this.container.readyState<=2&&(e<t?(this.container.currentTime=t,e=t):this._preload());var n=this.container.buffered.end(this.container.buffered.length-1);e<n-2*this.preloadTime&&(this.container.currentTime=n-this.preloadTime),e>n-this.preloadTime&&this._preload()}}},{key:"_preload",value:function(){if(!this._tsloader.loading&&!this._m3u8loader.loading){var e=this._playlist.getTs(),t=this._player.config.retry||{},n=t.count,r=t.delay;if(!e||e.downloaded||e.downloading){var i=this.preloadTime?this.preloadTime:0,a=(new Date).getTime();(!e||e.downloaded)&&(a-this._m3u8lasttime)/1e3>i&&(this._m3u8lasttime=a,this.emitTo("M3U8_LOADER",ye.LADER_START,this.url,{},n,r))}else this._playlist.downloading(e.url,!0),this.emitTo("TS_LOADER",ye.LADER_START,e.url,{},n,r)}}},{key:"load",value:function(e){this.url=e,this._preload()}},{key:"destroy",value:function(){clearInterval(this._timmer),this.off(ye.LOADER_COMPLETE,this._onLoadComplete),this.off(ve.INIT_SEGMENT,this.mse.addSourceBuffers),this.off(ve.MEDIA_SEGMENT,this.mse.doAppend),this.off(me.METADATA_PARSED,this._onMetadataParsed),this.off(me.DEMUX_COMPLETE,this._onDemuxComplete),this.mse=null,this.m3u8Text=null}}]),e}(),we=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),Te=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return we(e,null,[{key:"getSilentFrame",value:function(e,t){if("mp4a.40.2"===e){if(1===t)return new Uint8Array([0,200,0,128,35,128]);if(2===t)return new Uint8Array([33,0,73,144,2,25,0,35,128]);if(3===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]);if(4===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]);if(5===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]);if(6===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224])}else{if(1===t)return new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(2===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(3===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94])}return null}}]),e}(),Se=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),Ce=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return Se(e,null,[{key:"isHeader",value:function(t,n){return!!(n+1<t.length&&e.isHeaderPattern(t,n))}},{key:"getFrameDuration",value:function(e){return 9216e4/e}},{key:"isHeaderPattern",value:function(e,t){return 255===e[t]&&240==(246&e[t+1])}},{key:"getHeaderLength",value:function(e,t){return 1&e[t+1]?7:9}},{key:"getFullFrameLength",value:function(e,t){return(3&e[t+3])<<11|e[t+4]<<3|(224&e[t+5])>>>5}},{key:"parseFrameHeader",value:function(t,n,r,i,a){var o,s=void 0,l=t.length;if(o=e.getHeaderLength(t,n),s=e.getFullFrameLength(t,n),(s-=o)>0&&n+o+s<=l)return{headerLength:o,frameLength:s,stamp:r+i*a}}},{key:"appendFrame",value:function(t,n,r,i,a){var o=e.getFrameDuration(t),s=e.parseFrameHeader(n,r,i,a,o);if(s){var l=s.stamp,u=s.headerLength,c=s.frameLength;return{sample:{unit:n.subarray(r+u,r+u+c),pts:l,dts:l},length:c+u}}}}]),e}(),Oe=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),Ae=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.TAG="Golomb",this._buffer=t,this._bufferIndex=0,this._totalBytes=t.byteLength,this._totalBits=8*t.byteLength,this._currentWord=0,this._currentWordBitsLeft=0}return Oe(e,[{key:"destroy",value:function(){this._buffer=null}},{key:"_fillCurrentWord",value:function(){var e=this._totalBytes-this._bufferIndex,t=Math.min(4,e),n=new Uint8Array(4);n.set(this._buffer.subarray(this._bufferIndex,this._bufferIndex+t)),this._currentWord=new DataView(n.buffer).getUint32(0),this._bufferIndex+=t,this._currentWordBitsLeft=8*t}},{key:"readBits",value:function(e){var t=Math.min(this._currentWordBitsLeft,e),n=this._currentWord>>>32-t;if(e>32)throw new Error("Cannot read more than 32 bits at a time");return this._currentWordBitsLeft-=t,this._currentWordBitsLeft>0?this._currentWord<<=t:this._totalBytes-this._bufferIndex>0&&this._fillCurrentWord(),(t=e-t)>0&&this._currentWordBitsLeft?n<<t|this.readBits(t):n}},{key:"readBool",value:function(){return 1===this.readBits(1)}},{key:"readByte",value:function(){return this.readBits(8)}},{key:"_skipLeadingZero",value:function(){var e=void 0;for(e=0;e<this._currentWordBitsLeft;e++)if(0!=(this._currentWord&2147483648>>>e))return this._currentWord<<=e,this._currentWordBitsLeft-=e,e;return this._fillCurrentWord(),e+this._skipLeadingZero()}},{key:"readUEG",value:function(){var e=this._skipLeadingZero();return this.readBits(e+1)-1}},{key:"readSEG",value:function(){var e=this.readUEG();return 1&e?e+1>>>1:-1*(e>>>1)}},{key:"readSliceType",value:function(){return this.readByte(),this.readUEG(),this.readUEG()}}]),e}(),Re=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),De=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return Re(e,null,[{key:"_ebsp2rbsp",value:function(e){for(var t=e,n=t.byteLength,r=new Uint8Array(n),i=0,a=0;a<n;a++)a>=2&&3===t[a]&&0===t[a-1]&&0===t[a-2]||(r[i]=t[a],i++);return new Uint8Array(r.buffer,0,i)}},{key:"parseSPS",value:function(t){var n=e._ebsp2rbsp(t),r=new Ae(n);r.readByte();var i=r.readByte();r.readByte();var a=r.readByte();r.readUEG();var o=e.getProfileString(i),s=e.getLevelString(a),l=1,u=420,c=8;if((100===i||110===i||122===i||244===i||44===i||83===i||86===i||118===i||128===i||138===i||144===i)&&(3===(l=r.readUEG())&&r.readBits(1),l<=3&&(u=[0,420,422,444][l]),c=r.readUEG()+8,r.readUEG(),r.readBits(1),r.readBool()))for(var d=3!==l?8:12,p=0;p<d;p++)r.readBool()&&(p<6?e._skipScalingList(r,16):e._skipScalingList(r,64));r.readUEG();var f=r.readUEG();if(0===f)r.readUEG();else if(1===f){r.readBits(1),r.readSEG(),r.readSEG();for(var h=r.readUEG(),g=0;g<h;g++)r.readSEG()}r.readUEG(),r.readBits(1);var y=r.readUEG(),v=r.readUEG(),m=r.readBits(1);0===m&&r.readBits(1),r.readBits(1);var x=0,b=0,_=0,k=0;r.readBool()&&(x=r.readUEG(),b=r.readUEG(),_=r.readUEG(),k=r.readUEG());var E=1,w=1,T=0,S=!0,C=0,O=0;if(r.readBool()){if(r.readBool()){var A=r.readByte();A>0&&A<16?(E=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2][A-1],w=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1][A-1]):255===A&&(E=r.readByte()<<8|r.readByte(),w=r.readByte()<<8|r.readByte())}if(r.readBool()&&r.readBool(),r.readBool()&&(r.readBits(4),r.readBool()&&r.readBits(24)),r.readBool()&&(r.readUEG(),r.readUEG()),r.readBool()){var R=r.readBits(32),D=r.readBits(32);S=r.readBool(),T=(C=D)/(O=2*R)}}var L=1;1===E&&1===w||(L=E/w);var P=0,M=0;0===l?(P=1,M=2-m):(P=3===l?1:2,M=(1===l?2:1)*(2-m));var I=16*(y+1),B=16*(v+1)*(2-m);I-=(x+b)*P,B-=(_+k)*M;var U=Math.ceil(I*L);return r.destroy(),r=null,{profile_string:o,level_string:s,bit_depth:c,chroma_format:u,chroma_format_string:e.getChromaFormatString(u),frame_rate:{fixed:S,fps:T,fps_den:O,fps_num:C},par_ratio:{width:E,height:w},codec_size:{width:I,height:B},present_size:{width:U,height:B}}}},{key:"_skipScalingList",value:function(e,t){for(var n=8,r=8,i=0;i<t;i++)0!==r&&(r=(n+e.readSEG()+256)%256),n=0===r?n:r}},{key:"getProfileString",value:function(e){switch(e){case 66:return"Baseline";case 77:return"Main";case 88:return"Extended";case 100:return"High";case 110:return"High10";case 122:return"High422";case 244:return"High444";default:return"Unknown"}}},{key:"getLevelString",value:function(e){return(e/10).toFixed(1)}},{key:"getChromaFormatString",value:function(e){switch(e){case 420:return"4:2:0";case 422:return"4:2:2";case 444:return"4:4:4";default:return"Unknown"}}},{key:"toVideoMeta",value:function(e){var t={};e&&e.codec_size&&(t.codecWidth=e.codec_size.width,t.codecHeight=e.codec_size.height,t.presentWidth=e.present_size.width,t.presentHeight=e.present_size.height),t.profile=e.profile_string,t.level=e.level_string,t.bitDepth=e.bit_depth,t.chromaFormat=e.chroma_format,t.parRatio={width:e.par_ratio.width,height:e.par_ratio.height},t.frameRate=e.frame_rate;var n=t.frameRate.fps_den,r=t.frameRate.fps_num;return t.refSampleDuration=Math.floor(t.timescale*(n/r)),t}}]),e}(),Le=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),Pe=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return Le(e,null,[{key:"EBSP2RBSP",value:function(e){return e.filter((function(t,n){return n<2||!(0===e[n-2]&&0===e[n-1]&&3===t)}))}},{key:"EBSP2SODB",value:function(e){var t=e[e.byteLength-1];return t&&128===t?e.slice(0,e.byteLength-1):e}}]),e}(),Me=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),Ie=function(e){for(var t="",n=0;n<e.byteLength;n++)t+=String.fromCharCode(e[n]);return t},Be=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return Me(e,null,[{key:"_resolveNalu",value:function(e){return e.length>=1?Pe.EBSP2SODB(Pe.EBSP2RBSP(e.slice(1))):null}},{key:"parse",value:function(t){var n=e._resolveNalu(t),r=e.switchPayloadType(n),i=r.payloadType,a=r.offset,o=n.slice(a);switch(i){case 5:return e.user_data_unregistered(o);default:return{code:i,content:o}}}},{key:"switchPayloadType",value:function(e){for(var t=new DataView(e.buffer),n=0,r=0;255===t.getUint8(r);)r++,n+=255;return{payloadType:n+=t.getUint8(r++),offset:r}}},{key:"getPayloadLength",value:function(e){for(var t=new DataView(e.buffer),n=0,r=0;255===t.getUint8(r);)r++,n+=255;return{payloadLength:n+=t.getUint8(r++),offset:r}}},{key:"user_data_unregistered",value:function(t){var n=e.getPayloadLength(t),r=n.payloadLength,i=n.offset;if(r<16)return{uuid:"",content:null};var a=t.slice(i);return{code:5,uuid:Ie(a.slice(0,16)),content:Ie(a.slice(16,r))}}}]),e}(),Ue=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),je=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return Ue(e,null,[{key:"getNalunits",value:function(t){if(t.length-t.position<4)return[];var n=t.dataview,r=t.position;return 1===n.getInt32(r)||0===n.getInt16(r)&&1===n.getInt8(r+2)?e.getAnnexbNals(t):e.getAvccNals(t)}},{key:"getAnnexbNals",value:function(t){for(var n=[],r=e.getHeaderPositionAnnexB(t),i=r.pos,a=i;i<t.length-4;){var o=t.buffer.slice(i,i+r.headerLength);r.pos===t.position&&t.skip(r.headerLength),a=(r=e.getHeaderPositionAnnexB(t)).pos;var s={header:o,body:new Uint8Array(t.buffer.slice(i+o.byteLength,a))};e.analyseNal(s),s.type<=9&&0!==s.type&&n.push(s),t.skip(a-t.position),i=a}return n}},{key:"getAvccNals",value:function(t){for(var n=[];t.position<t.length-4;){var r=t.dataview.getInt32(t.dataview.position);if(!(t.length-t.position>=r))break;var i=t.buffer.slice(t.position,t.position+4);t.skip(4);var a=new Uint8Array(t.buffer.slice(t.position,t.position+r));t.skip(r);var o={header:i,body:a};e.analyseNal(o),o.type<=9&&0!==o.type&&n.push(o)}return n}},{key:"analyseNal",value:function(e){var t=31&e.body[0];switch(e.type=t,t){case 1:e.ndr=!0;break;case 5:e.idr=!0;break;case 6:try{e.sei=Be.parse(e.body)}catch(e){}break;case 7:e.sps=De.parseSPS(e.body);break;case 8:e.pps=!0}}},{key:"getHeaderPositionAnnexB",value:function(e){for(var t=e.position,n=0,r=e.length;3!==n&&4!==n&&t<r-4;)0===e.dataview.getInt16(t)?1===e.dataview.getInt16(t+2)?n=4:1===e.dataview.getInt8(t+2)?n=3:t++:t++;return t===r-4&&(0===e.dataview.getInt16(t)?1===e.dataview.getInt16(t+2)?n=4:t=r:(t++,0===e.dataview.getInt16(t)&&1===e.dataview.getInt8(t)?n=3:t=r)),{pos:t,headerLength:n}}},{key:"getAvcc",value:function(e,t){var n=new Uint8Array(e.byteLength+t.byteLength+11);n[0]=1,n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=255,n[5]=225;var r=6;return n.set(new Uint8Array([e.byteLength>>>8&255,255&e.byteLength]),r),r+=2,n.set(e,r),n[r+=e.byteLength]=1,r++,n.set(new Uint8Array([t.byteLength>>>8&255,255&t.byteLength]),r),r+=2,n.set(t,r),n}}]),e}(),Ne=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),Fe=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.TAG="Golomb",this._buffer=t,this._bufferIndex=0,this._totalBytes=t.byteLength,this._totalBits=8*t.byteLength,this._currentWord=0,this._currentWordBitsLeft=0}return Ne(e,[{key:"destroy",value:function(){this._buffer=null}},{key:"_fillCurrentWord",value:function(){var e=this._totalBytes-this._bufferIndex,t=Math.min(4,e),n=new Uint8Array(4);n.set(this._buffer.subarray(this._bufferIndex,this._bufferIndex+t)),this._currentWord=new DataView(n.buffer).getUint32(0),this._bufferIndex+=t,this._currentWordBitsLeft=8*t}},{key:"readBits",value:function(e){var t=Math.min(this._currentWordBitsLeft,e),n=this._currentWord>>>32-t;if(e>32)throw new Error("Cannot read more than 32 bits at a time");return this._currentWordBitsLeft-=t,this._currentWordBitsLeft>0?this._currentWord<<=t:this._totalBytes-this._bufferIndex>0&&this._fillCurrentWord(),(t=e-t)>0&&this._currentWordBitsLeft?n<<t|this.readBits(t):n}},{key:"readBool",value:function(){return 1===this.readBits(1)}},{key:"readByte",value:function(){return this.readBits(8)}},{key:"_skipLeadingZero",value:function(){var e=void 0;for(e=0;e<this._currentWordBitsLeft;e++)if(0!=(this._currentWord&2147483648>>>e))return this._currentWord<<=e,this._currentWordBitsLeft-=e,e;return this._fillCurrentWord(),e+this._skipLeadingZero()}},{key:"readUEG",value:function(){var e=this._skipLeadingZero();return this.readBits(e+1)-1}},{key:"readSEG",value:function(){var e=this.readUEG();return 1&e?e+1>>>1:-1*(e>>>1)}},{key:"readSliceType",value:function(){return this.readByte(),this.readUEG(),this.readUEG()}}]),e}(),ze=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),Ve=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return ze(e,null,[{key:"_ebsp2rbsp",value:function(e){for(var t=e,n=t.byteLength,r=new Uint8Array(n),i=0,a=0;a<n;a++)a>=2&&3===t[a]&&0===t[a-1]&&0===t[a-2]||(r[i]=t[a],i++);return new Uint8Array(r.buffer,0,i)}},{key:"parseSPS",value:function(t){var n,r,i,a,o,s,l=e._ebsp2rbsp(t),u=new Fe(l),c=0,d=0,p=0,f=0,h=0,g=0,y=0,v=0,m=0;return u.readByte(),u.readByte(),u.readBits(4),n=u.readBits(3),u.readBits(1),s=e._readProfileTierLevel(u,n),u.readUEG(),3===(r=u.readUEG())&&(c=u.readBits(1)),d=u.readUEG(),p=u.readUEG(),1===(i=u.readBits(1))&&(f=u.readUEG(),h=u.readUEG(),g=u.readUEG(),y=u.readUEG()),a=u.readUEG(),o=u.readUEG(),1===i&&(d-=(v=1!==r&&2!==r||0!==c?1:2)*h+v*f,p-=(m=1===r&&0===c?2:1)*y+m*g),u.destroy(),u=null,{width:d,height:p,general_profile_space:s.general_profile_space,general_tier_flag:s.general_tier_flag,general_profile_idc:s.general_profile_idc,general_level_idc:s.general_level_idc,chromaFormatIdc:r,bitDepthLumaMinus8:a,bitDepthChromaMinus8:o}}},{key:"_readProfileTierLevel",value:function(e,t){var n,r,i,a;n=e.readBits(2)||0,r=e.readBits(1)||0,i=e.readBits(5)||0,e.readBits(16),e.readBits(16),e.readBits(1),e.readBits(1),e.readBits(1),e.readBits(1),e.readBits(16),e.readBits(16),e.readBits(12),a=e.readBits(8)||0;for(var o=[],s=[],l=0;l<t;l++)o[l]=e.readBits(1),s[l]=e.readBits(1);t>0&&e.readBits(2*(8-t));for(var u=0;u<t;u++)0!==o[u]&&(e.readBits(2),e.readBits(1),e.readBits(5),e.readBits(16),e.readBits(16),e.readBits(4),e.readBits(16),e.readBits(16),e.readBits(12)),0!==s[u]&&e.readBits(8);return{general_profile_space:n,general_tier_flag:r,general_profile_idc:i,general_level_idc:a}}},{key:"_skipScalingList",value:function(e,t){for(var n=8,r=8,i=0;i<t;i++)0!==r&&(r=(n+e.readSEG()+256)%256),n=0===r?n:r}},{key:"getProfileString",value:function(e){switch(e){case 66:return"Baseline";case 77:return"Main";case 88:return"Extended";case 100:return"High";case 110:return"High10";case 122:return"High422";case 244:return"High444";default:return"Unknown"}}},{key:"getLevelString",value:function(e){return(e/10).toFixed(1)}},{key:"getChromaFormatString",value:function(e){switch(e){case 420:return"4:2:0";case 422:return"4:2:2";case 444:return"4:4:4";default:return"Unknown"}}},{key:"toVideoMeta",value:function(e){var t={};return e&&e.codec_size?(t.codecWidth=e.codec_size.width,t.codecHeight=e.codec_size.height,t.presentWidth=e.present_size.width,t.presentHeight=e.present_size.height):e.width&&e.height&&(t.codecWidth=e.width,t.codecHeight=e.height,t.presentWidth=e.width,t.presentHeight=e.height),t.profile=e.profile_string,t.level=e.level_string,t.bitDepth=e.bit_depth,t.chromaFormat=e.chroma_format,t}}]),e}(),Ge=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),He=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return Ge(e,null,[{key:"EBSP2RBSP",value:function(e){return e.filter((function(t,n){return n<2||!(0===e[n-2]&&0===e[n-1]&&3===t)}))}},{key:"EBSP2SODB",value:function(e){var t=e[e.byteLength-1];return t&&128===t?e.slice(0,e.byteLength-1):e}}]),e}(),qe=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),We=function(e){for(var t="",n=0;n<e.byteLength;n++)t+=String.fromCharCode(e[n]);return t},Ye=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return qe(e,null,[{key:"_resolveNalu",value:function(e){return e.length>=1?He.EBSP2SODB(He.EBSP2RBSP(e.slice(1))):null}},{key:"parse",value:function(t){var n=e._resolveNalu(t),r=e.switchPayloadType(n),i=r.payloadType,a=r.offset,o=n.slice(a);switch(i){case 5:return e.user_data_unregistered(o);default:return{code:i,content:o}}}},{key:"switchPayloadType",value:function(e){for(var t=new DataView(e.buffer),n=0,r=0;255===t.getUint8(r);)r++,n+=255;return{payloadType:n+=t.getUint8(r++),offset:r}}},{key:"getPayloadLength",value:function(e){for(var t=new DataView(e.buffer),n=0,r=0;255===t.getUint8(r);)r++,n+=255;return{payloadLength:n+=t.getUint8(r++),offset:r}}},{key:"user_data_unregistered",value:function(t){var n=e.getPayloadLength(t),r=n.payloadLength,i=n.offset;if(r<16)return{uuid:"",content:null};var a=t.slice(i);return{code:5,uuid:We(a.slice(0,16)),content:We(a.slice(16,r))}}}]),e}(),Xe=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),Ke=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return Xe(e,null,[{key:"getNalunits",value:function(t){if(t.length-t.position<4)return[];var n=t.dataview,r=t.position;return 1===n.getInt32(r)||0===n.getInt16(r)&&1===n.getInt8(r+2)?e.getAnnexbNals(t):e.getHvccNals(t)}},{key:"getAnnexbNals",value:function(t){for(var n=[],r=e.getHeaderPositionAnnexB(t),i=r.pos,a=i;i<t.length-4;){var o=t.buffer.slice(i,i+r.headerLength);r.pos===t.position&&t.skip(r.headerLength),a=(r=e.getHeaderPositionAnnexB(t)).pos;var s={header:o,body:new Uint8Array(t.buffer.slice(i+o.byteLength,a))};e.analyseNal(s),s.type<=40&&n.push(s),t.skip(a-t.position),i=a}return n}},{key:"getHvccNals",value:function(t){for(var n=[];t.position<t.length-4;){var r=t.dataview.getInt32(t.dataview.position);if(!(t.length-t.position>=r))break;var i=t.buffer.slice(t.position,t.position+4);t.skip(4);var a=new Uint8Array(t.buffer.slice(t.position,t.position+r));t.skip(r);var o={header:i,body:a};try{e.analyseNal(o)}catch(e){continue}o.type<=40&&n.push(o)}return n}},{key:"analyseNal",value:function(e){var t=e.body[0]>>>1&63;switch(e.type=t,t){case 0:e.slice_trail_n=!0;break;case 1:e.slice_trail_r=!0,e.key=!0;break;case 2:e.slice_tsa_n=!0;break;case 3:e.slice_tsa_r=!0,e.key=!0;break;case 4:e.slice_stsa_n=!0;break;case 5:e.slice_stsa_r=!0,e.key=!0;break;case 6:e.slice_radl_n=!0;break;case 7:e.slice_radl_r=!0,e.key=!0;break;case 8:e.slice_rasl_n=!0;break;case 9:e.slice_rasl_r=!0,e.key=!0;break;case 16:e.slice_bla_w_lp=!0;break;case 17:e.slice_bla_w_radl=!0;break;case 18:e.slice_bla_n_lp=!0;break;case 19:e.slice_idl_w_radl=!0,e.key=!0;break;case 20:e.slice_idr_n_lp=!0,e.key=!0;break;case 21:e.slice_cra_nut=!0,e.key=!0;break;case 32:e.vps=!0;break;case 33:e.sps=Ve.parseSPS(e.body);break;case 34:e.pps=!0;break;case 35:break;case 36:e.aud=!0;break;case 37:e.eob=!0;break;case 38:e.fd=!0;break;case 39:try{e.sei=Ye.parse(e.body.slice(1))}catch(e){}break;case 40:e.sei=Ye.parse(e.body.slice(1))}}},{key:"getHeaderPositionAnnexB",value:function(e){for(var t=e.position,n=0,r=e.length;3!==n&&4!==n&&t<r-4;)0===e.dataview.getInt16(t)?1===e.dataview.getInt16(t+2)?n=4:1===e.dataview.getInt8(t+2)?n=3:t++:t++;return t===r-4&&(0===e.dataview.getInt16(t)?1===e.dataview.getInt16(t+2)&&(n=4):(t++,0===e.dataview.getInt16(t)&&1===e.dataview.getInt8(t)?n=3:t=r)),{pos:t,headerLength:n}}}]),e}(),$e=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();function Ze(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}var Je=f.REMUX_EVENTS,Qe=function(){function e(){var t=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.TAG="Compatibility",this.nextAudioDts=0,this.nextVideoDts=0,this.lastAudioSamplesLen=0,this.lastVideoSamplesLen=0,this.lastVideoDts=void 0,this.lastAudioDts=void 0,this.allAudioSamplesCount=0,this.allVideoSamplesCount=0,this._firstAudioSample=null,this._firstVideoSample=null,this.filledAudioSamples=[],this.filledVideoSamples=[],this._lastSegmentId=0,this._currentSegmentId=0,this.videoLastSample=null,this.audioLastSample=null,Object.defineProperties(this,{_videoLargeGap:{set:function(e){t.___videoLargeGap=e,0!==e&&t.emit(Je.DETECT_LARGE_GAP,"video",e)},get:function(){return t.___videoLargeGap||0}},_audioLargeGap:{set:function(e){t.___audioLargeGap=e,0!==e&&t.emit(Je.DETECT_LARGE_GAP,"audio",e)},get:function(){return t.___audioLargeGap||0}}}),this.audioUnsyncTime=0}return $e(e,[{key:"init",value:function(){this.before(Je.REMUX_MEDIA,this.doFix.bind(this))}},{key:"reset",value:function(){this.nextAudioDts=null,this.nextVideoDts=null,this.lastAudioSamplesLen=0,this.lastVideoSamplesLen=0,this.lastVideoDts=void 0,this.lastAudioDts=void 0,this._audioLargeGap=0,this._videoLargeGap=0,this.videoLastSample=null,this.audioLastSample=null,this.filledAudioSamples=[],this.filledVideoSamples=[],this.audioUnsyncTime=0}},{key:"_isSegmentsContinuous",value:function(){return 0===this._lastSegmentId||this._currentSegmentId-this._lastSegmentId==1}},{key:"doFix",value:function(){var t=void 0,n=void 0,r=void 0,i=0;this.videoTrack.samples.length&&(t=(n=this.videoTrack.samples[0]).options,he.long&&he.log(this.TAG,this.videoTrack.samples.slice())),this.audioTrack.samples.length&&(r=this.audioTrack.samples[0],he.long&&he.log(this.TAG,this.audioTrack.samples.slice())),n&&r&&(i=r.dts-n.dts),this._currentSegmentId=t&&t.id||this._currentSegmentId+1,he.log(this.TAG,"lastSeg:"+this._lastSegmentId+" , currentSeg:"+this._currentSegmentId+", discontinue:"+(t&&t.discontinue)+" vSamp0:"+(n&&n.dts)+" aSamp0:"+(r&&r.dts));var a=this.getFirstSample(),o=a.isFirstAudioSamples,s=a.isFirstVideoSamples;this.recordSamplesCount(),this._firstVideoSample&&this.fixVideoRefSampleDuration(this.videoTrack.meta,this.videoTrack.samples);var l=e.detectChangeStream(this.videoTrack.samples,s),u=l.changed,c=l.changedIdxes;if(u){for(var d=!1,p=0;p<c.length;p++)this.fixChangeStreamVideo(c[p],s)&&(d=!0);d||this.doFixVideo(s)}else if(!this._isSegmentsContinuous()&&t&&void 0!==t.start){this.videoLastSample=null;var f=t.start;i<0&&(f-=i),he.log(this.TAG,"fix video for _isSegmentsContinuous()， delta:",i),this.doFixVideo(s,f),this.emit(Je.DETECT_FRAG_ID_DISCONTINUE,t.start/1e3)}else this.doFixVideo(s);this._appendSampleForLastSegment(t&&t.isLast);var h=e.detectChangeStream(this.audioTrack.samples,o),g=h.changed,y=h.changedIdxes;if(g){for(var v=!1,m=0;m<y.length;m++)this.fixChangeStreamAudio(y[m],o)&&(v=!0);if(v)return;this.doFixAudio(o)}else if(!this._isSegmentsContinuous()&&t&&void 0!==t.start){he.log(this.TAG,"fix audio for _isSegmentsContinuous()");var x=this.audioTrack.samples[0];this.nextAudioDts=t.start||x&&x.dts,this.doFixAudio(o,t.start),this.emit(Je.DETECT_FRAG_ID_DISCONTINUE,t.start/1e3)}else this.doFixAudio(o);this.removeInvalidSamples(),this._lastSegmentId=this._currentSegmentId}},{key:"doFixVideo",value:function(t,n){for(var r=this.videoTrack,i=r.samples,a=r.meta,o=i.length,s=0;s<o;s++){var l=i[s];l.originDts=l.dts,l.originPts=l.pts}if(i&&o&&this._firstVideoSample){var u=i[0];if(he.log(this.TAG,"doFixVideo:: lastVideoDts: "+this.lastVideoDts+" ,  _videoLargeGap: "+this._videoLargeGap+" ,streamChangeStart:"+n+", lastVideoSample:[dts="+(this.videoLastSample&&this.videoLastSample.dts)+" , pts="+(this.videoLastSample&&this.videoLastSample.pts)+"] ,  firstDTS:"+u.dts+" ,firstPTS:"+u.pts+" ,lastDTS:"+i[o-1].dts+" , lastPTS: "+i[o-1].pts),!t&&void 0===n&&this.videoLastSample&&e.detectVideoLargeGap(this.videoLastSample?this.videoLastSample.dts:0,u.dts+this._videoLargeGap)&&(this._videoLargeGap=this.videoLastSample.dts+a.refSampleDuration-u.dts),0!==this._videoLargeGap&&(e.doFixLargeGap(i,this._videoLargeGap),this._videoLargeGap!==this.preVideoGap&&(this.preVideoGap=this._videoLargeGap,this.emit(Je.DETECT_CHANGE_STREAM_DISCONTINUE,"video",{prevDts:this.videoLastSample&&this.videoLastSample.originDts,curDts:u.originDts}))),t||void 0===n||0===n||(this._videoLargeGap=n-u.dts,e.doFixLargeGap(i,this._videoLargeGap)),t&&this._firstAudioSample){var c=this._firstVideoSample.originDts,d=c-this._firstAudioSample.dts;if(d>2*a.refSampleDuration&&d<10*a.refSampleDuration){for(var p=Math.floor(d/a.refSampleDuration),f=0;f<p;f++){var h=Object.assign({},u);h.dts=c-(f+1)*a.refSampleDuration,h.pts=h.dts+h.cts,i.unshift(h),this.filledVideoSamples.push({dts:h.dts,size:h.data.byteLength})}this._firstVideoSample=this.filledVideoSamples[0]||this._firstVideoSample}else Math.abs(d)>2*a.refSampleDuration&&!this._videoLargeGap&&(this._videoLargeGap=-1*d,e.doFixLargeGap(i,-1*d))}for(var g=i.length,y=1;y<g;y++){var v=i[y],m=i[y-1],x=v.dts-v.pts;Math.abs(x)<2e3&&Math.abs(v.dts-m.dts)>1e4&&(v.dts=m.dts+a.refSampleDuration,v.pts=m.pts+a.refSampleDuration)}var b=i.pop();if(i.length&&(i[i.length-1].duration=b.dts-i[i.length-1].dts),g<4){var _=i[i.length-1],k=(_=_||b).options&&_.options.duration,E=a.refSampleDuration;if(k&&E&&k/E>5)for(var w=_.pts,T=_.dts,S=0;S<3;S++){T+=E,w+=E;var C=Object.assign({},_,{dts:T,pts:w});2===S&&(C.duration=k),i.push(C)}b=null}if(this.videoLastSample){var O=this.videoLastSample;O.duration=u.dts-O.dts,i.unshift(this.videoLastSample)}this.videoLastSample=b,i[i.length-1]&&(this.lastVideoDts=i[i.length-1].dts),this.videoTrack.samples=i}}},{key:"_appendSampleForLastSegment",value:function(e){e&&this.videoLastSample&&this.videoTrack.samples.push(this.videoLastSample)}},{key:"doFixAudio",value:function(t,n){var r=this,i=this.audioTrack,a=i.samples,o=i.meta;if(a&&a.length){this.fixAudioRefSampleDuration(o);for(var s=0,l=a.length;s<l;s++){var u=a[s];u.originDts=u.dts}var c=a.length,d=Te.getSilentFrame(o.originCodec,o.channelCount),p=Math.floor(o.refSampleDuration),f=this._firstAudioSample,h=a[0];if(he.log(this.TAG,"doFixAudio:: audioDtsBase:"+this.audioDtsBase+" ,  _audioLargeGap: "+this._audioLargeGap+", streamChangeStart:"+n+" ,  nextAudioDts:"+this.nextAudioDts+",  audio: firstDTS:"+h.dts+" ,firstPTS:"+h.pts+" ,lastDTS:"+a[c-1].dts+" , lastPTS: "+a[c-1].pts),!t&&null===this.nextAudioDts&&h.options&&h.options.start&&void 0!==n&&(n=h.options.start),!t&&void 0===n&&this.nextAudioDts&&e.detectAudioLargeGap(this.nextAudioDts||0,h.dts+this._audioLargeGap)){var g=this.nextAudioDts-h.dts;this._audioLargeGap=Math.abs(g-this._videoLargeGap)<200?this._videoLargeGap:g}if(0!==this._audioLargeGap?(e.doFixLargeGap(a,this._audioLargeGap),this._audioLargeGap!==this.preAudioGap&&(this.preAudioGap=this._audioLargeGap,this.emit(Je.DETECT_CHANGE_STREAM_DISCONTINUE,"audio",{prevDts:this.lastAudioOriginDts,curDts:h.originDts}))):t||void 0===n&&!e.detectAudioLargeGap(this.nextAudioDts,h.dts)||(void 0!==n&&0!==n&&(this.nextAudioDts=n),this._audioLargeGap=this.nextAudioDts-h.dts,h.options.start&&!h.options.start.isContinue&&e.doFixLargeGap(a,this._audioLargeGap)),this._firstVideoSample&&t){var y=this._firstVideoSample.originDts||this._firstVideoSample.dts,v=f.dts-y;if(v===this._videoLargeGap);else if(v>o.refSampleDuration&&v<10*o.refSampleDuration){var m=Math.floor((f.dts-y)/o.refSampleDuration);he.warn(this.TAG,"fill "+m+" frames for av align");for(var x=0;x<m;x++){var b={data:d,datasize:d.byteLength,dts:f.dts-(x+1)*o.refSampleDuration,filtered:0};a.unshift(b),this.filledAudioSamples.push({dts:b.dts,size:b.data.byteLength})}this._firstAudioSample=this.filledAudioSamples[0]||this._firstAudioSample}else v<-1*o.refSampleDuration&&(this._audioLargeGap=-1*v,e.doFixLargeGap(a,-1*v))}var _=void 0,k=a[0].dts;if(this.nextAudioDts){_=k-this.nextAudioDts;var E=Math.abs(_);if(_>=p&&_<1e4&&d){for(var w=Math.ceil(_/p),T=0;T<w;T++){var S=k-(T+1)*p,C={dts:S>this.nextAudioDts?S:this.nextAudioDts,pts:S>this.nextAudioDts?S:this.nextAudioDts,datasize:d.byteLength,filtered:0,data:d};this.filledAudioSamples.push({dts:C.dts,size:C.data.byteLength}),this.audioTrack.samples.unshift(C),h=C}this.emit(Je.DETECT_AUDIO_GAP,_,w)}else E<o.refSampleDuration&&E>0?(h.dts=this.nextAudioDts,h.pts=this.nextAudioDts):_<0&&E<p&&(e.doFixLargeGap(a,-1*_),this.emit(Je.DETECT_AUDIO_OVERLAP,_))}for(var O=a[0].dts+p,A=1;A<a.length;){var R=a[A],D=R.dts-O;if(D<=-1*p)he.warn("drop 1 audio sample for "+D+" ms overlap"),a.splice(A,1);else if(D>=10*p){var L=Math.round(D/p);if(L>1e3)break;he.warn(this.TAG,"inject "+L+" audio frame for "+D+" ms gap, index="+A);for(var P=0;P<L;P++){var M={data:d,datasize:d.byteLength,dts:O,originDts:O,filtered:0};a.splice(A,0,M),O+=p,A++}R.dts=R.pts=R.originDts=O,O+=p,A++}else R.dts=R.pts=R.originDts=O,O+=p,A++}var I=o.refSampleDuration-p;a.forEach((function(e,t){if(0!==t){var n=a[t-1];e.dts=e.pts=n.dts+n.duration}e.duration=p,r.audioUnsyncTime=r.audioUnsyncTime+I,r.audioUnsyncTime>=1&&(e.duration+=1,r.audioUnsyncTime-=1)}));var B=a[a.length-1];this.lastAudioDts=B.dts;var U=B.duration;this.lastAudioSamplesLen=c,this.nextAudioDts=this.lastAudioDts+(U||p),this.lastAudioOriginDts=B.originDts,this.audioTrack.samples=e.sortAudioSamples(a)}}},{key:"fixChangeStreamVideo",value:function(e){he.log(this.TAG,"fixChangeStreamVideo(), changeIdx=",e);var t=this.videoTrack.samples,n=0===e?this.lastVideoDts?this.lastVideoDts:this.getStreamChangeStart(t[0]):t[e-1].dts,r=t[e].dts,i=Math.abs(n-r)<=1e4;if(this.emit(Je.DETECT_CHANGE_STREAM,"video",r),i)return t[e].options?t[e].options.isContinue=!0:t[e].options={isContinue:!0},!1;this.emit(Je.DETECT_CHANGE_STREAM_DISCONTINUE,"video",{prevDts:n,curDts:r});var a=t.slice(0,e),o=t.slice(e),s=t[e],l=void 0;return this._videoLargeGap=0,this.videoLastSample=null,this.lastVideoDts=null,l=s.options&&void 0!==s.options.start?s.options.start:n-this.videoDtsBase,this.videoTrack.samples=t.slice(0,e),this.doFixVideo(!1),this.videoTrack.samples=t.slice(e),this.doFixVideo(!1,l),this.videoTrack.samples=a.concat(o),!0}},{key:"fixChangeStreamAudio",value:function(e){he.log(this.TAG,"fixChangeStreamAudio(), changeIdx=",e);var t=this.audioTrack.samples,n=0===e?this.lastAudioDts:t[e-1].dts,r=t[e].dts,i=Math.abs(n-r)<=1e4;if(this.emit(Je.DETECT_CHANGE_STREAM,"audio",r),i)return t[e].options?t[e].options.isContinue=!0:t[e].options={isContinue:!0},!1;this.emit(Je.DETECT_CHANGE_STREAM_DISCONTINUE,"audio",{prevDts:n,curDts:r}),this._audioLargeGap=0;var a=this.nextAudioDts;this.nextAudioDts=null;var o=t.slice(0,e),s=t.slice(e),l=t[e],u=void 0;return l.options&&void 0!==l.options.start?u=l.options.start:(u=a,l.options.isContinue=!0),this.audioTrack.samples=o,this.doFixAudio(!1),this.audioTrack.samples=s,this.doFixAudio(!1,u),this.audioTrack.samples=o.concat(s),!0}},{key:"getFirstSample",value:function(){var t=this.videoTrack.samples,n=this.audioTrack.samples,r=!1,i=!1;return!this._firstVideoSample&&t.length&&(this._firstVideoSample=e.findFirstVideoSample(t),this.removeInvalidSamples(),r=!0),!this._firstAudioSample&&n.length&&(this._firstAudioSample=e.findFirstAudioSample(n),this.removeInvalidSamples(),i=!0),{isFirstVideoSamples:r,isFirstAudioSamples:i}}},{key:"fixVideoRefSampleDuration",value:function(t,n){if(t){var r=this.allVideoSamplesCount,i=this._firstVideoSample.dts,a=this.filledVideoSamples.length;if(e.isRefSampleDurationValid(t.refSampleDuration)){if(t.refSampleDuration&&n.length>=5){var o=(n[n.length-1].dts-n[0].dts)/(n.length-1);if(o>0&&o<1e3){var s=Math.floor(Math.abs(t.refSampleDuration-o)<=5?t.refSampleDuration:o);e.isRefSampleDurationValid(s)&&(t.refSampleDuration=s)}}}else if(n.length>=1){var l=n[n.length-1].dts,u=Math.floor((l-i)/(r+a-1));e.isRefSampleDurationValid(u)&&(t.refSampleDuration=u)}e.isRefSampleDurationValid(t.refSampleDuration)||(t.refSampleDuration=66)}}},{key:"fixAudioRefSampleDuration",value:function(e){e&&(e.refSampleDuration=1024*e.timescale/e.sampleRate)}},{key:"recordSamplesCount",value:function(){var e=this.audioTrack,t=this.videoTrack;this.allAudioSamplesCount+=e.samples.length,this.allVideoSamplesCount+=t.samples.length}},{key:"removeInvalidSamples",value:function(){var e=this.audioTrack.samples[0],t=this.videoTrack.samples[0];e&&(this.audioTrack.samples=this.audioTrack.samples.filter((function(t,n){return t===e||t.dts>=e.dts}))),t&&(this.videoTrack.samples=this.videoTrack.samples.filter((function(e,n){return e===t||e.dts>=t.dts})))}},{key:"getStreamChangeStart",value:function(e){return e.options&&e.options.start?e.options.start-this.dtsBase:1/0}},{key:"tracks",get:function(){return this._context.getInstance("TRACKS")}},{key:"audioTrack",get:function(){return this.tracks&&this.tracks.audioTrack?this.tracks.audioTrack:{samples:[],meta:{}}}},{key:"videoTrack",get:function(){return this.tracks&&this.tracks.videoTrack?this.tracks.videoTrack:{samples:[],meta:{}}}},{key:"dtsBase",get:function(){var e=this._context.getInstance("MP4_REMUXER");return e?e._dtsBase:0}},{key:"audioDtsBase",get:function(){var e=this._context.getInstance("MP4_REMUXER");return e&&null!==e._audioDtsBase?e._audioDtsBase:this.dtsBase}},{key:"videoDtsBase",get:function(){var e=this._context.getInstance("MP4_REMUXER");return e&&null!==e._videoDtsBase?e._videoDtsBase:this.dtsBase}}],[{key:"sortAudioSamples",value:function(e){return 1===e.length?e:[].concat(Ze(e)).sort((function(e,t){return e.dts-t.dts}))}},{key:"isRefSampleDurationValid",value:function(e){return e&&e>0&&!Number.isNaN(e)}},{key:"findFirstAudioSample",value:function(t){return t&&0!==t.length?e.sortAudioSamples(t)[0]:null}},{key:"findFirstVideoSample",value:function(e){if(!e.length)return null;for(var t=[].concat(Ze(e)).sort((function(e,t){return e.dts-t.dts})),n=0,r=t.length;n<r;n++)if(t[n].isKeyframe)return t[n]}},{key:"detectVideoLargeGap",value:function(e,t){if(null!==e)return e-t>=1e3||t-e>=1e3}},{key:"detectAudioLargeGap",value:function(e,t){if(null!==e)return e-t>=1e3||t-e>=1e3}},{key:"doFixLargeGap",value:function(e,t){for(var n=0,r=e.length;n<r;n++){var i=e[n];i.dts+=t,i.pts&&(i.pts+=t)}var a=e[0];a&&0===a.dts&&(a.dts=a.pts=1)}},{key:"detectChangeStream",value:function(e,t){for(var n=!1,r=[],i=0,a=e.length;i<a;i++){var o=e[i];!o.options||!o.options.meta||t&&0===i||(n=!0,r.push(i))}return{changed:n,changedIdxes:r}}}]),e}(),et=W((function(e){var t=Object.prototype.hasOwnProperty,n="~";function r(){}function i(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function a(e,t,r,a,o){if("function"!=typeof r)throw new TypeError("The listener must be a function");var s=new i(r,a||e,o),l=n?n+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],s]:e._events[l].push(s):(e._events[l]=s,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new r:delete e._events[t]}function s(){this._events=new r,this._eventsCount=0}Object.create&&(r.prototype=Object.create(null),(new r).__proto__||(n=!1)),s.prototype.eventNames=function(){var e,r,i=[];if(0===this._eventsCount)return i;for(r in e=this._events)t.call(e,r)&&i.push(n?r.slice(1):r);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},s.prototype.listeners=function(e){var t=n?n+e:e,r=this._events[t];if(!r)return[];if(r.fn)return[r.fn];for(var i=0,a=r.length,o=new Array(a);i<a;i++)o[i]=r[i].fn;return o},s.prototype.listenerCount=function(e){var t=n?n+e:e,r=this._events[t];return r?r.fn?1:r.length:0},s.prototype.emit=function(e,t,r,i,a,o){var s=n?n+e:e;if(!this._events[s])return!1;var l,u,c=this._events[s],d=arguments.length;if(c.fn){switch(c.once&&this.removeListener(e,c.fn,void 0,!0),d){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,t),!0;case 3:return c.fn.call(c.context,t,r),!0;case 4:return c.fn.call(c.context,t,r,i),!0;case 5:return c.fn.call(c.context,t,r,i,a),!0;case 6:return c.fn.call(c.context,t,r,i,a,o),!0}for(u=1,l=new Array(d-1);u<d;u++)l[u-1]=arguments[u];c.fn.apply(c.context,l)}else{var p,f=c.length;for(u=0;u<f;u++)switch(c[u].once&&this.removeListener(e,c[u].fn,void 0,!0),d){case 1:c[u].fn.call(c[u].context);break;case 2:c[u].fn.call(c[u].context,t);break;case 3:c[u].fn.call(c[u].context,t,r);break;case 4:c[u].fn.call(c[u].context,t,r,i);break;default:if(!l)for(p=1,l=new Array(d-1);p<d;p++)l[p-1]=arguments[p];c[u].fn.apply(c[u].context,l)}}return!0},s.prototype.on=function(e,t,n){return a(this,e,t,n,!1)},s.prototype.once=function(e,t,n){return a(this,e,t,n,!0)},s.prototype.removeListener=function(e,t,r,i){var a=n?n+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var s=this._events[a];if(s.fn)s.fn!==t||i&&!s.once||r&&s.context!==r||o(this,a);else{for(var l=0,u=[],c=s.length;l<c;l++)(s[l].fn!==t||i&&!s[l].once||r&&s[l].context!==r)&&u.push(s[l]);u.length?this._events[a]=1===u.length?u[0]:u:o(this,a)}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&o(this,t)):(this._events=new r,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=n,s.EventEmitter=s,e.exports=s})),tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},nt=function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var n=[],r=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(r=(o=s.next()).done)&&(n.push(o.value),!t||n.length!==t);r=!0);}catch(e){i=!0,a=e}finally{try{!r&&s.return&&s.return()}finally{if(i)throw a}}return n}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")},rt="function"==typeof Symbol&&"symbol"===tt(Symbol.iterator)?function(e){return void 0===e?"undefined":tt(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":void 0===e?"undefined":tt(e)},it=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),at=je,ot=Ke,st={1:["video","MPEG-1"],2:["video","MPEG-2"],27:["video","AVC.H264"],36:["video","HVC.H265"],234:["video","VC-1"],3:["audio","MPEG-1"],4:["audio","MPEG-2"],15:["audio","MPEG-2.AAC"],17:["audio","MPEG-4.AAC"],128:["audio","LPCM"],129:["audio","AC3"],6:["audio","AC3"],130:["audio","DTS"],131:["audio","Dolby TrueHD"],132:["audio","AC3-Plus"],133:["audio","DTS-HD"],134:["audio","DTS-MA"],161:["audio","AC3-Plus-SEC"],162:["audio","DTS-HD-SEC"]},lt=function(e){function t(e){var n=e.videoTrack,r=e.audioTrack;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var i=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==(void 0===t?"undefined":tt(t))&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return i.TAG="TsDemuxer",i.demuxing=!1,i.videoTrack=n,i.audioTrack=r,i.pat=[],i.pmt=[],i._hasVideoMeta=!1,i._hasAudioMeta=!1,i.gopId=0,i}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+(void 0===t?"undefined":tt(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),it(t,null,[{key:"EVENTS",get:function(){return{DEMUX_COMPLETE:"DEMUX_COMPLETE",METADATA_PARSED:"METADATA_PARSED",VIDEO_SAMPLE_PARSED:"VIDEO_SAMPLE_PARSED",AUDIO_SAMPLE_PARSED:"AUDIO_SAMPLES_PARSED",SEI_PARSED:"SEI_PARSED"}}}]),it(t,[{key:"demux",value:function(e,n,r){if(e&&he.log(this.TAG,"do demux: id="+e.id+",demuxing="+this.demuxing),!this.demuxing){for(var i={pat:[],pmt:[]},a={};n.length>=188;){if(n.length>=1&&71!==n.array[0][n.offset])throw new Error("Untrust sync code: "+n.array[0][n.offset]+", try to recover;");for(;n.length>=1&&71!==n.array[0][n.offset];)n.shift(1);if(!(n.length<188)){var o=n.shift(188),s=new _(o.buffer),l={};t.read(s,l,i);var u=a[l.header.pid];l.pes?(l.pes.codec=l.header.codec,l.pes.streamType=l.header.streamType,a[l.header.pid]||(a[l.header.pid]=[]),a[l.header.pid].push(l.pes),l.pes.ES.buffer=[l.pes.ES.buffer]):u&&u[u.length-1].ES.buffer.push(l.payload.stream)}}for(var c=Object.assign({},e),d=Object.assign({},e),p=r&&this._hasVideoMeta&&!this._hasAudioMeta,f=r&&this._hasAudioMeta&&!this._hasVideoMeta,h=0;h<Object.keys(a).length;h++)for(var g=a[Object.keys(a)[h]],y=0;y<g.length;y++){var v=g[y];v.id=Object.keys(a)[h];var m=15===v.streamType||17===v.streamType;"audio"===v.type&&!p&&m?(v.ES.buffer=t.mergeAudioES(v.ES.buffer),this.pushAudioSample(v,c),c={}):"video"!==v.type||f||(v.ES.buffer=t.mergeVideoES(v.ES.buffer),"HVC.H265"===v.codec?this.pushVideoSampleHEVC(g[y],d):this.pushVideoSample(g[y],d),d={spsFound:!!d.spsFound})}}}},{key:"pushAudioSample",value:function(e,n){var r=new M({audioSampleRate:e.ES.frequence,sampleRate:e.ES.frequence,channelCount:e.ES.channel,codec:"mp4a.40."+e.ES.audioObjectType,originCodec:"mp4a.40."+e.ES.originAudioObjectType,originObjectType:e.ES.originAudioObjectType,config:e.ES.audioConfig,id:2,sampleRateIndex:e.ES.frequencyIndex});r.refSampleDuration=Math.floor(1024/r.audioSampleRate*r.timescale);var i=t.compareMeta(this.audioTrack.meta,r,!0);this._hasAudioMeta&&i||(this._hasAudioMeta=!0,n?n.meta=Object.assign({},r):n={meta:Object.assign({},r)},this.emit(t.EVENTS.METADATA_PARSED,"audio",r));var a=0;e.ES.buffer.skip(e.pesHeaderLength+9);for(var o=!1;e.ES.buffer.position<e.ES.buffer.length;)if(Ce.isHeader(new Uint8Array(e.ES.buffer.buffer),e.ES.buffer.position)&&e.ES.buffer.position+5<e.ES.buffer.length){var s=Ce.appendFrame(this.audioTrack.meta.sampleRate,new Uint8Array(e.ES.buffer.buffer),e.ES.buffer.position,e.pts,a);if(!s||!s.sample)break;e.ES.buffer.skip(s.length);var l=new j({dts:s.sample.dts,pts:s.sample.pts,data:s.sample.unit,options:o?{}:n});n.meta&&(o=!0),l.dts=l.pts=Math.ceil(l.pts/90),this.emit(t.EVENTS.AUDIO_SAMPLE_PARSED,l),a++}else e.ES.buffer.skip(1)}},{key:"pushVideoSample",value:function(e,n){for(var r=this,i=at.getNalunits(e.ES.buffer),a=new I,o=n.spsFound,s=0,l=!1,u=!1,c=[],d=0;d<i.length;d++){var p=i[d];if(p.type<9&&!p.sei&&(s+=4+p.body.byteLength),p.sps){l=p,a.sps=p.body,a.chromaFormat=l.sps.chroma_format,a.codec="avc1.";for(var f=1;f<4;f++){var h=l.body[f].toString(16);h.length<2&&(h="0"+h),a.codec+=h}a.codecHeight=l.sps.codec_size.height,a.codecWidth=l.sps.codec_size.width,a.frameRate=l.sps.frame_rate,a.id=1,a.level=l.sps.level_string,a.presentHeight=l.sps.present_size.height,a.presentWidth=l.sps.present_size.width,a.profile=l.sps.profile_string,a.refSampleDuration=Math.floor(a.timescale*(l.sps.frame_rate.fps_den/l.sps.frame_rate.fps_num)),a.sarRatio=l.sps.sar_ratio?l.sps.sar_ratio:l.sps.par_ratio}else p.pps?(a.pps=p.body,u=p):p.sei&&c.push(p.sei)}if(l&&u){a.avcc=at.getAvcc(l.body,u.body);var g=t.compareMeta(this.videoTrack.meta,a,!0);this._hasVideoMeta&&g||(n?n.meta=Object.assign({},a):n={meta:Object.assign({},a)},o||this.emit(t.EVENTS.METADATA_PARSED,"video",a),n.spsFound=!0,this._hasVideoMeta=!0)}for(var y=new Uint8Array(s),v=0,m=!1,x=0;x<i.length;x++){var b=i[x];if(!(b.type&&b.type>=9)){var _=b.body.byteLength;b.idr&&(m=!0),b.sei||(y.set(new Uint8Array([_>>>24&255,_>>>16&255,_>>>8&255,255&_]),v),v+=4,y.set(b.body,v),v+=_)}}var k=parseInt(e.dts/90),E=parseInt(e.pts/90);c.length&&c.forEach((function(e){e.dts=k,r.emit(t.EVENTS.SEI_PARSED,e)}));var w=new N({dts:k,pts:E,cts:E-k,originDts:e.dts,purePts:e.purePts,isKeyframe:m,data:y,nals:i,options:n,firstInGop:m,gopId:m?++this.gopId:this.gopId});this.emit(t.EVENTS.VIDEO_SAMPLE_PARSED,w)}},{key:"pushVideoSampleHEVC",value:function(e,n){var r=this,i=ot.getNalunits(e.ES.buffer);i=i.filter((function(e){return e.body&&e.body.length}));var a=new I;a.streamType=36;for(var o=0,s=!1,l=!1,u=!1,c=[],d=!1,p=!1,f=!1,h=!1,g=0;g<i.length;g++){var y=i[g];if(y.vps){if(d)continue;d=!0}else if(y.sps){if(p)continue;p=!0}else if(y.pps){if(f)continue;f=!0}else if(y.key)20!==y.type&&19!==y.type||(h=!0);else if(0===y.type);else if(35===y.type)continue;y.sps?(l=y,a.sps=y.body,a.presentWidth=l.sps.width,a.presentHeight=l.sps.height,a.general_profile_space=l.sps.general_profile_space,a.general_tier_flag=l.sps.general_tier_flag,a.general_profile_idc=l.sps.general_profile_idc,a.general_level_idc=l.sps.general_level_idc,a.codec="hev1.1.6.L93.B0",a.chromaFormatIdc=l.sps.chromaFormatIdc,a.bitDepthLumaMinus8=l.sps.bitDepthLumaMinus8,a.bitDepthChromaMinus8=l.sps.bitDepthChromaMinus8):y.pps?(a.pps=y.body,u=y):y.vps?(a.vps=y.body,s=y):y.sei&&c.push(y.sei),y.type<=40&&(o+=4+y.body.byteLength)}if(l&&u&&s){var v=t.compareMeta(this.videoTrack.meta,a,!0);this._hasVideoMeta&&v||(n?n.meta=Object.assign({},a):n={meta:Object.assign({},a)},a.streamType=36,this.videoTrack.meta=a,this._hasVideoMeta=!0,this.emit(t.EVENTS.METADATA_PARSED,"video",a))}var m=new Uint8Array(o),x=0,b=!1;d=!1,p=!1,f=!1;for(var _=0;_<i.length;_++){var k=i[_];if(!(k.type&&k.type>40)){if(k.vps){if(d)continue;d=!0}else if(k.sps){if(p)continue;p=!0}else if(k.pps){if(f)continue;f=!0}else if(k.key);else if(0===k.type);else if(35===k.type)continue;var E=k.body.byteLength;k.key&&(b=!0),m.set(new Uint8Array([E>>>24&255,E>>>16&255,E>>>8&255,255&E]),x),x+=4,m.set(k.body,x),x+=E}}var w=parseInt(e.dts/90),T=parseInt(e.pts/90);c&&c.forEach((function(e){e.dts=w,r.emit(t.EVENTS.SEI_PARSED,e)}));var S=new N({dts:w,pts:T,cts:T-w,originDts:e.dts,purePts:e.purePts,isKeyframe:b,data:m,nals:i,options:n,firstInGop:h,gopId:h?++this.gopId:this.gopId});this.emit(t.EVENTS.VIDEO_SAMPLE_PARSED,S)}},{key:"destroy",value:function(){this.removeAllListeners(),this.configs={},this.demuxing=!1,this.pat=[],this.pmt=[],this._hasVideoMeta=!1,this._hasAudioMeta=!1}}],[{key:"compareArray",value:function(e,t,n){var r=0,i=0;if("Uint8Array"===n?(r=e.byteLength,i=t.byteLength):"Array"===n&&(r=e.length,i=t.length),r!==i)return!1;for(var a=0;a<r;a++)if(e[a]!==t[a])return!1;return!0}},{key:"compareMeta",value:function(e,n,r){if(!e||!n)return!1;for(var i=0,a=Object.keys(e).length;i<a;i++){var o=e[Object.keys(e)[i]],s=n[Object.keys(e)[i]];if(!o&&!s)return!0;if(null==o&&s||o&&void 0===s)return!1;if("object"!==(void 0===o?"undefined":rt(o))){if(r&&"duration"!==Object.keys(e)[i]&&"refSampleDuration"!==Object.keys(e)[i]&&"refSampleDurationFixed"!==Object.keys(e)[i]&&o!==s)return!1}else if(void 0!==o.byteLength){if(void 0===s.byteLength)return!1;if(!t.compareArray(o,s,"Uint8Array"))return!1}else if(void 0!==o.length){if(void 0===s.length)return!1;if(!t.compareArray(o,s,"Array"))return!1}else if(!t.compareMeta(o,s))return!1}return!0}},{key:"mergeVideoES",value:function(e){for(var t=void 0,n=0,r=0,i=0;i<e.length;i++)n+=e[i].length-e[i].position;t=new Uint8Array(n);for(var a=0;a<e.length;a++){var o=e[a];t.set(new Uint8Array(o.buffer,o.position),r),r+=o.length-o.position}return new _(t.buffer)}},{key:"mergeAudioES",value:function(e){for(var t=void 0,n=0,r=0,i=0;i<e.length;i++)n+=e[i].length;t=new Uint8Array(n);for(var a=0;a<e.length;a++){var o=e[a];t.set(new Uint8Array(o.buffer),r),r+=o.length}return new _(t.buffer)}},{key:"read",value:function(e,n,r){t.readHeader(e,n),t.readPayload(e,n,r),"MEDIA"!==n.header.packet||1!==n.header.payload||n.unknownPIDs||(n.pes=t.PES(n))}},{key:"readPayload",value:function(e,n,r){var i=n.header.pid;switch(i){case 0:t.PAT(e,n,r);break;case 1:t.CAT(e,n,r);break;case 2:t.TSDT(e,n,r);break;case 8191:break;default:for(var a=!1,o=0,s=r.pat.length;o<s;o++)if(r.pat[o].pid===i){a=!0;break}if(a)t.PMT(e,n,r);else{for(var l=[],u=0,c=r.pmt.length;u<c;u++)if(r.pmt[u].pid===i){l.push(r.pmt[u]);break}if(l.length>0){var d=l[0].streamType;t.Media(e,n,d)}else n.unknownPIDs=!0}}}},{key:"readHeader",value:function(e,t){var n={};n.sync=e.readUint8();var r=e.readUint16();n.error=r>>>15,n.payload=r>>>14&1,n.priority=r>>>13&1,n.pid=8191&r,r=e.readUint8(),n.scrambling=r>>6&3,n.adaptation=r>>4&3,n.continuity=15&r,n.packet=0===n.pid?"PAT":"MEDIA",t.header=n}},{key:"PAT",value:function(e,t,n){var r={},i=e.readUint8();e.skip(i),i=e.readUint8(),r.tabelID=i,i=e.readUint16(),r.error=i>>>7,r.zero=i>>>6&1,r.sectionLength=4095&i,r.streamID=e.readUint16(),r.current=1&e.readUint8(),r.sectionNumber=e.readUint8(),r.lastSectionNumber=e.readUint8();for(var a=(r.sectionLength-9)/4,o=[],s=0;s<a;s++){var l=e.readUint16(),u=8191&e.readUint16();o.push({program:l,pid:u,type:0===l?"network":"mapPID"})}o.length>0&&(n.pat=n.pat.concat(o)),r.list=o,r.program=e.readUint16(),r.pid=8191&e.readUint16(),t.payload=r}},{key:"PMT",value:function(e,t,n){t.header.packet="PMT";var r=e.position;r+=e.getUint8(r);var i=(r+=1)+3+((15&e.getUint8(r+1))<<8|e.getUint8(r+2))-4;r+=12+((15&e.getUint8(r+10))<<8|e.getUint8(r+11));for(var a=[];r<i;){var o=(31&e.getUint8(r+1))<<8|e.getUint8(r+2);a.push({streamType:e.getUint8(r),pid:o}),r+=5+((15&e.getUint8(r+3))<<8|e.getUint8(r+4))}n.pmt=a,e.skip(i+4)}},{key:"Media",value:function(e,t,n){var r=t.header,i={},a=nt(st[n],2),o=a[0],s=a[1];if(r.streamType=n,r.type=o,r.codec=s,3===r.adaptation&&(i.adaptationLength=e.readUint8(),i.adaptationLength>0)){var l=e.readUint8();i.discontinue=l>>>7,i.access=l>>>6&1,i.priority=l>>>5&1,i.PCR=l>>>4&1,i.OPCR=l>>>3&1,i.splicePoint=l>>>2&1,i.transportPrivate=l>>>1&1,i.adaptationField=1&l;var u=e.position;if(1===i.PCR&&(i.programClockBase=e.readUint32()<<1,l=e.readUint16(),i.programClockBase|=l>>>15,i.programClockExtension=511&l),1===i.OPCR&&(i.originProgramClockBase=e.readUint32()<<1,l=e.readUint16(),i.originProgramClockBase+=l>>>15,i.originProgramClockExtension=511&l),1===i.splicePoint&&(i.spliceCountdown=e.readUint8()),1===i.transportPrivate)for(var c=e.readUint8(),d=[],p=0;p<c;p++)d.push(e.readUint8());if(1===i.adaptationField){var f=e.readUint8(),h=e.readUint8(),g=e.position,y=h>>>6&1,v=h>>>5&1;1===h>>>7&&(h=e.readUint16(),i.ltwValid=h>>>15,i.ltwOffset=61439&h),1===y&&(h=e.readUint24(),i.piecewiseRate=4194303&h),1===v&&(h=e.readInt8(),i.spliceType=h>>>4,i.dtsNextAU1=h>>>1&7,i.marker1=1&h,h=e.readUint16(),i.dtsNextAU2=h>>>1,i.marker2=1&h,h=e.readUint16(),i.dtsNextAU3=h),e.skip(f-1-(e.position-g))}var m=i.adaptationLength-1-(e.position-u);e.skip(m)}i.stream=new _(e.buffer.slice(e.position)),t.payload=i}},{key:"PES",value:function(e){var n={},r=e.payload.stream;if(1!==r.readUint24())n.ES={},n.ES.buffer=r;else{var i=r.readUint8();i>=224&&i<=239&&(n.type="video"),i>=192&&i<=223&&(n.type="audio");var a=r.readUint16();if(n.packetLength=a,"video"!==n.type&&"audio"!==n.type)throw new Error("format is not supported");var o=r.readUint8();if(2!==o>>>6)throw new Error("error when parse pes header");o=r.readUint8(),n.ptsDTSFlag=o>>>6,n.escrFlag=o>>>5&1,n.esRateFlag=o>>>4&1,n.dsmFlag=o>>>3&1,n.additionalFlag=o>>>2&1,n.crcFlag=o>>>1&1,n.extensionFlag=1&o,n.pesHeaderLength=r.readUint8();var s=n.pesHeaderLength,l=[];l.push(r.readUint8()),l.push(r.readUint8()),l.push(r.readUint8()),l.push(r.readUint8()),l.push(r.readUint8());var u=536870912*(14&l[0])+4194304*(255&l[1])+16384*(254&l[2])+128*(255&l[3])+(254&l[4])/2;if(n.purePts=u,r.dataview.position-=5,l=[],o=r.readUint8(),l.push(o>>>1&7),o=r.readUint16(),l.push(o>>>1),o=r.readUint16(),l.push(o>>>1),n.pts=l[0]<<30|l[1]<<15|l[2],s-=5,"video"===n.type&&(n.dts=n.pts),3===n.ptsDTSFlag){var c=[];o=r.readUint8(),c.push(o>>>1&7),o=r.readUint16(),c.push(o>>>1),o=r.readUint16(),c.push(o>>>1),n.dts=c[0]<<30|c[1]<<15|c[2],s-=5}if(1===n.escrFlag){var d=[],p=[];o=r.readUint8(),d.push(o>>>3&7),d.push(3&o),o=r.readUint16(),d.push(o>>>13),d.push(3&o),o=r.readUint16(),d.push(o>>>13),p.push(3&o),o=r.readUint8(),p.push(o>>>1),n.escr=300*(d[0]<<30|d[1]<<28|d[2]<<15|d[3]<<13|d[4])+(p[0]<<7|p[1]),s-=6}if(1===n.esRateFlag&&(o=r.readUint24(),n.esRate=o>>>1&4194303,s-=3),1===n.dsmFlag)throw new Error("not support DSM_trick_mode");if(1===n.additionalFlag&&(o=r.readUint8(),n.additionalCopyInfo=127&o,s-=1),1===n.crcFlag&&(n.pesCRC=r.readUint16(),s-=2),1===n.extensionFlag)throw new Error("not support extension");s>0&&r.skip(s),n.dts>n.pts&&(n.dts=n.pts),n.ES=t.ES(r,n.type,e.header.streamType)}return n}},{key:"ES",value:function(e,n,r){var i={};if("video"===n)i.buffer=e;else{if("audio"!==n)throw new Error("ES "+n+" is not supported");15!==r&&17!==r||(i=t.parseADTSHeader(e)),i.buffer=e}return i}},{key:"parseADTSHeader",value:function(e){var n={},r=e.readUint16();if(r>>>4!=4095)throw new Error("aac ES parse Error");return n.id=0==(r>>>3&1)?"MPEG-4":"MPEG-2",n.layer=r>>>1&3,n.absent=1&r,r=e.readUint16(),n.audioObjectType=1+(r>>>14&3),n.profile=n.audioObjectType-1,n.frequencyIndex=r>>>10&15,n.frequence=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350][n.frequencyIndex],n.channel=r>>>6&7,n.frameLength=(3&r)<<11|e.readUint16()>>>5,t.getAudioConfig(n),e.skip(1),n.buffer=e,n}},{key:"TSDT",value:function(e,t,n){t.payload={}}},{key:"CAT",value:function(e,t,n){var r={};r.tableID=e.readUint8();var i=e.readUint16();r.sectionIndicator=i>>>7,r.sectionLength=4095&i,e.skip(2),i=e.readUint8(),r.version=i>>>3,r.currentNextIndicator=1&i,r.sectionNumber=e.readUint8(),r.lastSectionNumber=e.readUint8(),this.sectionLength,r.crc32=e.readUint32(),t.payload=r}},{key:"getAudioConfig",value:function(e){var t=navigator.userAgent.toLowerCase(),n=void 0,r=void 0;e.originAudioObjectType=e.audioObjectType,/firefox/i.test(t)?e.frequencyIndex>=8?(e.audioObjectType=5,n=new Array(4),r=e.frequencyIndex-3):(e.audioObjectType=2,n=new Array(2),r=e.frequencyIndex):-1!==t.indexOf("android")?(e.audioObjectType=2,n=new Array(2),r=e.frequencyIndex):(e.audioObjectType=5,n=new Array(4),e.frequencyIndex>=6?r=e.frequencyIndex-3:(1===e.channel&&(e.audioObjectType=2,n=new Array(2)),r=e.frequencyIndex)),n[0]=e.audioObjectType<<3,n[0]|=(14&e.frequencyIndex)>>1,n[1]=(1&e.frequencyIndex)<<7,n[1]|=e.channel<<3,5===e.audioObjectType&&(n[1]|=(14&r)>>1,n[2]=(1&r)<<7,n[2]|=8,n[3]=0),e.audioConfig=n}}]),t}(et),ut=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),ct=f.DEMUX_EVENTS,dt=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.configs=Object.assign({},t),this.demuxer=null}return ut(e,[{key:"init",value:function(){this.on(ct.DEMUX_START,this.demux.bind(this))}},{key:"initDemuxer",value:function(){this.demuxer.on(lt.EVENTS.METADATA_PARSED,this.onMetaDataParsed.bind(this)),this.demuxer.on(lt.EVENTS.VIDEO_SAMPLE_PARSED,this.onVideoSampleParsed.bind(this)),this.demuxer.on(lt.EVENTS.AUDIO_SAMPLE_PARSED,this.onAudioSampleParsed.bind(this)),this.demuxer.on(lt.EVENTS.SEI_PARSED,this.emit.bind(this,ct.SEI_PARSED))}},{key:"demux",value:function(e,t){if(this._tracks){this._tracks.audioTrack||(this._tracks.audioTrack=new O),this._tracks.videoTrack||(this._tracks.videoTrack=new A),this.demuxer||(this.demuxer=new lt(this._tracks),this.initDemuxer());try{this.demuxer.demux(e,this.inputBuffer,t)}catch(e){this.emit(ct.DEMUX_ERROR,this.TAG,e,!1)}var n=this._tracks.videoTrack&&this._tracks.videoTrack.samples.length?1:0,r=this._tracks.audioTrack&&this._tracks.audioTrack.samples.length?1:0;this.emit(ct.DEMUX_COMPLETE,n,r)}}},{key:"onMetaDataParsed",value:function(e,t){var n=this._tracks,r=n.videoTrack,i=n.audioTrack,a=r;switch(e){case"video":a=r;break;case"audio":a=i;break;default:a=r}a.meta=t,this.emit(ct.METADATA_PARSED,e,t)}},{key:"onVideoSampleParsed",value:function(e){e.isKeyframe&&this.emit(ct.ISKEYFRAME,e.pts),this._tracks.videoTrack.samples.push(e)}},{key:"onAudioSampleParsed",value:function(e){this._tracks.audioTrack.samples.push(e)}},{key:"destroy",value:function(){this.off(ct.DEMUX_START,this.demux),this.configs={},this.demuxer&&this.demuxer.destroy()}},{key:"inputBuffer",get:function(){return this._context.getInstance(this.configs.inputbuffer)}},{key:"_tracks",get:function(){return this._context.getInstance("TRACKS")}}]),e}(),pt=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),ft=/^(?:[a-zA-Z0-9+\-.]+:)?\/\//,ht=/^((?:[a-zA-Z0-9+\-.]+:)?\/\/[^/?#]*)?([^?#]*\/)?/,gt=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return pt(e,null,[{key:"parse",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r={duration:0};if(t&&t.split){var i=t.split(/\r|\n/),a=(i=i.filter((function(e){return e}))).shift();if(!a.match("#EXTM3U"))throw new Error('Invalid m3u8 file: not "#EXTM3U"');a=i.shift();for(var o=!1,s=0;a;){var l=a.match(/#(.[A-Z|-]*):(.*)/),u=a.match(/#(.[A-Z|-]*)/);if(u&&l&&l.length>2)switch(l[1]){case"EXT-X-VERSION":r.version=parseInt(l[2]);break;case"EXT-X-MEDIA-SEQUENCE":r.sequence=parseInt(l[2]);break;case"EXT-X-TARGETDURATION":r.targetduration=parseFloat(l[2]);break;case"EXTINF":s=e.parseFrag(l,i,r,n,o,s),o=!1;break;case"EXT-X-KEY":e.parseDecrypt(l[2],r)}if(u&&u.length>1)switch(u[1]){case"EXT-X-DISCONTINUITY":o=!0;break;case"EXT-X-ENDLIST":var c=r.frags[r.frags.length-1];c.isLast=!0,r.end=!0}a=i[s++]}return r}}},{key:"parseFrag",value:function(e,t,n,r,i,a){n.frags||(n.frags=[]);var o=new H({start:n.duration,duration:parseInt(1e3*parseFloat(e[2]))});if(o.duration<200)return a;n.duration+=o.duration;var s=t[a++];if((s.match(/#(.*):(.*)/)||s.match(/^#/))&&(s=t[a++]),o.url=function(e,t){if(!t||!e||ft.test(e))return e;var n=ht.exec(t);return n?"/"===e[0]?n[1]+e:n[1]+n[2]+e:e}(s,r),o.discontinue=i,n.frags.length){var l=n.frags[n.frags.length-1];o.id=l.id+1,o.cc=i?l.cc+1:l.cc}else o.id=n.sequence||1,o.cc=0;return n.frags.push(o),a}},{key:"parseDecrypt",value:function(e,t){t.encrypt={};var n=e.split(",");for(var r in n){var i=n[r];if(i.match(/METHOD=(.*)/)&&(t.encrypt.method=i.match(/METHOD=(.*)/)[1]),i.match(/URI="(.*)"/)&&(t.encrypt.uri=i.match(/URI="(.*)"/)[1]),i.match(/IV=0x(.*)/)){var a=i.match(/IV=0x(.*)/)[1],o=Math.ceil(a.length/2);t.encrypt.ivb=new Uint8Array(o);for(var s=o-1;s>=0;s--){var l=parseInt(a.substr(2*s,2),16);t.encrypt.ivb[s]=l}t.encrypt.iv=a}}}},{key:"isHTTPS",value:function(e){return/^https:\/\//i.test(e)}}]),e}(),yt=W((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){for(var t=0,n=arguments.length,r=Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];var a=!0,o=!1,s=void 0;try{for(var l,u=r[Symbol.iterator]();!(a=(l=u.next()).done);a=!0){var c=l.value;t+=c.length}}catch(e){o=!0,s=e}finally{try{!a&&u.return&&u.return()}finally{if(o)throw s}}var d=new e(t),p=0,f=!0,h=!1,g=void 0;try{for(var y,v=r[Symbol.iterator]();!(f=(y=v.next()).done);f=!0){var m=y.value;d.set(m,p),p+=m.length}}catch(e){h=!0,g=e}finally{try{!f&&v.return&&v.return()}finally{if(h)throw g}}return d}}));q(yt);var vt=q(W((function(e){var t,n=(t=yt)&&t.__esModule?t:{default:t};e.exports=n.default}))),mt=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),xt=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.buffer=t||new Uint8Array(0)}return mt(e,[{key:"write",value:function(){for(var e=this,t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];n.forEach((function(t){e.buffer=vt(Uint8Array,e.buffer,t)}))}}],[{key:"writeUint32",value:function(e){return new Uint8Array([e>>24,e>>16&255,e>>8&255,255&e])}},{key:"readAsInt",value:function(e){var t="";return e.forEach((function(e){t+=e.toString(16).padStart(2,"0")})),parseInt(t,16)}}]),e}(),bt=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),_t=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return bt(e,null,[{key:"size",value:function(e){return xt.writeUint32(e)}},{key:"initBox",value:function(t,n){for(var r=new xt,i=arguments.length,a=Array(i>2?i-2:0),o=2;o<i;o++)a[o-2]=arguments[o];return r.write.apply(r,[e.size(t),e.type(n)].concat(a)),r.buffer}},{key:"extension",value:function(e,t){return new Uint8Array([e,t>>16&255,t>>8&255,255&t])}},{key:"ftyp",value:function(){return e.initBox(24,"ftyp",new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,97,118,99,49]))}},{key:"ftypHEVC",value:function(){return e.initBox(24,"ftyp",new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,100,97,115,104]))}},{key:"moov",value:function(t){var n=t.type,r=t.meta,i=8,a=e.mvhd(r.duration,r.timescale),o=void 0;o="video"===n?e.videoTrak(r):e.audioTrak(r);var s=e.mvex(r.duration,r.timescale||1e3,r.id);return[a,o,s].forEach((function(e){i+=e.byteLength})),e.initBox(i,"moov",a,o,s)}},{key:"mvhd",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3,r=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,n>>>24&255,n>>>16&255,n>>>8&255,255&n,t>>>24&255,t>>>16&255,t>>>8&255,255&t,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return e.initBox(8+r.length,"mvhd",new Uint8Array(r))}},{key:"videoTrak",value:function(t){var n=8,r=e.tkhd({id:1,duration:t.duration,timescale:t.timescale||1e3,width:t.presentWidth,height:t.presentHeight,type:"video"}),i=e.mdia({type:"video",timescale:t.timescale||1e3,duration:t.duration,avcc:t.avcc,parRatio:t.parRatio,width:t.presentWidth,height:t.presentHeight,streamType:t.streamType});return[r,i].forEach((function(e){n+=e.byteLength})),e.initBox(n,"trak",r,i)}},{key:"audioTrak",value:function(t){var n=8,r=e.tkhd({id:2,duration:t.duration,timescale:t.timescale||1e3,width:0,height:0,type:"audio"}),i=e.mdia({type:"audio",timescale:t.timescale||1e3,duration:t.duration,channelCount:t.channelCount,samplerate:t.sampleRate,config:t.config});return[r,i].forEach((function(e){n+=e.byteLength})),e.initBox(n,"trak",r,i)}},{key:"tkhd",value:function(t){var n=t.id,r=t.duration,i=t.width,a=t.height,o=new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,n>>>24&255,n>>>16&255,n>>>8&255,255&n,0,0,0,0,r>>>24&255,r>>>16&255,r>>>8&255,255&r,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,i>>>8&255,255&i,0,0,a>>>8&255,255&a,0,0]);return e.initBox(8+o.byteLength,"tkhd",o)}},{key:"edts",value:function(t){var n=new xt,r=t.duration,i=t.mediaTime;return n.write(e.size(36),e.type("edts")),n.write(e.size(28),e.type("elst")),n.write(new Uint8Array([0,0,0,1,r>>24&255,r>>16&255,r>>8&255,255&r,i>>24&255,i>>16&255,i>>8&255,255&i,0,0,0,1])),n.buffer}},{key:"mdia",value:function(t){var n=8,r=e.mdhd(t.timescale,t.duration),i=e.hdlr(t.type),a=e.minf(t);return[r,i,a].forEach((function(e){n+=e.byteLength})),e.initBox(n,"mdia",r,i,a)}},{key:"mdhd",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1e3,n=arguments[1],r=new Uint8Array([0,0,0,0,0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t,n>>>24&255,n>>>16&255,n>>>8&255,255&n,85,196,0,0]);return e.initBox(12+r.byteLength,"mdhd",e.extension(0,0),r)}},{key:"hdlr",value:function(t){var n=[0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0];return"audio"===t&&(n.splice.apply(n,[8,4].concat([115,111,117,110])),n.splice.apply(n,[24,13].concat([83,111,117,110,100,72,97,110,100,108,101,114,0]))),e.initBox(8+n.length,"hdlr",new Uint8Array(n))}},{key:"minf",value:function(t){var n=8,r="video"===t.type?e.vmhd():e.smhd(),i=e.dinf(),a=e.stbl(t);return[r,i,a].forEach((function(e){n+=e.byteLength})),e.initBox(n,"minf",r,i,a)}},{key:"vmhd",value:function(){return e.initBox(20,"vmhd",new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0]))}},{key:"smhd",value:function(){return e.initBox(16,"smhd",new Uint8Array([0,0,0,0,0,0,0,0]))}},{key:"dinf",value:function(){var t=new xt;return t.write(e.size(36),e.type("dinf"),e.size(28),e.type("dref"),new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1])),t.buffer}},{key:"stbl",value:function(t){var n=8,r=e.stsd(t),i=e.stts(),a=e.stsc(),o=e.stsz(),s=e.stco();return[r,i,a,o,s].forEach((function(e){n+=e.byteLength})),e.initBox(n,"stbl",r,i,a,o,s)}},{key:"stsd",value:function(t){var n=void 0;return n="audio"===t.type?e.mp4a(t):36===t.streamType?e.hvc1(t):e.avc1(t),e.initBox(16+n.byteLength,"stsd",e.extension(0,0),new Uint8Array([0,0,0,1]),n)}},{key:"mp4a",value:function(t){var n=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,t.channelCount,0,16,0,0,0,0,t.samplerate>>8&255,255&t.samplerate,0,0]),r=e.esds(t.config);return e.initBox(8+n.byteLength+r.byteLength,"mp4a",n,r)}},{key:"esds",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[43,146,8,0],n=t.length,r=new xt,i=new Uint8Array([0,0,0,0,3,23+n,0,1,0,4,15+n,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([n]).concat(t).concat([6,1,2]));return r.write(e.size(8+i.byteLength),e.type("esds"),i),r.buffer}},{key:"avc1",value:function(t){var n=new xt,r=t.width,i=t.height,a=t.parRatio.width,o=t.parRatio.height,s=t.avcc,l=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,r>>8&255,255&r,i>>8&255,255&i,0,72,0,0,0,72,0,0,0,0,0,0,0,1,18,100,97,105,108,121,109,111,116,105,111,110,47,104,108,115,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),u=new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192]),c=new Uint8Array([a>>24,a>>16&255,a>>8&255,255&a,o>>24,o>>16&255,o>>8&255,255&o]);return n.write(e.size(40+l.byteLength+s.byteLength+u.byteLength),e.type("avc1"),l,e.size(8+s.byteLength),e.type("avcC"),s,e.size(20),e.type("btrt"),u,e.size(16),e.type("pasp"),c),n.buffer}},{key:"hvc1",value:function(t){var n=new xt,r=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,t.width>>8&255,255&t.width,t.height>>8&255,255&t.height,0,72,0,0,0,72,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255,0,0,0,122,104,118,99,67,1,1,96,0,0,0,144,0,0,0,0,0,93,240,0,252,253,248,248,0,0,15,3,160,0,1,0,24,64,1,12,1,255,255,1,96,0,0,3,0,144,0,0,3,0,0,3,0,93,153,152,9,161,0,1,0,45,66,1,1,1,96,0,0,3,0,144,0,0,3,0,0,3,0,93,160,2,128,128,45,22,89,153,164,147,43,154,128,128,128,130,0,0,3,0,2,0,0,3,0,50,16,162,0,1,0,7,68,1,193,114,180,98,64]);return n.write(e.size(8+r.byteLength+10),e.type("hvc1"),r,e.size(10),e.type("fiel"),new Uint8Array([1,0])),n.buffer}},{key:"stts",value:function(){var t=new Uint8Array([0,0,0,0,0,0,0,0]);return e.initBox(16,"stts",t)}},{key:"stsc",value:function(){var t=new Uint8Array([0,0,0,0,0,0,0,0]);return e.initBox(16,"stsc",t)}},{key:"stco",value:function(){var t=new Uint8Array([0,0,0,0,0,0,0,0]);return e.initBox(16,"stco",t)}},{key:"stsz",value:function(){var t=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]);return e.initBox(20,"stsz",t)}},{key:"mvex",value:function(t){var n=arguments[2],r=new xt,i=xt.writeUint32(t);return r.write(e.size(56),e.type("mvex"),e.size(16),e.type("mehd"),e.extension(0,0),i,e.trex(n)),r.buffer}},{key:"trex",value:function(t){var n=new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return e.initBox(8+n.byteLength,"trex",n)}},{key:"moof",value:function(t){var n=8,r=e.mfhd(),i=e.traf(t);return[r,i].forEach((function(e){n+=e.byteLength})),e.initBox(n,"moof",r,i)}},{key:"mfhd",value:function(){var t=xt.writeUint32(e.sequence);return e.sequence+=1,e.initBox(16,"mfhd",e.extension(0,0),t)}},{key:"traf",value:function(t){var n=8,r=e.tfhd(t.id),i=e.tfdt(t.time),a=e.sdtp(t),o=e.trun(t,a.byteLength);return[r,i,o,a].forEach((function(e){n+=e.byteLength})),e.initBox(n,"traf",r,i,o,a)}},{key:"tfhd",value:function(t){var n=xt.writeUint32(t);return e.initBox(16,"tfhd",e.extension(0,0),n)}},{key:"tfdt",value:function(t){return e.initBox(16,"tfdt",e.extension(0,0),xt.writeUint32(t))}},{key:"trun",value:function(t,n){var r=new xt,i=xt.writeUint32(t.samples.length),a=xt.writeUint32(92+16*t.samples.length+n);return r.write(e.size(20+16*t.samples.length),e.type("trun"),new Uint8Array([0,0,15,1]),i,a),t.samples.forEach((function(e){var t=e.flags;r.write(new Uint8Array([e.duration>>>24&255,e.duration>>>16&255,e.duration>>>8&255,255&e.duration,e.size>>>24&255,e.size>>>16&255,e.size>>>8&255,255&e.size,t.isLeading<<2|t.dependsOn,t.isDependedOn<<6|t.hasRedundancy<<4|t.isNonSync,0,0,e.cts>>>24&255,e.cts>>>16&255,e.cts>>>8&255,255&e.cts]))})),r.buffer}},{key:"sdtp",value:function(t){var n=new xt;return n.write(e.size(12+t.samples.length),e.type("sdtp"),e.extension(0,0)),t.samples.forEach((function(e){var t=e.flags,r=t.isLeading<<6|t.dependsOn<<4|t.isDependedOn<<2|t.hasRedundancy;n.write(new Uint8Array([r]))})),n.buffer}},{key:"mdat",value:function(t){var n=new xt,r=8;t.samples.forEach((function(e){r+=e.size})),n.write(e.size(r),e.type("mdat"));var i=new Uint8Array(r),a=0;return i.set(n.buffer,a),a+=8,t.samples.forEach((function(e){e.buffer.forEach((function(e){i.set(e,a),a+=e.byteLength}))})),i}}]),e}();_t.type=function(e){return new Uint8Array([e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)])},_t.sequence=1;var kt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Et=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),wt=function(e){function t(e){var n=e.videoMeta,r=e.audioMeta,i=e.curTime;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var a=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==(void 0===t?"undefined":kt(t))&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return a.TAG="Fmp4Remuxer",a._dtsBase=1e3*i,a._videoMeta=n,a._audioMeta=r,a._audioDtsBase=null,a._videoDtsBase=null,a._isDtsBaseInited=!1,a.isFirstVideo=!0,a.isFirstAudio=!0,a.videoAllDuration=0,a.audioAllDuration=0,a.audioRemuxed=0,a.videoRemuxed=0,a.mp4Durations={keys:[]},a}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+(void 0===t?"undefined":kt(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),Et(t,[{key:"destroy",value:function(){this._dtsBase=-1,this._isDtsBaseInited=!1,this.mp4Durations={keys:[]},this.removeAllListeners()}},{key:"remux",value:function(e,t){!this._isDtsBaseInited&&this.calcDtsBase(e,t),this.remuxVideo(t),this.remuxAudio(e),he.groupEnd()}},{key:"resetDtsBase",value:function(){this._dtsBase=0}},{key:"seek",value:function(e){he.log(this.TAG,"set dtsBase for seek(),time=",e),this._isDtsBaseInited?(this._isDtsBaseInited=!1,this._dtsBase=1e3*e):this._dtsBase=1e3*e,this._audioDtsBase=this._videoDtsBase=null}},{key:"remuxInitSegment",value:function(e,t){he.log(this.TAG,"remuxInitSegment: type=",e);var n=new xt,r=36===t.streamType?_t.ftypHEVC():_t.ftyp(),i=_t.moov({type:e,meta:t});return n.write(r,i),n}},{key:"calcDtsBase",value:function(e,t){if(!e&&t.samples.length){var n=t.samples[0],r=void 0;return n.options&&n.options.start&&(r=n.options.start),this._videoDtsBase=n.dts-(r||this._dtsBase),void(this._isDtsBaseInited=!0)}if(e&&e.samples.length||t&&t.samples.length){var i=null,a=null,o=null;if(e&&e.samples&&e.samples.length){var s=e.samples[0];i=s.dts,s.options&&s.options.start&&(o=s.options.start)}if(t&&t.samples&&t.samples.length){var l=t.samples[0];a=l.dts,l.options&&l.options.start&&(o=l.options.start)}var u=i-a;i&&u<0&&null!==o&&t.samples.forEach((function(e){e.dts-=u,e.pts&&(e.pts-=u)})),this._videoDtsBase=(a||i)-(o||this._dtsBase),this._audioDtsBase=(i||a)-(o||this._dtsBase),this._dtsBase=Math.min(a,i),this._isDtsBaseInited=!0,he.log(this.TAG,"calcDtsBase"),he.log(this.TAG,"video first dts: "+a+" , start:"+o+" , _videoDtsBase:"+this._videoDtsBase+" , _dtsBase:"+this._dtsBase),he.log(this.TAG,"audio first dts: "+i+" , start:"+o+" , _audioDtsBase:"+this._audioDtsBase+", _dtsBase:"+this._dtsBase)}}},{key:"remuxVideo",value:function(e){var n=this,r=e||{};if(e&&e.samples&&e.samples.length){var i=r.samples;if(r.meta){for(var a=-1,o=null,s=[],l={samples:[]},u=1e4;i.length&&u-- >0;){var c=i.shift(),d=c.isKeyframe,p=c.options;if(!this.isFirstVideo&&p&&p.meta){o=this.remuxInitSegment("video",p.meta),p.meta=null,i.unshift(c),p.isContinue||(this._videoDtsBase=0);break}var f=Math.max(0,c.dts-this.videoDtsBase);-1===a&&(a=f);var h=void 0,g=void 0;void 0!==c.pts&&(h=(g=c.pts-this._dtsBase)-f),void 0!==c.cts&&(g=c.cts+f,h=c.cts);var y={buffer:[],size:0},v=0;v=c.duration?c.duration:i.length>=1?i[0].dts-this.videoDtsBase-f:s.length>=1?s[s.length-1].duration:this._videoMeta.refSampleDuration,this.videoAllDuration+=v,he.long&&he.log(this.TAG,"video dts "+f,"pts "+g,"cts: "+h,d,"originDts "+c.originDts,"duration "+v),v>=0&&(l.samples.push(y),y.buffer.push(c.data),y.size+=c.data.byteLength,s.push({dts:f,cts:h,pts:g,data:c.data,size:c.data.byteLength,isKeyframe:d,duration:v,flags:{isLeading:0,dependsOn:d?2:1,isDependedOn:d?1:0,hasRedundancy:0,isNonSync:d?0:1},originDts:f,type:"video"}),this.mp4Durations[g]=v,this.mp4Durations.keys.push(g)),d&&this.emit(t.EVENTS.RANDOM_ACCESS_POINT,g)}if(this.mp4Durations.keys.length>1e4){var m=this.mp4Durations;this.mp4Durations={},this.mp4Durations.keys=m.keys.slice(-100),this.mp4Durations.keys.forEach((function(e){n.mp4Durations[e]=m[e]}))}s.length&&he.log(this.TAG,"remux to mp4 video:",[a/1e3,s[s.length-1].dts/1e3]);var x=new xt;if(s.length&&r.meta){var b=_t.moof({id:r.meta.id,time:a,samples:s}),_=_t.mdat(l);x.write(b,_),this.segmentRemuxed("video",x,s[s.length-1].pts-s[0].pts)}if(o&&(this.segmentRemuxed("video",o),i.length))return r.samples=i,this.remuxVideo(r);this.isFirstVideo=!1,this.emit(t.EVENTS.TRACK_REMUXED,"video",x),r.samples=[],r.length=0}}}},{key:"remuxAudio",value:function(e){var n=(e||{}).samples,r=-1,i=[],a=null,o={samples:[]};if(n&&n.length){for(var s=1e4,l=!1;n.length&&s-- >0;){var u=n.shift(),c=u.data,d=u.options;if(!this.isFirstAudio&&d&&d.meta){a=this.remuxInitSegment("audio",d.meta),d.meta=null,n.unshift(u),d.isContinue||(this._audioDtsBase=0);break}var p=Math.max(0,u.dts-this.audioDtsBase),f=u.originDts;l||(r=p,l=!0);var h=0;h=u.duration?u.duration:this._audioMeta.refSampleDurationFixed?this._audioMeta.refSampleDurationFixed:n.length>=1?n[0].dts-this.audioDtsBase-p:i.length>=1?i[i.length-1].duration:this._audioMeta.refSampleDuration,this.audioAllDuration+=h;var g={dts:p,pts:p,cts:0,size:c.byteLength,duration:u.duration?u.duration:h,flags:{isLeading:0,dependsOn:1,isDependedOn:0,hasRedundancy:0,isNonSync:0},isKeyframe:!0,originDts:f,type:"audio"},y={buffer:[],size:0};h>=0&&(y.buffer.push(c),y.size+=c.byteLength,o.samples.push(y),i.push(g))}var v=new xt;if(i.length&&e.meta){he.log(this.TAG,"remux to mp4 audio:",[r/1e3,i[i.length-1].dts/1e3]);var m=_t.moof({id:e.meta.id,time:r,samples:i}),x=_t.mdat(o);v.write(m,x),this.segmentRemuxed("audio",v,i[i.length-1].dts-i[0].dts)}if(a&&(this.segmentRemuxed("audio",a),n.length))return e.samples=n,this.remuxAudio(e);this.isFirstAudio=!1,this.emit(t.EVENTS.TRACK_REMUXED,"audio",v),e.samples=[],e.length=0}}},{key:"segmentRemuxed",value:function(e,n,r){this.emit(t.EVENTS.MEDIA_SEGMENT,e,n,r)}},{key:"videoDtsBase",get:function(){return null!==this._videoDtsBase?this._videoDtsBase:this._dtsBase},set:function(e){this._videoDtsBase=e}},{key:"audioDtsBase",get:function(){return null!==this._audioDtsBase?this._audioDtsBase:this._dtsBase}}],[{key:"EVENTS",get:function(){return{MEDIA_SEGMENT:"MEDIA_SEGMENT",INIT_SEGMENT:"INIT_SEGMENT",RANDOM_ACCESS_POINT:"RANDOM_ACCESS_POINT",TRACK_REMUXED:"TRACK_REMUXED"}}}]),t}(et),Tt=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();function St(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var Ct=f.REMUX_EVENTS,Ot=f.PLAYER_EVENTS,At=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;St(this,e),this.TAG="Mp4Remuxer",this._curTime=t,this.remuxer||this.initRemuxer()}return Tt(e,[{key:"init",value:function(){this.on(Ct.REMUX_MEDIA,this.remux.bind(this)),this.on(Ct.REMUX_METADATA,this.onMetaDataReady.bind(this)),this.on(Ct.DETECT_CHANGE_STREAM,this.resetDtsBase.bind(this)),this.on(Ct.DETECT_FRAG_ID_DISCONTINUE,this.seek.bind(this)),this.on(Ot.SEEK,this.seek.bind(this))}},{key:"initRemuxer",value:function(){this.remuxer=new wt({audioMeta:null,videoMeta:null,curTime:this._curTime}),this.remuxer.on(wt.EVENTS.MEDIA_SEGMENT,this.writeToSource.bind(this)),this.remuxer.on(wt.EVENTS.TRACK_REMUXED,this.onTrackRemuxed.bind(this))}},{key:"remux",value:function(){this.remuxer._videoMeta||(this.remuxer._videoMeta=this.videoMeta,this.remuxer._audioMeta=this.audioMeta);var e=this._context.getInstance("TRACKS"),t=e.audioTrack,n=e.videoTrack;return this.remuxer.remux(t,n)}},{key:"resetDtsBase",value:function(){this.remuxer&&this.remuxer.resetDtsBase()}},{key:"seek",value:function(e){this.remuxer&&this.remuxer.seek(e)}},{key:"onMetaDataReady",value:function(e){this.remuxer||this.initRemuxer();var t=void 0;t="audio"===e?this._context.getInstance("TRACKS").audioTrack:this._context.getInstance("TRACKS").videoTrack;var n=this._context.getInstance("PRE_SOURCE_BUFFER"),r=n.getSource(e);r||(r=n.createSource(e)),r.mimetype=t.meta.codec,r.init=this.remuxer.remuxInitSegment(e,t.meta),this.emit(Ct.INIT_SEGMENT,e)}},{key:"onTrackRemuxed",value:function(e){this.emit(Ct.MEDIA_SEGMENT,e)}},{key:"writeToSource",value:function(e,t,n){var r=this._context.getInstance("PRE_SOURCE_BUFFER"),i=r.getSource(e);i||(i=r.createSource(e)),i.data.push(t),n&&(i.bufferDuration=n+(i.bufferDuration||0))}},{key:"destroy",value:function(){this.remuxer&&this.remuxer.destroy(),this.remuxer=null}},{key:"videoMeta",get:function(){return this._context.getInstance("TRACKS").videoTrack.meta}},{key:"audioMeta",get:function(){return this._context.getInstance("TRACKS").audioTrack.meta}}]),e}(),Rt={Mse:ae,Tracks:C,RemuxedBufferManager:x,XgBuffer:D,FetchLoader:pe,Compatibility:Qe,Mp4Remuxer:At,Crypto:ne,M3U8Parser:gt,TsDemuxer:dt,Playlist:g},Dt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Lt=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),Pt=function e(t,n,r){null===t&&(t=Function.prototype);var i=Object.getOwnPropertyDescriptor(t,n);if(void 0===i){var a=Object.getPrototypeOf(t);return null===a?void 0:e(a,n,r)}if("value"in i)return i.value;var o=i.get;return void 0!==o?o.call(r):void 0},Mt=f.HlsAllowedEvents,It=f.REMUX_EVENTS,Bt=e.util,Ut=function(t){function n(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n);var t=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==(void 0===t?"undefined":Dt(t))&&"function"!=typeof t?e:t}(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,e));return t.hlsOps={},t.hlsOps=Object.assign(t.hlsOps,Rt),Bt.deepCopy(t.hlsOps,e),t._played=!1,t}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+(void 0===t?"undefined":Dt(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(n,t),Lt(n,[{key:"_initEvents",value:function(){var e=this;this.__core__.once(It.INIT_SEGMENT,(function(){var t=e._context.getInstance("MSE");if(!e.started){var r=Bt.createDom("xg-live","正在直播",{},"xgplayer-live");Bt.addClass(e.root,"xgplayer-is-live"),e.controls.appendChild(r)}e.started=!0,Pt(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"start",e).call(e,t.url)}))}},{key:"start",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.url;e&&!this.started&&(this._context||(this._context=new Q(this,this.hlsOps,Mt)),this.hlsOps||(this.hlsOps={},this.hlsOps=Object.assign(this.hlsOps,Rt),Bt.deepCopy(this.hlsOps,this.config),this._played=!1),this.__core__=this._context.registry("HLS_LIVE_CONTROLLER",Ee)({player:this,container:this.video,preloadTime:this.config.preloadTime}),this._context.init(),this.url=e,this.__core__.load(e),this._initEvents(),this.started=!0)}},{key:"play",value:function(){var e=this;if(this._played)return this.src=this.config.url,void this.once("canplay",(function(){e.video.play()}));this._played=!0,Pt(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"play",this).call(this)}},{key:"destroy",value:function(){var e=this;this._context&&this._context.destroy();var t=new Promise((function(t){e.__core__&&e.__core__.mse?e.__core__.mse.destroy().then((function(){setTimeout((function(){t()}),50)})):setTimeout((function(){t()}),50)}));return Pt(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"destroy",this).call(this),t}},{key:"src",set:function(e){var t=this;this.onWaiting=this.onWaiting.bind(this),this.__core__.mse.destroy().then((function(){t._context.destroy(),t._context=null,t.started=!1,t.video.currentTime=0,t.start(e)}))}}],[{key:"isSupported",value:function(){return window.MediaSource&&window.MediaSource.isTypeSupported('video/mp4; codecs="avc1.42E01E,mp4a.40.2"')}},{key:"install",value:function(t,n){return e.install(t,n)}}]),n}(e);Ut.install=e.install.bind(e);var jt=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),Nt=Ke,Ft=je,zt=f.LOADER_EVENTS,Vt=f.DEMUX_EVENTS,Gt=f.HLS_EVENTS,Ht=f.CRYTO_EVENTS,qt=/#EXT-X-STREAM-INF:([^\n\r]*)[\r\n]+([^\r\n]+)/g,Wt=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.configs=Object.assign({},t),this.url="",this.sequence=0,this._playlist=null,this.retrytimes=this.configs.retrytimes||3,this.preloadTime=this.configs.preloadTime,this.container=this.configs.container,this._m3u8lasttime=0,this._timmer=setInterval(this._checkStatus.bind(this),50),this._lastCheck=0,this.m3u8Text=null}return jt(e,[{key:"init",value:function(){var e=this._player.hlsOps,t=e.XgBuffer,n=e.Tracks,r=e.Playlist,i=e.FetchLoader,a=e.TsDemuxer;this._context.registry("M3U8_BUFFER",t),this._context.registry("TS_BUFFER",t),this._context.registry("TRACKS",n),this._playlist=this._context.registry("PLAYLIST",r)({autoclear:!0}),this._m3u8loader=this._context.registry("M3U8_LOADER",i)({buffer:"M3U8_BUFFER",readtype:1}),this._tsloader=this._context.registry("TS_LOADER",i)({buffer:"TS_BUFFER",readtype:3}),this._context.registry("TS_DEMUXER",a)({inputbuffer:"TS_BUFFER"}),this.initEvents()}},{key:"initEvents",value:function(){this.on(zt.LOADER_COMPLETE,this._onLoadComplete.bind(this)),this.on(Vt.METADATA_PARSED,this._onMetadataParsed.bind(this)),this.on(Vt.SEI_PARSED,this._handleSEIParsed.bind(this)),this.on(Vt.DEMUX_COMPLETE,this._onDemuxComplete.bind(this)),this.on(zt.LOADER_ERROR,this._onLoadError.bind(this)),this.on(Vt.DEMUX_ERROR,this._onDemuxError.bind(this))}},{key:"_onError",value:function(e,t,n,r){var i={errorType:e,errorDetails:"["+t+"]: "+(n?n.message:""),errorFatal:r};this._player.emit("HLS_ERROR",i)}},{key:"_onDemuxComplete",value:function(){var e=this;if(this._player.video){var t=this._context.getInstance("TRACKS"),n=t.videoTrack,r=t.audioTrack;n.samples.forEach((function(t){if(!t.analyzed){t.analyzed=!0;var r=new _(t.data.buffer),i=void 0,a=(i=e._isHEVC(n.meta)?Nt.getHvccNals(r):Ft.getNalunits(r)).reduce((function(e,t){return e+4+t.body.byteLength}),0),o=new Uint8Array(a),s=0;i.forEach((function(e){o.set([0,0,0,1],s),s+=4,o.set(new Uint8Array(e.body),s),s+=e.body.byteLength})),t.data=o}})),this._player.video.onDemuxComplete(n,r)}}},{key:"_onMetadataParsed",value:function(e){if("audio"===e){var t=this._context.getInstance("TRACKS").audioTrack;t&&t.meta&&this._setMetaToAudio(t.meta)}else{var n=this._context.getInstance("TRACKS").videoTrack;n&&n.meta&&this._setMetaToVideo(n.meta)}}},{key:"_setMetaToAudio",value:function(e){this._player.video&&this._player.video.setAudioMeta(e)}},{key:"_setMetaToVideo",value:function(e){this._player.video&&this._player.video.setVideoMeta(e)}},{key:"_onLoadError",value:function(e,t){!this._tsloader.loading&&!this._m3u8loader.loading&&this.retrytimes>1?(this.retrytimes--,this._onError(zt.LOADER_ERROR,e,t,!1)):this.retrytimes<=1&&(this._onError(zt.LOADER_ERROR,e,t,!0),this.emit(Gt.RETRY_TIME_EXCEEDED),this._player.video&&this._player.video.handleEnded())}},{key:"_onDemuxError",value:function(e,t,n){void 0===n&&(n=!0),this._onError(zt.LOADER_ERROR,e,t,n)}},{key:"_handleSEIParsed",value:function(e){this._player.emit("SEI_PARSED",e)}},{key:"_onLoadComplete",value:function(e){if("M3U8_BUFFER"===e.TAG){var t=void 0;try{this.m3u8Text=e.shift();var n=qt.exec(this.m3u8Text);n&&n[2]?this.load(n[2]):t=this._player.hlsOps.M3U8Parser.parse(this.m3u8Text,this.url)}catch(e){this._onError("M3U8_PARSER_ERROR","M3U8_PARSER",e,!1)}if(!t)return void(this.retrytimes>0?(this.retrytimes--,this._preload()):(this.emit(Gt.RETRY_TIME_EXCEEDED),this._player.video&&this._player.video.handleEnded()));try{this._playlist.pushM3U8(t,!0)}catch(e){this._onError("M3U8_PARSER_ERROR","PLAYLIST",e,!1)}if(this._playlist.encrypt&&this._playlist.encrypt.uri&&!this._playlist.encrypt.key){var r=this._player.hlsOps,i=r.XgBuffer,a=r.FetchLoader;this._context.registry("DECRYPT_BUFFER",i)(),this._context.registry("KEY_BUFFER",i)(),this._tsloader.buffer="DECRYPT_BUFFER",this._keyLoader=this._context.registry("KEY_LOADER",a)({buffer:"KEY_BUFFER",readtype:3}),this.emitTo("KEY_LOADER",zt.LADER_START,this._playlist.encrypt.uri)}else this._m3u8Loaded(t)}else if("TS_BUFFER"===e.TAG)this.retrytimes=this.configs.retrytimes||3,this._playlist.downloaded(this._tsloader.url,!0),this.emit(Vt.DEMUX_START);else if("DECRYPT_BUFFER"===e.TAG)this.retrytimes=this.configs.retrytimes||3,this._playlist.downloaded(this._tsloader.url,!0),this.emitTo("CRYPTO",Ht.START_DECRYPT);else if("KEY_BUFFER"===e.TAG){var o=this._player.hlsOps.Crypto;this.retrytimes=this.configs.retrytimes||3,this._playlist.encrypt.key=e.shift(),this._crypto=this._context.registry("CRYPTO",o)({key:this._playlist.encrypt.key,iv:this._playlist.encrypt.ivb,method:this._playlist.encrypt.method,inputbuffer:"DECRYPT_BUFFER",outputbuffer:"TS_BUFFER"}),this._crypto.on(Ht.DECRYPTED,this._onDcripted.bind(this))}}},{key:"_onDcripted",value:function(){this.emit(Vt.DEMUX_START)}},{key:"_m3u8Loaded",value:function(e){this.preloadTime||(this.preloadTime=this._playlist.targetduration?this._playlist.targetduration:5),this._playlist.fragLength>0&&this._playlist.sequence<e.sequence?this.retrytimes=this.configs.retrytimes||3:this.retrytimes>0?(this.retrytimes--,this._preload()):(this.emit(Gt.RETRY_TIME_EXCEEDED),this._player.video&&this._player.video.handleEnded())}},{key:"_checkStatus",value:function(){if(!(this.retrytimes<1&&(new Date).getTime()-this._lastCheck<1e4))if(this._lastCheck=(new Date).getTime(),this.container.buffered.length<1)this._preload();else{var e=this.container.currentTime;this.container.readyState<=2&&this._preload(),e>this.container.buffered.end(this.container.buffered.length-1)-this.preloadTime&&this._preload()}}},{key:"_preload",value:function(){if(!this._tsloader.loading&&!this._m3u8loader.loading){var e=this._playlist.getTs();if(!e||e.downloaded||e.downloading){var t=this.preloadTime?this.preloadTime:0,n=(new Date).getTime();(!e||e.downloaded)&&(n-this._m3u8lasttime)/1e3>t&&(this._m3u8lasttime=n,this.emitTo("M3U8_LOADER",zt.LADER_START,this.url))}else this._playlist.downloading(e.url,!0),this.emitTo("TS_LOADER",zt.LADER_START,e.url)}}},{key:"_isHEVC",value:function(e){return e&&"hev1.1.6.L93.B0"===e.codec}},{key:"load",value:function(e){this.url=e,this._preload()}},{key:"destroy",value:function(){clearInterval(this._timmer),this.off(zt.LOADER_COMPLETE,this._onLoadComplete),this.off(Vt.METADATA_PARSED,this._onMetadataParsed),this.off(Vt.DEMUX_COMPLETE,this._onDemuxComplete),this.m3u8Text=null}}]),e}(),Yt={Tracks:C,XgBuffer:D,FetchLoader:pe,Crypto:ne,M3U8Parser:gt,TsDemuxer:dt,Playlist:g},Xt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Kt=function e(t,n,r){null===t&&(t=Function.prototype);var i=Object.getOwnPropertyDescriptor(t,n);if(void 0===i){var a=Object.getPrototypeOf(t);return null===a?void 0:e(a,n,r)}if("value"in i)return i.value;var o=i.get;return void 0!==o?o.call(r):void 0},$t=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),Zt=f.HlsAllowedEvents,Jt=function(t){function n(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n),t.mediaType||(t.mediaType="live-video",t.videoConfig?t.videoConfig.preloadtime=t.preloadTime||5:t.videoConfig={preloadtime:t.preloadTime||5});var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==(void 0===t?"undefined":Xt(t))&&"function"!=typeof t?e:t}(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,t));return r.hlsOps={},r.util=e.util,r.hlsOps=Object.assign(r.hlsOps,Yt),r.util.deepCopy(r.hlsOps,t),r.playerInited||r.initPlayer(),r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+(void 0===t?"undefined":Xt(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(n,t),$t(n,null,[{key:"isSupported",value:function(){var e="WebAssembly"in window,t=!1;return"customElements"in window&&window.customElements.define&&(t=window.customElements.get("live-video")),e&&t}}]),$t(n,[{key:"initPlayer",value:function(){this.video.width=Number.parseInt(this.hlsOps.width||600),this.video.height=Number.parseInt(this.hlsOps.height||337.5),this.video.style.outline="none",this.playerInited=!0}},{key:"_initEvents",value:function(){var e=this;this.once("canplay",(function(){e.video.play()}))}},{key:"start",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.hlsOps.url;e&&!this.started&&(this.playerInited||this.initPlayer(),this._context||(this._context=new Q(Zt)),this.__core__=this._context.registry("HLS_LIVE_CONTROLLER",Wt)({player:this,container:this.video,preloadTime:this.config.preloadTime}),this._context.init(),this.url=e,this.__core__.load(e),Kt(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"start",this).call(this,e),this._initEvents(),this.started=!0,this.addLiveFlag())}},{key:"play",value:function(){this.started&&(this._context.destroy(),this._context=new Q(Zt),this.__core__=this._context.registry("HLS_LIVE_CONTROLLER",Wt)({player:this,container:this.video,preloadTime:this.config.preloadTime}),this._context.init(),this._initEvents(),this.__core__.load(this.url)),Kt(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"play",this).call(this)}},{key:"addLiveFlag",value:function(){if(e.util.addClass(this.root,"xgplayer-is-live"),!e.util.findDom(this.root,"xg-live")){var t=e.util.createDom("xg-live","正在直播",{},"xgplayer-live");this.controls.appendChild(t)}}},{key:"destroy",value:function(){var e=this,t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return new Promise((function(r){Kt(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"destroy",e).call(e);var i=e.video,a=e.root;Kt(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"destroy",e).call(e,t),i&&i.remove?i.remove():a&&a.removeChild(i),i&&i.destroy(),setTimeout((function(){r()}),50)}))}},{key:"src",set:function(e){this.onWaiting=this.onWaiting.bind(this),this._context.destroy(),this._context=null,this.started=!1,this.video.currentTime=0,this.start(e)}}],[{key:"install",value:function(t,n){return e.install(t,n)}}]),n}(e);Jt.install=e.install.bind(e);var Qt=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),en=function(){function t(n){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),Jt.isSupported()&&n.useWASM?new Jt(n):Ut.isSupported()?new Ut(n):new e(n)}return Qt(t,null,[{key:"install",value:function(t,n){return e.install(t,n)}}]),t}();en.EVENTS=f;var tn=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),nn=f.LOADER_EVENTS,rn=f.REMUX_EVENTS,an=f.DEMUX_EVENTS,on=f.CRYTO_EVENTS,sn=f.MSE_EVENTS,ln=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.TAG="HlsVodController",this.configs=Object.assign({},t),this.url="",this.sequence=0,this._playlist=null,this.retrytimes=this.configs.retrytimes||3,this.container=this.configs.container,this.preloadTime=this.configs.preloadTime||5,this.mse=this.configs.mse,this._lastSeekTime=0,this.m3u8Text=null}return tn(e,[{key:"init",value:function(){this._context.registry("M3U8_BUFFER",D),this._tsBuffer=this._context.registry("TS_BUFFER",D)(),this._tracks=this._context.registry("TRACKS",C)(),this._playlist=this._context.registry("PLAYLIST",g)({autoclear:!1}),this._presource=this._context.registry("PRE_SOURCE_BUFFER",x)(),this._compat=this._context.registry("COMPATIBILITY",Qe)(),this._context.registry("M3U8_LOADER",pe)({buffer:"M3U8_BUFFER",readtype:1}),this._tsloader=this._context.registry("TS_LOADER",pe)({buffer:"TS_BUFFER",readtype:3}),this._demuxer=this._context.registry("TS_DEMUXER",dt)({inputbuffer:"TS_BUFFER"}),this._context.registry("MP4_REMUXER",At)(this._player.currentTime),this.mse||(this.mse=new ae({container:this.container,preloadTime:this.preloadTime},this._context),this.mse.init()),this.initEvents()}},{key:"initEvents",value:function(){this._onLoaderCompete=this._onLoaderCompete.bind(this),this._onLoadError=this._onLoadError.bind(this),this._onInitSegment=this._onInitSegment.bind(this),this._handleSEIParsed=this._handleSEIParsed.bind(this),this._onMediaSegment=this._onMediaSegment.bind(this),this._onMetadataParsed=this._onMetadataParsed.bind(this),this._onDemuxComplete=this._onDemuxComplete.bind(this),this._onDemuxError=this._onDemuxError.bind(this),this._onRemuxError=this._onRemuxError.bind(this),this._handleMseError=this._handleMseError.bind(this),this._onUpdateEnd=this._onUpdateEnd.bind(this),this._onTimeUpdate=this._onTimeUpdate.bind(this),this._onWaiting=this._onWaiting.bind(this),this.on(nn.LOADER_COMPLETE,this._onLoaderCompete),this.on(nn.LOADER_ERROR,this._onLoadError),this.on(rn.INIT_SEGMENT,this._onInitSegment),this.on(an.SEI_PARSED,this._handleSEIParsed),this.on(rn.MEDIA_SEGMENT,this._onMediaSegment),this.on(an.METADATA_PARSED,this._onMetadataParsed),this.on(an.DEMUX_COMPLETE,this._onDemuxComplete),this.on(an.DEMUX_ERROR,this._onDemuxError),this.on(rn.REMUX_ERROR,this._onRemuxError),this.on(sn.MSE_ERROR,this._handleMseError),this.on(sn.SOURCE_UPDATE_END,this._onUpdateEnd),this.on("TIME_UPDATE",this._onTimeUpdate),this.on(sn.SOURCE_UPDATE_END,this._onTimeUpdate),this.on("WAITING",this._onWaiting)}},{key:"_onError",value:function(e,t,n,r){var i={errorType:e,errorDetails:"["+t+"]: "+(n?n.message:""),errorFatal:r};this._player&&this._player.emit("HLS_ERROR",i)}},{key:"_onLoadError",value:function(e,t){this._player.emit("error",{errorType:"network",ex:"["+e+"]: "+t.message,errd:{}}),this._onError(nn.LOADER_ERROR,e,t,!0)}},{key:"_onDemuxError",value:function(e,t,n){void 0===n&&(n=!0),this._player.emit("error",{errorType:"parse",ex:"["+e+"]: "+t.message,errd:{}}),this._onError(nn.LOADER_ERROR,e,t,n)}},{key:"_onRemuxError",value:function(e,t,n){void 0===n&&(n=!0),this._player.emit("error",{errorType:"parse",ex:"["+e+"]: "+t.message,errd:{}}),this._onError(rn.REMUX_ERROR,e,t,n)}},{key:"_handleMseError",value:function(e,t,n){void 0===n&&(n=!1),this._player.emit("error",{errorType:"format",ex:"["+e+"]: "+t.message,errd:{}}),this._onError(sn.MSE_ERROR,e,t,n)}},{key:"_onWaiting",value:function(e){var t=!0;he.log(this.TAG,"waiting.......",this._player.video.currentTime),this._seekToBufferStart();var n=Object.keys(this._playlist.list),r=n.length;if(r){for(var i=0;i<r;i++)1e3*this.container.currentTime<parseInt(n[i])&&(t=!1);if(t){var a=this._playlist.getTs(1e3*this.container.currentTime);a?a.downloaded&&(this._player.emit("ended"),this.mse.endOfStream()):(this._player.emit("ended"),this.mse.endOfStream())}}}},{key:"_seekToBufferStart",value:function(){var e=this._player.video,t=e.buffered,n=[0,0],r=e.currentTime;if(t)for(var i=0,a=t.length;i<a;i++)if(n[0]=t.start(i),n[1]=t.end(i),n[0]<=r&&r<=n[1])return;var o=n[0];0===r&&r<o&&Math.abs(r-o)<5&&(e.currentTime=o)}},{key:"_checkEnd",value:function(){var e=this._player.video,t=e.buffered,n=t.length;if(n){var r=t.end(n-1);Math.abs(r-e.duration)<1&&this.mse.endOfStream()}}},{key:"_onUpdateEnd",value:function(){this._checkEnd(),this._seekToBufferStart(),this._preload(this._player.currentTime)}},{key:"_onTimeUpdate",value:function(){this._seekToBufferStart(),this._preload(this._player.currentTime)}},{key:"_onDemuxComplete",value:function(){this.emit(rn.REMUX_MEDIA,"ts")}},{key:"_handleSEIParsed",value:function(e){this._player.emit("SEI_PARSED",e)}},{key:"_onMetadataParsed",value:function(e){try{var t=this._tracks[e+"Track"].meta;he.warn(this.TAG,"meta detect or changed , ",e,Object.assign({},t))}catch(e){}var n=parseInt(this._playlist.duration);"video"===e?(this._context.mediaInfo.hasVideo=!0,this._tracks.videoTrack.meta.duration=n):"audio"===e&&(this._context.mediaInfo.hasAudio=!0,this._tracks.audioTrack.meta.duration=n),this.emit(rn.REMUX_METADATA,e)}},{key:"_onMediaSegment",value:function(){Object.keys(this.mse.sourceBuffers).length<1&&this.mse.addSourceBuffers(),this.mse.doAppend()}},{key:"_onInitSegment",value:function(){this.mse.addSourceBuffers()}},{key:"_onLoaderCompete",value:function(e){if("M3U8_BUFFER"===e.TAG){this.m3u8Text=e.shift();try{var t=gt.parse(this.m3u8Text,this.url);this._playlist.pushM3U8(t)}catch(e){this._onError("M3U8_PARSER_ERROR","PLAYLIST",e,!0)}if(this._playlist.encrypt&&this._playlist.encrypt.uri&&!this._playlist.encrypt.key)this._context.registry("DECRYPT_BUFFER",D)(),this._context.registry("KEY_BUFFER",D)(),this._tsloader.buffer="DECRYPT_BUFFER",this._keyLoader=this._context.registry("KEY_LOADER",pe)({buffer:"KEY_BUFFER",readtype:3}),this.emitTo("KEY_LOADER",nn.LADER_START,this._playlist.encrypt.uri);else{this.preloadTime||(this._playlist.targetduration?(this.preloadTime=this._playlist.targetduration,this.mse.preloadTime=this._playlist.targetduration):(this.preloadTime=5,this.mse.preloadTime=5));var n=this._playlist.getTs(1e3*(this._player.currentTime+.5));n?(this._logDownSegment(n),this._playlist.downloading(n.url,!0),this.emitTo("TS_LOADER",nn.LADER_START,n.url)):this.retrytimes>0&&(this.retrytimes--,this.emitTo("M3U8_LOADER",nn.LADER_START,this.url))}}else if("TS_BUFFER"===e.TAG)this._playlist.downloaded(this._tsloader.url,!0),this._demuxer.demux(Object.assign({url:this._tsloader.url},this._playlist._ts[this._tsloader.url]),!0),this._preload(this.mse.container.currentTime);else if("DECRYPT_BUFFER"===e.TAG)this.retrytimes=this.configs.retrytimes||3,this._playlist.downloaded(this._tsloader.url,!0),this.emitTo("CRYPTO",on.START_DECRYPT,Object.assign({url:this._tsloader.url},this._playlist._ts[this._tsloader.url]));else if("KEY_BUFFER"===e.TAG){this.retrytimes=this.configs.retrytimes||3,this._playlist.encrypt.key=e.shift(),this._crypto=this._context.registry("CRYPTO",ne)({key:this._playlist.encrypt.key,iv:this._playlist.encrypt.ivb,method:this._playlist.encrypt.method,inputbuffer:"DECRYPT_BUFFER",outputbuffer:"TS_BUFFER"}),this._crypto.on(on.DECRYPTED,this._onDcripted.bind(this));var r=this._playlist.getTs();r?(this._playlist.downloading(r.url,!0),this.emitTo("TS_LOADER",nn.LADER_START,r.url)):this.retrytimes>0&&(this.retrytimes--,this.emitTo("M3U8_LOADER",nn.LADER_START,this.url))}}},{key:"_onDcripted",value:function(){this.emit(an.DEMUX_START)}},{key:"seek",value:function(e){for(var t=this._player.video,n=0;n<t.buffered.length;n++)if(e>=t.buffered.start(n)&&e<t.buffered.end(n))return;he.warn(this.TAG,"seek: ",e,"tsloading:",this._tsloader.loading);var r=this._playlist.getTs(1e3*(e+.5));r&&this._tsloader.loading&&this._tsloader.url===r.url||(this._lastSeekTime=e,this._tsloader.destroy(),this._tsloader=this._context.registry("TS_LOADER",pe)({buffer:"TS_BUFFER",readtype:3}),this._presource.sources.video&&(this._presource.sources.video.data=[]),this._presource.sources.audio&&(this._presource.sources.audio.data=[]),this._tracks.audioTrack&&(this._tracks.audioTrack.samples=[]),this._tracks.videoTrack&&(this._tracks.videoTrack.samples=[]),this._compat&&this._compat.reset(),this._tsBuffer&&(this._tsBuffer.array=[],this._tsBuffer.length=0,this._tsBuffer.offset=0),this._playlist.clearDownloaded(),this._context.seek(e),this._preload(e))}},{key:"replay",value:function(){this._compat.reset(),this._playlist.clearDownloaded()}},{key:"load",value:function(e){this.url=e,this.emitTo("M3U8_LOADER",nn.LADER_START,e,null,0)}},{key:"_preload",value:function(e){if(e=Math.floor(e),!this._tsloader.loading){he.log(this.TAG,"preload: time=",e);var t=this.mse.container,n=-1;!e&&t.buffered.length&&(e=t.buffered.start(0));for(var r=0;r<t.buffered.length;r++)e>=Math.floor(t.buffered.start(r))&&e<=Math.ceil(t.buffered.end(r))&&(n=t.buffered.end(r));if(n<0){var i=this._playlist.getTs(1e3*(e+.5));i&&i.downloaded&&(i=this._playlist.getTs(i.time+i.duration)),!i||i.downloading||i.downloaded||(this._logDownSegment(i),this._playlist.downloading(i.url,!0),this.emitTo("TS_LOADER",nn.LADER_START,i.url))}else if(n<e+this.preloadTime){var a=this._playlist.getLastDownloadedTs()||this._playlist.getTs(1e3*n);if(!a)return;var o=a.time+a.duration,s=a.time;if(a.downloaded)for(var l=1e3;l-- >0&&(o+=10,(a=this._playlist.getTs(o))&&!(a.time>s)););!a||a.downloading||a.downloaded||(this._logDownSegment(a),this._playlist.downloading(a.url,!0),this.emitTo("TS_LOADER",nn.LADER_START,a.url))}}}},{key:"cleanBuffer",value:function(){var e=this,t=this._player.currentTime,n=this._player.video,r=n.buffered;if(r&&r.length){var i=[0,0],a=n.currentTime;if(r)for(var o=0,s=r.length;o<s&&(i[0]=r.start(o),i[1]=r.end(o),!(i[0]<=a&&a<=i[1]));o++);var l=i[0],u=i[1];(t-l>10||r.length>1)&&(this.mse.remove(Math.max(Math.min(t-10,u-10),.1),0),this.bufferClearTimer=setTimeout((function(){e.bufferClearTimer=null}),5e3))}}},{key:"destory",value:function(){this.configs={},this.url="",this.sequence=0,this._playlist=null,this.retrytimes=3,this.container=void 0,this.preloadTime=5,this._lastSeekTime=0,this.m3u8Text=null,this.mse=null,this.off(nn.LOADER_COMPLETE,this._onLoaderCompete),this.off(nn.LOADER_ERROR,this._onLoadError),this.off(rn.INIT_SEGMENT,this._onInitSegment),this.off(rn.MEDIA_SEGMENT,this._onMediaSegment),this.off(an.METADATA_PARSED,this._onMetadataParsed),this.off(an.DEMUX_COMPLETE,this._onDemuxComplete),this.off("TIME_UPDATE",this._onTimeUpdate),this.off("WAITING",this._onWaiting)}},{key:"_logDownSegment",value:function(e){e&&(he.groupEnd(),he.group(this.TAG,"load "+e.id+": ["+e.time/1e3+" , "+(e.time+e.duration)/1e3+"], downloading: "+e.downloading+" , donwloaded: "+e.downloaded))}}]),e}(),un="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},cn=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),dn=function e(t,n,r,i){var a=Object.getOwnPropertyDescriptor(t,n);if(void 0===a){var o=Object.getPrototypeOf(t);null!==o&&e(o,n,r,i)}else if("value"in a&&a.writable)a.value=r;else{var s=a.set;void 0!==s&&s.call(i,r)}return r},pn=function e(t,n,r){null===t&&(t=Function.prototype);var i=Object.getOwnPropertyDescriptor(t,n);if(void 0===i){var a=Object.getPrototypeOf(t);return null===a?void 0:e(a,n,r)}if("value"in i)return i.value;var o=i.get;return void 0!==o?o.call(r):void 0},fn=function(e,t){var n=Date.now(),r=null,i=!0;return function(){for(var a=arguments.length,o=Array(a),s=0;s<a;s++)o[s]=arguments[s];var l=Date.now();i&&(n=Date.now(),i=!1,e.apply(void 0,o)),l-n>t?(n=l,e.apply(void 0,o)):(r&&window.clearTimeout(r),r=setTimeout((function(){e.apply(void 0,o)}),t))}},hn=f.HlsAllowedEvents,gn=f.REMUX_EVENTS,yn=f.HLS_EVENTS,vn=f.MSE_EVENTS,mn=function(t){function n(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==(void 0===t?"undefined":un(t))&&"function"!=typeof t?e:t}(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,t));return r.hlsOps={},r.util=e.util,r.util.deepCopy(r.hlsOps,t),r._handleSetCurrentTime=fn(r._handleSetCurrentTime.bind(r),200),r.onWaiting=r.onWaiting.bind(r),r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+(void 0===t?"undefined":un(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(n,t),cn(n,[{key:"_handleSetCurrentTime",value:function(e){(e=parseFloat(e))!==this.currentTime&&(dn(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"currentTime",e,this),this._context&&this.__core__.seek(e))}},{key:"play",value:function(){var e=this;return this.video.play().catch((function(t){t&&20===t.code&&e.once("canplay",(function(){e.video.play()}))}))}},{key:"replay",value:function(){var e=this;this.__core__.mse.cleanBuffers().then((function(){e.__core__.replay(),pn(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"replay",e).call(e)}))}},{key:"_initEvents",value:function(){var t=this;this.__core__.once(gn.INIT_SEGMENT,(function(){var e=t.__core__.mse;pn(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"start",t).call(t,e.url)})),this.__core__.once(yn.RETRY_TIME_EXCEEDED,(function(){t.emit("error",new e.Errors("network",t.config.url))})),this.__core__.on(vn.SOURCE_UPDATE_END,(function(){t._onSourceUpdateEnd()})),this.once("canplay",(function(){t.config.autoplay&&t.play()}))}},{key:"initHlsBackupEvents",value:function(e,t){var r=this;e.once(gn.INIT_SEGMENT,(function(){if(!r.video.src){console.log("挂载 src blob");var t=e.mse;pn(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"start",r).call(r,t.url)}})),e.once(f.REMUX_EVENTS.MEDIA_SEGMENT,(function(){r.__core__=e,r.__core__.mse.cleanBuffers().then((function(){r.__core__.mse.resetContext(t),r.__core__.mse.doAppend(),r._context=t}))})),e.once(f.LOADER_EVENTS.LOADER_ERROR,(function(){t.destroy()}))}},{key:"_onSourceUpdateEnd",value:function(){if(1===this.video.readyState||2===this.video.readyState){var e=this.detectBufferGap(),t=e.gap,n=e.start,r=e.method;t&&("ceil"===r&&this.currentTime<Math[r](n)||"floor"===r&&this.currentTime>Math[r](n))&&(this.currentTime=Math[r](n))}}},{key:"start",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.url;e&&!this.started&&(this._context||(this._context=new Q(this,this.hlsOps,hn)),this.__core__=this._context.registry("HLS_VOD_CONTROLLER",ln)({player:this,container:this.video,preloadTime:this.config.preloadTime}),this._context.init(),this.__core__.load(e),this._initEvents(),this.started=!0)}},{key:"switchURL",value:function(e){this.config.url=e;var t=new Q(this,this.hlsOps,hn),n=t.registry("HLS_VOD_CONTROLLER",ln)({player:this,container:this.video,mse:this.__core__.mse,preloadTime:this.config.preloadTime});this.newContext=t,this.newHls=n,t.init(),this._context.destroy(),this.initHlsBackupEvents(n,t),this.__core__.mse.cleanBuffers().then((function(){n.load(e)}))}},{key:"destroy",value:function(){var e=this;return new Promise((function(t){e.__core__&&e.__core__.mse?(e.__core__.mse.destroy().then((function(){e._context&&e._context.destroy(),pn(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"destroy",e).call(e),t()})),setTimeout((function(){t()}),100)):(pn(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"destroy",e).call(e),setTimeout((function(){t()}),50))}))}},{key:"detectBufferGap",value:function(){for(var e=this.video,t={gap:!1,start:-1},n=0===this.currentTime,r=0;r<e.buffered.length;r++){var i=e.buffered.start(r),a=e.buffered.end(r);if((!e.played.length||i<=this.currentTime&&a-this.currentTime>=.5)&&!n)break;var o=i-this.currentTime,s=this.currentTime-a;if(o>.01&&(o<=2||n)){t={gap:!0,start:i,method:"ceil"};break}t=s>.1&&(s<=2||n)?{gap:!0,start:a,method:"floor"}:{gap:!1,start:-1}}return t}},{key:"currentTime",get:function(){return pn(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"currentTime",this)},set:function(e){this._handleSetCurrentTime(e)}},{key:"src",get:function(){return this.currentSrc},set:function(e){var t=this;this.onWaiting=this.onWaiting.bind(this),this.__core__.mse.destroy().then((function(){t._context.destroy(),t._context=null,t.video.src="",t.video.load(),t.started=!1,t.video.currentTime=0,t.paused?t.play():(t.pause(),t.once("canplay",(function(){t.play()}))),t.start(e)}))}},{key:"context",get:function(){return this._context}}],[{key:"isSupported",value:function(){return window.MediaSource&&window.MediaSource.isTypeSupported('video/mp4; codecs="avc1.42E01E,mp4a.40.2"')}},{key:"install",value:function(t,n){return e.install(t,n)}}]),n}(e),xn=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();return function(){function t(e){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),e.isLive?new en(e):new mn(e)}return xn(t,null,[{key:"isSupported",value:function(){return window.MediaSource&&window.MediaSource.isTypeSupported('video/mp4; codecs="avc1.42E01E,mp4a.40.2"')}},{key:"install",value:function(t,n){return e.install(t,n)}}]),t}()}))},5614:function(e){(function(t,n){e.exports=n()})(0,(function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=79)}([function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.util=t.PresentationMode=void 0,t.createDom=o,t.hasClass=s,t.addClass=l,t.removeClass=u,t.toggleClass=c,t.findDom=d,t.padStart=p,t.format=f,t.event=h,t.typeOf=g,t.deepCopy=y,t.getBgImage=v,t.copyDom=m,t._setInterval=x,t._clearInterval=b,t.createImgBtn=_,t.isWeiXin=k,t.isUc=E,t.computeWatchDur=w,t.offInDestroy=T,t.on=S,t.once=C,t.getBuffered2=O,t.checkIsBrowser=A,t.setStyle=R,t.checkWebkitSetPresentationMode=D;var r=n(7),i=a(r);function a(e){return e&&e.__esModule?e:{default:e}}function o(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"div",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",i=document.createElement(e);return i.className=r,i.innerHTML=t,Object.keys(n).forEach((function(t){var r=t,a=n[t];"video"===e||"audio"===e?a&&i.setAttribute(r,a):i.setAttribute(r,a)})),i}function s(e,t){return!!e&&(e.classList?Array.prototype.some.call(e.classList,(function(e){return e===t})):!!e.className&&!!e.className.match(new RegExp("(\\s|^)"+t+"(\\s|$)")))}function l(e,t){e&&(e.classList?t.replace(/(^\s+|\s+$)/g,"").split(/\s+/g).forEach((function(t){t&&e.classList.add(t)})):s(e,t)||(e.className+=" "+t))}function u(e,t){e&&(e.classList?t.split(/\s+/g).forEach((function(t){e.classList.remove(t)})):s(e,t)&&t.split(/\s+/g).forEach((function(t){var n=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className=e.className.replace(n," ")})))}function c(e,t){e&&t.split(/\s+/g).forEach((function(t){s(e,t)?u(e,t):l(e,t)}))}function d(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document,t=arguments[1],n=void 0;try{n=e.querySelector(t)}catch(r){0===t.indexOf("#")&&(n=e.getElementById(t.slice(1)))}return n}function p(e,t,n){var r=String(n),i=t|0,a=Math.ceil(i/r.length),o=[],s=String(e);while(a--)o.push(r);return o.join("").substring(0,i-s.length)+s}function f(e){if(window.isNaN(e))return"";var t=p(Math.floor(e/3600),2,0),n=p(Math.floor((e-3600*t)/60),2,0),r=p(Math.floor(e-3600*t-60*n),2,0);return("00"===t?[n,r]:[t,n,r]).join(":")}function h(e){if(e.touches){var t=e.touches[0]||e.changedTouches[0];e.clientX=t.clientX||0,e.clientY=t.clientY||0,e.offsetX=t.pageX-t.target.offsetLeft,e.offsetY=t.pageY-t.target.offsetTop}e._target=e.target||e.srcElement}function g(e){return Object.prototype.toString.call(e).match(/([^\s.*]+)(?=]$)/g)[0]}function y(e,t){if("Object"===g(t)&&"Object"===g(e))return Object.keys(t).forEach((function(n){"Object"!==g(t[n])||t[n]instanceof Node?"Array"===g(t[n])?e[n]="Array"===g(e[n])?e[n].concat(t[n]):t[n]:e[n]=t[n]:e[n]?y(e[n],t[n]):e[n]=t[n]})),e}function v(e){var t=(e.currentStyle||window.getComputedStyle(e,null)).backgroundImage;if(!t||"none"===t)return"";var n=document.createElement("a");return n.href=t.replace(/url\("|"\)/g,""),n.href}function m(e){if(e&&1===e.nodeType){var t=document.createElement(e.tagName);return Array.prototype.forEach.call(e.attributes,(function(e){t.setAttribute(e.name,e.value)})),e.innerHTML&&(t.innerHTML=e.innerHTML),t}return""}function x(e,t,n,r){e._interval[t]||(e._interval[t]=setInterval(n.bind(e),r))}function b(e,t){clearInterval(e._interval[t]),e._interval[t]=null}function _(e,t,n,r){var i=o("xg-"+e,"",{},"xgplayer-"+e+"-img");if(i.style.backgroundImage='url("'+t+'")',n&&r){var a=void 0,s=void 0,l=void 0;["px","rem","em","pt","dp","vw","vh","vm","%"].every((function(e){return!(n.indexOf(e)>-1&&r.indexOf(e)>-1)||(a=Number(n.slice(0,n.indexOf(e)).trim()),s=Number(r.slice(0,r.indexOf(e)).trim()),l=e,!1)})),i.style.width=""+a+l,i.style.height=""+s+l,i.style.backgroundSize=""+a+l+" "+s+l,i.style.margin="start"===e?"-"+s/2+l+" auto auto -"+a/2+l:"auto 5px auto 5px"}return i}function k(){var e=window.navigator.userAgent.toLowerCase();return e.indexOf("micromessenger")>-1}function E(){var e=window.navigator.userAgent.toLowerCase();return e.indexOf("ucbrowser")>-1}function w(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[],n=0;n<e.length;n++)if(!(!e[n].end||e[n].begin<0||e[n].end<0||e[n].end<e[n].begin))if(t.length<1)t.push({begin:e[n].begin,end:e[n].end});else for(var r=0;r<t.length;r++){var i=e[n].begin,a=e[n].end;if(a<t[r].begin){t.splice(r,0,{begin:i,end:a});break}if(!(i>t[r].end)){var o=t[r].begin,s=t[r].end;t[r].begin=Math.min(i,o),t[r].end=Math.max(a,s);break}if(r>t.length-2){t.push({begin:i,end:a});break}}for(var l=0,u=0;u<t.length;u++)l+=t[u].end-t[u].begin;return l}function T(e,t,n,r){function i(){e.off(t,n),e.off(r,i)}e.once(r,i)}function S(e,t,n,r){if(r)e.on(t,n),T(e,t,n,r);else{var i=function r(i){n(i),e.off(t,r)};e.on(t,i)}}function C(e,t,n,r){if(r)e.once(t,n),T(e,t,n,r);else{var i=function r(i){n(i),e.off(t,r)};e.once(t,i)}}function O(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.5,n=[],r=0;r<e.length;r++)n.push({start:e.start(r)<.5?0:e.start(r),end:e.end(r)});n.sort((function(e,t){var n=e.start-t.start;return n||t.end-e.end}));var a=[];if(t)for(var o=0;o<n.length;o++){var s=a.length;if(s){var l=a[s-1].end;n[o].start-l<t?n[o].end>l&&(a[s-1].end=n[o].end):a.push(n[o])}else a.push(n[o])}else a=n;return new i.default(a)}function A(){return!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement)}function R(e,t,n){var r=e.style;try{r[t]=n}catch(i){r.setProperty(t,n)}}t.PresentationMode={PIP:"picture-in-picture",INLINE:"inline",FULLSCREEN:"fullscreen"};function D(e){return"function"===typeof e.webkitSetPresentationMode}t.util={createDom:o,hasClass:s,addClass:l,removeClass:u,toggleClass:c,findDom:d,padStart:p,format:f,event:h,typeOf:g,deepCopy:y,getBgImage:v,copyDom:m,setInterval:x,clearInterval:b,createImgBtn:_,isWeiXin:k,isUc:E,computeWatchDur:w,offInDestroy:T,on:S,once:C,getBuffered2:O,checkIsBrowser:A,setStyle:R}},function(e,t){function n(e,t){var n=e[1]||"",i=e[3];if(!i)return n;if(t&&"function"===typeof btoa){var a=r(i),o=i.sources.map((function(e){return"/*# sourceURL="+i.sourceRoot+e+" */"}));return[n].concat(o).concat([a]).join("\n")}return[n].join("\n")}function r(e){var t=btoa(unescape(encodeURIComponent(JSON.stringify(e)))),n="sourceMappingURL=data:application/json;charset=utf-8;base64,"+t;return"/*# "+n+" */"}e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var r=n(t,e);return t[2]?"@media "+t[2]+"{"+r+"}":r})).join("")},t.i=function(e,n){"string"===typeof e&&(e=[[null,e,""]]);for(var r={},i=0;i<this.length;i++){var a=this[i][0];"number"===typeof a&&(r[a]=!0)}for(i=0;i<e.length;i++){var o=e[i];"number"===typeof o[0]&&r[o[0]]||(n&&!o[2]?o[2]=n:n&&(o[2]="("+o[2]+") and ("+n+")"),t.push(o))}},t}},function(e,t,n){var r={},i=function(e){var t;return function(){return"undefined"===typeof t&&(t=e.apply(this,arguments)),t}},a=i((function(){return window&&document&&document.all&&!window.atob})),o=function(e){return document.querySelector(e)},s=function(e){var t={};return function(e){if("function"===typeof e)return e();if("undefined"===typeof t[e]){var n=o.call(this,e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(r){n=null}t[e]=n}return t[e]}}(),l=null,u=0,c=[],d=n(36);function p(e,t){for(var n=0;n<e.length;n++){var i=e[n],a=r[i.id];if(a){a.refs++;for(var o=0;o<a.parts.length;o++)a.parts[o](i.parts[o]);for(;o<i.parts.length;o++)a.parts.push(x(i.parts[o],t))}else{var s=[];for(o=0;o<i.parts.length;o++)s.push(x(i.parts[o],t));r[i.id]={id:i.id,refs:1,parts:s}}}}function f(e,t){for(var n=[],r={},i=0;i<e.length;i++){var a=e[i],o=t.base?a[0]+t.base:a[0],s=a[1],l=a[2],u=a[3],c={css:s,media:l,sourceMap:u};r[o]?r[o].parts.push(c):n.push(r[o]={id:o,parts:[c]})}return n}function h(e,t){var n=s(e.insertInto);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var r=c[c.length-1];if("top"===e.insertAt)r?r.nextSibling?n.insertBefore(t,r.nextSibling):n.appendChild(t):n.insertBefore(t,n.firstChild),c.push(t);else if("bottom"===e.insertAt)n.appendChild(t);else{if("object"!==typeof e.insertAt||!e.insertAt.before)throw new Error("[Style Loader]\n\n Invalid value for parameter 'insertAt' ('options.insertAt') found.\n Must be 'top', 'bottom', or Object.\n (https://github.com/webpack-contrib/style-loader#insertat)\n");var i=s(e.insertInto+" "+e.insertAt.before);n.insertBefore(t,i)}}function g(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e);var t=c.indexOf(e);t>=0&&c.splice(t,1)}function y(e){var t=document.createElement("style");return e.attrs.type="text/css",m(t,e.attrs),h(e,t),t}function v(e){var t=document.createElement("link");return e.attrs.type="text/css",e.attrs.rel="stylesheet",m(t,e.attrs),h(e,t),t}function m(e,t){Object.keys(t).forEach((function(n){e.setAttribute(n,t[n])}))}function x(e,t){var n,r,i,a;if(t.transform&&e.css){if(a=t.transform(e.css),!a)return function(){};e.css=a}if(t.singleton){var o=u++;n=l||(l=y(t)),r=_.bind(null,n,o,!1),i=_.bind(null,n,o,!0)}else e.sourceMap&&"function"===typeof URL&&"function"===typeof URL.createObjectURL&&"function"===typeof URL.revokeObjectURL&&"function"===typeof Blob&&"function"===typeof btoa?(n=v(t),r=E.bind(null,n,t),i=function(){g(n),n.href&&URL.revokeObjectURL(n.href)}):(n=y(t),r=k.bind(null,n),i=function(){g(n)});return r(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;r(e=t)}else i()}}e.exports=function(e,t){if("undefined"!==typeof DEBUG&&DEBUG&&"object"!==typeof document)throw new Error("The style-loader cannot be used in a non-browser environment");t=t||{},t.attrs="object"===typeof t.attrs?t.attrs:{},t.singleton||"boolean"===typeof t.singleton||(t.singleton=a()),t.insertInto||(t.insertInto="head"),t.insertAt||(t.insertAt="bottom");var n=f(e,t);return p(n,t),function(e){for(var i=[],a=0;a<n.length;a++){var o=n[a],s=r[o.id];s.refs--,i.push(s)}if(e){var l=f(e,t);p(l,t)}for(a=0;a<i.length;a++){s=i[a];if(0===s.refs){for(var u=0;u<s.parts.length;u++)s.parts[u]();delete r[s.id]}}}};var b=function(){var e=[];return function(t,n){return e[t]=n,e.filter(Boolean).join("\n")}}();function _(e,t,n,r){var i=n?"":r.css;if(e.styleSheet)e.styleSheet.cssText=b(t,i);else{var a=document.createTextNode(i),o=e.childNodes;o[t]&&e.removeChild(o[t]),o.length?e.insertBefore(a,o[t]):e.appendChild(a)}}function k(e,t){var n=t.css,r=t.media;if(r&&e.setAttribute("media",r),e.styleSheet)e.styleSheet.cssText=n;else{while(e.firstChild)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}function E(e,t,n){var r=n.css,i=n.sourceMap,a=void 0===t.convertToAbsoluteUrls&&i;(t.convertToAbsoluteUrls||a)&&(r=d(r)),i&&(r+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(i))))+" */");var o=new Blob([r],{type:"text/css"}),s=e.href;e.href=URL.createObjectURL(o),s&&URL.revokeObjectURL(s)}},function(e,t,n){"use strict";var r=n(24)();e.exports=function(e){return e!==r&&null!==e}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(8);function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var a={network:{code:1,msg:"视频下载错误",remark:"只要视频下载错误就使用此类型，无论是video本身的超时还是xhr的分段请求超时或者资源不存在"},mse:{code:2,msg:"流追加错误",remark:"追加流的时候如果类型不对、无法被正确解码则会触发此类错误"},parse:{code:3,msg:"解析错误",remark:"mp4、hls、flv我们都是使用js进行格式解析，如果解析失败则会触发此类错误"},format:{code:4,msg:"格式错误",remark:"如果浏览器不支持的格式导致播放错误"},decoder:{code:5,msg:"解码错误",remark:"浏览器解码异常会抛出此类型错误"},runtime:{code:6,msg:"语法错误",remark:"播放器语法错误"},timeout:{code:7,msg:"播放超时",remark:"播放过程中无法正常请求下一个分段导致播放中断"},other:{code:8,msg:"其他错误",remark:"不可知的错误或被忽略的错误类型"}},o=function e(t,n,o,s,l,u,c,d){var p=arguments.length>8&&void 0!==arguments[8]?arguments[8]:{line:"",handle:"",msg:"",version:""},f=arguments[9],h=arguments[10];i(this,e);var g={};if(arguments.length>1)g.playerVersion=r.version,g.errorType=t,g.domain=document.domain,g.duration=o,g.currentTime=n,g.networkState=s,g.readyState=l,g.currentSrc=c,g.src=u,g.ended=d,g.errd=p,g.ex=(a[t]||{}).msg,g.errorCode=f,g.mediaError=h;else{var y=arguments[0];Object.keys(y).map((function(e){g[e]=y[e]})),g.ex=(y.type&&a[y.type]||{}).msg}return g};t.default=o,e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r={};Object.defineProperty(r,"device",{get:function(){var e=r.os;return e.isPc?"pc":"mobile"}}),Object.defineProperty(r,"browser",{get:function(){var e=navigator.userAgent.toLowerCase(),t={ie:/rv:([\d.]+)\) like gecko/,firfox:/firefox\/([\d.]+)/,chrome:/chrome\/([\d.]+)/,opera:/opera.([\d.]+)/,safari:/version\/([\d.]+).*safari/};return[].concat(Object.keys(t).filter((function(n){return t[n].test(e)})))[0]||""}}),Object.defineProperty(r,"os",{get:function(){var e=navigator.userAgent,t=/(?:Windows Phone)/.test(e),n=/(?:SymbianOS)/.test(e)||t,r=/(?:Android)/.test(e),i=/(?:Firefox)/.test(e),a=/(?:iPad|PlayBook)/.test(e)||r&&!/(?:Mobile)/.test(e)||i&&/(?:Tablet)/.test(e),o=/(?:iPhone)/.test(e)&&!a,s=!o&&!r&&!n&&!a;return{isTablet:a,isPhone:o,isAndroid:r,isPc:s,isSymbian:n,isWindowsPhone:t,isFireFox:i}}}),t.default=r,e.exports=t["default"]},function(e,t,n){"use strict";var r=void 0;e.exports=function(e){return e!==r&&null!==e}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var a=function(){function e(t){i(this,e),this.bufferedList=t}return r(e,[{key:"start",value:function(e){return this.bufferedList[e].start}},{key:"end",value:function(e){return this.bufferedList[e].end}},{key:"length",get:function(){return this.bufferedList.length}}]),e}();t.default=a,e.exports=t["default"]},function(e){e.exports=JSON.parse('{"version":"2.32.2"}')},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=function e(t,n,r){null===t&&(t=Function.prototype);var i=Object.getOwnPropertyDescriptor(t,n);if(void 0===i){var a=Object.getPrototypeOf(t);return null===a?void 0:e(a,n,r)}if("value"in i)return i.value;var o=i.get;return void 0!==o?o.call(r):void 0},a=n(11),o=x(a),s=n(0),l=n(5),u=x(l),c=n(7),d=x(c),p=n(4),f=x(p),h=n(31),g=x(h),y=n(10),v=x(y);n(34);var m=n(8);function x(e){return e&&e.__esModule?e:{default:e}}function b(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==typeof t&&"function"!==typeof t?e:t}function k(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var E=function(e){function t(e){b(this,t);var n=_(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));if(n.config=(0,s.deepCopy)({width:600,height:337.5,ignores:[],whitelist:[],lang:(document.documentElement.getAttribute("lang")||navigator.language||"zh-cn").toLocaleLowerCase(),inactive:3e3,volume:.6,controls:!0,controlsList:["nodownload"]},e),n.version=m.version,n.userTimer=null,n.waitTimer=null,n.history=[],n.isProgressMoving=!1,n.root=(0,s.findDom)(document,"#"+n.config.id),n.controls=(0,s.createDom)("xg-controls","",{unselectable:"on",onselectstart:"return false"},"xgplayer-controls"),n.config.isShowControl&&(n.controls.style.display="none"),!n.root){var r,i=n.config.el;if(!i||1!==i.nodeType)return n.emit("error",new f.default({type:"use",errd:{line:45,handle:"Constructor",msg:"container id can't be empty"},vid:n.config.vid})),console.error("container id can't be empty"),r=!1,_(n,r);n.root=i}if((0,s.addClass)(n.root,"xgplayer xgplayer-"+u.default.device+" xgplayer-nostart xgplayer-pause "+(n.config.controls?"":"xgplayer-no-controls")),n.root.appendChild(n.controls),n.config.fluid?(n.root.style["max-width"]="100%",n.root.style["width"]="100%",n.root.style["height"]="0",n.root.style["padding-top"]=100*n.config.height/n.config.width+"%",n.video.style["position"]="absolute",n.video.style["top"]="0",n.video.style["left"]="0"):(n.config.width&&("number"!==typeof n.config.width?n.root.style.width=n.config.width:n.root.style.width=n.config.width+"px"),n.config.height&&("number"!==typeof n.config.height?n.root.style.height=n.config.height:n.root.style.height=n.config.height+"px")),n.config.execBeforePluginsCall&&n.config.execBeforePluginsCall.forEach((function(e){e.call(n,n)})),n.config.closeI18n||t.install(v.default.name,v.default.method),n.config.controlStyle&&"String"===(0,s.typeOf)(n.config.controlStyle)){var a=n;fetch(a.config.controlStyle,{method:"GET",headers:{Accept:"application/json"}}).then((function(e){e.ok&&e.json().then((function(e){for(var t in e)e.hasOwnProperty(t)&&(a.config[t]=e[t]);a.pluginsCall()}))})).catch((function(e){console.log("Fetch错误:"+e)}))}else n.pluginsCall();n.config.controlPlugins&&t.controlsRun(n.config.controlPlugins,n),n.ev.forEach((function(e){var t=Object.keys(e)[0],r=n[e[t]];r&&n.on(t,r)})),["focus","blur"].forEach((function(e){n.on(e,n["on"+e.charAt(0).toUpperCase()+e.slice(1)])}));var o=n;function l(){o.root.removeEventListener("mousemove",o.mousemoveFunc),o.off("destroy",l)}return n.mousemoveFunc=function(){o.emit("focus"),o.config.closeFocusVideoFocus||o.video.focus()},n.root.addEventListener("mousemove",n.mousemoveFunc),n.playFunc=function(){o.emit("focus"),o.config.closePlayVideoFocus||o.video.focus()},o.once("play",n.playFunc),n.getVideoSize=function(){if(this.video.videoWidth&&this.video.videoHeight){var e=o.root.getBoundingClientRect();"auto"===o.config.fitVideoSize?e.width/e.height>this.video.videoWidth/this.video.videoHeight?o.root.style.height=this.video.videoHeight/this.video.videoWidth*e.width+"px":o.root.style.width=this.video.videoWidth/this.video.videoHeight*e.height+"px":"fixWidth"===o.config.fitVideoSize?o.root.style.height=this.video.videoHeight/this.video.videoWidth*e.width+"px":"fixHeight"===o.config.fitVideoSize&&(o.root.style.width=this.video.videoWidth/this.video.videoHeight*e.height+"px")}},o.once("loadeddata",n.getVideoSize),setTimeout((function(){n.emit("ready"),n.isReady=!0}),0),n.config.videoInit&&(0,s.hasClass)(n.root,"xgplayer-nostart")&&n.start(),o.config.rotate&&(o.on("requestFullscreen",n.updateRotateDeg),o.on("exitFullscreen",n.updateRotateDeg)),o.once("destroy",l),n}return k(t,e),r(t,[{key:"attachVideo",value:function(){var e=this;this.video&&1===this.video.nodeType&&this.root.insertBefore(this.video,this.root.firstChild),setTimeout((function(){e.emit("complete"),e.danmu&&"function"===typeof e.danmu.resize&&e.danmu.resize()}),1)}},{key:"start",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.url;if(this.video){var n=this;t&&""!==t?(this.canPlayFunc=function(){n.off("canplay",n.canPlayFunc);var e=n.video.play();void 0!==e&&e&&e.then((function(){n.emit("autoplay started")})).catch((function(){n.emit("autoplay was prevented"),(0,s.addClass)(n.root,"xgplayer-is-autoplay")}))},"Array"!==(0,s.typeOf)(t)?"String"===(0,s.typeOf)(t)&&t.indexOf("blob:")>-1&&t===this.video.src||(this.video.src=t):t.forEach((function(t){e.video.appendChild((0,s.createDom)("source","",{src:""+t.src,type:""+(t.type||"")}))})),this.config.autoplay&&(u.default.os.isPhone?this.canPlayFunc():this.on("canplay",this.canPlayFunc)),this.config.disableStartLoad||this.video.load(),this.attachVideo()):this.emit("urlNull")}}},{key:"reload",value:function(){this.video.load(),this.reloadFunc=function(){var e=this.play();void 0!==e&&e&&e.catch((function(e){}))},this.once("loadeddata",this.reloadFunc)}},{key:"destroy",value:function(){var e=this,n=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],r=this;for(var a in clearInterval(this.bulletResizeTimer),this._interval)clearInterval(this._interval[a]),this._interval[a]=null;function o(){if(this.emit("destroy"),this.video.removeAttribute("src"),this.video.load(),n){this.root.innerHTML="";var e=this.root.className.split(" ");e.length>0?this.root.className=e.filter((function(e){return e.indexOf("xgplayer")<0})).join(" "):this.root.className=""}for(var t in this)delete this[t];(0,g.default)(this)}this.checkTimer&&clearInterval(this.checkTimer),this.waitTimer&&clearTimeout(this.waitTimer),this.ev.forEach((function(t){var n=Object.keys(t)[0],r=e[t[n]];r&&e.off(n,r)})),this.loadeddataFunc&&this.off("loadeddata",this.loadeddataFunc),this.reloadFunc&&this.off("loadeddata",this.reloadFunc),this.replayFunc&&this.off("play",this.replayFunc),this.playFunc&&this.off("play",this.playFunc),this.getVideoSize&&this.off("loadeddata",this.getVideoSize),["focus","blur"].forEach((function(t){e.off(t,e["on"+t.charAt(0).toUpperCase()+t.slice(1)])})),this.config.keyShortcut&&"on"!==this.config.keyShortcut||["video","controls"].forEach((function(t){e[t]&&e[t].removeEventListener("keydown",(function(e){r.onKeydown(e,r)}))})),o.call(this),i(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"destroy",this).call(this)}},{key:"replay",value:function(){var e=this._replay;if((0,s.removeClass)(this.root,"xgplayer-ended"),u.default.browser.indexOf("ie")>-1&&(this.emit("play"),this.emit("playing")),e&&e instanceof Function)e();else{this.currentTime=0;var t=this.play();void 0!==t&&t&&t.catch((function(e){}))}}},{key:"userGestureTrigEvent",value:function(e,t){var n=this,r=function(e,t){n.emit(e,t)};this.config.userGestureEventMiddleware&&"function"===typeof this.config.userGestureEventMiddleware[e]?this.config.userGestureEventMiddleware[e].call(this,this,e,t,r):r.call(this,e,t)}},{key:"pluginsCall",value:function(){var e=this;t.plugins["s_i18n"]&&t.plugins["s_i18n"].call(this,this);var n=this;if(t.plugins){var r=this.config.ignores;Object.keys(t.plugins).forEach((function(i){var a=t.plugins[i];a&&"function"===typeof a?r.some((function(e){return i===e||i==="s_"+e}))||"s_i18n"===i||(["pc","tablet","mobile"].some((function(e){return e===i}))?i===u.default.device&&setTimeout((function(){n.video&&a.call(n,n)}),0):a.call(e,e)):console.warn("plugin name",i,"is invalid")}))}}},{key:"onFocus",value:function(){var e=this;(0,s.hasClass)(this.root,"xgplayer-inactive")&&e.emit("controlShow"),(0,s.removeClass)(this.root,"xgplayer-inactive"),e.userTimer&&clearTimeout(e.userTimer),e.userTimer=setTimeout((function(){e.emit("blur")}),e.config.inactive)}},{key:"onBlur",value:function(){!this.config.enablePausedInactive&&this.paused||this.ended||this.config.closeInactive||((0,s.hasClass)(this.root,"xgplayer-inactive")||this.emit("controlHide"),(0,s.addClass)(this.root,"xgplayer-inactive"))}},{key:"onPlay",value:function(){(0,s.addClass)(this.root,"xgplayer-isloading"),(0,s.addClass)(this.root,"xgplayer-playing"),(0,s.removeClass)(this.root,"xgplayer-pause")}},{key:"onPause",value:function(){(0,s.addClass)(this.root,"xgplayer-pause"),this.userTimer&&clearTimeout(this.userTimer),this.emit("focus")}},{key:"onEnded",value:function(){(0,s.addClass)(this.root,"xgplayer-ended"),(0,s.removeClass)(this.root,"xgplayer-playing")}},{key:"onSeeking",value:function(){this.isSeeking=!0,this.onWaiting()}},{key:"onSeeked",value:function(){var e=this;this.once("timeupdate",(function(){e.isSeeking=!1})),this.waitTimer&&clearTimeout(this.waitTimer),(0,s.removeClass)(this.root,"xgplayer-isloading")}},{key:"onWaiting",value:function(){var e=this;e.waitTimer&&clearTimeout(e.waitTimer),e.checkTimer&&(clearInterval(e.checkTimer),e.checkTimer=null);var t=e.currentTime;e.waitTimer=setTimeout((function(){(0,s.addClass)(e.root,"xgplayer-isloading"),e.checkTimer=setInterval((function(){e.currentTime!==t&&((0,s.removeClass)(e.root,"xgplayer-isloading"),clearInterval(e.checkTimer),e.checkTimer=null)}),1e3)}),500)}},{key:"onPlaying",value:function(){this.paused||(this.isSeeking=!1,this.waitTimer&&clearTimeout(this.waitTimer),(0,s.removeClass)(this.root,"xgplayer-isloading xgplayer-nostart xgplayer-pause xgplayer-ended xgplayer-is-error xgplayer-replay"),(0,s.addClass)(this.root,"xgplayer-playing"))}}],[{key:"install",value:function(e,n){(0,s.checkIsBrowser)()&&(t.plugins||(t.plugins={}),t.plugins[e]||(t.plugins[e]=n))}},{key:"installAll",value:function(e){for(var n=0;n<e.length;n++)t.install(e[n].name,e[n].method)}},{key:"use",value:function(e,n){t.plugins||(t.plugins={}),t.plugins[e]=n}},{key:"useAll",value:function(e){for(var n in e)t.use(e[n].name,e[n].method)}},{key:"controlsRun",value:function(e,t){e.forEach((function(e){e.method.call(t)}))}}]),t}(o.default);E.util=s.util,E.sniffer=u.default,E.Errors=f.default,E.XgplayerTimeRange=d.default,t.default=E,e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=function(){var e=this,t={en:{HAVE_NOTHING:"There is no information on whether audio/video is ready",HAVE_METADATA:"Audio/video metadata is ready ",HAVE_CURRENT_DATA:"Data about the current play location is available, but there is not enough data to play the next frame/millisecond",HAVE_FUTURE_DATA:"Current and at least one frame of data is available",HAVE_ENOUGH_DATA:"The available data is sufficient to start playing",NETWORK_EMPTY:"Audio/video has not been initialized",NETWORK_IDLE:"Audio/video is active and has been selected for resources, but no network is used",NETWORK_LOADING:"The browser is downloading the data",NETWORK_NO_SOURCE:"No audio/video source was found",MEDIA_ERR_ABORTED:"The fetch process is aborted by the user",MEDIA_ERR_NETWORK:"An error occurred while downloading",MEDIA_ERR_DECODE:"An error occurred while decoding",MEDIA_ERR_SRC_NOT_SUPPORTED:"Audio/video is not supported",REPLAY:"Replay",ERROR:"Network is offline",PLAY_TIPS:"Play",PAUSE_TIPS:"Pause",PLAYNEXT_TIPS:"Play next",DOWNLOAD_TIPS:"Download",ROTATE_TIPS:"Rotate",RELOAD_TIPS:"Reload",FULLSCREEN_TIPS:"Fullscreen",EXITFULLSCREEN_TIPS:"Exit fullscreen",CSSFULLSCREEN_TIPS:"Cssfullscreen",EXITCSSFULLSCREEN_TIPS:"Exit cssfullscreen",TEXTTRACK:"Caption",PIP:"Pip",MINIPLAYER:"Miniplayer",SCREENSHOT:"Screenshot",LIVE:"LIVE",OFF:"Off",MINIPLAYER_DRAG:"Click and hold to drag",AIRPLAY_TIPS:"Airplay"},"zh-cn":{HAVE_NOTHING:"没有关于音频/视频是否就绪的信息",HAVE_METADATA:"音频/视频的元数据已就绪",HAVE_CURRENT_DATA:"关于当前播放位置的数据是可用的，但没有足够的数据来播放下一帧/毫秒",HAVE_FUTURE_DATA:"当前及至少下一帧的数据是可用的",HAVE_ENOUGH_DATA:"可用数据足以开始播放",NETWORK_EMPTY:"音频/视频尚未初始化",NETWORK_IDLE:"音频/视频是活动的且已选取资源，但并未使用网络",NETWORK_LOADING:"浏览器正在下载数据",NETWORK_NO_SOURCE:"未找到音频/视频来源",MEDIA_ERR_ABORTED:"取回过程被用户中止",MEDIA_ERR_NETWORK:"当下载时发生错误",MEDIA_ERR_DECODE:"当解码时发生错误",MEDIA_ERR_SRC_NOT_SUPPORTED:"不支持的音频/视频格式",REPLAY:"重播",ERROR:"网络连接似乎出现了问题",PLAY_TIPS:"播放",PAUSE_TIPS:"暂停",PLAYNEXT_TIPS:"下一集",DOWNLOAD_TIPS:"下载",ROTATE_TIPS:"旋转",RELOAD_TIPS:"重新载入",FULLSCREEN_TIPS:"进入全屏",EXITFULLSCREEN_TIPS:"退出全屏",CSSFULLSCREEN_TIPS:"进入样式全屏",EXITCSSFULLSCREEN_TIPS:"退出样式全屏",TEXTTRACK:"字幕",PIP:"画中画",MINIPLAYER:"迷你播放器",SCREENSHOT:"截图",LIVE:"正在直播",OFF:"关闭",MINIPLAYER_DRAG:"点击按住可拖动视频",AIRPLAY_TIPS:"隔空播放"},"zh-hk":{HAVE_NOTHING:"沒有關於音頻/視頻是否就緒的信息",HAVE_METADATA:"音頻/視頻的元數據已就緒",HAVE_CURRENT_DATA:"關於當前播放位置的數據是可用的，但沒有足夠的數據來播放下壹幀/毫秒",HAVE_FUTURE_DATA:"當前及至少下壹幀的數據是可用的",HAVE_ENOUGH_DATA:"可用數據足以開始播放",NETWORK_EMPTY:"音頻/視頻尚未初始化",NETWORK_IDLE:"音頻/視頻是活動的且已選取資源，但並未使用網絡",NETWORK_LOADING:"瀏覽器正在下載數據",NETWORK_NO_SOURCE:"未找到音頻/視頻來源",MEDIA_ERR_ABORTED:"取回過程被用戶中止",MEDIA_ERR_NETWORK:"當下載時發生錯誤",MEDIA_ERR_DECODE:"當解碼時發生錯誤",MEDIA_ERR_SRC_NOT_SUPPORTED:"不支持的音頻/視頻格式",REPLAY:"重播",ERROR:"網絡連接似乎出現了問題",PLAY_TIPS:"播放",PAUSE_TIPS:"暫停",PLAYNEXT_TIPS:"下壹集",DOWNLOAD_TIPS:"下載",ROTATE_TIPS:"旋轉",RELOAD_TIPS:"重新載入",FULLSCREEN_TIPS:"進入全屏",EXITFULLSCREEN_TIPS:"退出全屏",CSSFULLSCREEN_TIPS:"進入樣式全屏",EXITCSSFULLSCREEN_TIPS:"退出樣式全屏",TEXTTRACK:"字幕",PIP:"畫中畫",MINIPLAYER:"迷妳播放器",SCREENSHOT:"截圖",LIVE:"正在直播",OFF:"關閉",MINIPLAYER_DRAG:"點擊按住可拖動視頻",AIRPLAY_TIPS:"隔空播放"},jp:{HAVE_NOTHING:"オーディオ/ビデオが準備できているか情報がありません",HAVE_METADATA:"オーディオ/ビデオのメタデータは準備できています",HAVE_CURRENT_DATA:"現在の再生位置に関するデータは利用可能ですが、次のフレーム/ミリ秒を再生するのに十分なデータがありません",HAVE_FUTURE_DATA:"現在、少なくとも次のフレームのデータが利用可能です",HAVE_ENOUGH_DATA:"利用可能なデータは再生を開始するのに十分です",NETWORK_EMPTY:"オーディオ/ビデオが初期化されていません",NETWORK_IDLE:"オーディオ/ビデオはアクティブでリソースが選択されていますが、ネットワークが使用されていません",NETWORK_LOADING:"ブラウザーはデータをダウンロードしています",NETWORK_NO_SOURCE:"オーディオ/ビデオ のソースが見つかりません",MEDIA_ERR_ABORTED:"ユーザーによってフェッチプロセスが中止されました",MEDIA_ERR_NETWORK:"ダウンロード中にエラーが発生しました",MEDIA_ERR_DECODE:"デコード中にエラーが発生しました",MEDIA_ERR_SRC_NOT_SUPPORTED:"オーディオ/ビデオ の形式がサポートされていません",REPLAY:"リプレイ",ERROR:"ネットワークの接続に問題が発生しました",PLAY_TIPS:"プレイ",PAUSE_TIPS:"一時停止",PLAYNEXT_TIPS:"次をプレイ",DOWNLOAD_TIPS:"ダウンロード",ROTATE_TIPS:"回転",RELOAD_TIPS:"再読み込み",FULLSCREEN_TIPS:"フルスクリーン",EXITFULLSCREEN_TIPS:"フルスクリーンを終了",CSSFULLSCREEN_TIPS:"シアターモード",EXITCSSFULLSCREEN_TIPS:"シアターモードを終了",TEXTTRACK:"字幕",PIP:"ミニプレーヤー",MINIPLAYER:"ミニプレーヤー",SCREENSHOT:"スクリーンショット",LIVE:"生放送",OFF:"オフ",MINIPLAYER_DRAG:"ボタンを押して働画をドラッグする",AIRPLAY_TIPS:"隔空放送"}};Object.defineProperty(e,"lang",{get:function(){return e.config&&t[e.config.lang]||t["en"]},set:function(e){"Object"===(0,r.typeOf)(e)&&Object.keys(e).forEach((function(n){t[n]=e[n]}))}})};t.default={name:"s_i18n",method:i},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=n(12),a=u(i),o=n(0),s=n(4),l=u(s);function u(e){return e&&e.__esModule?e:{default:e}}function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function d(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var p=function(){function e(t){var n=this;d(this,e),this._hasStart=!1,this.videoConfig={controls:!!t.isShowControl,autoplay:t.autoplay,playsinline:t.playsinline,"webkit-playsinline":t.playsinline,"x5-playsinline":t.playsinline,"x5-video-player-type":t["x5-video-player-type"]||t["x5VideoPlayerType"],"x5-video-player-fullscreen":t["x5-video-player-fullscreen"]||t["x5VideoPlayerFullscreen"],"x5-video-orientation":t["x5-video-orientation"]||t["x5VideoOrientation"],airplay:t["airplay"],"webkit-airplay":t["airplay"],tabindex:2,mediaType:t.mediaType||"video"},t.muted&&(this.videoConfig.muted="muted"),t.loop&&(this.videoConfig.loop="loop");var r="";if(this.textTrackShowDefault=!0,t.nativeTextTrack&&Array.isArray(t.nativeTextTrack)&&(t.nativeTextTrack.length>0&&!t.nativeTextTrack.some((function(e){return e.default}))&&(t.nativeTextTrack[0].default=!0,this.textTrackShowDefault=!1),t.nativeTextTrack.some((function(e){if(e.src&&e.label&&e.default)return r+='<track src="'+e.src+'" ',e.kind&&(r+='kind="'+e.kind+'" '),r+='label="'+e.label+'" ',e.srclang&&(r+='srclang="'+e.srclang+'" '),r+=(e.default?"default":"")+">",!0})),this.videoConfig.crossorigin="anonymous"),t.textTrackStyle){var i=document.createElement("style");this.textTrackStyle=i,document.head.appendChild(i);var s="";for(var l in t.textTrackStyle)s+=l+": "+t.textTrackStyle[l]+";";var u=t.id?"#"+t.id:t.el.id?"#"+t.el.id:"."+t.el.className;i.sheet.insertRule?i.sheet.insertRule(u+" video::cue { "+s+" }",0):i.sheet.addRule&&i.sheet.addRule(u+" video::cue",s)}var p=t.el?t.el:(0,o.findDom)(document,"#"+t.id),f=this.constructor.XgVideoProxy;if(f&&this.videoConfig.mediaType===f.mediaType?this.video=new f(p,t):this.video=(0,o.createDom)(this.videoConfig.mediaType,r,this.videoConfig,""),t.videoStyle&&Object.keys(t.videoStyle).forEach((function(e){(0,o.setStyle)(n.video,e,t.videoStyle[e])})),!this.textTrackShowDefault&&r){var h=this.video.getElementsByTagName("Track");h[0].track.mode="hidden"}t.autoplay&&(this.video.autoplay=!0,t.autoplayMuted&&(this.video.muted=!0)),this.ev=["play","playing","pause","ended","error","seeking","seeked","progress","timeupdate","waiting","canplay","canplaythrough","durationchange","volumechange","ratechange","loadedmetadata","loadeddata","loadstart"].map((function(e){return c({},e,"on"+e.charAt(0).toUpperCase()+e.slice(1))})),(0,a.default)(this),this._interval={};var g="0,0",y=this,v=function(e){n&&("play"===e?n.hasStart=!0:"canplay"===e?(0,o.removeClass)(n.root,"xgplayer-is-enter"):"waiting"===e?n.inWaitingStart=(new Date).getTime():"playing"===e&&((0,o.removeClass)(n.root,"xgplayer-is-enter"),n.inWaitingStart&&(n.inWaitingStart=void 0)),"error"===e?n._onError(e):n.emit(e,n),n.hasOwnProperty("_interval")&&(["ended","error","timeupdate"].indexOf(e)<0?((0,o._clearInterval)(n,"bufferedChange"),(0,o._setInterval)(n,"bufferedChange",(function(){if(this.video&&this.video.buffered){for(var e=[],t=0,n=this.video.buffered.length;t<n;t++)e.push([this.video.buffered.start(t),this.video.buffered.end(t)]);e.toString()!==g&&(g=e.toString(),this.emit("bufferedChange",e))}}),200)):"timeupdate"!==e&&(0,o._clearInterval)(n,"bufferedChange")))},m=function(e){t.videoEventMiddleware&&"function"===typeof t.videoEventMiddleware[e]?t.videoEventMiddleware[e].call(n,n,e,v):v.call(n,e)};this.ev.forEach((function(e){y.evItem=Object.keys(e)[0];var t=Object.keys(e)[0];y.video.addEventListener(Object.keys(e)[0],m.bind(y,t))}))}return r(e,[{key:"_onError",value:function(e){this.video&&this.video.error&&this.emit(e,new l.default("other",this.currentTime,this.duration,this.networkState,this.readyState,this.currentSrc,this.src,this.ended,{line:162,msg:this.error,handle:"Constructor"},this.video.error.code,this.video.error))}},{key:"destroy",value:function(){this.textTrackStyle&&this.textTrackStyle.parentNode.removeChild(this.textTrackStyle)}},{key:"play",value:function(){return this.video.play()}},{key:"pause",value:function(){this.video.pause()}},{key:"canPlayType",value:function(e){return this.video.canPlayType(e)}},{key:"getBufferedRange",value:function(e){var t=[0,0],n=this.video;e||(e=n.buffered);var r=n.currentTime;if(e)for(var i=0,a=e.length;i<a;i++)if(t[0]=e.start(i),t[1]=e.end(i),t[0]<=r&&r<=t[1])break;return t[0]-r<=0&&r-t[1]<=0?t:[0,0]}},{key:"proxyOn",value:function(e,t){(0,o.on)(this,e,t,"destroy")}},{key:"proxyOnce",value:function(e,t){(0,o.once)(this,e,t,"destroy")}},{key:"hasStart",get:function(){return this._hasStart},set:function(e){"boolean"!==typeof e||!0!==e||this._hasStart||(this._hasStart=!0,this.emit("hasstart"))}},{key:"autoplay",set:function(e){this.video&&(this.video.autoplay=e)},get:function(){return!!this.video&&this.video.autoplay}},{key:"buffered",get:function(){return this.video?this.video.buffered:void 0}},{key:"buffered2",get:function(){return(0,o.getBuffered2)(this.video.buffered)}},{key:"crossOrigin",get:function(){return!!this.video&&this.video.crossOrigin},set:function(e){this.video&&(this.video.crossOrigin=e)}},{key:"currentSrc",get:function(){return this.video?this.video.currentSrc:void 0}},{key:"currentTime",get:function(){return this.video&&this.video.currentTime||0},set:function(e){var t=this;("function"!==typeof isFinite||isFinite(e))&&((0,o.hasClass)(this.root,"xgplayer-ended")?(this.once("playing",(function(){t.video.currentTime=e})),this.replay()):this.video.currentTime=e,this.emit("currentTimeChange",e))}},{key:"defaultMuted",get:function(){return!!this.video&&this.video.defaultMuted},set:function(e){this.video&&(this.video.defaultMuted=e)}},{key:"duration",get:function(){return this.config.duration?this.video?Math.min(this.config.duration,this.video.duration):this.config.duration:this.video?this.video.duration:null}},{key:"ended",get:function(){return!this.video||(this.video.ended||!1)}},{key:"error",get:function(){var e=this.video.error;if(!e)return null;var t=[{en:"MEDIA_ERR_ABORTED",cn:"取回过程被用户中止"},{en:"MEDIA_ERR_NETWORK",cn:"当下载时发生错误"},{en:"MEDIA_ERR_DECODE",cn:"当解码时发生错误"},{en:"MEDIA_ERR_SRC_NOT_SUPPORTED",cn:"不支持音频/视频"}];return this.lang?this.lang[t[e.code-1].en]:t[e.code-1].en}},{key:"loop",get:function(){return!!this.video&&this.video.loop},set:function(e){this.video&&(this.video.loop=e)}},{key:"muted",get:function(){return!!this.video&&this.video.muted},set:function(e){this.video&&(this.video.muted=e)}},{key:"networkState",get:function(){var e=[{en:"NETWORK_EMPTY",cn:"音频/视频尚未初始化"},{en:"NETWORK_IDLE",cn:"音频/视频是活动的且已选取资源，但并未使用网络"},{en:"NETWORK_LOADING",cn:"浏览器正在下载数据"},{en:"NETWORK_NO_SOURCE",cn:"未找到音频/视频来源"}];return this.lang?this.lang[e[this.video.networkState].en]:e[this.video.networkState].en}},{key:"paused",get:function(){return(0,o.hasClass)(this.root,"xgplayer-pause")}},{key:"playbackRate",get:function(){return this.video?this.video.playbackRate:1},set:function(e){this.video&&(this.video.playbackRate=e)}},{key:"played",get:function(){return this.video?this.video.played:void 0}},{key:"preload",get:function(){return!!this.video&&this.video.preload},set:function(e){this.video&&(this.video.preload=e)}},{key:"readyState",get:function(){var e=[{en:"HAVE_NOTHING",cn:"没有关于音频/视频是否就绪的信息"},{en:"HAVE_METADATA",cn:"关于音频/视频就绪的元数据"},{en:"HAVE_CURRENT_DATA",cn:"关于当前播放位置的数据是可用的，但没有足够的数据来播放下一帧/毫秒"},{en:"HAVE_FUTURE_DATA",cn:"当前及至少下一帧的数据是可用的"},{en:"HAVE_ENOUGH_DATA",cn:"可用数据足以开始播放"}];return this.lang?this.lang[e[this.video.readyState].en]:e[this.video.readyState]}},{key:"seekable",get:function(){return!!this.video&&this.video.seekable}},{key:"seeking",get:function(){return!!this.video&&this.video.seeking}},{key:"src",get:function(){return this.video?this.video.src:void 0},set:function(e){(0,o.hasClass)(this.root,"xgplayer-ended")||this.emit("urlchange",this.video.src),(0,o.removeClass)(this.root,"xgplayer-ended xgplayer-is-replay xgplayer-is-error"),this.video.pause(),this.emit("pause"),this.video.src=e,this.emit("srcChange")}},{key:"poster",set:function(e){var t=(0,o.findDom)(this.root,".xgplayer-poster");t&&(t.style.backgroundImage="url("+e+")")}},{key:"volume",get:function(){return this.video?this.video.volume:1},set:function(e){this.video&&(this.video.volume=e)}},{key:"fullscreen",get:function(){return(0,o.hasClass)(this.root,"xgplayer-is-fullscreen")||(0,o.hasClass)(this.root,"xgplayer-fullscreen-active")}},{key:"bullet",get:function(){return!!(0,o.findDom)(this.root,"xg-danmu")&&(0,o.hasClass)((0,o.findDom)(this.root,"xg-danmu"),"xgplayer-has-danmu")}},{key:"textTrack",get:function(){return(0,o.hasClass)(this.root,"xgplayer-is-textTrack")}},{key:"pip",get:function(){return(0,o.hasClass)(this.root,"xgplayer-pip-active")}},{key:"isMiniPlayer",get:function(){return(0,o.hasClass)(this.root,"xgplayer-miniplayer-active")}}]),e}();t.default=p,e.exports=t["default"]},function(e,t,n){"use strict";var r,i,a,o,s,l,u,c=n(13),d=n(30),p=Function.prototype.apply,f=Function.prototype.call,h=Object.create,g=Object.defineProperty,y=Object.defineProperties,v=Object.prototype.hasOwnProperty,m={configurable:!0,enumerable:!1,writable:!0};r=function(e,t){var n;return d(t),v.call(this,"__ee__")?n=this.__ee__:(n=m.value=h(null),g(this,"__ee__",m),m.value=null),n[e]?"object"===typeof n[e]?n[e].push(t):n[e]=[n[e],t]:n[e]=t,this},i=function(e,t){var n,i;return d(t),i=this,r.call(this,e,n=function(){a.call(i,e,n),p.call(t,this,arguments)}),n.__eeOnceListener__=t,this},a=function(e,t){var n,r,i,a;if(d(t),!v.call(this,"__ee__"))return this;if(n=this.__ee__,!n[e])return this;if(r=n[e],"object"===typeof r)for(a=0;i=r[a];++a)i!==t&&i.__eeOnceListener__!==t||(2===r.length?n[e]=r[a?0:1]:r.splice(a,1));else r!==t&&r.__eeOnceListener__!==t||delete n[e];return this},o=function(e){var t,n,r,i,a;if(v.call(this,"__ee__")&&(i=this.__ee__[e],i))if("object"===typeof i){for(n=arguments.length,a=new Array(n-1),t=1;t<n;++t)a[t-1]=arguments[t];for(i=i.slice(),t=0;r=i[t];++t)p.call(r,this,a)}else switch(arguments.length){case 1:f.call(i,this);break;case 2:f.call(i,this,arguments[1]);break;case 3:f.call(i,this,arguments[1],arguments[2]);break;default:for(n=arguments.length,a=new Array(n-1),t=1;t<n;++t)a[t-1]=arguments[t];p.call(i,this,a)}},s={on:r,once:i,off:a,emit:o},l={on:c(r),once:c(i),off:c(a),emit:c(o)},u=y({},l),e.exports=t=function(e){return null==e?h(u):y(Object(e),l)},t.methods=s},function(e,t,n){"use strict";var r=n(6),i=n(14),a=n(18),o=n(26),s=n(27),l=e.exports=function(e,t){var n,i,l,u,c;return arguments.length<2||"string"!==typeof e?(u=t,t=e,e=null):u=arguments[2],r(e)?(n=s.call(e,"c"),i=s.call(e,"e"),l=s.call(e,"w")):(n=l=!0,i=!1),c={value:t,configurable:n,enumerable:i,writable:l},u?a(o(u),c):c};l.gs=function(e,t,n){var l,u,c,d;return"string"!==typeof e?(c=n,n=t,t=e,e=null):c=arguments[3],r(t)?i(t)?r(n)?i(n)||(c=n,n=void 0):n=void 0:(c=t,t=n=void 0):t=void 0,r(e)?(l=s.call(e,"c"),u=s.call(e,"e")):(l=!0,u=!1),d={get:t,set:n,configurable:l,enumerable:u},c?a(o(c),d):d}},function(e,t,n){"use strict";var r=n(15),i=/^\s*class[\s{/}]/,a=Function.prototype.toString;e.exports=function(e){return!!r(e)&&!i.test(a.call(e))}},function(e,t,n){"use strict";var r=n(16);e.exports=function(e){if("function"!==typeof e)return!1;if(!hasOwnProperty.call(e,"length"))return!1;try{if("number"!==typeof e.length)return!1;if("function"!==typeof e.call)return!1;if("function"!==typeof e.apply)return!1}catch(t){return!1}return!r(e)}},function(e,t,n){"use strict";var r=n(17);e.exports=function(e){if(!r(e))return!1;try{return!!e.constructor&&e.constructor.prototype===e}catch(t){return!1}}},function(e,t,n){"use strict";var r=n(6),i={object:!0,function:!0,undefined:!0};e.exports=function(e){return!!r(e)&&hasOwnProperty.call(i,typeof e)}},function(e,t,n){"use strict";e.exports=n(19)()?Object.assign:n(20)},function(e,t,n){"use strict";e.exports=function(){var e,t=Object.assign;return"function"===typeof t&&(e={foo:"raz"},t(e,{bar:"dwa"},{trzy:"trzy"}),e.foo+e.bar+e.trzy==="razdwatrzy")}},function(e,t,n){"use strict";var r=n(21),i=n(25),a=Math.max;e.exports=function(e,t){var n,o,s,l=a(arguments.length,2);for(e=Object(i(e)),s=function(r){try{e[r]=t[r]}catch(i){n||(n=i)}},o=1;o<l;++o)t=arguments[o],r(t).forEach(s);if(void 0!==n)throw n;return e}},function(e,t,n){"use strict";e.exports=n(22)()?Object.keys:n(23)},function(e,t,n){"use strict";e.exports=function(){try{return Object.keys("primitive"),!0}catch(e){return!1}}},function(e,t,n){"use strict";var r=n(3),i=Object.keys;e.exports=function(e){return i(r(e)?Object(e):e)}},function(e,t,n){"use strict";e.exports=function(){}},function(e,t,n){"use strict";var r=n(3);e.exports=function(e){if(!r(e))throw new TypeError("Cannot use null or undefined");return e}},function(e,t,n){"use strict";var r=n(3),i=Array.prototype.forEach,a=Object.create,o=function(e,t){var n;for(n in e)t[n]=e[n]};e.exports=function(e){var t=a(null);return i.call(arguments,(function(e){r(e)&&o(Object(e),t)})),t}},function(e,t,n){"use strict";e.exports=n(28)()?String.prototype.contains:n(29)},function(e,t,n){"use strict";var r="razdwatrzy";e.exports=function(){return"function"===typeof r.contains&&(!0===r.contains("dwa")&&!1===r.contains("foo"))}},function(e,t,n){"use strict";var r=String.prototype.indexOf;e.exports=function(e){return r.call(this,e,arguments[1])>-1}},function(e,t,n){"use strict";e.exports=function(e){if("function"!==typeof e)throw new TypeError(e+" is not a function");return e}},function(e,t,n){"use strict";var r=n(32),i=Object.prototype.hasOwnProperty;e.exports=function(e){var t,n=arguments[1];if(r(e),void 0===n)i.call(e,"__ee__")&&delete e.__ee__;else{if(t=i.call(e,"__ee__")&&e.__ee__,!t)return;t[n]&&delete t[n]}}},function(e,t,n){"use strict";var r=n(33);e.exports=function(e){if(!r(e))throw new TypeError(e+" is not an Object");return e}},function(e,t,n){"use strict";var r=n(3),i={function:!0,object:!0};e.exports=function(e){return r(e)&&i[typeof e]||!1}},function(e,t,n){var r,i=n(35);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default{background:#000;width:100%;height:100%;position:relative;-webkit-user-select:none;-moz-user-select:none;user-select:none;-ms-user-select:none}.xgplayer-skin-default *{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline}.xgplayer-skin-default.xgplayer-rotate-fullscreen{position:absolute;top:0;left:100%;bottom:0;right:0;height:100vw!important;width:100vh!important;-webkit-transform-origin:top left;-ms-transform-origin:top left;transform-origin:top left;-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg)}.xgplayer-skin-default.xgplayer-is-fullscreen{width:100%!important;height:100%!important;padding-top:0!important;z-index:9999}.xgplayer-skin-default.xgplayer-is-fullscreen.xgplayer-inactive{cursor:none}.xgplayer-skin-default video{width:100%;height:100%;outline:none}.xgplayer-skin-default .xgplayer-none{display:none}@-webkit-keyframes loadingRotate{0%{-webkit-transform:rotate(0);transform:rotate(0)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes loadingRotate{0%{-webkit-transform:rotate(0);transform:rotate(0)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@-webkit-keyframes loadingDashOffset{0%{stroke-dashoffset:236}to{stroke-dashoffset:0}}@keyframes loadingDashOffset{0%{stroke-dashoffset:236}to{stroke-dashoffset:0}}.xgplayer-skin-default .xgplayer-controls{display:-webkit-flex;display:-moz-box;display:flex;position:absolute;bottom:0;left:0;right:0;height:40px;background-image:linear-gradient(180deg,transparent,rgba(0,0,0,.37),rgba(0,0,0,.75),rgba(0,0,0,.75));z-index:10}.xgplayer-skin-default.xgplayer-inactive .xgplayer-controls,.xgplayer-skin-default.xgplayer-is-live .xgplayer-controls .xgplayer-progress,.xgplayer-skin-default.xgplayer-is-live .xgplayer-controls .xgplayer-time,.xgplayer-skin-default.xgplayer-no-controls .xgplayer-controls,.xgplayer-skin-default.xgplayer-nostart .xgplayer-controls{display:none}.xgplayer-skin-default.xgplayer-is-live .xgplayer-controls .xgplayer-live{display:block}.xgplayer-skin-default .xgplayer-live{display:block;font-size:12px;color:#fff;line-height:40px;-webkit-order:1;-moz-box-ordinal-group:2;order:1}.xgplayer-skin-default .xgplayer-icon{display:block;width:40px;height:40px;overflow:hidden;fill:#fff}.xgplayer-skin-default .xgplayer-icon svg{position:absolute}.xgplayer-skin-default .xgplayer-tips{background:rgba(0,0,0,.54);border-radius:1px;display:none;position:absolute;font-family:PingFangSC-Regular;font-size:11px;color:#fff;padding:2px 4px;text-align:center;top:-30px;left:50%;margin-left:-16px;width:auto;white-space:nowrap}.xgplayer-skin-default.xgplayer-mobile .xgplayer-tips{display:none!important}.xgplayer-skin-default .xgplayer-screen-container{display:block;width:100%}",""])},function(e,t){e.exports=function(e){var t="undefined"!==typeof window&&window.location;if(!t)throw new Error("fixUrls requires window.location");if(!e||"string"!==typeof e)return e;var n=t.protocol+"//"+t.host,r=n+t.pathname.replace(/\/[^\/]*$/,"/"),i=e.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,(function(e,t){var i,a=t.trim().replace(/^"(.*)"$/,(function(e,t){return t})).replace(/^'(.*)'$/,(function(e,t){return t}));return/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(a)?e:(i=0===a.indexOf("//")?a:0===a.indexOf("/")?n+a:r+a.replace(/^\.\//,""),"url("+JSON.stringify(i)+")")}));return i}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=function(){var e=this,t=e.root,n=0,i=void 0,a={first:"",second:""};function o(t){e.video.addEventListener("touchend",(function(t){e.onElementTouchend(t,e.video)})),e.video.addEventListener("touchstart",(function(){e.isTouchMove=!1})),e.video.addEventListener("touchmove",(function(){e.isTouchMove=!0})),e.config.autoplay&&e.start()}function s(){e.off("ready",o),e.off("destroy",s)}e.onElementTouchend=function(e,o){this.config.closeVideoPreventDefault||e.preventDefault(),this.config.closeVideoStopPropagation||e.stopPropagation();var s=this;if((0,r.hasClass)(t,"xgplayer-inactive")?s.emit("focus"):s.emit("blur"),!s.config.closeVideoTouch&&!s.isTouchMove){var l=function(){i=setTimeout((function(){if((0,r.hasClass)(s.root,"xgplayer-nostart"))return!1;if(!s.ended)if(s.paused){var e=s.play();void 0!==e&&e&&e.catch((function(e){}))}else s.pause();n=0}),200)};s.config.closeVideoClick||(n++,i&&clearTimeout(i),1===n?s.config.enableVideoDbltouch?a.first=new Date:l():2===n&&s.config.enableVideoDbltouch?(a.second=new Date,Math.abs(a.first-a.second)<400?l():(a.first=new Date,n=1)):n=0)}},e.once("ready",o),e.once("destroy",s)};t.default={name:"mobile",method:i},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=function(){var e=this;if(e.controls&&e.video){var t=e.controls,n=e.root,i=0,a=void 0;e.onElementClick=function(e,t){this.config.closeVideoPreventDefault||e.preventDefault(),this.config.closeVideoStopPropagation||e.stopPropagation();var n=this;n.config.closeVideoClick||(i++,a&&clearTimeout(a),1===i?a=setTimeout((function(){if((0,r.hasClass)(n.root,"xgplayer-nostart"))return!1;if(!n.ended)if(n.paused){var e=n.play();void 0!==e&&e&&e.catch((function(e){}))}else n.pause();i=0}),200):i=0)},e.video.addEventListener("click",(function(t){e.onElementClick(t,e.video)}),!1),e.onElementDblclick=function(e,n){this.config.closeVideoPreventDefault||e.preventDefault(),this.config.closeVideoStopPropagation||e.stopPropagation();var r=this;if(!r.config.closeVideoDblclick){var i=t.querySelector(".xgplayer-fullscreen");if(i){var a=void 0;document.createEvent?(a=document.createEvent("Event"),a.initEvent("click",!0,!0)):a=new Event("click"),i.dispatchEvent(a)}}},e.video.addEventListener("dblclick",(function(t){e.onElementDblclick(t,e.video)}),!1),n.addEventListener("mouseenter",o),n.addEventListener("mouseleave",s),t.addEventListener("mouseenter",l),t.addEventListener("mouseleave",u),t.addEventListener("click",c),e.once("ready",d),e.once("destroy",p)}function o(){clearTimeout(e.leavePlayerTimer),e.emit("focus",e)}function s(){e.config.closePlayerBlur||(e.leavePlayerTimer=setTimeout((function(){e.emit("blur",e)}),e.config.leavePlayerTime||0))}function l(t){e.userTimer&&clearTimeout(e.userTimer)}function u(t){e.config.closeControlsBlur||e.emit("focus",e)}function c(e){e.preventDefault(),e.stopPropagation()}function d(t){e.config.autoplay&&e.start()}function p(){n.removeEventListener("mouseenter",o),n.removeEventListener("mouseleave",s),e.off("ready",d),e.off("destroy",p)}};t.default={name:"pc",method:i},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=function(){var e=this,t=e.root;function n(){e.off("canplay",n);var t=e.play();void 0!==t&&t&&t.catch((function(e){}))}function i(){(0,r.hasClass)(t,"xgplayer-nostart")?((0,r.removeClass)(t,"xgplayer-nostart"),(0,r.addClass)(t,"xgplayer-is-enter"),"function"===typeof t.contains?e.video&&1===e.video.nodeType&&!t.contains(e.video)||e.video&&1!==e.video.nodeType&&void 0===e.video.mediaSource?(e.once("canplay",n),e.start()):n():e.video&&1===e.video.nodeType&&!t.querySelector(this.videoConfig.mediaType)||e.video&&1!==e.video.nodeType&&void 0===e.video.mediaSource?(e.once("canplay",n),e.start()):n()):e.paused&&((0,r.removeClass)(t,"xgplayer-nostart xgplayer-isloading"),setTimeout((function(){var t=e.play();void 0!==t&&t&&t.catch((function(e){}))}),10))}function a(){e.off("startBtnClick",i),e.off("canplay",n),e.off("destroy",a)}e.on("startBtnClick",i),e.once("destroy",a)};t.default={name:"start",method:i},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(41),a=l(i),o=n(42),s=l(o);function l(e){return e&&e.__esModule?e:{default:e}}n(43);var u=function(){var e=this,t=e.root,n=(0,r.createDom)("xg-start",'<div class="xgplayer-icon-play">'+a.default+'</div>\n                                      <div class="xgplayer-icon-pause">'+s.default+"</div>",{},"xgplayer-start");function i(e){(0,r.addClass)(e.root,"xgplayer-skin-default"),e.config&&(e.config.autoplay&&!(0,r.isWeiXin)()&&!(0,r.isUc)()&&(0,r.addClass)(e.root,"xgplayer-is-enter"),e.config.lang&&"en"===e.config.lang?(0,r.addClass)(e.root,"xgplayer-lang-is-en"):"jp"===e.config.lang&&(0,r.addClass)(e.root,"xgplayer-lang-is-jp"),e.config.enableContextmenu||e.video.addEventListener("contextmenu",(function(e){e.preventDefault(),e.stopPropagation()})))}e.config&&e.config.hideStartBtn&&(0,r.addClass)(t,"xgplayer-start-hide"),e.isReady?(t.appendChild(n),i(e)):e.once("ready",(function(){t.appendChild(n),i(e)})),e.once("autoplay was prevented",(function(){(0,r.removeClass)(e.root,"xgplayer-is-enter"),(0,r.addClass)(e.root,"xgplayer-nostart")})),e.once("canplay",(function(){(0,r.removeClass)(e.root,"xgplayer-is-enter")})),n.onclick=function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("startBtnClick")}};t.default={name:"s_start",method:u},e.exports=t["default"]},function(e,t,n){"use strict";n.r(t),t["default"]='<svg xmlns="http://www.w3.org/2000/svg" width="70" height="70" viewBox="0 0 70 70">\n  <path transform="translate(15,15) scale(0.04,0.04)" d="M576,363L810,512L576,661zM342,214L576,363L576,661L342,810z"></path>\n</svg>\n'},function(e,t,n){"use strict";n.r(t),t["default"]='<svg xmlns="http://www.w3.org/2000/svg" width="70" height="70" viewBox="0 0 70 70">\n  <path transform="translate(15,15) scale(0.04 0.04)" d="M598,214h170v596h-170v-596zM256 810v-596h170v596h-170z"></path>\n</svg>\n'},function(e,t,n){var r,i=n(44);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-start{border-radius:50%;display:inline-block;width:70px;height:70px;background:rgba(0,0,0,.38);overflow:hidden;text-align:center;line-height:70px;vertical-align:middle;position:absolute;left:50%;top:50%;z-index:115;margin:-35px auto auto -35px;cursor:pointer}.xgplayer-skin-default .xgplayer-start div{position:absolute}.xgplayer-skin-default .xgplayer-start div svg{fill:hsla(0,0%,100%,.7)}.xgplayer-skin-default .xgplayer-start .xgplayer-icon-play{display:block}.xgplayer-skin-default .xgplayer-start .xgplayer-icon-pause{display:none}.xgplayer-skin-default .xgplayer-start:hover{opacity:.85}.xgplayer-skin-default.xgplayer-pause.xgplayer-start-hide .xgplayer-start,.xgplayer-skin-default.xgplayer-playing .xgplayer-start,.xgplayer-skin-default.xgplayer-playing .xgplayer-start .xgplayer-icon-play,.xgplayer-skin-default.xgplayer-start-hide .xgplayer-start{display:none}.xgplayer-skin-default.xgplayer-playing .xgplayer-start .xgplayer-icon-pause{display:block}.xgplayer-skin-default.xgplayer-pause .xgplayer-start{display:inline-block}.xgplayer-skin-default.xgplayer-pause .xgplayer-start .xgplayer-icon-play{display:block}.xgplayer-skin-default.xgplayer-is-replay .xgplayer-start,.xgplayer-skin-default.xgplayer-pause .xgplayer-start .xgplayer-icon-pause{display:none}.xgplayer-skin-default.xgplayer-is-replay .xgplayer-start .xgplayer-icon-play{display:block}.xgplayer-skin-default.xgplayer-is-replay .xgplayer-start .xgplayer-icon-pause{display:none}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=function(){var e=this,t=e.root;function n(){e.config.rotateFullscreen?(0,r.hasClass)(t,"xgplayer-rotate-fullscreen")?e.exitRotateFullscreen():e.getRotateFullscreen():(0,r.hasClass)(t,"xgplayer-is-fullscreen")?e.exitFullscreen(t):e.getFullscreen(t)}function i(){var n=document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement;n&&n===t?((0,r.addClass)(t,"xgplayer-is-fullscreen"),e.emit("requestFullscreen")):(0,r.hasClass)(t,"xgplayer-is-fullscreen")&&((0,r.removeClass)(t,"xgplayer-is-fullscreen"),e.emit("exitFullscreen")),e.danmu&&"function"===typeof e.danmu.resize&&e.danmu.resize()}function a(n){var i=e.video.webkitPresentationMode;i!==r.PresentationMode.FULLSCREEN&&((0,r.removeClass)(t,"xgplayer-is-fullscreen"),e.emit("exitFullscreen"))}function o(){e.off("fullscreenBtnClick",n),["fullscreenchange","webkitfullscreenchange","mozfullscreenchange","MSFullscreenChange"].forEach((function(e){document.removeEventListener(e,i)})),(0,r.checkWebkitSetPresentationMode)(e.video)&&e.video.removeEventListener("webkitpresentationmodechanged",a),e.off("destroy",o)}e.on("fullscreenBtnClick",n),["fullscreenchange","webkitfullscreenchange","mozfullscreenchange","MSFullscreenChange"].forEach((function(e){document.addEventListener(e,i)})),e.video.addEventListener("webkitbeginfullscreen",(function(){(0,r.addClass)(t,"xgplayer-is-fullscreen"),e.emit("requestFullscreen")})),e.video.addEventListener("webkitendfullscreen",(function(){(0,r.removeClass)(t,"xgplayer-is-fullscreen"),e.emit("exitFullscreen")})),(0,r.checkWebkitSetPresentationMode)(e.video)&&e.video.addEventListener("webkitpresentationmodechanged",a),e.once("destroy",o),e.getFullscreen=function(e){var t=this;if(e.requestFullscreen){var n=e.requestFullscreen();n&&n.catch((function(){t.emit("fullscreen error")}))}else e.mozRequestFullScreen?e.mozRequestFullScreen():e.webkitRequestFullscreen?e.webkitRequestFullscreen(window.Element.ALLOW_KEYBOARD_INPUT):t.video.webkitSupportsFullscreen?t.video.webkitEnterFullscreen():e.msRequestFullscreen?e.msRequestFullscreen():(0,r.addClass)(e,"xgplayer-is-cssfullscreen")},e.exitFullscreen=function(e){document.exitFullscreen?document.exitFullscreen():document.webkitExitFullscreen?document.webkitExitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.msExitFullscreen&&document.msExitFullscreen(),(0,r.removeClass)(e,"xgplayer-is-cssfullscreen")},e.getRotateFullscreen=function(){var e=this;document.documentElement.style.width="100%",document.documentElement.style.height="100%",e.config.fluid&&(e.root.style["padding-top"]="",e.root.style["max-width"]="unset"),e.root&&!(0,r.hasClass)(e.root,"xgplayer-rotate-fullscreen")&&(0,r.addClass)(e.root,"xgplayer-rotate-fullscreen"),e.emit("getRotateFullscreen")},e.exitRotateFullscreen=function(){var e=this;document.documentElement.style.width="unset",document.documentElement.style.height="unset",e.config.fluid&&(e.root.style["width"]="100%",e.root.style["height"]="0",e.root.style["padding-top"]=100*e.config.height/e.config.width+"%",e.root.style["max-width"]="100%"),e.root&&(0,r.hasClass)(e.root,"xgplayer-rotate-fullscreen")&&(0,r.removeClass)(e.root,"xgplayer-rotate-fullscreen"),e.emit("exitRotateFullscreen")}};t.default={name:"fullscreen",method:i},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=function(){var e=this;function t(){if(e.config.allowPlayAfterEnded||!e.ended)if((0,r.hasClass)(e.root,"xgplayer-nostart")&&e.start(),e.paused){var t=e.play();void 0!==t&&t&&t.catch((function(e){}))}else e.pause()}function n(){e.off("playBtnClick",t),e.off("destroy",n)}e.on("playBtnClick",t),e.once("destroy",n)};t.default={name:"play",method:i},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=function(){var e=this,t=e.root;function n(){(0,r.removeClass)(t,"xgplayer-is-replay"),e.replay()}function i(){e.config.loop||(0,r.addClass)(t,"xgplayer-is-replay")}function a(){e.off("replayBtnClick",n),e.off("destroy",a)}e.on("replayBtnClick",n),e.on("ended",i),e.once("destroy",a)};t.default={name:"replay",method:i},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(49),a=l(i),o=n(50),s=l(o);function l(e){return e&&e.__esModule?e:{default:e}}n(51);var u=function(){var e=this,t=e.config.playBtn?e.config.playBtn:{},n=void 0;n="img"===t.type?(0,r.createImgBtn)("play",t.url.play,t.width,t.height):(0,r.createDom)("xg-play",'<xg-icon class="xgplayer-icon">\n                                      <div class="xgplayer-icon-play">'+a.default+'</div>\n                                      <div class="xgplayer-icon-pause">'+s.default+"</div>\n                                     </xg-icon>",{},"xgplayer-play");var i={};i.play=e.lang.PLAY_TIPS,i.pause=e.lang.PAUSE_TIPS;var o=(0,r.createDom)("xg-tips",'<span class="xgplayer-tip-play">'+i.play+'</span>\n                                        <span class="xgplayer-tip-pause">'+i.pause+"</span>",{},"xgplayer-tips");n.appendChild(o),e.once("ready",(function(){e.controls&&e.controls.appendChild(n)})),["click","touchend"].forEach((function(t){n.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("playBtnClick")}))}))};t.default={name:"s_play",method:u},e.exports=t["default"]},function(e,t,n){"use strict";n.r(t),t["default"]='<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">\n  <path transform="translate(2,2) scale(0.0320625 0.0320625)" d="M576,363L810,512L576,661zM342,214L576,363L576,661L342,810z"></path>\n</svg>\n'},function(e,t,n){"use strict";n.r(t),t["default"]='<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">\n  <path transform="translate(2,2) scale(0.0320625 0.0320625)" d="M598,214h170v596h-170v-596zM256 810v-596h170v596h-170z"></path>\n</svg>\n'},function(e,t,n){var r,i=n(52);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-play,.xgplayer-skin-default .xgplayer-play-img{width:40px;position:relative;-webkit-order:0;-moz-box-ordinal-group:1;order:0;display:block;cursor:pointer;margin-left:3px}.xgplayer-skin-default .xgplayer-play-img .xgplayer-icon,.xgplayer-skin-default .xgplayer-play .xgplayer-icon{margin-top:3px;width:32px}.xgplayer-skin-default .xgplayer-play-img .xgplayer-icon div,.xgplayer-skin-default .xgplayer-play .xgplayer-icon div{position:absolute}.xgplayer-skin-default .xgplayer-play-img .xgplayer-icon .xgplayer-icon-play,.xgplayer-skin-default .xgplayer-play .xgplayer-icon .xgplayer-icon-play{display:block}.xgplayer-skin-default .xgplayer-play-img .xgplayer-icon .xgplayer-icon-pause,.xgplayer-skin-default .xgplayer-play .xgplayer-icon .xgplayer-icon-pause{display:none}.xgplayer-skin-default .xgplayer-play-img .xgplayer-tips .xgplayer-tip-play,.xgplayer-skin-default .xgplayer-play .xgplayer-tips .xgplayer-tip-play{display:block}.xgplayer-skin-default .xgplayer-play-img .xgplayer-tips .xgplayer-tip-pause,.xgplayer-skin-default .xgplayer-play .xgplayer-tips .xgplayer-tip-pause{display:none}.xgplayer-skin-default .xgplayer-play-img:hover,.xgplayer-skin-default .xgplayer-play:hover{opacity:.85}.xgplayer-skin-default .xgplayer-play-img:hover .xgplayer-tips,.xgplayer-skin-default .xgplayer-play:hover .xgplayer-tips{display:block}.xgplayer-skin-default.xgplayer-playing .xgplayer-play-img .xgplayer-icon .xgplayer-icon-play,.xgplayer-skin-default.xgplayer-playing .xgplayer-play .xgplayer-icon .xgplayer-icon-play{display:none}.xgplayer-skin-default.xgplayer-playing .xgplayer-play-img .xgplayer-icon .xgplayer-icon-pause,.xgplayer-skin-default.xgplayer-playing .xgplayer-play .xgplayer-icon .xgplayer-icon-pause{display:block}.xgplayer-skin-default.xgplayer-playing .xgplayer-play-img .xgplayer-tips .xgplayer-tip-play,.xgplayer-skin-default.xgplayer-playing .xgplayer-play .xgplayer-tips .xgplayer-tip-play{display:none}.xgplayer-skin-default.xgplayer-pause .xgplayer-play-img .xgplayer-icon .xgplayer-icon-play,.xgplayer-skin-default.xgplayer-pause .xgplayer-play .xgplayer-icon .xgplayer-icon-play,.xgplayer-skin-default.xgplayer-playing .xgplayer-play-img .xgplayer-tips .xgplayer-tip-pause,.xgplayer-skin-default.xgplayer-playing .xgplayer-play .xgplayer-tips .xgplayer-tip-pause{display:block}.xgplayer-skin-default.xgplayer-pause .xgplayer-play-img .xgplayer-icon .xgplayer-icon-pause,.xgplayer-skin-default.xgplayer-pause .xgplayer-play .xgplayer-icon .xgplayer-icon-pause{display:none}.xgplayer-skin-default.xgplayer-pause .xgplayer-play-img .xgplayer-tips .xgplayer-tip-play,.xgplayer-skin-default.xgplayer-pause .xgplayer-play .xgplayer-tips .xgplayer-tip-play{display:block}.xgplayer-skin-default.xgplayer-pause .xgplayer-play-img .xgplayer-tips .xgplayer-tip-pause,.xgplayer-skin-default.xgplayer-pause .xgplayer-play .xgplayer-tips .xgplayer-tip-pause{display:none}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0);n(54);var i=function(){var e=this,t=e.root;if(e.config.poster){var n=(0,r.createDom)("xg-poster","",{},"xgplayer-poster");n.style.backgroundImage="url("+e.config.poster+")",t.appendChild(n)}};t.default={name:"s_poster",method:i},e.exports=t["default"]},function(e,t,n){var r,i=n(55);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-poster{display:none;position:absolute;left:0;top:0;width:100%;height:100%;z-index:100;background-size:cover;background-position:50%}.xgplayer-skin-default.xgplayer-nostart .xgplayer-poster{display:block}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0);n(57);var i=function(){var e=this,t=(0,r.createDom)("xg-placeholder","",{},"xgplayer-placeholder");e.controls.appendChild(t)};t.default={name:"s_flex",method:i},e.exports=t["default"]},function(e,t,n){var r,i=n(58);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-placeholder{-webkit-flex:1;-moz-box-flex:1;flex:1;-webkit-order:3;-moz-box-ordinal-group:4;order:3;display:block}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(60),a=l(i),o=n(61),s=l(o);function l(e){return e&&e.__esModule?e:{default:e}}n(62);var u=function(){var e=this,t=e.config.fullscreenBtn?e.config.fullscreenBtn:{},n=void 0;n="img"===t.type?(0,r.createImgBtn)("fullscreen",t.url.request,t.width,t.height):(0,r.createDom)("xg-fullscreen",'<xg-icon class="xgplayer-icon">\n                                             <div class="xgplayer-icon-requestfull">'+a.default+'</div>\n                                             <div class="xgplayer-icon-exitfull">'+s.default+"</div>\n                                           </xg-icon>",{},"xgplayer-fullscreen");var i={};i.requestfull=e.lang.FULLSCREEN_TIPS,i.exitfull=e.lang.EXITFULLSCREEN_TIPS;var o=(0,r.createDom)("xg-tips",'<span class="xgplayer-tip-requestfull">'+i.requestfull+'</span>\n                                        <span class="xgplayer-tip-exitfull">'+i.exitfull+"</span>",{},"xgplayer-tips");n.appendChild(o),e.once("ready",(function(){e.controls&&e.controls.appendChild(n)})),["click","touchend"].forEach((function(t){n.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("fullscreenBtnClick")}))}))};t.default={name:"s_fullscreen",method:u},e.exports=t["default"]},function(e,t,n){"use strict";n.r(t),t["default"]='<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">\n  <path transform="scale(0.0320625 0.0320625)" d="M598 214h212v212h-84v-128h-128v-84zM726 726v-128h84v212h-212v-84h128zM214 426v-212h212v84h-128v128h-84zM298 598v128h128v84h-212v-212h84z"></path>\n</svg>\n'},function(e,t,n){"use strict";n.r(t),t["default"]='<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">\n  <path transform="scale(0.0320625 0.0320625)" d="M682 342h128v84h-212v-212h84v128zM598 810v-212h212v84h-128v128h-84zM342 342v-128h84v212h-212v-84h128zM214 682v-84h212v212h-84v-128h-128z"></path>\n</svg>\n'},function(e,t,n){var r,i=n(63);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-fullscreen,.xgplayer-skin-default .xgplayer-fullscreen-img{position:relative;-webkit-order:13;-moz-box-ordinal-group:14;order:13;display:block;cursor:pointer;margin-left:5px;margin-right:3px}.xgplayer-skin-default .xgplayer-fullscreen-img .xgplayer-icon,.xgplayer-skin-default .xgplayer-fullscreen .xgplayer-icon{margin-top:3px}.xgplayer-skin-default .xgplayer-fullscreen-img .xgplayer-icon div,.xgplayer-skin-default .xgplayer-fullscreen .xgplayer-icon div{position:absolute}.xgplayer-skin-default .xgplayer-fullscreen-img .xgplayer-icon .xgplayer-icon-requestfull,.xgplayer-skin-default .xgplayer-fullscreen .xgplayer-icon .xgplayer-icon-requestfull{display:block}.xgplayer-skin-default .xgplayer-fullscreen-img .xgplayer-icon .xgplayer-icon-exitfull,.xgplayer-skin-default .xgplayer-fullscreen .xgplayer-icon .xgplayer-icon-exitfull{display:none}.xgplayer-skin-default .xgplayer-fullscreen-img .xgplayer-tips,.xgplayer-skin-default .xgplayer-fullscreen .xgplayer-tips{position:absolute;right:0;left:auto}.xgplayer-skin-default .xgplayer-fullscreen-img .xgplayer-tips .xgplayer-tip-requestfull,.xgplayer-skin-default .xgplayer-fullscreen .xgplayer-tips .xgplayer-tip-requestfull{display:block}.xgplayer-skin-default .xgplayer-fullscreen-img .xgplayer-tips .xgplayer-tip-exitfull,.xgplayer-skin-default .xgplayer-fullscreen .xgplayer-tips .xgplayer-tip-exitfull{display:none}.xgplayer-skin-default .xgplayer-fullscreen-img:hover,.xgplayer-skin-default .xgplayer-fullscreen:hover{opacity:.85}.xgplayer-skin-default .xgplayer-fullscreen-img:hover .xgplayer-tips,.xgplayer-skin-default .xgplayer-fullscreen:hover .xgplayer-tips{display:block}.xgplayer-skin-default.xgplayer-is-fullscreen .xgplayer-fullscreen-img .xgplayer-icon .xgplayer-icon-requestfull,.xgplayer-skin-default.xgplayer-is-fullscreen .xgplayer-fullscreen .xgplayer-icon .xgplayer-icon-requestfull,.xgplayer-skin-default.xgplayer-rotate-fullscreen .xgplayer-fullscreen-img .xgplayer-icon .xgplayer-icon-requestfull,.xgplayer-skin-default.xgplayer-rotate-fullscreen .xgplayer-fullscreen .xgplayer-icon .xgplayer-icon-requestfull{display:none}.xgplayer-skin-default.xgplayer-is-fullscreen .xgplayer-fullscreen-img .xgplayer-icon .xgplayer-icon-exitfull,.xgplayer-skin-default.xgplayer-is-fullscreen .xgplayer-fullscreen .xgplayer-icon .xgplayer-icon-exitfull,.xgplayer-skin-default.xgplayer-rotate-fullscreen .xgplayer-fullscreen-img .xgplayer-icon .xgplayer-icon-exitfull,.xgplayer-skin-default.xgplayer-rotate-fullscreen .xgplayer-fullscreen .xgplayer-icon .xgplayer-icon-exitfull{display:block}.xgplayer-skin-default.xgplayer-is-fullscreen .xgplayer-fullscreen-img .xgplayer-tips .xgplayer-tip-requestfull,.xgplayer-skin-default.xgplayer-is-fullscreen .xgplayer-fullscreen .xgplayer-tips .xgplayer-tip-requestfull,.xgplayer-skin-default.xgplayer-rotate-fullscreen .xgplayer-fullscreen-img .xgplayer-tips .xgplayer-tip-requestfull,.xgplayer-skin-default.xgplayer-rotate-fullscreen .xgplayer-fullscreen .xgplayer-tips .xgplayer-tip-requestfull{display:none}.xgplayer-skin-default.xgplayer-is-fullscreen .xgplayer-fullscreen-img .xgplayer-tips .xgplayer-tip-exitfull,.xgplayer-skin-default.xgplayer-is-fullscreen .xgplayer-fullscreen .xgplayer-tips .xgplayer-tip-exitfull,.xgplayer-skin-default.xgplayer-rotate-fullscreen .xgplayer-fullscreen-img .xgplayer-tips .xgplayer-tip-exitfull,.xgplayer-skin-default.xgplayer-rotate-fullscreen .xgplayer-fullscreen .xgplayer-tips .xgplayer-tip-exitfull{display:block}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(65),a=o(i);function o(e){return e&&e.__esModule?e:{default:e}}n(66);var s=function(){var e=this,t=e.root,n=(0,r.createDom)("xg-loading",""+a.default,{},"xgplayer-loading");e.once("ready",(function(){t.appendChild(n)}))};t.default={name:"s_loading",method:s},e.exports=t["default"]},function(e,t,n){"use strict";n.r(t),t["default"]='<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewbox="0 0 100 100">\n  <path d="M100,50A50,50,0,1,1,50,0"></path>\n</svg>\n'},function(e,t,n){var r,i=n(67);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-loading{display:none;width:100px;height:100px;overflow:hidden;-webkit-transform:scale(.7);-ms-transform:scale(.7);transform:scale(.7);position:absolute;left:50%;top:50%;margin:-50px auto auto -50px}.xgplayer-skin-default .xgplayer-loading svg{border-radius:50%;-webkit-transform-origin:center;-ms-transform-origin:center;transform-origin:center;-webkit-animation:loadingRotate 1s linear infinite;animation:loadingRotate 1s linear infinite}.xgplayer-skin-default .xgplayer-loading svg path{stroke:#ddd;stroke-dasharray:236;-webkit-animation:loadingDashOffset 2s linear infinite;animation:loadingDashOffset 2s linear infinite;animation-direction:alternate-reverse;fill:none;stroke-width:12px}.xgplayer-skin-default.xgplayer-nostart .xgplayer-loading{display:none}.xgplayer-skin-default.xgplayer-pause .xgplayer-loading{display:none!important}.xgplayer-skin-default.xgplayer-isloading .xgplayer-loading{display:block}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(5),a=o(i);function o(e){return e&&e.__esModule?e:{default:e}}n(69);var s=function(e){return(0,r.hasClass)(e.root,"xgplayer-rotate-fullscreen")},l=function(){var e=this,t=(0,r.createDom)("xg-progress",'<xg-outer class="xgplayer-progress-outer">\n                                                   <xg-cache class="xgplayer-progress-cache"></xg-cache>\n                                                   <xg-played class="xgplayer-progress-played">\n                                                     <xg-progress-btn class="xgplayer-progress-btn"></xg-progress-btn>\n                                                     <xg-point class="xgplayer-progress-point xgplayer-tips"></xg-point>\n                                                     <xg-thumbnail class="xgplayer-progress-thumbnail xgplayer-tips"></xg-thumbnail>\n                                                   </xg-played>\n                                                 </xg-outer>',{tabindex:1},"xgplayer-progress"),n=void 0;e.controls.appendChild(t);var i=t.querySelector(".xgplayer-progress-played"),o=t.querySelector(".xgplayer-progress-outer"),l=t.querySelector(".xgplayer-progress-cache"),u=t.querySelector(".xgplayer-progress-point"),c=t.querySelector(".xgplayer-progress-thumbnail");function d(n,i){n.addEventListener("mouseenter",(function(e){i&&((0,r.addClass)(n,"xgplayer-progress-dot-show"),(0,r.addClass)(t,"xgplayer-progress-dot-active"))})),n.addEventListener("mouseleave",(function(e){i&&((0,r.removeClass)(n,"xgplayer-progress-dot-show"),(0,r.removeClass)(t,"xgplayer-progress-dot-active"))})),n.addEventListener("touchend",(function(a){a.stopPropagation(),i&&((0,r.hasClass)(n,"xgplayer-progress-dot-show")||Object.keys(e.dotArr).forEach((function(t){e.dotArr[t]&&(0,r.removeClass)(e.dotArr[t],"xgplayer-progress-dot-show")})),(0,r.toggleClass)(n,"xgplayer-progress-dot-show"),(0,r.toggleClass)(t,"xgplayer-progress-dot-active"))}))}function p(){e.config.progressDot&&"Array"===(0,r.typeOf)(e.config.progressDot)&&e.config.progressDot.forEach((function(t){if(t.time>=0&&t.time<=e.duration){var n=(0,r.createDom)("xg-progress-dot",t.text?'<span class="xgplayer-progress-tip">'+t.text+"</span>":"",{},"xgplayer-progress-dot");if(n.style.left=t.time/e.duration*100+"%",t.duration>=0&&(n.style.width=Math.min(t.duration,e.duration-t.time)/e.duration*100+"%"),t.style)for(var i in t.style)n.style[i]=t.style[i];o.appendChild(n),e.dotArr[t.time]=n,d(n,t.text)}}))}e.dotArr={},e.once("canplay",p),e.addProgressDot=function(t,n,i,a){if(!e.dotArr[t]&&t>=0&&t<=e.duration){var s=(0,r.createDom)("xg-progress-dot",n?'<span class="xgplayer-progress-tip">'+n+"</span>":"",{},"xgplayer-progress-dot");if(s.style.left=t/e.duration*100+"%",i>=0&&(s.style.width=Math.min(i,e.duration-t)/e.duration*100+"%"),a)for(var l in a)s.style[l]=a[l];o.appendChild(s),e.dotArr[t]=s,d(s,n)}},e.removeProgressDot=function(t){if(t>=0&&t<=e.duration&&e.dotArr[t]){var n=e.dotArr[t];n.parentNode.removeChild(n),n=null,e.dotArr[t]=null}},e.removeAllProgressDot=function(){Object.keys(e.dotArr).forEach((function(t){if(e.dotArr[t]){var n=e.dotArr[t];n.parentNode.removeChild(n),n=null,e.dotArr[t]=null}}))};var f=0,h=0,g=0,y=0,v=0,m=0,x=[],b=void 0,_=void 0,k=function(){e.config.thumbnail&&(e.config.thumbnail.isShowCoverPreview&&!b&&(i.removeChild(c),b=(0,r.createDom)("xg-coverpreview",'<xg-outer class="xgplayer-coverpreview-outer">\n            <xg-thumbnail class="xgplayer-coverpreview-thumbnail"></xg-thumbnail>\n            <xg-point class="xgplayer-coverpreview-point"></xg-point>\n          </xg-outer>',{tabindex:1},"xgplayer-coverpreview"),b.querySelector(".xgplayer-coverpreview-outer"),_=b.querySelector(".xgplayer-coverpreview-point"),c=b.querySelector(".xgplayer-coverpreview-thumbnail"),e.root.appendChild(b)),f=e.config.thumbnail.pic_num,h=e.config.thumbnail.width,g=e.config.thumbnail.height,y=e.config.thumbnail.col,v=e.config.thumbnail.row,x=e.config.thumbnail.urls,c.style.width=h+"px",c.style.height=g+"px")};e.on("loadedmetadata",k),"function"===typeof e.config.disableSwipeHandler&&"function"===typeof e.config.enableSwipeHandler&&(e.root.addEventListener("touchmove",(function(t){t.preventDefault(),e.disableSwipe||(e.disableSwipe=!0,e.config.disableSwipeHandler.call(e))})),e.root.addEventListener("touchstart",(function(t){e.disableSwipe=!0,e.config.disableSwipeHandler.call(e)})),e.root.addEventListener("touchend",(function(t){e.disableSwipe=!1,e.config.enableSwipeHandler.call(e)})));var E=["touchstart","mousedown"];"mobile"===a.default.device&&E.pop(),E.forEach((function(o){t.addEventListener(o,(function(o){if(!e.config.disableProgress){if(o.stopPropagation(),(0,r.event)(o),o._target===u||!e.config.allowSeekAfterEnded&&e.ended)return!0;t.focus();var l=i.getBoundingClientRect(),d=l.left,p=s(e);p?(d=i.getBoundingClientRect().top,n=t.getBoundingClientRect().height):(n=t.getBoundingClientRect().width,d=i.getBoundingClientRect().left);var k=function(t){t.stopPropagation(),(0,r.event)(t),e.isProgressMoving=!0;var a=(p?t.clientY:t.clientX)-d;a>n&&(a=n);var o=a/n*e.duration;if(o<0&&(o=0),e.config.allowSeekPlayed&&Number(o).toFixed(1)>e.maxPlayedTime);else if(i.style.width=100*a/n+"%","video"!==e.videoConfig.mediaType||e.dash||e.config.closeMoveSeek){var s=(0,r.findDom)(e.controls,".xgplayer-time");s&&(s.innerHTML='<span class="xgplayer-time-current">'+(0,r.format)(o||0)+"</span><span>"+(0,r.format)(e.duration)+"</span>")}else console.log("trigger touchmove"),e.currentTime=Number(o).toFixed(1);if(e.config.thumbnail&&e.config.thumbnail.isShowCoverPreview){_.innerHTML="<span>"+(0,r.format)(o)+"</span> / "+(0,r.format)(e.duration||0),m=e.duration/f;var l=Math.floor(o/m);c.style.backgroundImage="url("+x[Math.ceil((l+1)/(y*v))-1]+")";var u=l+1-y*v*(Math.ceil((l+1)/(y*v))-1),k=Math.ceil(u/v)-1,E=u-k*v-1;c.style["background-position"]="-"+E*h+"px -"+k*g+"px",b.style.display="block"}e.emit("focus")},E=function o(s){if(console.log("up event",s),s.stopPropagation(),(0,r.event)(s),window.removeEventListener("mousemove",k),window.removeEventListener("touchmove",k,{passive:!1}),window.removeEventListener("mouseup",o),window.removeEventListener("touchend",o),a.default.browser.indexOf("ie")<0&&t.blur(),!e.isProgressMoving||e.videoConfig&&"audio"===e.videoConfig.mediaType||e.dash||e.config.closeMoveSeek){var l=(p?s.clientY:s.clientX)-d;l>n&&(l=n);var u=l/n*e.duration;u<0&&(u=0),e.config.allowSeekPlayed&&Number(u).toFixed(1)>e.maxPlayedTime||(i.style.width=100*l/n+"%",console.warn("trigger touchup"),e.currentTime=Number(u).toFixed(1))}e.config.thumbnail&&e.config.thumbnail.isShowCoverPreview&&(b.style.display="none"),e.emit("focus"),e.isProgressMoving=!1};return window.addEventListener("touchmove",k,{passive:!1}),window.addEventListener("touchend",E),window.addEventListener("mousemove",k),window.addEventListener("mouseup",E),!0}}))})),t.addEventListener("mouseenter",(function(n){if(!e.config.allowSeekAfterEnded&&e.ended)return!0;var i=s(e),a=i?t.getBoundingClientRect().top:t.getBoundingClientRect().left,o=i?t.getBoundingClientRect().height:t.getBoundingClientRect().width,l=function(n){var s=((i?n.clientY:n.clientX)-a)/o*e.duration;s=s<0?0:s,u.textContent=(0,r.format)(s);var l=u.getBoundingClientRect().width;if(e.config.thumbnail&&!e.config.thumbnail.isShowCoverPreview){m=e.duration/f;var d=Math.floor(s/m);c.style.backgroundImage="url("+x[Math.ceil((d+1)/(y*v))-1]+")";var p=d+1-y*v*(Math.ceil((d+1)/(y*v))-1),b=Math.ceil(p/v)-1,_=p-b*v-1;c.style["background-position"]="-"+_*h+"px -"+b*g+"px";var k=(i?n.clientY:n.clientX)-a-h/2;k=k>0?k:0,k=k<o-h?k:o-h,c.style.left=k+"px",c.style.top=-10-g+"px",c.style.display="block",u.style.left=k+h/2-l/2+"px"}else{var E=n.clientX-a-l/2;E=E>0?E:0,E=E>o-l?o-l:E,u.style.left=E+"px"}(0,r.hasClass)(t,"xgplayer-progress-dot-active")?u.style.display="none":u.style.display="block"},d=function(e){l(e)},p=function n(r){t.removeEventListener("mousemove",d,!1),t.removeEventListener("mouseleave",n,!1),l(r),u.style.display="none",e.config.thumbnail&&!e.config.thumbnail.isShowCoverPreview&&(c.style.display="none")};t.addEventListener("mousemove",d,!1),t.addEventListener("mouseleave",p,!1),l(n)}),!1);var w=function(){if(void 0===e.maxPlayedTime&&(e.maxPlayedTime=0),e.maxPlayedTime<e.currentTime&&(e.maxPlayedTime=e.currentTime),!n&&t&&(n=t.getBoundingClientRect().width),!e.isProgressMoving&&!e.isSeeking&&!e.seeking){var r=e.currentTime/e.duration,a=Number(i.style.width.replace("%","")||"0")/Number(t.style.width||"100");Math.abs(r-a)<=1&&(i.style.width=100*e.currentTime/e.duration+"%")}};e.on("timeupdate",w);var T=function(t){i.style.width=100*t/e.duration+"%"};e.on("currentTimeChange",T);var S=function(){i.style.width="0%"};e.on("srcChange",S);var C=function(){var t=e.buffered;if(t&&t.length>0){for(var n=t.end(t.length-1),r=0,i=t.length;r<i;r++)if(e.currentTime>=t.start(r)&&e.currentTime<=t.end(r)){n=t.end(r);for(var a=r+1;a<t.length;a++)if(t.start(a)-t.end(a-1)>=2){n=t.end(a-1);break}break}l.style.width=n/e.duration*100+"%"}},O=["bufferedChange","cacheupdate","ended","timeupdate"];function A(){e.removeAllProgressDot(),e.off("canplay",p),e.off("timeupdate",w),e.off("currentTimeChange",T),e.off("srcChange",S),e.off("loadedmetadata",k),O.forEach((function(t){e.off(t,C)})),e.off("destroy",A)}O.forEach((function(t){e.on(t,C)})),e.once("destroy",A)};t.default={name:"s_progress",method:l},e.exports=t["default"]},function(e,t,n){var r,i=n(70);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-progress{display:block;position:absolute;height:20px;line-height:20px;left:12px;right:12px;outline:none;top:-15px;z-index:35}.xgplayer-skin-default .xgplayer-progress-outer{background:hsla(0,0%,100%,.3);display:block;height:3px;line-height:3px;margin-top:8.5px;width:100%;position:relative;cursor:pointer}.xgplayer-skin-default .xgplayer-progress-cache,.xgplayer-skin-default .xgplayer-progress-played{display:block;height:100%;line-height:1;position:absolute;left:0;top:0}.xgplayer-skin-default .xgplayer-progress-cache{width:0;background:hsla(0,0%,100%,.5)}.xgplayer-skin-default .xgplayer-progress-played{display:block;width:0;background-image:linear-gradient(-90deg,#fa1f41,#e31106);border-radius:0 1.5px 1.5px 0}.xgplayer-skin-default .xgplayer-progress-btn{display:none;position:absolute;left:0;top:-5px;width:13px;height:13px;border-radius:30px;background:#fff;box-shadow:0 0 2px 0 rgba(0,0,0,.26);left:100%;-webkit-transform:translate(-50%);-ms-transform:translate(-50%);transform:translate(-50%);z-index:36}.xgplayer-skin-default .xgplayer-progress-point{position:absolute}.xgplayer-skin-default .xgplayer-progress-point.xgplayer-tips{margin-left:0;top:-25px;display:none;z-index:100}.xgplayer-skin-default .xgplayer-progress-dot{display:inline-block;position:absolute;height:3px;width:5px;top:0;background:#fff;border-radius:6px;z-index:16}.xgplayer-skin-default .xgplayer-progress-dot .xgplayer-progress-tip{position:absolute;bottom:200%;right:50%;-webkit-transform:translateX(50%);-ms-transform:translateX(50%);transform:translateX(50%);height:auto;line-height:30px;width:auto;background:rgba(0,0,0,.3);border-radius:6px;border:1px solid rgba(0,0,0,.8);cursor:default;white-space:nowrap;display:none}.xgplayer-skin-default .xgplayer-progress-dot-show .xgplayer-progress-tip{display:block}.xgplayer-skin-default .xgplayer-progress-thumbnail{position:absolute;-moz-box-sizing:border-box;box-sizing:border-box}.xgplayer-skin-default .xgplayer-progress-thumbnail.xgplayer-tips{margin-left:0;display:none;z-index:99}.xgplayer-skin-default .xgplayer-coverpreview{position:absolute;width:100%;height:100%;top:0;left:0;display:none}.xgplayer-skin-default .xgplayer-coverpreview .xgplayer-coverpreview-outer{position:absolute;display:block;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.xgplayer-skin-default .xgplayer-coverpreview .xgplayer-coverpreview-outer .xgplayer-coverpreview-thumbnail{display:block}.xgplayer-skin-default .xgplayer-coverpreview .xgplayer-coverpreview-outer .xgplayer-coverpreview-point{display:block;text-align:center;font-family:PingFangSC-Regular;font-size:11px;color:#ccc;padding:2px 4px}.xgplayer-skin-default .xgplayer-coverpreview .xgplayer-coverpreview-outer .xgplayer-coverpreview-point span{color:#fff}.xgplayer-skin-default .xgplayer-progress:focus .xgplayer-progress-outer,.xgplayer-skin-default .xgplayer-progress:hover .xgplayer-progress-outer{height:6px;margin-top:7px}.xgplayer-skin-default .xgplayer-progress:focus .xgplayer-progress-dot,.xgplayer-skin-default .xgplayer-progress:hover .xgplayer-progress-dot{height:6px}.xgplayer-skin-default .xgplayer-progress:focus .xgplayer-progress-btn,.xgplayer-skin-default .xgplayer-progress:hover .xgplayer-progress-btn{display:block;top:-3px}.xgplayer-skin-default.xgplayer-definition-active .xgplayer-progress,.xgplayer-skin-default.xgplayer-playbackrate-active .xgplayer-progress,.xgplayer-skin-default.xgplayer-texttrack-active .xgplayer-progress,.xgplayer-skin-default.xgplayer-volume-active .xgplayer-progress{z-index:15}.xgplayer-skin-default.xgplayer-mobile .xgplayer-progress-btn{display:block!important}.xgplayer-skin-default.xgplayer-mobile .xgplayer-progress:focus .xgplayer-progress-outer,.xgplayer-skin-default.xgplayer-mobile .xgplayer-progress:hover .xgplayer-progress-outer{height:3px!important;margin-top:8.5px!important}.xgplayer-skin-default.xgplayer-mobile .xgplayer-progress:focus .xgplayer-progress-btn,.xgplayer-skin-default.xgplayer-mobile .xgplayer-progress:hover .xgplayer-progress-btn{display:block!important;top:-5px!important}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0);n(72);var i=function(){var e=this,t=(0,r.createDom)("xg-time",'<span class="xgplayer-time-current">'+(e.currentTime||(0,r.format)(0))+"</span>\n                                           <span>"+(e.duration||(0,r.format)(0))+"</span>",{},"xgplayer-time");e.once("ready",(function(){e.controls&&e.controls.appendChild(t)}));var n=function(){"audio"===e.videoConfig.mediaType&&e.isProgressMoving&&e.dash||(t.innerHTML='<span class="xgplayer-time-current">'+(0,r.format)(e.currentTime||0)+"</span><span>"+(0,r.format)(e.duration)+"</span>")};function i(){e.off("durationchange",n),e.off("timeupdate",n),e.off("destroy",i)}e.on("durationchange",n),e.on("timeupdate",n),e.once("destroy",i)};t.default={name:"s_time",method:i},e.exports=t["default"]},function(e,t,n){var r,i=n(73);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,'.xgplayer-skin-default .xgplayer-time{-webkit-order:2;-moz-box-ordinal-group:3;order:2;font-family:ArialMT;font-size:13px;color:#fff;line-height:40px;height:40px;text-align:center;display:inline-block;margin:auto 8px}.xgplayer-skin-default .xgplayer-time span{color:hsla(0,0%,100%,.5)}.xgplayer-skin-default .xgplayer-time .xgplayer-time-current{color:#fff}.xgplayer-skin-default .xgplayer-time .xgplayer-time-current:after{content:"/";display:inline-block;padding:0 3px}',""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(75),a=o(i);function o(e){return e&&e.__esModule?e:{default:e}}n(76);var s=function(){var e=this,t=e.root,n=e.lang.REPLAY,i=(0,r.createDom)("xg-replay",a.default+'\n                                         <xg-replay-txt class="xgplayer-replay-txt">'+n+"</xg-replay-txt>\n                                        ",{},"xgplayer-replay");function o(){var e=i.querySelector("path");if(e){var t=window.getComputedStyle(e).getPropertyValue("transform");if("string"===typeof t&&t.indexOf("none")>-1)return;e.setAttribute("transform",t)}}function s(e){e.preventDefault(),e.stopPropagation()}e.once("ready",(function(){t.appendChild(i)})),e.on("ended",o),i.addEventListener("click",s);var l=i.querySelector("svg");function u(){e.off("ended",o),e.off("destroy",u)}["click","touchend"].forEach((function(t){l.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("replayBtnClick")}))})),e.once("destroy",u)};t.default={name:"s_replay",method:s},e.exports=t["default"]},function(e,t,n){"use strict";n.r(t),t["default"]='<svg class="xgplayer-replay-svg" xmlns="http://www.w3.org/2000/svg" width="78" height="78" viewbox="0 0 78 78">\n  <path d="M8.22708362,13.8757234 L11.2677371,12.6472196 C11.7798067,12.4403301 12.3626381,12.6877273 12.5695276,13.1997969 L12.9441342,14.1269807 C13.1510237,14.6390502 12.9036264,15.2218816 12.3915569,15.4287712 L6.8284538,17.6764107 L5.90126995,18.0510173 C5.38920044,18.2579068 4.80636901,18.0105096 4.5994795,17.49844 L1.97723335,11.0081531 C1.77034384,10.4960836 2.0177411,9.91325213 2.52981061,9.70636262 L3.45699446,9.33175602 C3.96906396,9.12486652 4.5518954,9.37226378 4.75878491,9.88433329 L5.67885163,12.1615783 C7.99551726,6.6766934 13.3983951,3 19.5,3 C27.7842712,3 34.5,9.71572875 34.5,18 C34.5,26.2842712 27.7842712,33 19.5,33 C15.4573596,33 11.6658607,31.3912946 8.87004692,28.5831991 C8.28554571,27.9961303 8.28762719,27.0463851 8.87469603,26.4618839 C9.46176488,25.8773827 10.4115101,25.8794641 10.9960113,26.466533 C13.2344327,28.7147875 16.263503,30 19.5,30 C26.127417,30 31.5,24.627417 31.5,18 C31.5,11.372583 26.127417,6 19.5,6 C14.4183772,6 9.94214483,9.18783811 8.22708362,13.8757234 Z"></path>\n</svg>\n'},function(e,t,n){var r,i=n(77);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-replay{position:absolute;left:0;top:0;width:100%;height:100%;z-index:105;display:none;-webkit-justify-content:center;-moz-box-pack:center;justify-content:center;-webkit-align-items:center;-moz-box-align:center;align-items:center;background:rgba(0,0,0,.54);-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;flex-direction:column}.xgplayer-skin-default .xgplayer-replay svg{background:rgba(0,0,0,.58);border-radius:100%;cursor:pointer}.xgplayer-skin-default .xgplayer-replay svg path{-webkit-transform:translate(20px,21px);-ms-transform:translate(20px,21px);transform:translate(20px,21px);fill:#ddd}.xgplayer-skin-default .xgplayer-replay svg:hover{background:rgba(0,0,0,.38)}.xgplayer-skin-default .xgplayer-replay svg:hover path{fill:#fff}.xgplayer-skin-default .xgplayer-replay .xgplayer-replay-txt{display:inline-block;font-family:PingFangSC-Regular;font-size:14px;color:#fff;line-height:34px}.xgplayer-skin-default.xgplayer.xgplayer-ended .xgplayer-controls{display:none}.xgplayer-skin-default.xgplayer.xgplayer-ended .xgplayer-replay{display:-webkit-flex;display:-moz-box;display:flex}",""])},function(e,t,n){var r,i=n(161);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){e.exports=n(80)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(9),i=Q(r),a=n(81),o=Q(a),s=n(82),l=Q(s),u=n(83),c=Q(u),d=n(84),p=Q(d),f=n(85),h=Q(f),g=n(88),y=Q(g),v=n(45),m=Q(v),x=n(89),b=Q(x),_=n(90),k=Q(_),E=n(91),w=Q(E),T=n(92),S=Q(T),C=n(37),O=Q(C),A=n(38),R=Q(A),D=n(98),L=Q(D),P=n(46),M=Q(P),I=n(99),B=Q(I),U=n(100),j=Q(U),N=n(47),F=Q(N),z=n(101),V=Q(z),G=n(102),H=Q(G),q=n(103),W=Q(q),Y=n(39),X=Q(Y),K=n(104),$=Q(K),Z=n(105),J=Q(Z);function Q(e){return e&&e.__esModule?e:{default:e}}n(107),i.default.installAll([o.default,l.default,c.default,p.default,h.default,y.default,m.default,b.default,k.default,w.default,S.default,O.default,R.default,L.default,M.default,B.default,j.default,F.default,V.default,H.default,W.default,X.default,$.default,J.default]),t.default=i.default,e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){var e=this;function t(){e.video.webkitShowPlaybackTargetPicker()}function n(){e.off("airplayBtnClick",t),e.off("destroy",n)}e.config.airplay&&window.WebKitPlaybackTargetAvailabilityEvent&&(e.on("airplayBtnClick",t),e.once("destroy",n))};t.default={name:"airplay",method:r},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=function(){var e=this,t=e.root;function n(){(0,r.hasClass)(t,"xgplayer-is-cssfullscreen")?e.exitCssFullscreen():e.getCssFullscreen()}function i(){e.off("cssFullscreenBtnClick",n),e.off("destroy",i)}e.on("cssFullscreenBtnClick",n),e.on("exitFullscreen",(function(){(0,r.removeClass)(t,"xgplayer-is-cssfullscreen")})),e.once("destroy",i),e.getCssFullscreen=function(){var e=this;e.config.fluid&&(e.root.style["padding-top"]=""),(0,r.addClass)(e.root,"xgplayer-is-cssfullscreen"),e.emit("requestCssFullscreen")},e.exitCssFullscreen=function(){var e=this;e.config.fluid&&(e.root.style["width"]="100%",e.root.style["height"]="0",e.root.style["padding-top"]=100*e.config.height/e.config.width+"%"),(0,r.removeClass)(e.root,"xgplayer-is-cssfullscreen"),e.emit("exitCssFullscreen")}};t.default={name:"cssFullscreen",method:i},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=function(){var e=this;function t(t){var n=e.root.querySelector("xg-danmu");if((0,r.addClass)(n,"xgplayer-has-danmu"),!e.config.danmu.closeDefaultBtn){var i=function(){t.start()},a=function(){(0,r.hasClass)(e.danmuBtn,"danmu-switch-active")&&t.pause()},o=function(){(0,r.hasClass)(e.danmuBtn,"danmu-switch-active")&&t.play()},s=function(){(0,r.hasClass)(e.danmuBtn,"danmu-switch-active")&&(t.stop(),t.start())},l=function t(){e.off("timeupdate",i),e.off("pause",a),e.off("play",o),e.off("seeked",s),e.off("destroy",t)};e.danmuBtn=(0,r.copyDom)(t.bulletBtn.createSwitch(!0)),e.controls.appendChild(e.danmuBtn),["click","touchend"].forEach((function(a){e.danmuBtn.addEventListener(a,(function(a){a.preventDefault(),a.stopPropagation(),(0,r.toggleClass)(e.danmuBtn,"danmu-switch-active"),(0,r.hasClass)(e.danmuBtn,"danmu-switch-active")?(e.emit("danmuBtnOn"),(0,r.addClass)(n,"xgplayer-has-danmu"),e.once("timeupdate",i)):(e.emit("danmuBtnOff"),(0,r.removeClass)(n,"xgplayer-has-danmu"),t.stop())}))})),e.onElementClick&&n.addEventListener("click",(function(t){e.onElementClick(t,n)}),!1),e.onElementDblclick&&n.addEventListener("dblclick",(function(t){e.onElementDblclick(t,n)}),!1),e.on("pause",a),e.on("play",o),e.on("seeked",s),e.once("destroy",l)}}e.on("initDefaultDanmu",t)};t.default={name:"danmu",method:i},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){var e=this;function t(){e.off("destroy",t)}e.once("destroy",t)};t.default={name:"definition",method:r},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(86),i=o(r),a=n(87);function o(e){return e&&e.__esModule?e:{default:e}}var s=function(){var e=this;function t(){e.download()}function n(){e.off("downloadBtnClick",t),e.off("destroy",n)}e.on("downloadBtnClick",t),e.once("destroy",n),e.download=function(){var e=(0,a.getAbsoluteURL)(this.config.url);(0,i.default)(e)}};t.default={name:"download",method:s},e.exports=t["default"]},function(e,t,n){var r,i,a;(function(n,o){i=[],r=o,a="function"===typeof r?r.apply(t,i):r,void 0===a||(e.exports=a)})(0,(function(){return function e(t,n,r){var i,a,o=window,s="application/octet-stream",l=r||s,u=t,c=!n&&!r&&u,d=document.createElement("a"),p=function(e){return String(e)},f=o.Blob||o.MozBlob||o.WebKitBlob||p,h=n||"download";if(f=f.call?f.bind(o):Blob,"true"===String(this)&&(u=[u,l],l=u[0],u=u[1]),c&&c.length<2048&&(h=c.split("/").pop().split("?")[0],d.href=c,-1!==d.href.indexOf(c))){var g=new XMLHttpRequest;return g.open("GET",c,!0),g.responseType="blob",g.onload=function(t){e(t.target.response,h,s)},setTimeout((function(){g.send()}),0),g}if(/^data:([\w+-]+\/[\w+.-]+)?[,;]/.test(u)){if(!(u.length>2096103.424&&f!==p))return navigator.msSaveBlob?navigator.msSaveBlob(x(u),h):b(u);u=x(u),l=u.type||s}else if(/([\x80-\xff])/.test(u)){var y=0,v=new Uint8Array(u.length),m=v.length;for(y;y<m;++y)v[y]=u.charCodeAt(y);u=new f([v],{type:l})}function x(e){var t=e.split(/[:;,]/),n=t[1],r="base64"==t[2]?atob:decodeURIComponent,i=r(t.pop()),a=i.length,o=0,s=new Uint8Array(a);for(o;o<a;++o)s[o]=i.charCodeAt(o);return new f([s],{type:n})}function b(e,t){if("download"in d)return d.href=e,d.setAttribute("download",h),d.className="download-js-link",d.innerHTML="downloading...",d.style.display="none",document.body.appendChild(d),setTimeout((function(){d.click(),document.body.removeChild(d),!0===t&&setTimeout((function(){o.URL.revokeObjectURL(d.href)}),250)}),66),!0;if(/(Version)\/(\d+)\.(\d+)(?:\.(\d+))?.*Safari\//.test(navigator.userAgent))return/^data:/.test(e)&&(e="data:"+e.replace(/^data:([\w\/\-\+]+)/,s)),window.open(e)||confirm("Displaying New Document\n\nUse Save As... to download, then click back to return to this page.")&&(location.href=e),!0;var n=document.createElement("iframe");document.body.appendChild(n),!t&&/^data:/.test(e)&&(e="data:"+e.replace(/^data:([\w\/\-\+]+)/,s)),n.src=e,setTimeout((function(){document.body.removeChild(n)}),333)}if(i=u instanceof f?u:new f([u],{type:l}),navigator.msSaveBlob)return navigator.msSaveBlob(i,h);if(o.URL)b(o.URL.createObjectURL(i),!0);else{if("string"===typeof i||i.constructor===p)try{return b("data:"+l+";base64,"+o.btoa(i))}catch(_){return b("data:"+l+","+encodeURIComponent(i))}a=new FileReader,a.onload=function(e){b(this.result)},a.readAsDataURL(i)}return!0}}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.getAbsoluteURL=function(e){if(!e.match(/^https?:\/\//)){var t=document.createElement("div");t.innerHTML='<a href="'+e+'">x</a>',e=t.firstChild.href}return e}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(4),i=a(r);function a(e){return e&&e.__esModule?e:{default:e}}var o={maxCount:3,backupUrl:"",isFetch:!0,fetchTimeout:100};function s(){var e=this,t=this;if(t.config.errorConfig&&!(t.src.indexOf("blob:")>-1)){var n={},r=t.config.errorConfig;for(var a in o)void 0===r[a]?n[a]=o[a]:n[a]=r[a];t.retryData={count:0,errfTimer:null,isFetchReturn:!1,currentTime:0};var s=t._onError;t._onError=function(r){var a=e.retryData.count;if(a>n.maxCount)n.isFetch?l(e,e.currentSrc,n.fetchTimeout).then((function(t){e.emit("error",new i.default({type:"network",currentTime:e.currentTime,duration:e.duration||0,networkState:e.networkState,readyState:e.readyState,currentSrc:e.currentSrc,src:e.src,ended:e.ended,httpCode:t.status,httpMsg:t.statusText,errd:{line:101,msg:e.error,handle:"plugin errorRetry"},errorCode:e.video&&e.video.error.code,mediaError:e.video&&e.video.error})),s.call(e,t)})):s.call(e,r);else{0===a&&(e.retryData.currentTime=e.currentTime,e.once("canplay",u.bind(e)));var o="";o=n.count<2?n.backupUrl?n.backupUrl:t.currentSrc:n.backupUrl&&a>1?n.backupUrl:t.currentSrc,e.retryData.count++,e.src=o}}}function l(e,t,n){var r=function(t,n){e.retryData.isFetchReturn||(e.retryData.isFetchReturn=!0,t(n))};return new Promise((function(i,a){try{var o=new window.XMLHttpRequest;o.open("get",t),o.onload=function(){r(i,{status:o.status,statusText:o.statusText,xhr:o})},o.onerror=function(){r(i,{status:o.status,statusText:o.statusText||"The network environment is disconnected or the address is invalid",xhr:o})},o.onabort=function(){},e.retryData.errfTimer=window.setTimeout((function(){var t=e.retryData.errfTimer;window.clearTimeout(t),e.retryData.errfTimer=null,r(i,{status:-1,statusText:"request timeout"})}),n),o.send()}catch(s){e.retryData.isFetchReturn=!0,r(i,{status:-2,statusText:"request error"})}}))}function u(){this.currentTime=this.retryData.currentTime,this.play(),this.retryData.retryCode=0,this.retryData.isFetchReturn=!1,this.retryData.currentTime=0}}t.default={name:"errorretry",method:s},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=n(0);function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var o=function(){function e(t){a(this,e),this.player=t,this.state={playbackRate:0,isRepeat:!1,keyCode:0,repeat:0,isBody:!1},this.timer=null,this.initEvents()}return r(e,[{key:"initEvents",value:function(){var e=this,t=this.player,n=t.root,r=t.config;if(this.player.onBodyKeydown=this.onBodyKeydown.bind(this),this.player.onKeydown=this.onKeydown.bind(this),this.player.onKeyup=this.onKeyup.bind(this),!r.keyShortcut||"on"===r.keyShortcut){document.addEventListener("keydown",this.player.onBodyKeydown),n.addEventListener("keydown",this.player.onKeydown);var a=function(){document.removeEventListener("keydown",e.player.onBodyKeydown),n.removeEventListener("keydown",e.player.onKeydown),clearTimeout(e.timer),e.timer=null};(0,i.on)(this.player,"destroy",a)}}},{key:"checkTarget",value:function(e){var t=this.player;return e.target===t.root||e.target===t.video||e.target===t.controls}},{key:"onBodyKeydown",value:function(e){var t=e||window.event,n=t.keyCode;if(t.target===document.body&&(37===n||39===n||32===n))return t.preventDefault(),t.cancelBubble=!0,t.returnValue=!1,t.repeat||document.addEventListener("keyup",this.player.onKeyup),this.handler(t),!1}},{key:"onKeydown",value:function(e){var t=e||window.event,n=t.keyCode;if(this.checkTarget(t)&&(37===n||38===n||39===n||40===n||32===n||27===n))return t.preventDefault(),t.cancelBubble=!0,t.returnValue=!1,this.player.emit("focus"),t.repeat||this.player.root.addEventListener("keyup",this.player.onKeyup),this.handler(t),!1}},{key:"onKeyup",value:function(){var e=this.state,t=this.player;document.removeEventListener("keyup",this.player.onKeyup),t.root.removeEventListener("keyup",this.player.onKeyup),e.keyCode&&(0!==e.playbackRate&&(t.playbackRate=e.playbackRate),e.isRepeat||this.handlerKeyCode(e.keyCode,!1),e.playbackRate=0,e.isRepeat=!1,e.keyCode=0,e.repeat=0,this.changeVolumeSlide())}},{key:"handler",value:function(e){var t=this.state,n=this.player;t.keyCode=e.keyCode,t.isRepeat=e.repeat,e.repeat&&(n.config.disableLongPress?this.handlerKeyCode(t.keyCode,!1):t.repeat%2===0&&this.handlerKeyCode(t.keyCode,!0),t.repeat++)}},{key:"handlerKeyCode",value:function(e,t){var n=this.player,r=this.state;switch(e){case 39:t?0===r.repeat&&this.changeRate():this.seek(!1,t);break;case 37:this.seek(!0,t);break;case 38:this.changeVolume(!0);break;case 40:this.changeVolume(!1);break;case 32:t||(n.paused?n.play():n.pause());break;case 27:(0,i.hasClass)(n.root,"xgplayer-is-cssfullscreen")&&n.exitCssFullscreen();break;default:}}},{key:"seek",value:function(e,t){var n=this.player,r=n.config.keyShortcutStep||{},i=r.currentTime||10;n.isLoading||n.isSeeking||t&&this.state.repeat%8>0||(e?n.currentTime-i>=0?n.currentTime-=i:n.currentTime=0:n.maxPlayedTime&&n.config.allowSeekPlayed&&n.currentTime+i>n.maxPlayedTime?n.currentTime=n.maxPlayedTime:n.currentTime+i<=n.duration?n.currentTime+=i:n.currentTime=n.duration+1)}},{key:"changeRate",value:function(){this.state.playbackRate=this.player.playbackRate,this.player.playbackRate=this.player.config.keyboardRate||5}},{key:"changeVolumeSlide",value:function(e){var t=this.player;t.controls&&(e?(t.emit("focus"),(0,i.hasClass)(t.root,"xgplayer-volume-active")||(0,i.addClass)(t.root,"xgplayer-volume-active")):(clearTimeout(this.timer),this.timer=setTimeout((function(){(0,i.removeClass)(t.root,"xgplayer-volume-active")}),1e3)))}},{key:"changeVolume",value:function(e){var t=this.player,n=t.config.keyShortcutStep||{},r=n.volume||.1;this.changeVolumeSlide(!0);var i=t.volume;e&&i+r<=1?t.volume=i+r:!e&&i-r>=0&&(t.volume=i-r)}}]),e}(),s=function(){var e=this;e.keyboard=new o(e)};t.default={name:"keyboard",method:s},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=function(){var e=this,t=e.root;function n(n){e.uploadFile=n.files[0];var i=URL.createObjectURL(e.uploadFile);if((0,r.hasClass)(t,"xgplayer-nostart"))e.config.url=i,e.start();else{e.src=i;var a=e.play();void 0!==a&&a&&a.catch((function(e){}))}}function i(){e.off("upload",n),e.off("destroy",i)}e.on("upload",n),e.once("destroy",i)};t.default={name:"localPreview",method:i},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){var e=this;e.on("memoryPlayStart",(function(t){setTimeout((function(){console.log("memoryPlayStart",t,e.readyState,11),e.currentTime=t}))}))};t.default={name:"memoryPlay",method:r},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(93),a=o(i);function o(e){return e&&e.__esModule?e:{default:e}}var s=function(){var e=this,t=e.root;function n(){(0,r.hasClass)(t,"xgplayer-miniplayer-active")?e.exitMiniplayer():e.getMiniplayer()}function i(){e.off("miniplayerBtnClick",n),e.off("destroy",i)}e.on("miniplayerBtnClick",n),e.once("destroy",i),e.getMiniplayer=function(){(0,r.hasClass)(t,"xgplayer-is-fullscreen")&&this.exitFullscreen(t),(0,r.hasClass)(t,"xgplayer-is-cssfullscreen")&&this.exitCssFullscreen(),(0,r.hasClass)(t,"xgplayer-rotate-fullscreen")&&this.exitRotateFullscreen();var e=(0,r.createDom)("xg-miniplayer-lay","<div></div>",{},"xgplayer-miniplayer-lay");this.root.appendChild(e);var n=(0,r.createDom)("xg-miniplayer-drag",'<div class="drag-handle"><span>'+this.lang.MINIPLAYER_DRAG+"</span></div>",{tabindex:9},"xgplayer-miniplayer-drag");this.root.appendChild(n);new a.default(".xgplayer",{handle:".drag-handle"});(0,r.addClass)(this.root,"xgplayer-miniplayer-active"),this.root.style.right=0,this.root.style.bottom="200px",this.root.style.top="",this.root.style.left="",this.root.style.width="320px",this.root.style.height="180px",this.config.miniplayerConfig&&(void 0!==this.config.miniplayerConfig.top&&(this.root.style.top=this.config.miniplayerConfig.top+"px",this.root.style.bottom=""),void 0!==this.config.miniplayerConfig.bottom&&(this.root.style.bottom=this.config.miniplayerConfig.bottom+"px"),void 0!==this.config.miniplayerConfig.left&&(this.root.style.left=this.config.miniplayerConfig.left+"px",this.root.style.right=""),void 0!==this.config.miniplayerConfig.right&&(this.root.style.right=this.config.miniplayerConfig.right+"px"),void 0!==this.config.miniplayerConfig.width&&(this.root.style.width=this.config.miniplayerConfig.width+"px"),void 0!==this.config.miniplayerConfig.height&&(this.root.style.height=this.config.miniplayerConfig.height+"px")),this.config.fluid&&(this.root.style["padding-top"]="");var i=this;["click","touchend"].forEach((function(t){e.addEventListener(t,(function(e){e.preventDefault(),e.stopPropagation(),i.exitMiniplayer()}))}))},e.exitMiniplayer=function(){(0,r.removeClass)(this.root,"xgplayer-miniplayer-active"),this.root.style.right="",this.root.style.bottom="",this.root.style.top="",this.root.style.left="",this.config.fluid?(this.root.style["width"]="100%",this.root.style["height"]="0",this.root.style["padding-top"]=100*this.config.height/this.config.width+"%"):(this.config.width&&("number"!==typeof this.config.width?this.root.style.width=this.config.width:this.root.style.width=this.config.width+"px"),this.config.height&&("number"!==typeof this.config.height?this.root.style.height=this.config.height:this.root.style.height=this.config.height+"px"));var e=(0,r.findDom)(this.root,".xgplayer-miniplayer-lay");e&&e.parentNode&&e.parentNode.removeChild(e);var t=(0,r.findDom)(this.root,".xgplayer-miniplayer-drag");t&&t.parentNode&&t.parentNode.removeChild(t)}};t.default={name:"miniplayer",method:s},e.exports=t["default"]},function(e,t,n){var r,i;
/*!
 * Draggabilly v2.4.1
 * Make that shiz draggable
 * https://draggabilly.desandro.com
 * MIT license
 */(function(a,o){r=[n(94),n(95)],i=function(e,t){return o(a,e,t)}.apply(t,r),void 0===i||(e.exports=i)})(window,(function(e,t,n){function r(e,t){for(var n in t)e[n]=t[n];return e}function i(){}var a=e.jQuery;function o(e,t){this.element="string"==typeof e?document.querySelector(e):e,a&&(this.$element=a(this.element)),this.options=r({},this.constructor.defaults),this.option(t),this._create()}var s=o.prototype=Object.create(n.prototype);o.defaults={},s.option=function(e){r(this.options,e)};var l={relative:!0,absolute:!0,fixed:!0};function u(e,t,n){return n=n||"round",t?Math[n](e/t)*t:e}return s._create=function(){this.position={},this._getPosition(),this.startPoint={x:0,y:0},this.dragPoint={x:0,y:0},this.startPosition=r({},this.position);var e=getComputedStyle(this.element);l[e.position]||(this.element.style.position="relative"),this.on("pointerMove",this.onPointerMove),this.on("pointerUp",this.onPointerUp),this.enable(),this.setHandles()},s.setHandles=function(){this.handles=this.options.handle?this.element.querySelectorAll(this.options.handle):[this.element],this.bindHandles()},s.dispatchEvent=function(e,t,n){var r=[t].concat(n);this.emitEvent(e,r),this.dispatchJQueryEvent(e,t,n)},s.dispatchJQueryEvent=function(t,n,r){var i=e.jQuery;if(i&&this.$element){var a=i.Event(n);a.type=t,this.$element.trigger(a,r)}},s._getPosition=function(){var e=getComputedStyle(this.element),t=this._getPositionCoord(e.left,"width"),n=this._getPositionCoord(e.top,"height");this.position.x=isNaN(t)?0:t,this.position.y=isNaN(n)?0:n,this._addTransformPosition(e)},s._getPositionCoord=function(e,n){if(-1!=e.indexOf("%")){var r=t(this.element.parentNode);return r?parseFloat(e)/100*r[n]:0}return parseInt(e,10)},s._addTransformPosition=function(e){var t=e.transform;if(0===t.indexOf("matrix")){var n=t.split(","),r=0===t.indexOf("matrix3d")?12:4,i=parseInt(n[r],10),a=parseInt(n[r+1],10);this.position.x+=i,this.position.y+=a}},s.onPointerDown=function(e,t){this.element.classList.add("is-pointer-down"),this.dispatchJQueryEvent("pointerDown",e,[t])},s.pointerDown=function(e,t){var n=this.okayPointerDown(e);n&&this.isEnabled?(this.pointerDownPointer={pageX:t.pageX,pageY:t.pageY},e.preventDefault(),this.pointerDownBlur(),this._bindPostStartEvents(e),this.element.classList.add("is-pointer-down"),this.dispatchEvent("pointerDown",e,[t])):this._pointerReset()},s.dragStart=function(e,t){this.isEnabled&&(this._getPosition(),this.measureContainment(),this.startPosition.x=this.position.x,this.startPosition.y=this.position.y,this.setLeftTop(),this.dragPoint.x=0,this.dragPoint.y=0,this.element.classList.add("is-dragging"),this.dispatchEvent("dragStart",e,[t]),this.animate())},s.measureContainment=function(){var e=this.getContainer();if(e){var n=t(this.element),r=t(e),i=this.element.getBoundingClientRect(),a=e.getBoundingClientRect(),o=r.borderLeftWidth+r.borderRightWidth,s=r.borderTopWidth+r.borderBottomWidth,l=this.relativeStartPosition={x:i.left-(a.left+r.borderLeftWidth),y:i.top-(a.top+r.borderTopWidth)};this.containSize={width:r.width-o-l.x-n.width,height:r.height-s-l.y-n.height}}},s.getContainer=function(){var e=this.options.containment;if(e){var t=e instanceof HTMLElement;return t?e:"string"==typeof e?document.querySelector(e):this.element.parentNode}},s.onPointerMove=function(e,t,n){this.dispatchJQueryEvent("pointerMove",e,[t,n])},s.dragMove=function(e,t,n){if(this.isEnabled){var r=n.x,i=n.y,a=this.options.grid,o=a&&a[0],s=a&&a[1];r=u(r,o),i=u(i,s),r=this.containDrag("x",r,o),i=this.containDrag("y",i,s),r="y"==this.options.axis?0:r,i="x"==this.options.axis?0:i,this.position.x=this.startPosition.x+r,this.position.y=this.startPosition.y+i,this.dragPoint.x=r,this.dragPoint.y=i,this.dispatchEvent("dragMove",e,[t,n])}},s.containDrag=function(e,t,n){if(!this.options.containment)return t;var r="x"==e?"width":"height",i=this.relativeStartPosition[e],a=u(-i,n,"ceil"),o=this.containSize[r];return o=u(o,n,"floor"),Math.max(a,Math.min(o,t))},s.onPointerUp=function(e,t){this.element.classList.remove("is-pointer-down"),this.dispatchJQueryEvent("pointerUp",e,[t])},s.dragEnd=function(e,t){this.isEnabled&&(this.element.style.transform="",this.setLeftTop(),this.element.classList.remove("is-dragging"),this.dispatchEvent("dragEnd",e,[t]))},s.animate=function(){if(this.isDragging){this.positionDrag();var e=this;requestAnimationFrame((function(){e.animate()}))}},s.setLeftTop=function(){this.element.style.left=this.position.x+"px",this.element.style.top=this.position.y+"px"},s.positionDrag=function(){this.element.style.transform="translate3d( "+this.dragPoint.x+"px, "+this.dragPoint.y+"px, 0)"},s.staticClick=function(e,t){this.dispatchEvent("staticClick",e,[t])},s.setPosition=function(e,t){this.position.x=e,this.position.y=t,this.setLeftTop()},s.enable=function(){this.isEnabled=!0},s.disable=function(){this.isEnabled=!1,this.isDragging&&this.dragEnd()},s.destroy=function(){this.disable(),this.element.style.transform="",this.element.style.left="",this.element.style.top="",this.element.style.position="",this.unbindHandles(),this.$element&&this.$element.removeData("draggabilly")},s._init=i,a&&a.bridget&&a.bridget("draggabilly",o),o}))},function(e,t,n){var r,i;
/*!
 * getSize v2.0.3
 * measure size of elements
 * MIT license
 */(function(a,o){r=o,i="function"===typeof r?r.call(t,n,t,e):r,void 0===i||(e.exports=i)})(window,(function(){"use strict";function e(e){var t=parseFloat(e),n=-1==e.indexOf("%")&&!isNaN(t);return n&&t}function t(){}var n="undefined"==typeof console?t:function(e){console.error(e)},r=["paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom","borderLeftWidth","borderRightWidth","borderTopWidth","borderBottomWidth"],i=r.length;function a(){for(var e={width:0,height:0,innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0},t=0;t<i;t++){var n=r[t];e[n]=0}return e}function o(e){var t=getComputedStyle(e);return t||n("Style returned "+t+". Are you running this code in a hidden iframe on Firefox? See https://bit.ly/getsizebug1"),t}var s,l=!1;function u(){if(!l){l=!0;var t=document.createElement("div");t.style.width="200px",t.style.padding="1px 2px 3px 4px",t.style.borderStyle="solid",t.style.borderWidth="1px 2px 3px 4px",t.style.boxSizing="border-box";var n=document.body||document.documentElement;n.appendChild(t);var r=o(t);s=200==Math.round(e(r.width)),c.isBoxSizeOuter=s,n.removeChild(t)}}function c(t){if(u(),"string"==typeof t&&(t=document.querySelector(t)),t&&"object"==typeof t&&t.nodeType){var n=o(t);if("none"==n.display)return a();var l={};l.width=t.offsetWidth,l.height=t.offsetHeight;for(var c=l.isBorderBox="border-box"==n.boxSizing,d=0;d<i;d++){var p=r[d],f=n[p],h=parseFloat(f);l[p]=isNaN(h)?0:h}var g=l.paddingLeft+l.paddingRight,y=l.paddingTop+l.paddingBottom,v=l.marginLeft+l.marginRight,m=l.marginTop+l.marginBottom,x=l.borderLeftWidth+l.borderRightWidth,b=l.borderTopWidth+l.borderBottomWidth,_=c&&s,k=e(n.width);!1!==k&&(l.width=k+(_?0:g+x));var E=e(n.height);return!1!==E&&(l.height=E+(_?0:y+b)),l.innerWidth=l.width-(g+x),l.innerHeight=l.height-(y+b),l.outerWidth=l.width+v,l.outerHeight=l.height+m,l}}return c}))},function(e,t,n){var r,i;
/*!
 * Unidragger v2.4.0
 * Draggable base class
 * MIT license
 */(function(a,o){r=[n(96)],i=function(e){return o(a,e)}.apply(t,r),void 0===i||(e.exports=i)})(window,(function(e,t){"use strict";function n(){}var r=n.prototype=Object.create(t.prototype);r.bindHandles=function(){this._bindHandles(!0)},r.unbindHandles=function(){this._bindHandles(!1)},r._bindHandles=function(t){t=void 0===t||t;for(var n=t?"addEventListener":"removeEventListener",r=t?this._touchActionValue:"",i=0;i<this.handles.length;i++){var a=this.handles[i];this._bindStartEvent(a,t),a[n]("click",this),e.PointerEvent&&(a.style.touchAction=r)}},r._touchActionValue="none",r.pointerDown=function(e,t){var n=this.okayPointerDown(e);n&&(this.pointerDownPointer={pageX:t.pageX,pageY:t.pageY},e.preventDefault(),this.pointerDownBlur(),this._bindPostStartEvents(e),this.emitEvent("pointerDown",[e,t]))};var i={TEXTAREA:!0,INPUT:!0,SELECT:!0,OPTION:!0},a={radio:!0,checkbox:!0,button:!0,submit:!0,image:!0,file:!0};return r.okayPointerDown=function(e){var t=i[e.target.nodeName],n=a[e.target.type],r=!t||n;return r||this._pointerReset(),r},r.pointerDownBlur=function(){var e=document.activeElement,t=e&&e.blur&&e!=document.body;t&&e.blur()},r.pointerMove=function(e,t){var n=this._dragPointerMove(e,t);this.emitEvent("pointerMove",[e,t,n]),this._dragMove(e,t,n)},r._dragPointerMove=function(e,t){var n={x:t.pageX-this.pointerDownPointer.pageX,y:t.pageY-this.pointerDownPointer.pageY};return!this.isDragging&&this.hasDragStarted(n)&&this._dragStart(e,t),n},r.hasDragStarted=function(e){return Math.abs(e.x)>3||Math.abs(e.y)>3},r.pointerUp=function(e,t){this.emitEvent("pointerUp",[e,t]),this._dragPointerUp(e,t)},r._dragPointerUp=function(e,t){this.isDragging?this._dragEnd(e,t):this._staticClick(e,t)},r._dragStart=function(e,t){this.isDragging=!0,this.isPreventingClicks=!0,this.dragStart(e,t)},r.dragStart=function(e,t){this.emitEvent("dragStart",[e,t])},r._dragMove=function(e,t,n){this.isDragging&&this.dragMove(e,t,n)},r.dragMove=function(e,t,n){e.preventDefault(),this.emitEvent("dragMove",[e,t,n])},r._dragEnd=function(e,t){this.isDragging=!1,setTimeout(function(){delete this.isPreventingClicks}.bind(this)),this.dragEnd(e,t)},r.dragEnd=function(e,t){this.emitEvent("dragEnd",[e,t])},r.onclick=function(e){this.isPreventingClicks&&e.preventDefault()},r._staticClick=function(e,t){this.isIgnoringMouseUp&&"mouseup"==e.type||(this.staticClick(e,t),"mouseup"!=e.type&&(this.isIgnoringMouseUp=!0,setTimeout(function(){delete this.isIgnoringMouseUp}.bind(this),400)))},r.staticClick=function(e,t){this.emitEvent("staticClick",[e,t])},n.getPointerPoint=t.getPointerPoint,n}))},function(e,t,n){var r,i;
/*!
 * Unipointer v2.4.0
 * base class for doing one thing with pointer event
 * MIT license
 */(function(a,o){r=[n(97)],i=function(e){return o(a,e)}.apply(t,r),void 0===i||(e.exports=i)})(window,(function(e,t){"use strict";function n(){}function r(){}var i=r.prototype=Object.create(t.prototype);i.bindStartEvent=function(e){this._bindStartEvent(e,!0)},i.unbindStartEvent=function(e){this._bindStartEvent(e,!1)},i._bindStartEvent=function(t,n){n=void 0===n||n;var r=n?"addEventListener":"removeEventListener",i="mousedown";"ontouchstart"in e?i="touchstart":e.PointerEvent&&(i="pointerdown"),t[r](i,this)},i.handleEvent=function(e){var t="on"+e.type;this[t]&&this[t](e)},i.getTouch=function(e){for(var t=0;t<e.length;t++){var n=e[t];if(n.identifier==this.pointerIdentifier)return n}},i.onmousedown=function(e){var t=e.button;t&&0!==t&&1!==t||this._pointerDown(e,e)},i.ontouchstart=function(e){this._pointerDown(e,e.changedTouches[0])},i.onpointerdown=function(e){this._pointerDown(e,e)},i._pointerDown=function(e,t){e.button||this.isPointerDown||(this.isPointerDown=!0,this.pointerIdentifier=void 0!==t.pointerId?t.pointerId:t.identifier,this.pointerDown(e,t))},i.pointerDown=function(e,t){this._bindPostStartEvents(e),this.emitEvent("pointerDown",[e,t])};var a={mousedown:["mousemove","mouseup"],touchstart:["touchmove","touchend","touchcancel"],pointerdown:["pointermove","pointerup","pointercancel"]};return i._bindPostStartEvents=function(t){if(t){var n=a[t.type];n.forEach((function(t){e.addEventListener(t,this)}),this),this._boundPointerEvents=n}},i._unbindPostStartEvents=function(){this._boundPointerEvents&&(this._boundPointerEvents.forEach((function(t){e.removeEventListener(t,this)}),this),delete this._boundPointerEvents)},i.onmousemove=function(e){this._pointerMove(e,e)},i.onpointermove=function(e){e.pointerId==this.pointerIdentifier&&this._pointerMove(e,e)},i.ontouchmove=function(e){var t=this.getTouch(e.changedTouches);t&&this._pointerMove(e,t)},i._pointerMove=function(e,t){this.pointerMove(e,t)},i.pointerMove=function(e,t){this.emitEvent("pointerMove",[e,t])},i.onmouseup=function(e){this._pointerUp(e,e)},i.onpointerup=function(e){e.pointerId==this.pointerIdentifier&&this._pointerUp(e,e)},i.ontouchend=function(e){var t=this.getTouch(e.changedTouches);t&&this._pointerUp(e,t)},i._pointerUp=function(e,t){this._pointerDone(),this.pointerUp(e,t)},i.pointerUp=function(e,t){this.emitEvent("pointerUp",[e,t])},i._pointerDone=function(){this._pointerReset(),this._unbindPostStartEvents(),this.pointerDone()},i._pointerReset=function(){this.isPointerDown=!1,delete this.pointerIdentifier},i.pointerDone=n,i.onpointercancel=function(e){e.pointerId==this.pointerIdentifier&&this._pointerCancel(e,e)},i.ontouchcancel=function(e){var t=this.getTouch(e.changedTouches);t&&this._pointerCancel(e,t)},i._pointerCancel=function(e,t){this._pointerDone(),this.pointerCancel(e,t)},i.pointerCancel=function(e,t){this.emitEvent("pointerCancel",[e,t])},r.getPointerPoint=function(e){return{x:e.pageX,y:e.pageY}},r}))},function(e,t,n){var r,i;(function(a,o){r=o,i="function"===typeof r?r.call(t,n,t,e):r,void 0===i||(e.exports=i)})("undefined"!=typeof window&&window,(function(){"use strict";function e(){}var t=e.prototype;return t.on=function(e,t){if(e&&t){var n=this._events=this._events||{},r=n[e]=n[e]||[];return-1==r.indexOf(t)&&r.push(t),this}},t.once=function(e,t){if(e&&t){this.on(e,t);var n=this._onceEvents=this._onceEvents||{},r=n[e]=n[e]||{};return r[t]=!0,this}},t.off=function(e,t){var n=this._events&&this._events[e];if(n&&n.length){var r=n.indexOf(t);return-1!=r&&n.splice(r,1),this}},t.emitEvent=function(e,t){var n=this._events&&this._events[e];if(n&&n.length){n=n.slice(0),t=t||[];for(var r=this._onceEvents&&this._onceEvents[e],i=0;i<n.length;i++){var a=n[i],o=r&&r[a];o&&(this.off(e,a),delete r[a]),a.apply(this,t)}return this}},t.allOff=function(){delete this._events,delete this._onceEvents},e}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=function(){var e=this,t=this;function n(){t.video!==document.pictureInPictureElement?t.video.requestPictureInPicture():document.exitPictureInPicture()}t.on("pipBtnClick",n);var i=function(n){var i=t.video.webkitPresentationMode;e.pMode=i,i===r.PresentationMode.PIP?t.emit("requestPictureInPicture",n.pictureInPictureWindow):i===r.PresentationMode.INLINE&&t.emit("exitPictureInPicture")};function a(){t.off("pipBtnClick",n),t.off("destroy",a),(0,r.checkWebkitSetPresentationMode)(t.video)&&t.video.removeEventListener("webkitpresentationmodechanged",i)}t.video.addEventListener("enterpictureinpicture",(function(e){(0,r.addClass)(t.root,"xgplayer-pip-active"),t.emit("requestPictureInPicture",e)})),t.video.addEventListener("leavepictureinpicture",(function(){(0,r.removeClass)(t.root,"xgplayer-pip-active"),t.emit("exitPictureInPicture")})),(0,r.checkWebkitSetPresentationMode)(t.video)&&t.video.addEventListener("webkitpresentationmodechanged",i),t.once("destroy",a)};t.default={name:"pip",method:i},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){var e=this,t=e.config.playNext;function n(){e.currentVideoIndex+1<t.urlList.length&&(e.currentVideoIndex++,e.video.autoplay=!0,e.src=t.urlList[e.currentVideoIndex],e.emit("playerNext",e.currentVideoIndex+1),e.currentVideoIndex+1===t.urlList.length&&e.emit("urlListEnd"))}function r(){e.off("playNextBtnClick",n),e.off("destroy",r)}e.currentVideoIndex=-1,e.on("playNextBtnClick",n),e.once("destroy",r)};t.default={name:"playNext",method:r},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=function(){var e=this,t=e.config.reload;function n(){(0,r.removeClass)(e.root,"xgplayer-is-error"),e.src=e.config.url}function i(){e.off("reloadBtnClick",n),e.off("destroy",i)}t&&(e.on("reloadBtnClick",n),e.once("destroy",i))};t.default={name:"reload",method:i},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){var e=this,t=e.config.rotate;function n(){e.rotate(t.clockwise,t.innerRotate)}function r(){e.off("rotateBtnClick",n),e.off("destroy",r)}t&&(e.on("rotateBtnClick",n),e.once("destroy",r),e.updateRotateDeg=function(){var e=this;e.rotateDeg||(e.rotateDeg=0);var t=e.root.offsetWidth,n=e.root.offsetHeight,r=e.video.videoWidth,i=e.video.videoHeight;!e.config.rotate.innerRotate&&e.config.rotate.controlsFix&&(e.root.style.width=n+"px",e.root.style.height=t+"px");var a=void 0;if(.25===e.rotateDeg||.75===e.rotateDeg){if(e.config.rotate.innerRotate)if(r/i>n/t){var o=0;o=i/r>n/t?n*r/i:t,a=n/o}else{var s=0;s=i/r>n/t?n:t*i/r,a=t/s}else a=t>=n?t/n:n/t;a=Number(a.toFixed(5))}else a=1;e.config.rotate.innerRotate||e.config.rotate.controlsFix?(e.video.style.transformOrigin="center center",e.video.style.transform="rotate("+e.rotateDeg+"turn) scale("+a+")",e.video.style.webKitTransform="rotate("+e.rotateDeg+"turn) scale("+a+")"):(e.root.style.transformOrigin="center center",e.root.style.transform="rotate("+e.rotateDeg+"turn) scale(1)",e.root.style.webKitTransform="rotate("+e.rotateDeg+"turn) scale(1)")},e.rotate=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this;n.rotateDeg||(n.rotateDeg=0);var r=e?1:-1;n.rotateDeg=(n.rotateDeg+1+.25*r*t)%1,this.updateRotateDeg(),n.emit("rotate",360*n.rotateDeg)})};t.default={name:"rotate",method:r},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){var e=this,t=e.config.screenShot,n=null;if(t){e.video.setAttribute("crossOrigin","anonymous");var r=.92;(t.quality||0===t.quality)&&(r=t.quality);var i=void 0===t.type?"image/png":t.type,a=void 0===t.format?".png":t.format,o=document.createElement("canvas"),s=o.getContext("2d"),l=new Image;o.width=this.config.width||600,o.height=this.config.height||337.5;var u=function(e,t){var n=document.createElement("a");n.href=e,n.download=t;var r=document.createEvent("MouseEvents");r.initMouseEvent("click",!0,!1,window,0,0,0,0,0,!1,!1,!1,!1,0,null),n.dispatchEvent(r)};e.screenShot=function(){var c=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];c=void 0===t.saveImg?c:t.saveImg,o.width=e.video.videoWidth||600,o.height=e.video.videoHeight||337.5,n=t.callBack,l.onload=function(){s.drawImage(e.video,0,0,o.width,o.height),l.src=o.toDataURL(i,r).replace(i,"image/octet-stream");var d=l.src.replace(/^data:image\/[^;]+/,"data:application/octet-stream"),p=t.fileName||e.lang.SCREENSHOT;e.emit("screenShot",d),c&&n?n(d,p,a):c&&u(d,p+a)}()},e.on("screenShotBtnClick",e.screenShot),e.once("destroy",c)}function c(){e.off("screenShotBtnClick",e.screenShot),e.off("destroy",c)}};t.default={name:"screenShot",method:r},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(4),i=a(r);function a(e){return e&&e.__esModule?e:{default:e}}function o(){var e=this;if(e.config.enableStallCheck){var t=0,n=void 0,r=void 0;e.once("complete",(function(){setInterval((function(){e.currentTime-(t||0)>.1||e.paused?1!==n&&2!==n||(n=0,clearTimeout(r),r=null):n||(n=1,r=setTimeout((function(){1===n&&(n=2,e.emit("error",new i.default("STALL"))),r=null}),2e4)),t=e.currentTime}),1e3)}))}}t.default={name:"stallCheck",method:o},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(5),a=o(i);function o(e){return e&&e.__esModule?e:{default:e}}var s=function(){var e=this,t=e.root,n=void 0,i=void 0,o=void 0,s=void 0;function l(){e.controls&&(e.volume=e.config.volume,n=e.controls.querySelector(".xgplayer-volume"),n&&(i=n.querySelector(".xgplayer-slider"),o=n.querySelector(".xgplayer-bar"),s=n.querySelector(".xgplayer-drag"),"mobile"===a.default.device&&(0===e.volume&&(e.video.muted=!0),h())))}function u(t){if(i){e.video.muted=!1,i.focus(),(0,r.event)(t);var n=o.getBoundingClientRect(),a={x:t.clientX,y:t.clientY},l=s.getBoundingClientRect().height,u=!1,c=function(t){t.preventDefault(),t.stopPropagation(),(0,r.event)(t),u=!0;var i=l-t.clientY+a.y,o=i/n.height;s.style.height=i+"px",e.volume=Math.max(Math.min(o,1),0)},d=function t(a){if(a.preventDefault(),a.stopPropagation(),(0,r.event)(a),window.removeEventListener("mousemove",c),window.removeEventListener("touchmove",c),window.removeEventListener("mouseup",t),window.removeEventListener("touchend",t),!u){var o=n.height-(a.clientY-n.top),l=o/n.height;s.style.height=o+"px",l<=0&&(e.volume>0?s.volume=e.video.volume:l=s.volume),e.volume=Math.max(Math.min(l,1),0)}i.volume=e.volume,u=!1};return window.addEventListener("mousemove",c),window.addEventListener("touchmove",c),window.addEventListener("mouseup",d),window.addEventListener("touchend",d),!1}}function c(){if("mobile"===a.default.device)e.video.muted?(e.video.muted=!1,e.emit("unmute"),e.volume=1):(e.video.muted=!0,e.emit("mute"),e.volume=0);else{if(!i)return;e.video.muted=!1,e.volume<.1?(i.volume<.1?e.volume=.6:e.volume=i.volume,e.emit("unmute")):(e.volume=0,e.emit("mute"))}}function d(){(0,r.addClass)(t,"xgplayer-volume-active"),n&&n.focus()}function p(){(0,r.removeClass)(t,"xgplayer-volume-active")}e.once("canplay",l),e.on("volumeBarClick",u),e.on("volumeIconClick",c),e.on("volumeIconEnter",d),e.on("volumeIconLeave",p);var f=null;function h(){f&&clearTimeout(f),f=setTimeout((function(){if("mobile"===a.default.device)(0,r.removeClass)(t,"xgplayer-volume-muted"),(0,r.removeClass)(t,"xgplayer-volume-large"),e.video.muted||e.video.defaultMuted?(e.video.muted||(e.video.muted=!0),e.video.defaultMuted=!1,(0,r.addClass)(t,"xgplayer-volume-muted")):(0,r.addClass)(t,"xgplayer-volume-large");else{if((0,r.removeClass)(t,"xgplayer-volume-muted"),(0,r.removeClass)(t,"xgplayer-volume-small"),(0,r.removeClass)(t,"xgplayer-volume-large"),0===e.volume||e.muted?(0,r.addClass)(t,"xgplayer-volume-muted"):e.volume<.5?(0,r.addClass)(t,"xgplayer-volume-small"):(0,r.addClass)(t,"xgplayer-volume-large"),!o)return;var n=o.getBoundingClientRect().height||76;s.style.height=e.volume*n+"px"}}),50)}function g(){e.off("canplay",l),e.off("volumeBarClick",u),e.off("volumeIconClick",c),e.off("volumeIconEnter",d),e.off("volumeIconLeave",p),e.off("volumechange",h),e.off("destroy",g),f&&(clearTimeout(f),f=null)}e.on("volumechange",h),e.once("destroy",g)};t.default={name:"volume",method:s},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=n(106),a=o(i);function o(e){return e&&e.__esModule?e:{default:e}}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var l={follow:!0,mode:"stroke",followBottom:50,fitVideo:!0,offsetBottom:2,baseSizeX:49,baseSizeY:28,minSize:16,minMobileSize:13,line:"double",fontColor:"#fff"},u=function(){function e(t,n,r){var i=this;s(this,e);var a=this.create(n,r,t.textTrackShowDefault);a.attachPlayer(t),this.subtitle=a,this.player=t,this.positionData={vBottom:0,marginBottom:0},this.isActive=!1,this.followBottom=r.followBottom,["onSubtitleResize","onFocus","onBlur"].map((function(e){i[e]=i[e].bind(i)})),t.controls&&r.follow&&(this.subtitle.on("resize",this.onSubtitleResize),t.on("focus",this.onFocus),t.on("blur",this.onBlur))}return r(e,[{key:"create",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r={subTitles:e,defaultOpen:n};return Object.keys(t).map((function(e){r[e]=t[e]})),new a.default(r)}},{key:"switch",value:function(e){return this.subtitle.switch(e)}},{key:"switchOff",value:function(){return this.subtitle.switchOff()}},{key:"setSubTitles",value:function(e,t,n){return this.subtitle.setSubTitles(e,t,n)}},{key:"onFocus",value:function(){var e=this.positionData,t=e.marginBottom,n=e.vBottom;if(!this.isActive&&t){this.isActive=!0;var r=t+n;this.followBottom>r&&(r=this.followBottom),this.subtitle&&(this.subtitle.root.style.bottom=r+"px")}}},{key:"onBlur",value:function(){this.isActive=!1;var e=this.positionData.vBottom+this.positionData.marginBottom;this.subtitle&&(this.subtitle.root.style.bottom=e+"px")}},{key:"onSubtitleResize",value:function(e){this.positionData.vBottom=e.vBottom,this.positionData.marginBottom=e.marginBottom}},{key:"destroy",value:function(){this.subtitle.off("resize",this.onSubtitleResize),this.player.off("focus",this.onFocus),this.player.off("blur",this.onBlur),this.subtitle.destroy(),this.subtitle=null}}]),e}(),c=function(){var e=this,t=this,n=t.config.textTrack;if(n){var r=t.config.textTrackStyle||{};Object.keys(l).map((function(e){void 0===r[e]&&(r[e]=l[e])})),t.textTrackShowDefault=!1,t.config.textTrack.map((function(e,n){e.id||e.language||(e.id=n),!e.url&&(e.url=e.src),!e.language&&(e.language=e.srclang),void 0===e.isDefault&&(e.isDefault=e.default),!t.textTrackShowDefault&&(t.textTrackShowDefault=e.isDefault||e.default)})),this.subTitles=new u(t,t.config.textTrack,r),t.setSubTitles=function(n){var r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=!1;n.map((function(e,t){e.id||e.language||(e.id=t),!e.url&&(e.url=e.src),!e.language&&(e.language=e.srclang),void 0===e.isDefault&&(e.isDefault=e.default),e.isDefault&&(i=!0)})),t.textTrackShowDefault=i,e.subTitles.setSubTitles(n,i,r),t.emit("subtitle_change",{off:!1,isListUpdate:!0,list:n})},t.switchSubTitle=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{id:"",language:""};e.subTitles.switch(n).then((function(e){0===e.code&&(n.off=!1,n.isListUpdate=!1,n.list=[],t.emit("subtitle_change",n))}))},t.switchOffSubtile=function(){e.subTitles.switchOff(),t.emit("subtitle_change",{off:!0,isListUpdate:!1,list:[]})},t.once("destroy",i)}function i(){this.subTitles.destroy(),this.subTitles=null}};t.default={name:"textTrack",method:c},e.exports=t["default"]},function(e,t,n){!function(t,n){e.exports=n()}(0,(function(){"use strict";function e(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function t(e,t){return t={exports:{}},e(t,t.exports),t.exports}function n(e){var t,n;this.promise=new e((function(e,r){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=r})),this.resolve=U(t),this.reject=U(n)}function r(e){var t=e.length;return 3===t?(60*(60*Number(e[0])+Number(e[1]))*1e3+1e3*Number(e[2]))/1e3:2===t?(60*Number(e[0])*1e3+1e3*Number(e[1]))/1e3:Number(e[0])}function i(e){return/^(\-|\+)?\d+(\.\d+)?$/.test(e)}function a(e){return e}function o(e,t){return e>=0&&e<t.length?t[e]:""}function s(e,t){if(!e)return!1;if(e.classList)return Array.prototype.some.call(e.classList,(function(e){return e===t}));var n=e.className&&"object"===Xt(e.className)?e.getAttribute("class"):e.className;return n&&!!n.match(new RegExp("(\\s|^)"+t+"(\\s|$)"))}function l(e,t){e&&(e.classList?t.replace(/(^\s+|\s+$)/g,"").split(/\s+/g).forEach((function(t){t&&e.classList.add(t)})):s(e,t)||(e.className&&"object"===Xt(e.className)?e.setAttribute("class",e.getAttribute("class")+" "+t):e.className+=" "+t))}function u(e,t){e&&(e.classList?t.split(/\s+/g).forEach((function(t){e.classList.remove(t)})):s(e,t)&&t.split(/\s+/g).forEach((function(t){var n=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className&&"object"===Xt(e.className)?e.setAttribute("class",e.getAttribute("class").replace(n," ")):e.className=e.className.replace(n," ")})))}function c(e,t,n){var r=t.length;if(r<1)return-1;if(n=n<0?0:n>=r?r-1:n,t[n].start<=e&&e<t[n].end)return n;for(var i=t[n].end<=e?n+1:0;i<r;i++){if(t[i].start<=e&&e<t[i].end)return i;if(e>t[i].end&&i+1<r&&e<t[i+1].start)return-1;if(e>t[i].end&&i+1>=r)return-1}return-1}function d(e,t,n){var r=t.length;if(r<1)return[];var i=[];if((n=n<0?0:n>=r?r-1:n)<r)for(var a=t[n].end<=e?n:0;a<r&&(t[a].start<=e&&e<t[a].end&&i.push(a),!(e<t[a].start));a++);return i}function p(e){return Object.prototype.toString.call(e).match(/([^\s.*]+)(?=]$)/g)[0]}function f(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"div",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",i=document.createElement(e);return i.className=r,i.innerHTML=t,kr(n).forEach((function(t){var r=t,a=n[t];"video"===e||"audio"===e||"live-video"===e?a&&i.setAttribute(r,a):i.setAttribute(r,a)})),i}function h(){var e=navigator.userAgent,t=/(?:Windows Phone)/.test(e),n=/(?:SymbianOS)/.test(e)||t,r=/(?:Android)/.test(e),i=/(?:Firefox)/.test(e),a=/(?:iPad|PlayBook)/.test(e)||r&&!/(?:Mobile)/.test(e)||i&&/(?:Tablet)/.test(e);return/(?:iPhone)/.test(e)&&!a||r||n||a}function g(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n="";e.map((function(e){n+=" "+t+" "+e.key+" {"+e.style+"}"}));var r=document.createElement("style"),i=document.head||document.getElementsByTagName("head")[0];if(r.type="text/css",r.id="ssss",r.styleSheet){var a=function(){try{r.styleSheet.cssText=n}catch(e){}};r.styleSheet.disabled?setTimeout(a,10):a()}else{var o=document.createTextNode(n);r.appendChild(o)}i.appendChild(r)}function y(e,t){Br||(Br=new Ir),Br.addObserver(e,t)}function v(e,t){Br&&Br.unObserver(e,t)}function m(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n={code:Ur[e].code,msg:Ur[e].msg};return kr(t).map((function(e){n[e]=t[e]})),n}function x(e,t,n,r,i){i?n(m(2,i),{format:r.format}):r.format?(e.list=r.list,e.format=r.format,e.styles=r.styles,t(e)):n(m(3))}function b(e){return new br((function(t,n){if(e.list)t(e);else{if(e.json){var r=Mr.parseJson(e.json);return e.list=r,e.format="json",void t(e)}if(e.stringContent&&!e.url)Mr.parse(e.stringContent,(function(r,i){x(e,t,n,r,i)}));else if(e.url)new wr({url:e.url,type:"text"}).then((function(r){Mr.parse(r.res.response,(function(r,i){x(e,t,n,r,i)}))})).catch((function(t){var r=m(1,{statusText:t.statusText,status:t.status,type:t.type,message:"http load error",url:e.url});n(r)}));else{var i=m(8);n(i)}}}))}function _(e,t){return!!(e.id&&e.id===t.id||e.language&&e.language===t.language)}var k=function(e){if(void 0==e)throw TypeError("Can't call method on  "+e);return e},E=function(e){return Object(k(e))},w={}.hasOwnProperty,T=function(e,t){return w.call(e,t)},S=t((function(e){var t=e.exports={version:"2.6.12"};"number"==typeof __e&&(__e=t)})),C=(S.version,t((function(e){var t=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=t)}))),O=t((function(e){var t=C["__core-js_shared__"]||(C["__core-js_shared__"]={});(e.exports=function(e,n){return t[e]||(t[e]=void 0!==n?n:{})})("versions",[]).push({version:S.version,mode:"pure",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})})),A=0,R=Math.random(),D=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++A+R).toString(36))},L=O("keys"),P=function(e){return L[e]||(L[e]=D(e))},M=P("IE_PROTO"),I=Object.prototype,B=Object.getPrototypeOf||function(e){return e=E(e),T(e,M)?e[M]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?I:null},U=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e},j=function(e,t,n){if(U(e),void 0===t)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 2:return function(n,r){return e.call(t,n,r)};case 3:return function(n,r,i){return e.call(t,n,r,i)}}return function(){return e.apply(t,arguments)}},N=function(e){return"object"==typeof e?null!==e:"function"==typeof e},F=function(e){if(!N(e))throw TypeError(e+" is not an object!");return e},z=function(e){try{return!!e()}catch(e){return!0}},V=!z((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})),G=C.document,H=N(G)&&N(G.createElement),q=function(e){return H?G.createElement(e):{}},W=!V&&!z((function(){return 7!=Object.defineProperty(q("div"),"a",{get:function(){return 7}}).a})),Y=function(e,t){if(!N(e))return e;var n,r;if(t&&"function"==typeof(n=e.toString)&&!N(r=n.call(e)))return r;if("function"==typeof(n=e.valueOf)&&!N(r=n.call(e)))return r;if(!t&&"function"==typeof(n=e.toString)&&!N(r=n.call(e)))return r;throw TypeError("Can't convert object to primitive value")},X=Object.defineProperty,K={f:V?Object.defineProperty:function(e,t,n){if(F(e),t=Y(t,!0),F(n),W)try{return X(e,t,n)}catch(e){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(e[t]=n.value),e}},$=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},Z=V?function(e,t,n){return K.f(e,t,$(1,n))}:function(e,t,n){return e[t]=n,e},J=function(e,t,n){var r,i,a,o=e&J.F,s=e&J.G,l=e&J.S,u=e&J.P,c=e&J.B,d=e&J.W,p=s?S:S[t]||(S[t]={}),f=p.prototype,h=s?C:l?C[t]:(C[t]||{}).prototype;for(r in s&&(n=t),n)(i=!o&&h&&void 0!==h[r])&&T(p,r)||(a=i?h[r]:n[r],p[r]=s&&"function"!=typeof h[r]?n[r]:c&&i?j(a,C):d&&h[r]==a?function(e){var t=function(t,n,r){if(this instanceof e){switch(arguments.length){case 0:return new e;case 1:return new e(t);case 2:return new e(t,n)}return new e(t,n,r)}return e.apply(this,arguments)};return t.prototype=e.prototype,t}(a):u&&"function"==typeof a?j(Function.call,a):a,u&&((p.virtual||(p.virtual={}))[r]=a,e&J.R&&f&&!f[r]&&Z(f,r,a)))};J.F=1,J.G=2,J.S=4,J.P=8,J.B=16,J.W=32,J.U=64,J.R=128;var Q=J,ee=function(e,t){var n=(S.Object||{})[e]||Object[e],r={};r[e]=t(n),Q(Q.S+Q.F*z((function(){n(1)})),"Object",r)};ee("getPrototypeOf",(function(){return function(e){return B(E(e))}}));var te=S.Object.getPrototypeOf,ne=e(t((function(e){e.exports={default:te,__esModule:!0}}))),re=e(t((function(e,t){t.__esModule=!0,t.default=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}}))),ie=Math.ceil,ae=Math.floor,oe=function(e){return isNaN(e=+e)?0:(e>0?ae:ie)(e)},se=Z,le={},ue={}.toString,ce=function(e){return ue.call(e).slice(8,-1)},de=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==ce(e)?e.split(""):Object(e)},pe=function(e){return de(k(e))},fe=Math.min,he=function(e){return e>0?fe(oe(e),9007199254740991):0},ge=Math.max,ye=Math.min,ve=function(e,t){return e=oe(e),e<0?ge(e+t,0):ye(e,t)},me=function(e){return function(t,n,r){var i,a=pe(t),o=he(a.length),s=ve(r,o);if(e&&n!=n){for(;o>s;)if((i=a[s++])!=i)return!0}else for(;o>s;s++)if((e||s in a)&&a[s]===n)return e||s||0;return!e&&-1}}(!1),xe=P("IE_PROTO"),be=function(e,t){var n,r=pe(e),i=0,a=[];for(n in r)n!=xe&&T(r,n)&&a.push(n);for(;t.length>i;)T(r,n=t[i++])&&(~me(a,n)||a.push(n));return a},_e="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(","),ke=Object.keys||function(e){return be(e,_e)},Ee=V?Object.defineProperties:function(e,t){F(e);for(var n,r=ke(t),i=r.length,a=0;i>a;)K.f(e,n=r[a++],t[n]);return e},we=C.document,Te=we&&we.documentElement,Se=P("IE_PROTO"),Ce=function(){},Oe=function(){var e,t=q("iframe"),n=_e.length;for(t.style.display="none",Te.appendChild(t),t.src="javascript:",(e=t.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),Oe=e.F;n--;)delete Oe.prototype[_e[n]];return Oe()},Ae=Object.create||function(e,t){var n;return null!==e?(Ce.prototype=F(e),n=new Ce,Ce.prototype=null,n[Se]=e):n=Oe(),void 0===t?n:Ee(n,t)},Re=t((function(e){var t=O("wks"),n=C.Symbol,r="function"==typeof n;(e.exports=function(e){return t[e]||(t[e]=r&&n[e]||(r?n:D)("Symbol."+e))}).store=t})),De=K.f,Le=Re("toStringTag"),Pe=function(e,t,n){e&&!T(e=n?e:e.prototype,Le)&&De(e,Le,{configurable:!0,value:t})},Me={};Z(Me,Re("iterator"),(function(){return this}));var Ie=function(e,t,n){e.prototype=Ae(Me,{next:$(1,n)}),Pe(e,t+" Iterator")},Be=Re("iterator"),Ue=!([].keys&&"next"in[].keys()),je=function(){return this},Ne=function(e,t,n,r,i,a,o){Ie(n,t,r);var s,l,u,c=function(e){if(!Ue&&e in h)return h[e];switch(e){case"keys":case"values":return function(){return new n(this,e)}}return function(){return new n(this,e)}},d=t+" Iterator",p="values"==i,f=!1,h=e.prototype,g=h[Be]||h["@@iterator"]||i&&h[i],y=g||c(i),v=i?p?c("entries"):y:void 0,m="Array"==t&&h.entries||g;if(m&&(u=B(m.call(new e)))!==Object.prototype&&u.next&&Pe(u,d,!0),p&&g&&"values"!==g.name&&(f=!0,y=function(){return g.call(this)}),o&&(Ue||f||!h[Be])&&Z(h,Be,y),le[t]=y,le[d]=je,i)if(s={values:p?y:c("values"),keys:a?y:c("keys"),entries:v},o)for(l in s)l in h||se(h,l,s[l]);else Q(Q.P+Q.F*(Ue||f),t,s);return s},Fe=function(e){return function(t,n){var r,i,a=String(k(t)),o=oe(n),s=a.length;return o<0||o>=s?e?"":void 0:(r=a.charCodeAt(o),r<55296||r>56319||o+1===s||(i=a.charCodeAt(o+1))<56320||i>57343?e?a.charAt(o):r:e?a.slice(o,o+2):i-56320+(r-55296<<10)+65536)}}(!0);Ne(String,"String",(function(e){this._t=String(e),this._i=0}),(function(){var e,t=this._t,n=this._i;return n>=t.length?{value:void 0,done:!0}:(e=Fe(t,n),this._i+=e.length,{value:e,done:!1})}));var ze=function(e,t){return{value:t,done:!!e}};Ne(Array,"Array",(function(e,t){this._t=pe(e),this._i=0,this._k=t}),(function(){var e=this._t,t=this._k,n=this._i++;return!e||n>=e.length?(this._t=void 0,ze(1)):ze(0,"keys"==t?n:"values"==t?e[n]:[n,e[n]])}),"values"),le.Arguments=le.Array;for(var Ve=Re("toStringTag"),Ge="CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList".split(","),He=0;He<Ge.length;He++){var qe=Ge[He],We=C[qe],Ye=We&&We.prototype;Ye&&!Ye[Ve]&&Z(Ye,Ve,qe),le[qe]=le.Array}var Xe={f:Re},Ke=Xe.f("iterator"),$e=t((function(e){e.exports={default:Ke,__esModule:!0}}));e($e);var Ze=t((function(e){var t=D("meta"),n=K.f,r=0,i=Object.isExtensible||function(){return!0},a=!z((function(){return i(Object.preventExtensions({}))})),o=function(e){n(e,t,{value:{i:"O"+ ++r,w:{}}})},s=function(e,n){if(!N(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!T(e,t)){if(!i(e))return"F";if(!n)return"E";o(e)}return e[t].i},l=function(e,n){if(!T(e,t)){if(!i(e))return!0;if(!n)return!1;o(e)}return e[t].w},u=function(e){return a&&c.NEED&&i(e)&&!T(e,t)&&o(e),e},c=e.exports={KEY:t,NEED:!1,fastKey:s,getWeak:l,onFreeze:u}})),Je=(Ze.KEY,Ze.NEED,Ze.fastKey,Ze.getWeak,Ze.onFreeze,K.f),Qe=function(e){var t=S.Symbol||(S.Symbol={});"_"==e.charAt(0)||e in t||Je(t,e,{value:Xe.f(e)})},et={f:Object.getOwnPropertySymbols},tt={f:{}.propertyIsEnumerable},nt=function(e){var t=ke(e),n=et.f;if(n)for(var r,i=n(e),a=tt.f,o=0;i.length>o;)a.call(e,r=i[o++])&&t.push(r);return t},rt=Array.isArray||function(e){return"Array"==ce(e)},it=_e.concat("length","prototype"),at={f:Object.getOwnPropertyNames||function(e){return be(e,it)}},ot=at.f,st={}.toString,lt="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],ut=function(e){try{return ot(e)}catch(e){return lt.slice()}},ct={f:function(e){return lt&&"[object Window]"==st.call(e)?ut(e):ot(pe(e))}},dt=Object.getOwnPropertyDescriptor,pt={f:V?dt:function(e,t){if(e=pe(e),t=Y(t,!0),W)try{return dt(e,t)}catch(e){}if(T(e,t))return $(!tt.f.call(e,t),e[t])}},ft=Ze.KEY,ht=pt.f,gt=K.f,yt=ct.f,vt=C.Symbol,mt=C.JSON,xt=mt&&mt.stringify,bt=Re("_hidden"),_t=Re("toPrimitive"),kt={}.propertyIsEnumerable,Et=O("symbol-registry"),wt=O("symbols"),Tt=O("op-symbols"),St=Object.prototype,Ct="function"==typeof vt&&!!et.f,Ot=C.QObject,At=!Ot||!Ot.prototype||!Ot.prototype.findChild,Rt=V&&z((function(){return 7!=Ae(gt({},"a",{get:function(){return gt(this,"a",{value:7}).a}})).a}))?function(e,t,n){var r=ht(St,t);r&&delete St[t],gt(e,t,n),r&&e!==St&&gt(St,t,r)}:gt,Dt=function(e){var t=wt[e]=Ae(vt.prototype);return t._k=e,t},Lt=Ct&&"symbol"==typeof vt.iterator?function(e){return"symbol"==typeof e}:function(e){return e instanceof vt},Pt=function(e,t,n){return e===St&&Pt(Tt,t,n),F(e),t=Y(t,!0),F(n),T(wt,t)?(n.enumerable?(T(e,bt)&&e[bt][t]&&(e[bt][t]=!1),n=Ae(n,{enumerable:$(0,!1)})):(T(e,bt)||gt(e,bt,$(1,{})),e[bt][t]=!0),Rt(e,t,n)):gt(e,t,n)},Mt=function(e,t){F(e);for(var n,r=nt(t=pe(t)),i=0,a=r.length;a>i;)Pt(e,n=r[i++],t[n]);return e},It=function(e,t){return void 0===t?Ae(e):Mt(Ae(e),t)},Bt=function(e){var t=kt.call(this,e=Y(e,!0));return!(this===St&&T(wt,e)&&!T(Tt,e))&&(!(t||!T(this,e)||!T(wt,e)||T(this,bt)&&this[bt][e])||t)},Ut=function(e,t){if(e=pe(e),t=Y(t,!0),e!==St||!T(wt,t)||T(Tt,t)){var n=ht(e,t);return!n||!T(wt,t)||T(e,bt)&&e[bt][t]||(n.enumerable=!0),n}},jt=function(e){for(var t,n=yt(pe(e)),r=[],i=0;n.length>i;)T(wt,t=n[i++])||t==bt||t==ft||r.push(t);return r},Nt=function(e){for(var t,n=e===St,r=yt(n?Tt:pe(e)),i=[],a=0;r.length>a;)!T(wt,t=r[a++])||n&&!T(St,t)||i.push(wt[t]);return i};Ct||(se((vt=function(){if(this instanceof vt)throw TypeError("Symbol is not a constructor!");var e=D(arguments.length>0?arguments[0]:void 0),t=function(n){this===St&&t.call(Tt,n),T(this,bt)&&T(this[bt],e)&&(this[bt][e]=!1),Rt(this,e,$(1,n))};return V&&At&&Rt(St,e,{configurable:!0,set:t}),Dt(e)}).prototype,"toString",(function(){return this._k})),pt.f=Ut,K.f=Pt,at.f=ct.f=jt,tt.f=Bt,et.f=Nt,Xe.f=function(e){return Dt(Re(e))}),Q(Q.G+Q.W+Q.F*!Ct,{Symbol:vt});for(var Ft="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),zt=0;Ft.length>zt;)Re(Ft[zt++]);for(var Vt=ke(Re.store),Gt=0;Vt.length>Gt;)Qe(Vt[Gt++]);Q(Q.S+Q.F*!Ct,"Symbol",{for:function(e){return T(Et,e+="")?Et[e]:Et[e]=vt(e)},keyFor:function(e){if(!Lt(e))throw TypeError(e+" is not a symbol!");for(var t in Et)if(Et[t]===e)return t},useSetter:function(){At=!0},useSimple:function(){At=!1}}),Q(Q.S+Q.F*!Ct,"Object",{create:It,defineProperty:Pt,defineProperties:Mt,getOwnPropertyDescriptor:Ut,getOwnPropertyNames:jt,getOwnPropertySymbols:Nt});var Ht=z((function(){et.f(1)}));Q(Q.S+Q.F*Ht,"Object",{getOwnPropertySymbols:function(e){return et.f(E(e))}}),mt&&Q(Q.S+Q.F*(!Ct||z((function(){var e=vt();return"[null]"!=xt([e])||"{}"!=xt({a:e})||"{}"!=xt(Object(e))}))),"JSON",{stringify:function(e){for(var t,n,r=[e],i=1;arguments.length>i;)r.push(arguments[i++]);if(n=t=r[1],(N(t)||void 0!==e)&&!Lt(e))return rt(t)||(t=function(e,t){if("function"==typeof n&&(t=n.call(this,e,t)),!Lt(t))return t}),r[1]=t,xt.apply(mt,r)}}),vt.prototype[_t]||Z(vt.prototype,_t,vt.prototype.valueOf),Pe(vt,"Symbol"),Pe(Math,"Math",!0),Pe(C.JSON,"JSON",!0),Qe("asyncIterator"),Qe("observable");var qt=S.Symbol,Wt=t((function(e){e.exports={default:qt,__esModule:!0}}));e(Wt);var Yt=t((function(e,t){function n(e){return e&&e.__esModule?e:{default:e}}t.__esModule=!0;var r=n($e),i=n(Wt),a="function"==typeof i.default&&"symbol"==typeof r.default?function(e){return typeof e}:function(e){return e&&"function"==typeof i.default&&e.constructor===i.default&&e!==i.default.prototype?"symbol":typeof e};t.default="function"==typeof i.default&&"symbol"===a(r.default)?function(e){return void 0===e?"undefined":a(e)}:function(e){return e&&"function"==typeof i.default&&e.constructor===i.default&&e!==i.default.prototype?"symbol":void 0===e?"undefined":a(e)}})),Xt=e(Yt),Kt=e(t((function(e,t){t.__esModule=!0;var n=function(e){return e&&e.__esModule?e:{default:e}}(Yt);t.default=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==(void 0===t?"undefined":(0,n.default)(t))&&"function"!=typeof t?e:t}})));Q(Q.S+Q.F*!V,"Object",{defineProperty:K.f});var $t=S.Object,Zt=function(e,t,n){return $t.defineProperty(e,t,n)},Jt=t((function(e){e.exports={default:Zt,__esModule:!0}}));e(Jt);var Qt=e(t((function(e,t){t.__esModule=!0;var n=function(e){return e&&e.__esModule?e:{default:e}}(Jt);t.default=function(){function e(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),(0,n.default)(e,i.key,i)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}()}))),en=function(e,t){if(F(e),!N(t)&&null!==t)throw TypeError(t+": can't set as prototype!")},tn={set:Object.setPrototypeOf||("__proto__"in{}?function(e,t,n){try{(n=j(Function.call,pt.f(Object.prototype,"__proto__").set,2))(e,[]),t=!(e instanceof Array)}catch(e){t=!0}return function(e,r){return en(e,r),t?e.__proto__=r:n(e,r),e}}({},!1):void 0),check:en};Q(Q.S,"Object",{setPrototypeOf:tn.set});var nn=S.Object.setPrototypeOf,rn=t((function(e){e.exports={default:nn,__esModule:!0}}));e(rn),Q(Q.S,"Object",{create:Ae});var an=S.Object,on=function(e,t){return an.create(e,t)},sn=t((function(e){e.exports={default:on,__esModule:!0}}));e(sn);var ln,un,cn,dn=e(t((function(e,t){function n(e){return e&&e.__esModule?e:{default:e}}t.__esModule=!0;var r=n(rn),i=n(sn),a=n(Yt);t.default=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+(void 0===t?"undefined":(0,a.default)(t)));e.prototype=(0,i.default)(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(r.default?(0,r.default)(e,t):e.__proto__=t)}}))),pn=Re("toStringTag"),fn="Arguments"==ce(function(){return arguments}()),hn=function(e,t){try{return e[t]}catch(e){}},gn=function(e){var t,n,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=hn(t=Object(e),pn))?n:fn?ce(t):"Object"==(r=ce(t))&&"function"==typeof t.callee?"Arguments":r},yn=function(e,t,n,r){if(!(e instanceof t)||void 0!==r&&r in e)throw TypeError(n+": incorrect invocation!");return e},vn=function(e,t,n,r){try{return r?t(F(n)[0],n[1]):t(n)}catch(t){var i=e.return;throw void 0!==i&&F(i.call(e)),t}},mn=Re("iterator"),xn=Array.prototype,bn=function(e){return void 0!==e&&(le.Array===e||xn[mn]===e)},_n=Re("iterator"),kn=S.getIteratorMethod=function(e){if(void 0!=e)return e[_n]||e["@@iterator"]||le[gn(e)]},En=t((function(e){var t={},n={},r=e.exports=function(e,r,i,a,o){var s,l,u,c,d=o?function(){return e}:kn(e),p=j(i,a,r?2:1),f=0;if("function"!=typeof d)throw TypeError(e+" is not iterable!");if(bn(d)){for(s=he(e.length);s>f;f++)if((c=r?p(F(l=e[f])[0],l[1]):p(e[f]))===t||c===n)return c}else for(u=d.call(e);!(l=u.next()).done;)if((c=vn(u,p,l.value,r))===t||c===n)return c};r.BREAK=t,r.RETURN=n})),wn=Re("species"),Tn=function(e,t){var n,r=F(e).constructor;return void 0===r||void 0==(n=F(r)[wn])?t:U(n)},Sn=function(e,t,n){var r=void 0===n;switch(t.length){case 0:return r?e():e.call(n);case 1:return r?e(t[0]):e.call(n,t[0]);case 2:return r?e(t[0],t[1]):e.call(n,t[0],t[1]);case 3:return r?e(t[0],t[1],t[2]):e.call(n,t[0],t[1],t[2]);case 4:return r?e(t[0],t[1],t[2],t[3]):e.call(n,t[0],t[1],t[2],t[3])}return e.apply(n,t)},Cn=C.process,On=C.setImmediate,An=C.clearImmediate,Rn=C.MessageChannel,Dn=C.Dispatch,Ln=0,Pn={},Mn=function(){var e=+this;if(Pn.hasOwnProperty(e)){var t=Pn[e];delete Pn[e],t()}},In=function(e){Mn.call(e.data)};On&&An||(On=function(e){for(var t=[],n=1;arguments.length>n;)t.push(arguments[n++]);return Pn[++Ln]=function(){Sn("function"==typeof e?e:Function(e),t)},ln(Ln),Ln},An=function(e){delete Pn[e]},"process"==ce(Cn)?ln=function(e){Cn.nextTick(j(Mn,e,1))}:Dn&&Dn.now?ln=function(e){Dn.now(j(Mn,e,1))}:Rn?(cn=(un=new Rn).port2,un.port1.onmessage=In,ln=j(cn.postMessage,cn,1)):C.addEventListener&&"function"==typeof postMessage&&!C.importScripts?(ln=function(e){C.postMessage(e+"","*")},C.addEventListener("message",In,!1)):ln="onreadystatechange"in q("script")?function(e){Te.appendChild(q("script")).onreadystatechange=function(){Te.removeChild(this),Mn.call(e)}}:function(e){setTimeout(j(Mn,e,1),0)});var Bn={set:On,clear:An},Un=Bn.set,jn=C.MutationObserver||C.WebKitMutationObserver,Nn=C.process,Fn=C.Promise,zn="process"==ce(Nn),Vn={f:function(e){return new n(e)}},Gn=function(e){try{return{e:!1,v:e()}}catch(e){return{e:!0,v:e}}},Hn=C.navigator,qn=Hn&&Hn.userAgent||"",Wn=function(e,t){if(F(e),N(t)&&t.constructor===e)return t;var n=Vn.f(e);return(0,n.resolve)(t),n.promise},Yn=Re("species"),Xn=Re("iterator"),Kn=!1;try{var $n=[7][Xn]();$n.return=function(){Kn=!0},Array.from($n,(function(){throw 2}))}catch(e){}var Zn,Jn,Qn,er,tr=Bn.set,nr=function(){var e,t,n,r=function(){var r,i;for(zn&&(r=Nn.domain)&&r.exit();e;){i=e.fn,e=e.next;try{i()}catch(r){throw e?n():t=void 0,r}}t=void 0,r&&r.enter()};if(zn)n=function(){Nn.nextTick(r)};else if(!jn||C.navigator&&C.navigator.standalone)if(Fn&&Fn.resolve){var i=Fn.resolve(void 0);n=function(){i.then(r)}}else n=function(){Un.call(C,r)};else{var a=!0,o=document.createTextNode("");new jn(r).observe(o,{characterData:!0}),n=function(){o.data=a=!a}}return function(r){var i={fn:r,next:void 0};t&&(t.next=i),e||(e=i,n()),t=i}}(),rr=C.TypeError,ir=C.process,ar=ir&&ir.versions,or=ar&&ar.v8||"",sr=C.Promise,lr="process"==gn(ir),ur=function(){},cr=Jn=Vn.f,dr=!!function(){try{var e=sr.resolve(1),t=(e.constructor={})[Re("species")]=function(e){e(ur,ur)};return(lr||"function"==typeof PromiseRejectionEvent)&&e.then(ur)instanceof t&&0!==or.indexOf("6.6")&&-1===qn.indexOf("Chrome/66")}catch(e){}}(),pr=function(e){var t;return!(!N(e)||"function"!=typeof(t=e.then))&&t},fr=function(e,t){if(!e._n){e._n=!0;var n=e._c;nr((function(){for(var r=e._v,i=1==e._s,a=0;n.length>a;)!function(t){var n,a,o,s=i?t.ok:t.fail,l=t.resolve,u=t.reject,c=t.domain;try{s?(i||(2==e._h&&yr(e),e._h=1),!0===s?n=r:(c&&c.enter(),n=s(r),c&&(c.exit(),o=!0)),n===t.promise?u(rr("Promise-chain cycle")):(a=pr(n))?a.call(n,l,u):l(n)):u(r)}catch(e){c&&!o&&c.exit(),u(e)}}(n[a++]);e._c=[],e._n=!1,t&&!e._h&&hr(e)}))}},hr=function(e){tr.call(C,(function(){var t,n,r,i=e._v,a=gr(e);if(a&&(t=Gn((function(){lr?ir.emit("unhandledRejection",i,e):(n=C.onunhandledrejection)?n({promise:e,reason:i}):(r=C.console)&&r.error&&r.error("Unhandled promise rejection",i)})),e._h=lr||gr(e)?2:1),e._a=void 0,a&&t.e)throw t.v}))},gr=function(e){return 1!==e._h&&0===(e._a||e._c).length},yr=function(e){tr.call(C,(function(){var t;lr?ir.emit("rejectionHandled",e):(t=C.onrejectionhandled)&&t({promise:e,reason:e._v})}))},vr=function(e){var t=this;t._d||(t._d=!0,(t=t._w||t)._v=e,t._s=2,t._a||(t._a=t._c.slice()),fr(t,!0))},mr=function(e){var t,n=this;if(!n._d){n._d=!0,n=n._w||n;try{if(n===e)throw rr("Promise can't be resolved itself");(t=pr(e))?nr((function(){var r={_w:n,_d:!1};try{t.call(e,j(mr,r,1),j(vr,r,1))}catch(e){vr.call(r,e)}})):(n._v=e,n._s=1,fr(n,!1))}catch(e){vr.call({_w:n,_d:!1},e)}}};dr||(sr=function(e){yn(this,sr,"Promise","_h"),U(e),Zn.call(this);try{e(j(mr,this,1),j(vr,this,1))}catch(e){vr.call(this,e)}},(Zn=function(e){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1}).prototype=function(e,t,n){for(var r in t)n&&e[r]?e[r]=t[r]:Z(e,r,t[r]);return e}(sr.prototype,{then:function(e,t){var n=cr(Tn(this,sr));return n.ok="function"!=typeof e||e,n.fail="function"==typeof t&&t,n.domain=lr?ir.domain:void 0,this._c.push(n),this._a&&this._a.push(n),this._s&&fr(this,!1),n.promise},catch:function(e){return this.then(void 0,e)}}),Qn=function(){var e=new Zn;this.promise=e,this.resolve=j(mr,e,1),this.reject=j(vr,e,1)},Vn.f=cr=function(e){return e===sr||e===er?new Qn(e):Jn(e)}),Q(Q.G+Q.W+Q.F*!dr,{Promise:sr}),Pe(sr,"Promise"),function(e){var t="function"==typeof S[e]?S[e]:C[e];V&&t&&!t[Yn]&&K.f(t,Yn,{configurable:!0,get:function(){return this}})}("Promise"),er=S.Promise,Q(Q.S+Q.F*!dr,"Promise",{reject:function(e){var t=cr(this);return(0,t.reject)(e),t.promise}}),Q(Q.S+!0*Q.F,"Promise",{resolve:function(e){return Wn(this===er?sr:this,e)}}),Q(Q.S+Q.F*!(dr&&function(e,t){if(!t&&!Kn)return!1;var n=!1;try{var r=[7],i=r[Xn]();i.next=function(){return{done:n=!0}},r[Xn]=function(){return i},e(r)}catch(e){}return n}((function(e){sr.all(e).catch(ur)}))),"Promise",{all:function(e){var t=this,n=cr(t),r=n.resolve,i=n.reject,a=Gn((function(){var n=[],a=0,o=1;En(e,!1,(function(e){var s=a++,l=!1;n.push(void 0),o++,t.resolve(e).then((function(e){l||(l=!0,n[s]=e,--o||r(n))}),i)})),--o||r(n)}));return a.e&&i(a.v),n.promise},race:function(e){var t=this,n=cr(t),r=n.reject,i=Gn((function(){En(e,!1,(function(e){t.resolve(e).then(n.resolve,r)}))}));return i.e&&r(i.v),n.promise}}),Q(Q.P+Q.R,"Promise",{finally:function(e){var t=Tn(this,S.Promise||C.Promise),n="function"==typeof e;return this.then(n?function(n){return Wn(t,e()).then((function(){return n}))}:e,n?function(n){return Wn(t,e()).then((function(){throw n}))}:e)}}),Q(Q.S,"Promise",{try:function(e){var t=Vn.f(this),n=Gn(e);return(n.e?t.reject:t.resolve)(n.v),t.promise}});var xr=S.Promise,br=e(t((function(e){e.exports={default:xr,__esModule:!0}})));ee("keys",(function(){return function(e){return ke(E(e))}}));var _r=S.Object.keys,kr=e(t((function(e){e.exports={default:_r,__esModule:!0}}))),Er=t((function(e){function t(){}function n(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function r(e,t,r,i,a){if("function"!=typeof r)throw new TypeError("The listener must be a function");var o=new n(r,i||e,a),l=s?s+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],o]:e._events[l].push(o):(e._events[l]=o,e._eventsCount++),e}function i(e,n){0==--e._eventsCount?e._events=new t:delete e._events[n]}function a(){this._events=new t,this._eventsCount=0}var o=Object.prototype.hasOwnProperty,s="~";Object.create&&(t.prototype=Object.create(null),(new t).__proto__||(s=!1)),a.prototype.eventNames=function(){var e,t,n=[];if(0===this._eventsCount)return n;for(t in e=this._events)o.call(e,t)&&n.push(s?t.slice(1):t);return Object.getOwnPropertySymbols?n.concat(Object.getOwnPropertySymbols(e)):n},a.prototype.listeners=function(e){var t=s?s+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var r=0,i=n.length,a=new Array(i);r<i;r++)a[r]=n[r].fn;return a},a.prototype.listenerCount=function(e){var t=s?s+e:e,n=this._events[t];return n?n.fn?1:n.length:0},a.prototype.emit=function(e,t,n,r,i,a){var o=s?s+e:e;if(!this._events[o])return!1;var l,u,c=this._events[o],d=arguments.length;if(c.fn){switch(c.once&&this.removeListener(e,c.fn,void 0,!0),d){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,t),!0;case 3:return c.fn.call(c.context,t,n),!0;case 4:return c.fn.call(c.context,t,n,r),!0;case 5:return c.fn.call(c.context,t,n,r,i),!0;case 6:return c.fn.call(c.context,t,n,r,i,a),!0}for(u=1,l=new Array(d-1);u<d;u++)l[u-1]=arguments[u];c.fn.apply(c.context,l)}else{var p,f=c.length;for(u=0;u<f;u++)switch(c[u].once&&this.removeListener(e,c[u].fn,void 0,!0),d){case 1:c[u].fn.call(c[u].context);break;case 2:c[u].fn.call(c[u].context,t);break;case 3:c[u].fn.call(c[u].context,t,n);break;case 4:c[u].fn.call(c[u].context,t,n,r);break;default:if(!l)for(p=1,l=new Array(d-1);p<d;p++)l[p-1]=arguments[p];c[u].fn.apply(c[u].context,l)}}return!0},a.prototype.on=function(e,t,n){return r(this,e,t,n,!1)},a.prototype.once=function(e,t,n){return r(this,e,t,n,!0)},a.prototype.removeListener=function(e,t,n,r){var a=s?s+e:e;if(!this._events[a])return this;if(!t)return i(this,a),this;var o=this._events[a];if(o.fn)o.fn!==t||r&&!o.once||n&&o.context!==n||i(this,a);else{for(var l=0,u=[],c=o.length;l<c;l++)(o[l].fn!==t||r&&!o[l].once||n&&o[l].context!==n)&&u.push(o[l]);u.length?this._events[a]=1===u.length?u[0]:u:i(this,a)}return this},a.prototype.removeAllListeners=function(e){var n;return e?(n=s?s+e:e,this._events[n]&&i(this,n)):(this._events=new t,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=s,a.EventEmitter=a,e.exports=a})),wr=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.url,r=t.method,i=void 0===r?"GET":r,a=t.type,o=void 0===a?"arraybuffer":a,s=t.timeout,l=void 0===s?1e4:s,u=t.data,c=void 0===u?{}:u,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return re(this,e),new br((function(e,t){var r=new window.XMLHttpRequest,a=i.toUpperCase(),s=[];for(var u in o&&(r.responseType=o),l&&(r.timeout=l),c)s.push("k="+c[u]);if(r.onload=function(){200===r.status||206===r.status?e({context:d,res:r}):t(new Error({context:d,res:r,type:"error"}))},r.onerror=function(e){t(new Error({context:d,res:r,type:"error"}))},r.ontimeout=function(e){t(new Error({context:d,res:r,type:"error"}))},r.onabort=function(){t(new Error({context:d,res:r,type:"error"}))},"GET"===a)r.open(a,""+n),r.send();else{if("post"!==a)throw new Error("xhr "+a+" is not supported");r.open(a,n),r.setRequestHeader("Content-type","application/x-www-form-urlencoded"),r.send(s.join("&"))}}))},Tr=/^WEBVTT/,Sr=/^STYLE+$/,Cr=/^\:\:cue/,Or=/^}+$/,Ar=/^\[Script Info\].*/,Rr=[/[0-9]{1,3}:[0-9]{2}:[0-9]{2}\.[0-9]{1,3}-->[0-9]{1,3}:[0-9]{2}:[0-9]{2}\.[0-9]{1,3}/,/[0-9]{1,2}:[0-9]{2}\.[0-9]{1,3}-->[0-9]{1,2}:[0-9]{2}\.[0-9]{1,3}/,/[0-9]{1,2}\.[0-9]{1,3}-->[0-9]{1,2}\.[0-9]{1,3}/],Dr=/^Format:\s/,Lr=/^Style:\s/,Pr=/^Dialogue:\s/,Mr=function(){function e(){re(this,e)}return Qt(e,null,[{key:"parseJson",value:function(e){for(var t=[],n=0,r=0;r<e.length;r++){if(n>=50&&(n=0),0===n){var i={start:e[r].start,list:[e[r]],end:e[r].end};t.push(i)}else t[t.length-1].list.push(e[r]),t[t.length-1].end=e[r].end;n++}return t}},{key:"parse",value:function(t,n){var r=e.checkFormat(t);r||n({format:r});try{var i=[];"ass"===r?i=e.parseASS(t):"vtt"===r&&(i=e.parseVTT(t)),n({format:r,list:i.list,styles:i.styles})}catch(e){console.error(e),n({format:r},e)}}},{key:"parseASSItem",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=e.split(","),i={},o="";try{var s=n.length-t.length;return o=s>0?n.splice(t.length-1,s+1).join(",")+"":n[n.length-1]+"",o=o.replace(/\\n+/g,""),o=a(o),n[t.length-1]=o,t.map((function(e,t){"end"===e||"start"===e?i[e]=r(n[t].split(":")):"text"===e?i[e]=[n[t]]:"layer"===e?(i[e]=[n[t]],i.textTag=[n[t]]):i[e]="style"===e?[n[t]]:Number(n[t])?Number(n[t]):n[t]})),i}catch(e){return console.error(e),{}}}},{key:"parseASS",value:function(t){for(var n=t.split("\n"),r=[],i=0,a=0,o=[],s=[],l=null;i<n.length;){if(Dr.test(n[i]))s=(s=n[i].replace(Dr,"").replace(/\s+/g,"").split(",")).map((function(e){return e.toLocaleLowerCase()}));else if(Lr.test(n[i]))o.push(n[i].replace(Lr,"").replace(/\s+/g,""));else if(Pr.test(n[i])){var u=e.parseASSItem(n[i].replace(Pr,""),s);if(l&&u.start===l.start&&u.end===l.end)try{var c=l,d=c.text,p=c.textTag,f=c.style;d.push(u.text[0]),p.push(u.textTag[0]),f.push(u.style[0])}catch(e){console.error(e)}else{l=u;var h=null;a%50==0?((h={start:l.start,end:l.end,list:[]}).list.push(l),r.push(h)):((h=r[r.length-1]).end=l.end,h.list.push(l)),a++}}i++}return{list:r,style:{}}}},{key:"parseVTTStyle",value:function(e,t){var n=e.split(":");if(n.length>1){var r=n[0].trim().split("-"),i="";r.length>1?r.map((function(e,t){i+=0===t?e:e.charAt(0).toUpperCase()+e.slice(1)})):i=r[0],t[i]=n[1].trim().replace(/;$/,"")}return t}},{key:"parseVTT",value:function(e){for(var t=(e=e.replace(Tr,"")).split("\n"),n=[],r=0,a=0,s=null,l=!1,u=!1,c=null,d=null,p=[];r<t.length;){var f=o(r,t).trim();if(!f||l&&i(f))l=!f;else if(Cr.test(f)&&Sr.test(o(r-1,t).trim())){u=!0;var h=/\((.+?)\)/g.exec(f);d=h?h[1]:"",c=""}else if(u)Or.test(f)?(p.push({key:d,style:c}),c=null,d=null,u=!1):c+=f;else if(f){l=!1;var g=this.checkIsTime(t[r]);if(g){var y=this.parseVttTime(g);if(!s||y.start!==s.start||y.end!==s.end){(s=y).text=[],s.textTag=[];var v=null;a%50==0?((v={start:s.start,end:s.end,list:[]}).list.push(s),n.push(v)):((v=n[n.length-1]).end=s.end,v.list.push(s)),a++}}else if(s){var m=s,x=m.text,b=m.textTag,_=this.parseVttText(t[r]);x.push(_.text),b.push(_.tag)}l=!1}r++}return{list:n,styles:p}}},{key:"checkIsTime",value:function(e){e=e.replace(/\s+/g,"");for(var t=0,n=null;t<Rr.length&&!(n=Rr[t].exec(e));)t++;return n?n[0]:null}},{key:"parseVttText",value:function(e){var t=/^(<?.+?>)/g.exec(e),n="",r="default";if(t){r=t[0].replace(/\<|\>|\&/g,"");var i=RegExp("^<"+r+">(([\\s\\S])*?)</"+r+">$").exec(e);i?n=i[1]:(n=e,r="")}else n=e;for(var o=/<(\w+).(\w+)>/g,s=o.exec(n);s&&s.length>2;)n=n.replace(s[0],"<"+s[1]+' class="'+s[2]+'">'),s=o.exec(n);return{tag:r,text:a(n.replace(/\\n+/g,"<br/>"))}}},{key:"parseVttTime",value:function(e){var t=e.split("--\x3e"),n=void 0,i=0;if(2===t.length){var a=t[0].split(":"),o=t[1].split(":");n=r(a),i=r(o)}return{start:n,end:i,time:e}}},{key:"isVTT",value:function(e){return Tr.test(e)}},{key:"isASS",value:function(e){return Ar.test(e)}},{key:"checkFormat",value:function(e){return e?Tr.test(e)?"vtt":Ar.test(e)?"ass":null:null}}]),e}(),Ir=function(){function e(){var t=this;re(this,e),this.__handlers=[],window.ResizeObserver&&(this.observer=new window.ResizeObserver((function(e){t.__trigger(e)})))}return Qt(e,[{key:"addObserver",value:function(e,t){if(this.observer){this.observer&&this.observer.observe(e);for(var n=this.__handlers,r=-1,i=0;i<n.length;i++)n[i]&&e===n[i].target&&(r=i);r>-1?this.__handlers[r].handler.push(t):this.__handlers.push({target:e,handler:[t]})}}},{key:"unObserver",value:function(e){var t=-1;this.__handlers.map((function(n,r){e===n.target&&(t=r)})),this.observer&&this.observer.unobserve(e),t>-1&&this.__handlers.splice(t,1)}},{key:"destroyObserver",value:function(){this.observer&&this.observer.disconnect(),this.observer=null,this.__handlers=null}},{key:"__runHandler",value:function(e,t){for(var n=this.__handlers,r=0;r<n.length;r++)if(n[r]&&e===n[r].target){n[r].handler&&n[r].handler.map((function(n){try{n(e,t)}catch(e){console.error(e)}}));break}}},{key:"__trigger",value:function(e){var t=this;e.map((function(e){var n=e.contentRect;t.__runHandler(e.target,n)}))}}]),e}(),Br=null;!function(e,t){void 0===t&&(t={});var n=t.insertAt;if(e&&"undefined"!=typeof document){var r=document.head||document.getElementsByTagName("head")[0],i=document.createElement("style");i.type="text/css","top"===n&&r.firstChild?r.insertBefore(i,r.firstChild):r.appendChild(i),i.styleSheet?i.styleSheet.cssText=e:i.appendChild(document.createTextNode(e))}}('xg-text-track.xg-text-track {\n  font-family: "PingFang SC","SF Pro SC","SF Pro Text","SF Pro Icons","Helvetica Neue","Helvetica","Arial",sans-serif;\n  -webkit-font-smoothing: antialiased;\n  position: absolute;\n  bottom: 0;\n  color: #fff;\n  left: 0;\n  right: 0;\n  pointer-events: none;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-pack: center;\n      -ms-flex-pack: center;\n          justify-content: center; }\n  xg-text-track.xg-text-track.text-track-no-fitvideo {\n    margin-bottom: 2%; }\n  xg-text-track.xg-text-track.text-track-hide {\n    opacity: 0;\n    visibility: hidden; }\n  xg-text-track.xg-text-track.text-track-show {\n    opacity: 1;\n    visibility: visible; }\n  xg-text-track.xg-text-track xg-text-track-inner {\n    display: block;\n    max-width: 92%; }\n  xg-text-track.xg-text-track xg-text-track-span {\n    display: -webkit-box;\n    text-align: center;\n    text-overflow: ellipsis;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n    padding: 1px 4px;\n    -webkit-line-clamp: 1;\n    line-height: 120%;\n    word-break: break-word; }\n    xg-text-track.xg-text-track xg-text-track-span.text-track-deputy {\n      font-size: 75%; }\n    xg-text-track.xg-text-track xg-text-track-span.text-track-single {\n      -webkit-line-clamp: 1; }\n    xg-text-track.xg-text-track xg-text-track-span.text-track-double {\n      -webkit-line-clamp: 2; }\n    xg-text-track.xg-text-track xg-text-track-span.text-track-three {\n      -webkit-line-clamp: 3; }\n  xg-text-track.xg-text-track.text-track-bg xg-text-track-inner {\n    background-color: rgba(0, 0, 0, .54);\n    border-radius: 2px; }\n  xg-text-track.xg-text-track.text-track-stroke xg-text-track-inner {\n    background-color: none;\n    border-radius: 0;\n    text-shadow: -1px 1px 0 rgba(0, 0, 0, .7), 1px 1px 0 rgba(0, 0, 0, .7), 1px -1px 0 rgba(0, 0, 0, .7), -1px -1px 0 rgba(0, 0, 0, .7); }\n');var Ur=[{code:0,msg:"SUCCESS"},{code:1,msg:"LOAD_ERROR"},{code:2,msg:"PARSER_ERROR"},{code:3,msg:"FORMAT_NOT_SUPPORTED"},{code:4,msg:"ID_OR_LANGUAGE_NOT_EXIST"},{code:5,msg:"PARAMETERS_ERROR"},{code:6,msg:"ABORT"},{code:7,msg:"UNKNOWN"},{code:8,msg:"DATA_ERROR:subtitle.url is null"}],jr={RESIZE:"resize"},Nr=!1;return function(e){function t(e){re(this,t);var n=Kt(this,(t.__proto__||ne(t)).call(this));return Nr=h(),n.currentText=null,n.textTrack=[],n._cid=-1,n._gid=-1,n._cids=[],n._iId=null,n._iC=0,n.player=null,n.root=null,n.config={line:"double",bottom:0,mode:"stroke",defaultOpen:!1,baseSizeX:49,baseSizeY:28,minSize:16,minMobileSize:13,fitVideo:!0,offsetBottom:2,fontColor:"#fff"},n._ctime=0,n._loadingTrack={},kr(n.config).map((function(t){void 0!==e[t]&&null!==e[t]&&(n.config[t]=e[t])})),n._isOpen=!1,n._videoMeta={scale:0,videoHeight:0,videoWidth:0,lwidth:0,lheight:0,vWidth:0,vHeight:0,vBottom:0,vLeft:0,marginBottom:0},e.subTitles&&"Array"===p(e.subTitles)?(e.player&&n.attachPlayer(e.player),n.setSubTitles(e.subTitles,n.config.defaultOpen),n):Kt(n)}return dn(t,e),Qt(t,[{key:"version",get:function(){return"1.0.12"}}]),Qt(t,[{key:"setSubTitles",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],i=this._isOpen||n;r&&this.innerRoot&&this.switchOff(),this.currentText=null,this.textTrack=[],e.map((function(e){var n={};kr(e).map((function(t){n[t]=e[t]})),n.isDefault&&(t.currentText=n),t.textTrack.push(n)})),this.currentText&&b(this.currentText).then((function(e){t.addStyles(e),i&&t.switch()}))}},{key:"addStyles",value:function(e){var t=e.styles,n=e.format;t&&"vtt"===n&&(t.map((function(e){e.key||(e.key="xg-text-track-span")})),g(t,"xg-text-track"))}},{key:"attachPlayer",value:function(e){var t=this;if(e){this.player&&this.detachPlayer();var n=this.config,r=n.fontColor,i=n.mode,a=n.fitVideo;this.player=e,this.root=document.createElement("xg-text-track"),this.root.className="xg-text-track",!this._isOpen&&l(this.root,"text-track-hide"),!a&&l(this.root,"text-track-no-fitvideo"),i&&l(this.root,"text-track-"+i),this.innerRoot=document.createElement("xg-text-track-inner"),this.root.appendChild(this.innerRoot),r&&(this.root.style.color=r),this.currentText&&["language","id","label"].map((function(e){t.root.setAttribute("data-"+e,t.currentText[e]||"")})),this.player.root.appendChild(this.root),["destroy","__onTimeupdate","_onResize"].map((function(e){t[e]=t[e].bind(t)})),this.player.on("destroy",this.destroy),this.player.on("timeupdate",this.__onTimeupdate),this._isOpen&&this.switch(),y(e.root,this._onResize)}}},{key:"detachPlayer",value:function(){var e=this.player;e&&(e.off("destroy",this.destroy),e.off("timeupdate",this.__onTimeupdate),e.root&&(v(e.root,this._onResize),e.root.removeChild(this.root)),this.innerRoot=null,this.root=null,this.player=null)}},{key:"switch",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{id:"",language:""};return this._loadingTrack=t,new br((function(n,r){if(t.id||t.language)if(e.currentText&&_(t,e.currentText))e._loadingTrack={},e._updateCurrent(e.currentText),e._isOpen=!0,e.show(),n(m(0));else{for(var i=null,a=0;a<e.textTrack.length;a++)if(_(t,e.textTrack[a])){i=e.textTrack[a];break}if(i)i.list?(e._loadingTrack={},e._updateCurrent(i),e._isOpen=!0,e.show(),n(m(0))):b(i).then((function(t){if(e.addStyles(t),e._loadingTrack.id===i.id||e._loadingTrack.language===t.language)e._loadingTrack={},e._updateCurrent(t),e._isOpen=!0,e.show(),n(m(0));else{var a=m(6,{message:"check _loadingTrack fail id: "+e._loadingTrack.id+"  nextSubtitle:"+t.id});console.trace(a),r(a)}})).catch((function(e){r(e)}));else{var o=m(4,new Error("The is no subtitle with id:[{"+t.id+"}] or language:["+t.language+"]"));console.trace(o),r(o)}}else{if(e.currentText){e._loadingTrack={},e._updateCurrent(e.currentText),e._isOpen=!0,e.show();var s=m(0,{message:"switch default subtitle success"});return void n(s)}var l=m(5,{message:"no default subtitle"});r(l)}}))}},{key:"switchOff",value:function(){this._isOpen=!1,this.hide()}},{key:"_updateCurrent",value:function(e){var t=this;if(this.root){this.currentText=e,["language","id","label"].map((function(e){t.root.setAttribute("data-"+e,t.currentText[e]||"")})),this.__remove(this._cids);var n=this.player.currentTime;this._cids=[],this._gid=-1,this._cid=-1,this._update(n)}}},{key:"__loadAll",value:function(){this.textTrack.map((function(e){b(e)}))}},{key:"getDelCid",value:function(e,t){for(var n=[],r=0;r<e.length;r++)t.includes(e[r])||n.push(e[r]);return n}},{key:"getNewCid",value:function(e,t){for(var n=[],r=0;r<t.length;r++)e.includes(t[r])||n.push(t[r]);return n}},{key:"_update",value:function(e){var t=this,n=c(e,this.currentText.list,this._gid),r=[];if(n>-1&&(r=d(e,this.currentText.list[n].list,this._cid)),r.length<1)return this._cids.length>0&&this.__remove(this._cids),void(this._cids=[]);if(this._cids!==r||n!==this._gid){this._gid=n,this._cid=r[0];var i=this.getDelCid(this._cids,r),a=this.getNewCid(this._cids,r);this._cids=r,this.__remove(i);var o=[];a.map((function(e){var r=t.currentText.list[n].list[e];r.index=e,o.push(r)})),this.__render(o,e)}}},{key:"__onTimeupdate",value:function(){if(this._isOpen){var e=this.player.video,t=e.videoWidth,n=e.videoHeight;!this._videoMeta.scale&&t&&n&&this._onResize(this.player.root);var r=this.player.currentTime;Math.round(Math.abs(1e3*r-this._ctime))<200||(this._ctime=1e3*r,this.currentText&&this.currentText.list&&this._update(r))}}},{key:"_onResize",value:function(e){var t=this._videoMeta;if(e&&e instanceof window.Element||(e=this.player.root),this._iId&&(clearTimeout(this._iId),this._iId=null),!t.scale){if(!this.player.video)return;var n=this.player.video,r=n.videoWidth,i=n.videoHeight;if(!r||!i)return;t.videoWidth=r,t.videoHeight=i,t.scale=parseInt(i/r*100,10)}this.__startResize(e)}},{key:"resize",value:function(e,t){var n=this,r=this.config,i=r.baseSizeX,a=r.baseSizeY,o=r.minMobileSize,s=r.minSize,l=r.fitVideo,u=r.offsetBottom,c=this._videoMeta.scale;this._videoMeta.lwidth=e,this._videoMeta.lheight=t;var d=void 0,p=0;t/e*100>=c?(p=parseInt(c*e,10)/100,d=e):(p=t,d=parseInt(t/c*100,10)),this._videoMeta.vWidth=d,this._videoMeta.vHeight=p;var f=0,h=0;c>120?(f=a,h=parseInt(f*p/1080,10)):(f=i,h=parseInt(f*d/1920,10));var g=Nr?o:s,y={fontSize:h=h<g?g:h>f?f:h},v=parseInt((t-p)/2,10),m=parseInt((e-d)/2,10),x=parseInt(p*u,10)/100;this._videoMeta.vBottom=v,this._videoMeta.vLeft=m,this._videoMeta.marginBottom=x,l&&(y.bottom=v+x,y.left=y.right=m),kr(y).map((function(e){n.root.style[e]=y[e]+"px"})),this.emit(jr.RESIZE,{vLeft:m,vBottom:v,marginBottom:x,vWidth:d,vHeight:p,fontSize:h,scale:c})}},{key:"__startResize",value:function(e){var t=this,n=e.getBoundingClientRect(),r=this._videoMeta,i=n.width,a=n.height;if(this._iId&&(clearTimeout(this._iId),this._iId=null),i>0&&a>0&&(i!==r.lwidth||a!==r.lheight))this._iC=0,this.resize(i,a);else{if(this._iC>=5)return void(this._iC=0);this._iC++,this._iId=setTimeout((function(){t.__startResize(e)}),50)}}},{key:"__remove",value:function(e){var t=this;if(e&&!(e.length<1)){for(var n=this.innerRoot.children,r=[],i=0;i<n.length;i++){var a=Number(n[i].getAttribute("data-index"));e.includes(a)&&r.push(n[i])}r.map((function(e){t.innerRoot.removeChild(e)}))}}},{key:"__render",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];t.length>0&&t.map((function(t){var n="text-track-"+e.config.line;t.text.map((function(r,i){i>0&&(n+=" text-track-deputy");var a={"data-start":t.start,"data-end":t.end,"data-index":t.index};e.innerRoot.appendChild(f("xg-text-track-span",r,a,n))}))}))}},{key:"show",value:function(){u(this.root,"text-track-hide")}},{key:"hide",value:function(){l(this.root,"text-track-hide"),this.innerRoot.innerHTML=""}},{key:"destroy",value:function(){this.detachPlayer(),this.removeAllListeners(),this.player=null,this.textTrack=null}},{key:"marginBottom",get:function(){var e=this._videoMeta,t=e.bottom,n=e.marginBottom;return this.config.fitVideo?t+n:n}}]),t}(Er)}))},function(e,t,n){"use strict";var r=n(9),i=ue(r),a=n(10),o=ue(a),s=n(108),l=ue(s),u=n(48),c=ue(u),d=n(40),p=ue(d),f=n(53),h=ue(f),g=n(56),y=ue(g),v=n(59),m=ue(v),x=n(111),b=ue(x),_=n(116),k=ue(_),E=n(122),w=ue(E),T=n(64),S=ue(T),C=n(68),O=ue(C),A=n(71),R=ue(A),D=n(74),L=ue(D),P=n(125),M=ue(P),I=n(128),B=ue(I),U=n(129),j=ue(U),N=n(133),F=ue(N),z=n(139),V=ue(z),G=n(142),H=ue(G),q=n(145),W=ue(q),Y=n(149),X=ue(Y),K=n(153),$=ue(K),Z=n(157),J=ue(Z),Q=n(160),ee=ue(Q),te=n(162),ne=ue(te),re=n(163),ie=ue(re),ae=n(166),oe=ue(ae),se=n(169),le=ue(se);function ue(e){return e&&e.__esModule?e:{default:e}}i.default.installAll([o.default,l.default,c.default,p.default,h.default,y.default,m.default,b.default,k.default,w.default,S.default,O.default,R.default,L.default,M.default,B.default,j.default,F.default,V.default,H.default,W.default,X.default,$.default,J.default,ee.default,ne.default,ie.default,oe.default,le.default])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0);n(109);var i=function(){for(var e=this,t=e.root,n="",i=1;i<=12;i++)n+='<div class="xgplayer-enter-bar'+i+'"></div>';var a=(0,r.createDom)("xg-enter",'<div class="xgplayer-enter-spinner">\n                                                  '+n+"\n                                                </div>",{},"xgplayer-enter");t.appendChild(a)};t.default={name:"s_enter",method:i},e.exports=t["default"]},function(e,t,n){var r,i=n(110);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-enter{display:none;position:absolute;left:0;top:0;width:100%;height:100%;background:#000;z-index:120}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner{display:block;position:absolute;left:50%;top:50%;height:100px;width:100px;position:relative;-webkit-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div{width:12%;height:26%;background-color:hsla(0,0%,100%,.7);position:absolute;left:44%;top:37%;opacity:0;border-radius:30px;-webkit-animation:fade 1s linear infinite;animation:fade 1s linear infinite}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar1{-webkit-transform:rotate(0deg) translateY(-142%);-ms-transform:rotate(0deg) translateY(-142%);transform:rotate(0deg) translateY(-142%);-webkit-animation-delay:0s;animation-delay:0s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar2{-webkit-transform:rotate(30deg) translateY(-142%);-ms-transform:rotate(30deg) translateY(-142%);transform:rotate(30deg) translateY(-142%);-webkit-animation-delay:-.9163s;animation-delay:-.9163s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar3{-webkit-transform:rotate(60deg) translateY(-142%);-ms-transform:rotate(60deg) translateY(-142%);transform:rotate(60deg) translateY(-142%);-webkit-animation-delay:-.833s;animation-delay:-.833s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar4{-webkit-transform:rotate(90deg) translateY(-142%);-ms-transform:rotate(90deg) translateY(-142%);transform:rotate(90deg) translateY(-142%);-webkit-animation-delay:-.7497s;animation-delay:-.7497s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar5{-webkit-transform:rotate(120deg) translateY(-142%);-ms-transform:rotate(120deg) translateY(-142%);transform:rotate(120deg) translateY(-142%);-webkit-animation-delay:-.6664s;animation-delay:-.6664s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar6{-webkit-transform:rotate(150deg) translateY(-142%);-ms-transform:rotate(150deg) translateY(-142%);transform:rotate(150deg) translateY(-142%);-webkit-animation-delay:-.5831s;animation-delay:-.5831s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar7{-webkit-transform:rotate(180deg) translateY(-142%);-ms-transform:rotate(180deg) translateY(-142%);transform:rotate(180deg) translateY(-142%);-webkit-animation-delay:-.4998s;animation-delay:-.4998s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar8{-webkit-transform:rotate(210deg) translateY(-142%);-ms-transform:rotate(210deg) translateY(-142%);transform:rotate(210deg) translateY(-142%);-webkit-animation-delay:-.4165s;animation-delay:-.4165s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar9{-webkit-transform:rotate(240deg) translateY(-142%);-ms-transform:rotate(240deg) translateY(-142%);transform:rotate(240deg) translateY(-142%);-webkit-animation-delay:-.3332s;animation-delay:-.3332s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar10{-webkit-transform:rotate(270deg) translateY(-142%);-ms-transform:rotate(270deg) translateY(-142%);transform:rotate(270deg) translateY(-142%);-webkit-animation-delay:-.2499s;animation-delay:-.2499s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar11{-webkit-transform:rotate(300deg) translateY(-142%);-ms-transform:rotate(300deg) translateY(-142%);transform:rotate(300deg) translateY(-142%);-webkit-animation-delay:-.1666s;animation-delay:-.1666s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar12{-webkit-transform:rotate(330deg) translateY(-142%);-ms-transform:rotate(330deg) translateY(-142%);transform:rotate(330deg) translateY(-142%);-webkit-animation-delay:-.0833s;animation-delay:-.0833s}@-webkit-keyframes fade{0%{opacity:1}to{opacity:.25}}@keyframes fade{0%{opacity:1}to{opacity:.25}}.xgplayer-skin-default.xgplayer-is-enter .xgplayer-enter{display:block}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(112),a=l(i),o=n(113),s=l(o);function l(e){return e&&e.__esModule?e:{default:e}}n(114);var u=function(){var e=this;if(e.config.cssFullscreen){var t=(0,r.createDom)("xg-cssfullscreen",'<xg-icon class="xgplayer-icon">\n                                             <div class="xgplayer-icon-requestfull">'+a.default+'</div>\n                                             <div class="xgplayer-icon-exitfull">'+s.default+"</div>\n                                           </xg-icon>",{},"xgplayer-cssfullscreen"),n={};n.requestfull=e.lang.CSSFULLSCREEN_TIPS,n.exitfull=e.lang.EXITCSSFULLSCREEN_TIPS;var i=(0,r.createDom)("xg-tips",'<span class="xgplayer-tip-requestfull">'+n.requestfull+'</span>\n                                        <span class="xgplayer-tip-exitfull">'+n.exitfull+"</span>",{},"xgplayer-tips");t.appendChild(i),e.once("ready",(function(){e.controls.appendChild(t)})),["click","touchend"].forEach((function(n){t.addEventListener(n,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("cssFullscreenBtnClick")}))}))}};t.default={name:"s_cssFullscreen",method:u},e.exports=t["default"]},function(e,t,n){"use strict";n.r(t),t["default"]='<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">\n  <path transform="scale(0.028 0.028)" d="M843.617212 67.898413 175.411567 67.898413c-61.502749 0-111.367437 49.856501-111.367437 111.367437l0 668.205645c0 61.510936 49.864688 111.367437 111.367437 111.367437L843.617212 958.838931c61.510936 0 111.367437-49.856501 111.367437-111.367437L954.984648 179.26585C954.984648 117.754914 905.12917 67.898413 843.617212 67.898413zM398.146441 736.104057c15.380292 0 27.842115 12.461823 27.842115 27.842115 0 15.379269-12.461823 27.841092-27.842115 27.841092L259.725858 791.787264c-7.785314 0-14.781658-3.217275-19.838837-8.365528-5.383614-4.577249-8.791224-11.228739-8.791224-19.475564L231.095797 624.736621c0-15.371082 12.471033-27.842115 27.842115-27.842115 15.380292 0 27.842115 12.471033 27.842115 27.842115l-0.61603 71.426773 133.036969-133.037992 39.378869 39.378869L324.962651 736.113267 398.146441 736.104057zM419.199942 463.611943 286.162974 330.565764l0.61603 71.435982c0 15.380292-12.461823 27.842115-27.842115 27.842115-15.371082 0-27.842115-12.461823-27.842115-27.842115L231.094774 262.791172c0-8.256034 3.40761-14.908548 8.791224-19.476587 5.057179-5.148253 12.053524-8.374738 19.838837-8.374738l138.420583 0.00921c15.380292 0 27.842115 12.461823 27.842115 27.842115s-12.461823 27.842115-27.842115 27.842115l-73.175603-0.00921 133.607974 133.607974L419.199942 463.611943zM787.932981 763.946172c0 8.247848-3.40761 14.899338-8.791224 19.475564-5.057179 5.148253-12.053524 8.365528-19.839861 8.365528L620.881314 791.787264c-15.379269 0-27.841092-12.461823-27.841092-27.841092 0-15.380292 12.461823-27.842115 27.841092-27.842115l73.185836 0.00921L560.449967 602.50427l39.378869-39.378869L732.875015 696.163393l-0.62524-71.426773c0-15.371082 12.462846-27.842115 27.842115-27.842115 15.380292 0 27.842115 12.471033 27.842115 27.842115L787.934005 763.946172zM787.932981 402.000724c0 15.380292-12.461823 27.842115-27.842115 27.842115-15.379269 0-27.842115-12.461823-27.842115-27.842115l0.62524-71.435982L599.828836 463.611943l-39.378869-39.378869 133.617184-133.607974-73.185836 0.00921c-15.379269 0-27.841092-12.461823-27.841092-27.842115s12.461823-27.842115 27.841092-27.842115l138.421606-0.00921c7.785314 0 14.781658 3.226484 19.839861 8.374738 5.383614 4.568039 8.791224 11.219529 8.791224 19.476587L787.934005 402.000724z"></path>\n</svg>\n'},function(e,t,n){"use strict";n.r(t),t["default"]='<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">\n  <path transform="scale(0.028 0.028)" d="M834.56 81.92H189.44c-59.392 0-107.52 48.128-107.52 107.52v645.12c0 59.392 48.128 107.52 107.52 107.52h645.12c59.392 0 107.52-48.128 107.52-107.52V189.44c0-59.392-48.128-107.52-107.52-107.52zM458.24 727.04c0 14.848-12.288 26.624-26.624 26.624S404.48 741.888 404.48 727.04v-69.632L289.28 773.12c-10.752 10.24-27.648 10.24-37.888 0-10.24-10.752-10.24-27.648 0-37.888L366.592 619.52H296.96c-14.848 0-26.624-12.288-26.624-26.624s12.288-26.624 26.624-26.624h134.144c14.848 0 26.624 12.288 26.624 26.624V727.04z m0-295.936c0 14.848-12.288 26.624-26.624 26.624H296.96c-14.848 0-26.624-12.288-26.624-26.624S282.112 404.48 296.96 404.48h69.632L251.392 289.28c-10.24-10.752-10.24-27.648 0-37.888 5.12-5.12 12.288-7.68 18.944-7.68 6.656 0 13.824 2.56 18.944 7.68L404.48 366.592V296.96c0-14.848 12.288-26.624 26.624-26.624s26.624 12.288 26.624 26.624v134.144zM773.12 773.12c-10.752 10.24-27.648 10.24-37.888 0L619.52 657.408V727.04c0 14.848-12.288 26.624-26.624 26.624s-26.624-11.776-26.624-26.624v-134.144c0-14.848 12.288-26.624 26.624-26.624H727.04c14.848 0 26.624 12.288 26.624 26.624s-12.288 26.624-26.624 26.624h-69.632l115.2 115.2c10.752 10.752 10.752 27.648 0.512 38.4z m0-483.84L657.408 404.48H727.04c14.848 0 26.624 12.288 26.624 26.624 0 14.848-12.288 26.624-26.624 26.624h-134.144c-14.848 0-26.624-12.288-26.624-26.624V296.96c0-14.848 12.288-26.624 26.624-26.624s26.624 12.288 26.624 26.624v69.632L734.72 250.88c5.12-5.12 12.288-7.68 18.944-7.68s13.824 2.56 18.944 7.68c10.752 10.752 10.752 27.648 0.512 38.4z"></path>\n</svg>\n'},function(e,t,n){var r,i=n(115);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-cssfullscreen,.xgplayer-skin-default .xgplayer-cssfullscreen-img{position:relative;-webkit-order:12;-moz-box-ordinal-group:13;order:12;display:block;cursor:pointer}.xgplayer-skin-default .xgplayer-cssfullscreen-img .xgplayer-icon,.xgplayer-skin-default .xgplayer-cssfullscreen .xgplayer-icon{width:32px;margin-top:5px}.xgplayer-skin-default .xgplayer-cssfullscreen-img .xgplayer-icon div,.xgplayer-skin-default .xgplayer-cssfullscreen .xgplayer-icon div{position:absolute}.xgplayer-skin-default .xgplayer-cssfullscreen-img .xgplayer-icon .xgplayer-icon-requestfull,.xgplayer-skin-default .xgplayer-cssfullscreen .xgplayer-icon .xgplayer-icon-requestfull{display:block}.xgplayer-skin-default .xgplayer-cssfullscreen-img .xgplayer-icon .xgplayer-icon-exitfull,.xgplayer-skin-default .xgplayer-cssfullscreen .xgplayer-icon .xgplayer-icon-exitfull{display:none}.xgplayer-skin-default .xgplayer-cssfullscreen-img .xgplayer-tips,.xgplayer-skin-default .xgplayer-cssfullscreen .xgplayer-tips{margin-left:-40px}.xgplayer-skin-default .xgplayer-cssfullscreen-img .xgplayer-tips .xgplayer-tip-requestfull,.xgplayer-skin-default .xgplayer-cssfullscreen .xgplayer-tips .xgplayer-tip-requestfull{display:block}.xgplayer-skin-default .xgplayer-cssfullscreen-img .xgplayer-tips .xgplayer-tip-exitfull,.xgplayer-skin-default .xgplayer-cssfullscreen .xgplayer-tips .xgplayer-tip-exitfull{display:none}.xgplayer-skin-default .xgplayer-cssfullscreen-img:hover,.xgplayer-skin-default .xgplayer-cssfullscreen:hover{opacity:.85}.xgplayer-skin-default .xgplayer-cssfullscreen-img:hover .xgplayer-tips,.xgplayer-skin-default .xgplayer-cssfullscreen:hover .xgplayer-tips{display:block}.xgplayer-skin-default.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen-img .xgplayer-icon .xgplayer-icon-requestfull,.xgplayer-skin-default.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen .xgplayer-icon .xgplayer-icon-requestfull{display:none}.xgplayer-skin-default.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen-img .xgplayer-icon .xgplayer-icon-exitfull,.xgplayer-skin-default.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen .xgplayer-icon .xgplayer-icon-exitfull{display:block}.xgplayer-skin-default.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen-img .xgplayer-tips,.xgplayer-skin-default.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen .xgplayer-tips{margin-left:-47px}.xgplayer-skin-default.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen-img .xgplayer-tips .xgplayer-tip-requestfull,.xgplayer-skin-default.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen .xgplayer-tips .xgplayer-tip-requestfull{display:none}.xgplayer-skin-default.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen-img .xgplayer-tips .xgplayer-tip-exitfull,.xgplayer-skin-default.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen .xgplayer-tips .xgplayer-tip-exitfull{display:block}.xgplayer-skin-default.xgplayer-is-fullscreen .xgplayer-cssfullscreen,.xgplayer-skin-default.xgplayer-is-fullscreen .xgplayer-cssfullscreen-img{display:none}.xgplayer-skin-default.xgplayer-is-cssfullscreen{position:fixed!important;left:0!important;top:0!important;width:100%!important;height:100%!important;z-index:99999!important}.xgplayer-lang-is-en .xgplayer-cssfullscreen-img .xgplayer-tips,.xgplayer-lang-is-en .xgplayer-cssfullscreen .xgplayer-tips,.xgplayer-lang-is-en.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen-img .xgplayer-tips,.xgplayer-lang-is-en.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen .xgplayer-tips{margin-left:-46px}.lang-is-jp .xgplayer-cssfullscreen-img .xgplayer-tips,.lang-is-jp .xgplayer-cssfullscreen .xgplayer-tips{margin-left:-120px}.lang-is-jp.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen-img .xgplayer-tips,.lang-is-jp.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen .xgplayer-tips{margin-left:-60px}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(117),a=c(i),o=n(118),s=c(o),l=n(119),u=c(l);function c(e){return e&&e.__esModule?e:{default:e}}n(120);var d=function(){var e=this,t=(0,r.createDom)("xg-volume",'<xg-icon class="xgplayer-icon">\n                                         <div class="xgplayer-icon-large">'+u.default+'</div>\n                                         <div class="xgplayer-icon-small">'+s.default+'</div>\n                                         <div class="xgplayer-icon-muted">'+a.default+'</div>\n                                       </xg-icon>\n                                       <xg-slider class="xgplayer-slider" tabindex="2">\n                                         <xg-bar class="xgplayer-bar">\n                                           <xg-drag class="xgplayer-drag"></xg-drag>\n                                         </xg-bar>\n                                       </xg-slider>',{},"xgplayer-volume");e.once("ready",(function(){e.controls&&e.controls.appendChild(t)}));var n=t.querySelector(".xgplayer-slider"),i=t.querySelector(".xgplayer-bar"),o=t.querySelector(".xgplayer-drag"),l=t.querySelector(".xgplayer-icon");o.style.height=100*e.config.volume+"%",n.volume=e.config.volume,i.addEventListener("mousedown",(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("volumeBarClick",t)})),["click","touchend"].forEach((function(t){l.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("volumeIconClick")}))})),l.addEventListener("mouseenter",(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("volumeIconEnter")})),["blur","mouseleave"].forEach((function(n){t.addEventListener(n,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("volumeIconLeave")}))}))};t.default={name:"s_volume",method:d},e.exports=t["default"]},function(e,t,n){"use strict";n.r(t),t["default"]='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">\n  <path transform="scale(0.0220625 0.0220625)" d="M358.4 358.4h-204.8v307.2h204.8l256 256v-819.2l-256 256z"></path>\n  <path transform="scale(0.0220625 0.0220625)" d="M920.4 439.808l-108.544-109.056-72.704 72.704 109.568 108.544-109.056 108.544 72.704 72.704 108.032-109.568 108.544 109.056 72.704-72.704-109.568-108.032 109.056-108.544-72.704-72.704-108.032 109.568z"></path>\n</svg>\n'},function(e,t,n){"use strict";n.r(t),t["default"]='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">\n  <path transform="scale(0.0220625 0.0220625)" d="M358.4 358.4h-204.8v307.2h204.8l256 256v-819.2l-256 256z"></path>\n  <path transform="scale(0.0220625 0.0220625)" d="M795.648 693.248l-72.704-72.704c27.756-27.789 44.921-66.162 44.921-108.544s-17.165-80.755-44.922-108.546l0.002 0.002 72.704-72.704c46.713 46.235 75.639 110.363 75.639 181.248s-28.926 135.013-75.617 181.227l-0.021 0.021zM795.648 693.248l-72.704-72.704c27.756-27.789 44.921-66.162 44.921-108.544s-17.165-80.755-44.922-108.546l0.002 0.002 72.704-72.704c46.713 46.235 75.639 110.363 75.639 181.248s-28.926 135.013-75.617 181.227l-0.021 0.021z"></path>\n</svg>\n'},function(e,t,n){"use strict";n.r(t),t["default"]='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">\n  <path transform="scale(0.0220625 0.0220625)" d="M358.4 358.4h-204.8v307.2h204.8l256 256v-819.2l-256 256z"></path>\n  <path transform="scale(0.0220625 0.0220625)" d="M940.632 837.632l-72.192-72.192c65.114-64.745 105.412-154.386 105.412-253.44s-40.299-188.695-105.396-253.424l-0.016-0.016 72.192-72.192c83.639 83.197 135.401 198.37 135.401 325.632s-51.762 242.434-135.381 325.612l-0.020 0.020zM795.648 693.248l-72.704-72.704c27.756-27.789 44.921-66.162 44.921-108.544s-17.165-80.755-44.922-108.546l0.002 0.002 72.704-72.704c46.713 46.235 75.639 110.363 75.639 181.248s-28.926 135.013-75.617 181.227l-0.021 0.021z"></path>\n</svg>\n'},function(e,t,n){var r,i=n(121);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,'.xgplayer-skin-default .xgplayer-volume{outline:none;-webkit-order:4;-moz-box-ordinal-group:5;order:4;width:40px;height:40px;display:block;position:relative;z-index:18}.xgplayer-skin-default .xgplayer-volume .xgplayer-icon{margin-top:8px;cursor:pointer;position:absolute;bottom:-9px}.xgplayer-skin-default .xgplayer-volume .xgplayer-icon div{position:absolute}.xgplayer-skin-default .xgplayer-volume .xgplayer-icon .xgplayer-icon-large{display:block}.xgplayer-skin-default .xgplayer-volume .xgplayer-icon .xgplayer-icon-muted,.xgplayer-skin-default .xgplayer-volume .xgplayer-icon .xgplayer-icon-small{display:none}.xgplayer-skin-default .xgplayer-slider{display:none;position:absolute;width:28px;height:92px;background:rgba(0,0,0,.54);border-radius:1px;bottom:42px;outline:none}.xgplayer-skin-default .xgplayer-slider:after{content:" ";display:block;height:15px;width:28px;position:absolute;bottom:-15px;left:0;z-index:20}.xgplayer-skin-default .xgplayer-bar,.xgplayer-skin-default .xgplayer-drag{display:block;position:absolute;bottom:6px;left:12px;background:hsla(0,0%,100%,.3);border-radius:100px;width:4px;height:76px;outline:none;cursor:pointer}.xgplayer-skin-default .xgplayer-drag{bottom:0;left:0;background:#fa1f41;max-height:76px}.xgplayer-skin-default .xgplayer-drag:after{content:" ";display:inline-block;width:8px;height:8px;background:#fff;box-shadow:0 0 5px 0 rgba(0,0,0,.26);position:absolute;border-radius:50%;left:-2px;top:-6px}.xgplayer-skin-default.xgplayer-volume-active .xgplayer-slider,.xgplayer-skin-default.xgplayer-volume-large .xgplayer-volume .xgplayer-icon .xgplayer-icon-large{display:block}.xgplayer-skin-default.xgplayer-volume-large .xgplayer-volume .xgplayer-icon .xgplayer-icon-muted,.xgplayer-skin-default.xgplayer-volume-large .xgplayer-volume .xgplayer-icon .xgplayer-icon-small,.xgplayer-skin-default.xgplayer-volume-small .xgplayer-volume .xgplayer-icon .xgplayer-icon-large{display:none}.xgplayer-skin-default.xgplayer-volume-small .xgplayer-volume .xgplayer-icon .xgplayer-icon-small{display:block}.xgplayer-skin-default.xgplayer-volume-muted .xgplayer-volume .xgplayer-icon .xgplayer-icon-large,.xgplayer-skin-default.xgplayer-volume-muted .xgplayer-volume .xgplayer-icon .xgplayer-icon-small,.xgplayer-skin-default.xgplayer-volume-small .xgplayer-volume .xgplayer-icon .xgplayer-icon-muted{display:none}.xgplayer-skin-default.xgplayer-volume-muted .xgplayer-volume .xgplayer-icon .xgplayer-icon-muted{display:block}.xgplayer-skin-default.xgplayer-mobile .xgplayer-volume .xgplayer-slider{display:none}',""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(5),a=o(i);function o(e){return e&&e.__esModule?e:{default:e}}n(123);var s=function(){var e=this,t=e.root,n=void 0,i=(0,r.createDom)("xg-definition","",{tabindex:3},"xgplayer-definition");function o(){var n=e.definitionList,a=["<ul>"],o=e.config.url,s=document.createElement("a");e.switchURL?["mp4","hls","__flv__","dash"].every((function(t){return!e[t]||(e[t].url&&(s.href=e[t].url),"__flv__"===t&&(e[t]._options?s.href=e[t]._options.url:s.href=e[t]._mediaDataSource.url),"hls"===t&&(s.href=e[t].originUrl||e[t].url,o=s.href),o=s.href,!1)})):o=e.currentSrc||e.src,n.forEach((function(t){s.href=t.url,e.dash?a.push("<li url='"+t.url+"' cname='"+t.name+"' class='"+(t.selected?"selected":"")+"'>"+t.name+"</li>"):a.push("<li url='"+t.url+"' cname='"+t.name+"' class='"+(s.href===o?"selected":"")+"'>"+t.name+"</li>")}));var l=n.filter((function(t){return s.href=t.url,e.dash?!0===t.selected:s.href===o}));console.warn("cursrc:",l,"src:",o,"list:",n),a.push("</ul><p class='name'>"+(l[0]||{name:""}).name+"</p>");var u=t.querySelector(".xgplayer-definition");if(u){u.innerHTML=a.join("");var c=u.querySelector(".name");e.config.definitionActive&&"hover"!==e.config.definitionActive||c.addEventListener("mouseenter",(function(t){t.preventDefault(),t.stopPropagation(),(0,r.addClass)(e.root,"xgplayer-definition-active"),u.focus()}))}else{i.innerHTML=a.join("");var d=i.querySelector(".name");e.config.definitionActive&&"hover"!==e.config.definitionActive||d.addEventListener("mouseenter",(function(t){t.preventDefault(),t.stopPropagation(),(0,r.addClass)(e.root,"xgplayer-definition-active"),i.focus()})),e.controls.appendChild(i)}}function s(n){e.definitionList=n,n&&n instanceof Array&&n.length>0&&((0,r.addClass)(t,"xgplayer-is-definition"),e.once("canplay",o))}function l(){if(e.currentTime=e.curTime,n)e.pause();else{var t=e.play();void 0!==t&&t&&t.catch((function(e){}))}}function u(){e.once("timeupdate",l)}function c(){(0,r.removeClass)(t,"xgplayer-definition-active")}function d(){e.off("resourceReady",s),e.off("canplay",o),navigator.userAgent.toLowerCase().indexOf("android")>-1?(e.off("timeupdate",u),e.off("timeupdate",l)):e.off("loadedmetadata",l),e.off("blur",c),e.off("destroy",d)}"mobile"===a.default.device&&(e.config.definitionActive="click"),e.on("resourceReady",s),["touchend","click"].forEach((function(t){i.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation();var o=e.definitionList,s=t.target||t.srcElement,c=document.createElement("a");if(s&&"li"===s.tagName.toLocaleLowerCase()){var d=void 0,p=void 0;if(Array.prototype.forEach.call(s.parentNode.childNodes,(function(t){(0,r.hasClass)(t,"selected")&&(d=t.getAttribute("cname"),(0,r.removeClass)(t,"selected"),e.emit("beforeDefinitionChange",t.getAttribute("url")))})),e.dash&&o.forEach((function(e){e.selected=!1,e.name===s.innerHTML&&(e.selected=!0)})),(0,r.addClass)(s,"selected"),p=s.getAttribute("cname"),s.parentNode.nextSibling.innerHTML=""+s.getAttribute("cname"),c.href=s.getAttribute("url"),n=e.paused,e.switchURL){var f=document.createElement("a");["mp4","hls","__flv__","dash"].every((function(t){return!e[t]||(e[t].url&&(f.href=e[t].url),"__flv__"===t&&(e[t]._options?f.href=e[t]._options.url:f.href=e[t]._mediaDataSource.url),"hls"===t&&(f.href=e[t].originUrl||e[t].url),!1)})),f.href===c.href||e.ended||e.switchURL(c.href)}else{if(e["hls"]){document.createElement("a");e["hls"].url}c.href!==e.currentSrc&&(e.curTime=e.currentTime,e.ended||(e.src=c.href))}navigator.userAgent.toLowerCase().indexOf("android")>-1?e.once("timeupdate",u):e.once("loadedmetadata",l),e.emit("definitionChange",{from:d,to:p}),"mobile"===a.default.device&&(0,r.removeClass)(e.root,"xgplayer-definition-active")}else"click"!==e.config.definitionActive||!s||"p"!==s.tagName.toLocaleLowerCase()&&"em"!==s.tagName.toLocaleLowerCase()||("mobile"===a.default.device?(0,r.toggleClass)(e.root,"xgplayer-definition-active"):(0,r.addClass)(e.root,"xgplayer-definition-active"),i.focus());e.emit("focus")}),!1)})),i.addEventListener("mouseleave",(function(e){e.preventDefault(),e.stopPropagation(),(0,r.removeClass)(t,"xgplayer-definition-active")})),e.on("blur",c),e.once("destroy",d),e.getCurrentDefinition=function(){for(var t=e.controls.querySelectorAll(".xgplayer-definition ul li"),n=0;n<t.length;n++)if(t[n].className&&t[n].className.indexOf("selected")>-1)return{name:t[n].getAttribute("cname"),url:t[n].getAttribute("url")};return{name:t[0].getAttribute("cname"),url:t[0].getAttribute("url")}},e.switchDefinition=function(){for(var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.controls.querySelectorAll(".xgplayer-definition ul li"),r=0;r<n.length;r++)n[r].getAttribute("cname")!==t.name&&n[r].getAttribute("url")!==t.url&&r!==t.index||n[r].click()}};t.default={name:"s_definition",method:s},e.exports=t["default"]},function(e,t,n){var r,i=n(124);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-definition{-webkit-order:5;-moz-box-ordinal-group:6;order:5;width:60px;height:150px;z-index:18;position:relative;outline:none;display:none;cursor:default;margin-left:10px;margin-top:-119px}.xgplayer-skin-default .xgplayer-definition ul{display:none;list-style:none;width:78px;background:rgba(0,0,0,.54);border-radius:1px;position:absolute;bottom:30px;left:0;text-align:center;white-space:nowrap;margin-left:-10px;z-index:26;cursor:pointer}.xgplayer-skin-default .xgplayer-definition ul li{opacity:.7;font-family:PingFangSC-Regular;font-size:11px;color:hsla(0,0%,100%,.8);padding:6px 13px}.xgplayer-skin-default .xgplayer-definition ul li.selected,.xgplayer-skin-default .xgplayer-definition ul li:hover{color:#fff;opacity:1}.xgplayer-skin-default .xgplayer-definition .name{text-align:center;font-family:PingFangSC-Regular;font-size:13px;cursor:pointer;color:hsla(0,0%,100%,.8);position:absolute;bottom:0;width:60px;height:20px;line-height:20px;background:rgba(0,0,0,.38);border-radius:10px;display:inline-block;vertical-align:middle}.xgplayer-skin-default.xgplayer-definition-active .xgplayer-definition ul,.xgplayer-skin-default.xgplayer-is-definition .xgplayer-definition{display:block}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(5),a=o(i);function o(e){return e&&e.__esModule?e:{default:e}}n(126);var s=function(){var e=this,t=[];if(!e.config.playbackRate)return!1;t=[].concat(e.config.playbackRate),t.sort((function(e,t){return t-e}));var n=void 0!==e.config.playbackRateUnit?e.config.playbackRateUnit:"x",i=(0,r.createDom)("xg-playbackrate"," ",{},"xgplayer-playbackrate");"mobile"===a.default.device&&(e.config.playbackRateActive="click");var o=[];t.forEach((function(e){o.push({name:""+e,rate:""+e+n,selected:!1})}));var s=1,l=["<ul>"];o.forEach((function(t){e.config.defaultPlaybackRate&&e.config.defaultPlaybackRate.toString()===t.name?(t.selected=!0,s=e.config.defaultPlaybackRate,e.once("playing",(function(){e.video.playbackRate=e.config.defaultPlaybackRate}))):"1.0"!==t.name&&"1"!==t.name||e.config.defaultPlaybackRate&&1!==e.config.defaultPlaybackRate||(t.selected=!0),l.push("<li cname='"+t.name+"' class='"+(t.selected?"selected":"")+"'>"+t.rate+"</li>")})),l.push("</ul><p class='name'>"+s+n+"</p>");var u=e.root.querySelector(".xgplayer-playbackrate");if(u){u.innerHTML=l.join("");var c=u.querySelector(".name");e.config.playbackRateActive&&"hover"!==e.config.playbackRateActive||c.addEventListener("mouseenter",(function(t){t.preventDefault(),t.stopPropagation(),(0,r.addClass)(e.root,"xgplayer-playbackrate-active"),u.focus()}))}else{i.innerHTML=l.join("");var d=i.querySelector(".name");e.config.playbackRateActive&&"hover"!==e.config.playbackRateActive||d.addEventListener("mouseenter",(function(t){t.preventDefault(),t.stopPropagation(),(0,r.addClass)(e.root,"xgplayer-playbackrate-active"),i.focus()})),e.once("ready",(function(){e.controls.appendChild(i)}))}var p=["touchend","click"];function f(){(0,r.removeClass)(e.root,"xgplayer-playbackrate-active")}p.forEach((function(t){i.addEventListener(t,(function(t){t.stopPropagation(),t.preventDefault();var l=t.target;if(l&&"li"===l.tagName.toLocaleLowerCase()){var u=void 0,c=void 0;o.forEach((function(t){t.selected=!1,l.textContent.replace(/\s+/g,"")===t.rate&&(Array.prototype.forEach.call(l.parentNode.childNodes,(function(e){(0,r.hasClass)(e,"selected")&&(u=Number(e.getAttribute("cname")),(0,r.removeClass)(e,"selected"))})),t.selected=!0,e.video.playbackRate=1*t.name,s=1*t.name)})),(0,r.addClass)(l,"selected"),c=Number(l.getAttribute("cname")),l.parentNode.nextSibling.innerHTML=""+l.getAttribute("cname")+n,e.emit("playbackrateChange",{from:u,to:c}),"mobile"===a.default.device&&(0,r.removeClass)(e.root,"xgplayer-playbackrate-active")}else"click"!==e.config.playbackRateActive||!l||"p"!==l.tagName.toLocaleLowerCase()&&"span"!==l.tagName.toLocaleLowerCase()||("mobile"===a.default.device?(0,r.toggleClass)(e.root,"xgplayer-playbackrate-active"):(0,r.addClass)(e.root,"xgplayer-playbackrate-active"),i.focus());e.emit("focus")}),!1)})),i.addEventListener("mouseleave",(function(t){t.preventDefault(),t.stopPropagation(),(0,r.removeClass)(e.root,"xgplayer-playbackrate-active")})),e.on("blur",f),e.on("play",(function(){e.video.playbackRate.toFixed(1)!==s.toFixed(1)&&(e.video.playbackRate=s)})),e.switchPlaybackRate=function(){for(var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.controls.querySelectorAll(".xgplayer-playbackrate ul li"),i=0;i<n.length;i++)(0,r.hasClass)(n[i],"selected")||n[i].getAttribute("cname")!==""+t.playbackRate&&i!==t.index||n[i].click()},e.on("ratechange",(function(){e.switchPlaybackRate({playbackRate:e.playbackRate})}))};t.default={name:"s_playbackRate",method:s},e.exports=t["default"]},function(e,t,n){var r,i=n(127);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-playbackrate{-webkit-order:8;-moz-box-ordinal-group:9;order:8;width:60px;height:150px;z-index:18;position:relative;display:inline-block;cursor:default;margin-top:-119px}.xgplayer-skin-default .xgplayer-playbackrate ul{display:none;list-style:none;width:78px;background:rgba(0,0,0,.54);border-radius:1px;position:absolute;bottom:30px;left:50%;-webkit-transform:translateX(-50%);-ms-transform:translateX(-50%);transform:translateX(-50%);text-align:left;white-space:nowrap;z-index:26;cursor:pointer}.xgplayer-skin-default .xgplayer-playbackrate ul li{opacity:.7;font-family:PingFangSC-Regular;font-size:11px;color:hsla(0,0%,100%,.8);position:relative;padding:4px 0;text-align:center}.xgplayer-skin-default .xgplayer-playbackrate ul li.selected,.xgplayer-skin-default .xgplayer-playbackrate ul li:hover{color:#fff;opacity:1}.xgplayer-skin-default .xgplayer-playbackrate ul li:first-child{position:relative;margin-top:12px}.xgplayer-skin-default .xgplayer-playbackrate ul li:last-child{position:relative;margin-bottom:12px}.xgplayer-skin-default .xgplayer-playbackrate .name{width:60px;height:20px;position:absolute;bottom:0;text-align:center;font-family:PingFangSC-Regular;font-size:13px;background:rgba(0,0,0,.38);color:hsla(0,0%,100%,.8);border-radius:10px;line-height:20px}.xgplayer-skin-default .xgplayer-playbackrate span{position:relative;top:19px;font-weight:700;text-shadow:0 0 4px rgba(0,0,0,.6)}.xgplayer-skin-default .xgplayer-playbackrate:hover{opacity:1}.xgplayer-skin-default.xgplayer-playbackrate-active .xgplayer-playbackrate ul{display:block}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=function(){var e=this;if(e.config.preview&&e.config.preview.uploadEl){var t=(0,r.createDom)("xg-preview",'<input type="file">',{},"xgplayer-preview"),n=t.querySelector("input");e.config.preview.uploadEl.appendChild(t),n.onchange=function(){e.emit("upload",n)}}};t.default={name:"s_localPreview",method:i},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(130),a=o(i);function o(e){return e&&e.__esModule?e:{default:e}}n(131);var s=function(){var e=this;if(e.config.download){var t=(0,r.createDom)("xg-download",'<xg-icon class="xgplayer-icon">'+a.default+"</xg-icon>",{},"xgplayer-download"),n=e.lang.DOWNLOAD_TIPS,i=(0,r.createDom)("xg-tips",'<span class="xgplayer-tip-download">'+n+"</span>",{},"xgplayer-tips");t.appendChild(i),e.once("ready",(function(){e.controls.appendChild(t)})),["click","touchend"].forEach((function(n){t.addEventListener(n,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("downloadBtnClick")}))}))}};t.default={name:"s_download",method:s},e.exports=t["default"]},function(e,t,n){"use strict";n.r(t),t["default"]='<svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24">\n  <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n    <g transform="translate(-488.000000, -340.000000)" fill="#FFFFFF">\n      <g id="Group-2">\n        <g id="volme_big-copy" transform="translate(488.000000, 340.000000)">\n          <rect id="Rectangle-18" x="11" y="4" width="2" height="12" rx="1"></rect>\n          <rect id="Rectangle-2" x="3" y="18" width="18" height="2" rx="1"></rect>\n          <rect id="Rectangle-2" transform="translate(4.000000, 17.500000) rotate(90.000000) translate(-4.000000, -17.500000) " x="1.5" y="16.5" width="5" height="2" rx="1"></rect><rect id="Rectangle-2-Copy-3" transform="translate(20.000000, 17.500000) rotate(90.000000) translate(-20.000000, -17.500000) " x="17.5" y="16.5" width="5" height="2" rx="1"></rect>\n          <path d="M9.48791171,8.26502656 L9.48791171,14.2650266 C9.48791171,14.8173113 9.04019646,15.2650266 8.48791171,15.2650266 C7.93562696,15.2650266 7.48791171,14.8173113 7.48791171,14.2650266 L7.48791171,7.26502656 C7.48791171,6.71274181 7.93562696,6.26502656 8.48791171,6.26502656 L15.4879117,6.26502656 C16.0401965,6.26502656 16.4879117,6.71274181 16.4879117,7.26502656 C16.4879117,7.81731131 16.0401965,8.26502656 15.4879117,8.26502656 L9.48791171,8.26502656 Z" id="Combined-Shape" transform="translate(11.987912, 10.765027) scale(1, -1) rotate(45.000000) translate(-11.987912, -10.765027) "></path>\n        </g>\n      </g>\n    </g>\n  </g>\n</svg>\n'},function(e,t,n){var r,i=n(132);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-download{position:relative;-webkit-order:9;-moz-box-ordinal-group:10;order:9;display:block;cursor:pointer}.xgplayer-skin-default .xgplayer-download .xgplayer-icon{margin-top:3px}.xgplayer-skin-default .xgplayer-download .xgplayer-icon div{position:absolute}.xgplayer-skin-default .xgplayer-download .xgplayer-icon svg{position:relative;top:5px;left:5px}.xgplayer-skin-default .xgplayer-download .xgplayer-tips{margin-left:-20px}.xgplayer-skin-default .xgplayer-download .xgplayer-tips .xgplayer-tip-download{display:block}.xgplayer-skin-default .xgplayer-download:hover{opacity:.85}.xgplayer-skin-default .xgplayer-download:hover .xgplayer-tips{display:block}.xgplayer-lang-is-en .xgplayer-download .xgplayer-tips{margin-left:-32px}.xgplayer-lang-is-jp .xgplayer-download .xgplayer-tips{margin-left:-40px}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(134),a=l(i),o=n(136),s=l(o);function l(e){return e&&e.__esModule?e:{default:e}}n(137);var u=function(){var e=this,t=e.root;if(e.config.danmu){var n=(0,r.createDom)("xg-danmu","",{},"xgplayer-danmu");e.once("ready",(function(){t.appendChild(n)}));var i=(0,r.deepCopy)({container:n,player:e.video,comments:[],area:{start:0,end:1}},e.config.danmu),o=void 0;e.config.danmu.panel&&(o=(0,r.createDom)("xg-panel",'<xg-panel-icon class="xgplayer-panel-icon">\n                                                '+s.default+'\n                                              </xg-panel-icon>\n                                              <xg-panel-slider class="xgplayer-panel-slider">\n                                                <xg-hidemode class="xgplayer-hidemode">\n                                                  <p class="xgplayer-hidemode-font">屏蔽类型</p>\n                                                  <ul class="xgplayer-hidemode-radio">\n                                                    <li class="xgplayer-hidemode-scroll" id="false">滚动</li><li class="xgplayer-hidemode-top" id="false">顶部</li><li class="xgplayer-hidemode-bottom" id="false">底部</li><li class="xgplayer-hidemode-color" id="false">色彩</li>\n                                                  </ul>\n                                                </xg-hidemode>\n                                                <xg-transparency class="xgplayer-transparency">\n                                                  <span>不透明度</span>\n                                                  <input class="xgplayer-transparency-line xgplayer-transparency-color xgplayer-transparency-bar xgplayer-transparency-gradient" type="range" min="0" max="100" step="0.1" value="50"></input>\n                                                </xg-transparency>\n                                                <xg-showarea class="xgplayer-showarea">\n                                                  <div class="xgplayer-showarea-name">显示区域</div>\n                                                  <div class="xgplayer-showarea-control">\n                                                    <div class="xgplayer-showarea-control-up">\n                                                      <span class="xgplayer-showarea-control-up-item xgplayer-showarea-onequarters">1/4</span>\n                                                      <span class="xgplayer-showarea-control-up-item xgplayer-showarea-twoquarters selected-color">1/2</span>\n                                                      <span class="xgplayer-showarea-control-up-item xgplayer-showarea-threequarters">3/4</span>\n                                                      <span class="xgplayer-showarea-control-up-item xgplayer-showarea-full">1</span>\n                                                    </div>\n                                                    <div class="xgplayer-showarea-control-down">\n                                                      <div class="xgplayer-showarea-control-down-dots">\n                                                        <span class="xgplayer-showarea-onequarters-dot"></span>\n                                                        <span class="xgplayer-showarea-twoquarters-dot"></span>\n                                                        <span class="xgplayer-showarea-threequarters-dot"></span>\n                                                        <span class="xgplayer-showarea-full-dot"></span>\n                                                      </div>\n                                                      <input class="xgplayer-showarea-line xgplayer-showarea-color xgplayer-showarea-bar xgplayer-gradient" type="range" min="1" max="4" step="1" value="1">\n                                                    </div>\n                                                  </div>\n                                                </xg-showarea>\n                                                <xg-danmuspeed class="xgplayer-danmuspeed">\n                                                  <div class="xgplayer-danmuspeed-name">弹幕速度</div>\n                                                  <div class="xgplayer-danmuspeed-control">\n                                                    <div class="xgplayer-danmuspeed-control-up">\n                                                      <span class="xgplayer-danmuspeed-control-up-item xgplayer-danmuspeed-small">慢</span>\n                                                      <span class="xgplayer-danmuspeed-control-up-item xgplayer-danmuspeed-middle selected-color">中</span>\n                                                      <span class="xgplayer-danmuspeed-control-up-item xgplayer-danmuspeed-large">快</span>\n                                                    </div>\n                                                    <div class="xgplayer-danmuspeed-control-down">\n                                                      <div class="xgplayer-danmuspeed-control-down-dots">\n                                                        <span class="xgplayer-danmuspeed-small-dot"></span>\n                                                        <span class="xgplayer-danmuspeed-middle-dot"></span>\n                                                        <span class="xgplayer-danmuspeed-large-dot"></span>\n                                                      </div>\n                                                      <input class="xgplayer-danmuspeed-line xgplayer-danmuspeed-color xgplayer-danmuspeed-bar xgplayer-gradient" type="range" min="50" max="150" step="50" value="100">\n                                                    </div>\n                                                  </div>\n                                                </xg-danmuspeed>\n                                                <xg-danmufont class="xgplayer-danmufont">\n                                                  <div class="xgplayer-danmufont-name">字体大小</div>\n                                                  <div class="xgplayer-danmufont-control">\n                                                    <div class="xgplayer-danmufont-control-up">\n                                                      <span class="xgplayer-danmufont-control-up-item xgplayer-danmufont-small">小</span>\n                                                      <span class="xgplayer-danmufont-control-up-item xgplayer-danmufont-middle">中</span>\n                                                      <span class="xgplayer-danmufont-control-up-item xgplayer-danmufont-large selected-color">大</span>\n                                                    </div>\n                                                    <div class="xgplayer-danmufont-control-down">\n                                                      <div class="xgplayer-danmufont-control-down-dots">\n                                                        <span class="xgplayer-danmufont-small-dot"></span>\n                                                        <span class="xgplayer-danmufont-middle-dot"></span>\n                                                        <span class="xgplayer-danmufont-large-dot"></span>\n                                                      </div>\n                                                      <input class="xgplayer-danmufont-line xgplayer-danmufont-color xgplayer-danmufont-bar xgplayer-gradient" type="range" min="20" max="30" step="5" value="25">\n                                                    </div>\n                                                  </div>\n                                                </xg-danmufont>\n                                              </xg-panel-slider>',{tabindex:7},"xgplayer-panel"),e.once("ready",(function(){e.controls.appendChild(o)}))),e.once("complete",(function(){var t=new a.default(i);if(e.emit("initDefaultDanmu",t),e.danmu=t,e.config.danmu.panel){var n=o.querySelector(".xgplayer-panel-slider"),s=void 0,l=["mouseenter","touchend","click"];l.forEach((function(e){o.addEventListener(e,(function(e){e.preventDefault(),e.stopPropagation(),(0,r.addClass)(n,"xgplayer-panel-active"),o.focus(),s=!0}))})),o.addEventListener("mouseleave",(function(e){e.preventDefault(),e.stopPropagation(),(0,r.removeClass)(n,"xgplayer-panel-active"),s=!1})),n.addEventListener("mouseleave",(function(e){e.preventDefault(),e.stopPropagation(),!1===s&&(0,r.removeClass)(n,"xgplayer-panel-active")}));var u=e.config.danmu,c=o.querySelector(".xgplayer-hidemode-scroll"),d=o.querySelector(".xgplayer-hidemode-top"),p=o.querySelector(".xgplayer-hidemode-bottom"),f=o.querySelector(".xgplayer-hidemode-color"),h={scroll:c,top:d,bottom:p,color:f},g=function(t){var n=t,r=["touchend","click"];r.forEach((function(t){h[n].addEventListener(t,(function(t){"true"!==h[n].getAttribute("id")?(h[n].style.color="#f85959",h[n].setAttribute("id","true"),e.danmu.hide(n)):(h[n].style.color="#aaa",h[n].setAttribute("id","false"),e.danmu.show(n))}))}))};for(var y in h)g(y);var v=o.querySelector(".xgplayer-transparency-line"),m=o.querySelector(".xgplayer-transparency-gradient"),x=50;m.style.background="linear-gradient(to right, #f85959 0%, #f85959 "+x+"%, #aaa "+x+"%, #aaa)",v.addEventListener("input",(function(e){e.preventDefault(),e.stopPropagation(),x=e.target.value,m.style.background="linear-gradient(to right, #f85959 0%, #f85959 "+x+"%, #aaa "+x+"%, #aaa)",u.comments.forEach((function(e){e.style.opacity=x/100}))}));var b=o.querySelector(".xgplayer-showarea-line");b.addEventListener("input",(function(t){t.preventDefault(),t.stopPropagation();var n=t.target.value;e.danmu.config.area.end=n/100,e.config.danmu.area.end=n/100,e.danmu.bulletBtn.main.channel.resize()}));var _=o.querySelector(".xgplayer-danmuspeed-line");_.addEventListener("input",(function(e){e.preventDefault(),e.stopPropagation();var t=e.target.value;u.comments.forEach((function(e){e.duration=100*(200-t)}))}));var k=o.querySelector(".xgplayer-danmufont-line");if(k.addEventListener("input",(function(e){e.preventDefault(),e.stopPropagation();var t=e.target.value;u.comments.forEach((function(e){e.style.fontSize=t+"px"}))})),navigator.userAgent.indexOf("Firefox")>-1)for(var E=0;E<n.querySelectorAll("input").length;E++)n.querySelectorAll("input")[E].style.marginTop="10px"}}))}};t.default={name:"s_danmu",method:u},e.exports=t["default"]},function(e,t,n){"use strict";(function(e){var n,r,i,a="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};!function(o,s){"object"==a(t)&&"object"==a(e)?e.exports=s():(r=[],n=s,i="function"===typeof n?n.apply(t,r):n,void 0===i||(e.exports=i))}(window,(function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==("undefined"===typeof e?"undefined":a(e))&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=4)}([function(e,t,n){function r(e,t){return e.classList?Array.prototype.some.call(e.classList,(function(e){return e===t})):!!e.className.match(new RegExp("(\\s|^)"+t+"(\\s|$)"))}function i(e,t){e.classList?t.replace(/(^\s+|\s+$)/g,"").split(/\s+/g).forEach((function(t){t&&e.classList.add(t)})):r(e,t)||(e.className+=" "+t)}function a(e,t){e.classList?t.split(/\s+/g).forEach((function(t){e.classList.remove(t)})):r(e,t)&&t.split(/\s+/g).forEach((function(t){var n=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className=e.className.replace(n," ")}))}function o(e){return Object.prototype.toString.call(e).match(/([^\s.*]+)(?=]$)/g)[0]}Object.defineProperty(t,"__esModule",{value:!0}),t.createDom=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"div",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",i=document.createElement(e);return i.className=r,i.innerHTML=t,Object.keys(n).forEach((function(t){var r=t,a=n[t];"video"===e||"audio"===e?a&&i.setAttribute(r,a):i.setAttribute(r,a)})),i},t.hasClass=r,t.addClass=i,t.removeClass=a,t.toggleClass=function(e,t){t.split(/\s+/g).forEach((function(t){r(e,t)?a(e,t):i(e,t)}))},t.findDom=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document,t=arguments[1],n=void 0;try{n=e.querySelector(t)}catch(r){t.startsWith("#")&&(n=e.getElementById(t.slice(1)))}return n},t.deepCopy=function e(t,n){if("Object"===o(n)&&"Object"===o(t))return Object.keys(n).forEach((function(r){"Object"!==o(n[r])||n[r]instanceof Node?"Array"===o(n[r])?t[r]="Array"===o(t[r])?t[r].concat(n[r]):n[r]:t[r]=n[r]:t[r]?e(t[r],n[r]):t[r]=n[r]})),t},t.typeOf=o,t.copyDom=function(e){if(e&&1===e.nodeType){var t=document.createElement(e.tagName);return Array.prototype.forEach.call(e.attributes,(function(e){t.setAttribute(e.name,e.value)})),e.innerHTML&&(t.innerHTML=e.innerHTML),t}return""},t.attachEventListener=function(e,t,n,r){r?(e.on(t,n),function(e,t,n,r){e.once(r,(function i(){e.off(t,n),e.off(r,i)}))}(e,t,n,r)):e.on(t,(function r(i){n(i),e.off(t,r)}))},t.styleUtil=function(e,t,n){var r=e.style;try{r[t]=n}catch(e){r.setProperty(t,n)}},t.isNumber=function(e){return"number"==typeof e&&!Number.isNaN(e)},t.throttle=function(e,t){var n=this,r=0;return function(){for(var i=arguments.length,a=Array(i),o=0;o<i;o++)a[o]=arguments[o];clearTimeout(r),r=setTimeout((function(){return e.apply(n,a)}),t)}},t.hasOwnProperty=Object.prototype.hasOwnProperty},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r,i=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),a=n(26),o=(r=a)&&r.__esModule?r:{default:r},s=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return i(e,[{key:"setLogger",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";this.logger=new o.default(e+".js")}}]),e}();t.default=s,e.exports=t.default},function(e,t,n){var r=n(18)();e.exports=function(e){return e!==r&&null!==e}},function(e,t,n){e.exports=function(e){return null!=e}},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r,i=n(5),a=(r=i)&&r.__esModule?r:{default:r};n(34),t.default=a.default,e.exports=t.default},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.DanmuJs=void 0;var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=p(n(6)),o=n(25),s=p(n(1)),l=p(n(27)),u=p(n(32)),c=n(33),d=n(0);function p(e){return e&&e.__esModule?e:{default:e}}function f(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=("undefined"===typeof t?"undefined":a(t))&&"function"!=typeof t?e:t}var h=t.DanmuJs=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=f(this,(t.__proto__||Object.getPrototypeOf(t)).call(this)),r=n;r.setLogger("danmu"),r.logger&&r.logger.info("danmu.js version: "+o.version);var a=r.config={overlap:!1,area:{start:0,end:1,lines:void 0},live:!1,comments:[],direction:"r2l",needResizeObserver:!1,dropStaleComments:!1,channelSize:void 0,maxCommentsLength:void 0,bulletOffset:void 0,interval:2e3};if((0,d.deepCopy)(a,e),(0,i.default)(r),r.hideArr=[],r.domObj=new u.default,r.freezeId=null,a.comments.forEach((function(e){e.duration=e.duration?e.duration:5e3,e.mode||(e.mode="scroll")})),r.container=a.container&&1===a.container.nodeType?a.container:null,!r.container)return r.emit("error","container id can't be empty"),f(n,!1);if(a.containerStyle){var s=a.containerStyle;Object.keys(s).forEach((function(e){r.container.style[e]=s[e]}))}return r.live=a.live,r.player=a.player,r.direction=a.direction,(0,d.addClass)(r.container,"danmu"),r.bulletBtn=new l.default(r),r.main=r.bulletBtn.main,r.isReady=!0,r.emit("ready"),n.logger&&n.logger.info("ready"),n.addResizeObserver(),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+("undefined"===typeof t?"undefined":a(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"addResizeObserver",value:function(){var e=this;this.config.needResizeObserver&&(0,c.addObserver)(this.container,(function(){e.logger&&e.logger.info("needResizeObserver"),e.resize()}))}},{key:"start",value:function(){this.logger&&this.logger.info("start"),this.main.start()}},{key:"pause",value:function(){this.logger&&this.logger.info("pause"),this.main.pause()}},{key:"play",value:function(){this.logger&&this.logger.info("play"),this.main.play()}},{key:"stop",value:function(){this.logger&&this.logger.info("stop"),this.main.stop()}},{key:"clear",value:function(){this.logger&&this.logger.info("clear"),this.main.clear()}},{key:"destroy",value:function(){for(var e in(0,c.unObserver)(this.container),this.logger&&this.logger.info("destroy"),this.stop(),this.bulletBtn.destroy(),this.domObj.destroy(),this)delete this[e];this.emit("destroy")}},{key:"sendComment",value:function(e){this.logger&&this.logger.info("sendComment: "+(e.txt||"[DOM Element]")),e.duration||(e.duration=15e3),e&&e.id&&e.duration&&(e.el||e.txt)&&(e.duration=e.duration?e.duration:5e3,e.style||(e.style={opacity:void 0,fontSize:void 0}),e.style&&(this.opacity&&this.opacity!==e.style.opacity&&(e.style.opacity=this.opacity),this.fontSize&&this.fontSize!==e.style.fontSize&&(e.style.fontSize=this.fontSize)),e.prior||e.realTime?(this.main.data.unshift(e),e.realTime&&(this.main.readData(),this.main.dataHandle())):this.main.data.push(e))}},{key:"setCommentID",value:function(e,t){var n=this;this.logger&&this.logger.info("setCommentID: oldID "+e+" newID "+t),e&&t&&(this.main.data.some((function(n){return n.id===e&&(n.id=t,!0)})),this.main.queue.some((function(r){return r.id===e&&(r.id=t,r.pauseMove(),"paused"!==n.main.status&&r.startMove(),!0)})))}},{key:"setCommentDuration",value:function(e,t){var n=this;this.logger&&this.logger.info("setCommentDuration: id "+e+" duration "+t),e&&t&&(t=t||5e3,this.main.data.some((function(n){return n.id===e&&(n.duration=t,!0)})),this.main.queue.some((function(r){return r.id===e&&(r.duration=t,r.pauseMove(),"paused"!==n.main.status&&r.startMove(),!0)})))}},{key:"setCommentLike",value:function(e,t){this.logger&&this.logger.info("setCommentLike: id "+e+" like "+t),e&&t&&(this.main.data.some((function(n){return n.id===e&&(n.like=t,!0)})),this.main.queue.some((function(n){return n.id===e&&(n.pauseMove(),n.setLikeDom(t.el,t.style),"paused"!==n.danmu.main.status&&n.startMove(),!0)})))}},{key:"restartComment",value:function(e){if(this.logger&&this.logger.info("restartComment: id "+e),e){var t=this.main;if(this._releaseCtrl(e),"closed"===t.status)return;t.queue.some((function(n){return n.id===e&&("paused"!==t.status?n.startMove(!0):n.status="paused",!0)}))}}},{key:"_releaseCtrl",value:function(e){this.freezeId&&e===this.freezeId&&(this.mouseControl=!1,this.freezeId=null)}},{key:"_freezeCtrl",value:function(e){this.mouseControl=!0,this.freezeId=e}},{key:"freezeComment",value:function(e){this.logger&&this.logger.info("freezeComment: id "+e),e&&(this._freezeCtrl(e),this.main.queue.some((function(t){return t.id===e&&(t.status="forcedPause",t.pauseMove(),t.el&&t.el.style&&(0,d.styleUtil)(t.el,"zIndex",10),!0)})))}},{key:"removeComment",value:function(e){this.logger&&this.logger.info("removeComment: id "+e),e&&(this._releaseCtrl(e),this.main.queue.some((function(t){return t.id===e&&(t.remove(),!0)})),this.main.data=this.main.data.filter((function(t){return t.id!==e})))}},{key:"updateComments",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=this.config,r=this.main,i=this.player,a=[],o=0;if(this.logger&&this.logger.info("updateComments: "+e.length+", isClear "+t),"boolean"==typeof t&&t&&(r.data=[]),r.data=r.data.concat(e),r.sortData(),"number"==typeof n.maxCommentsLength&&r.data.length>n.maxCommentsLength){o=r.data.length-n.maxCommentsLength;for(var s,l=0;l<o;l++)(s=r.data[l]).prior&&!s.attached_&&a.push(r.data[l])}else if(n.dropStaleComments&&i&&i.currentTime){var u=Math.floor(1e3*i.currentTime),c=u-n.interval;if(c>0)for(var d,p=0;p<r.data.length;p++)if((d=r.data[p]).prior&&!d.attached_&&a.push(r.data[p]),d.start>c){o=p;break}o>0&&(r.data.splice(0,o),r.data=a.concat(r.data))}}},{key:"willChange",value:function(){var e=this.container,t=this.main;e.style.willChange="opacity",t.willChanges.push("contents"),t.queue.forEach((function(e){e.willChange()}))}},{key:"stopWillChange",value:function(){this.container.style.willChange="",this.main.willChanges.splice(0),this.main.queue.forEach((function(e){e.willChange()}))}},{key:"setAllDuration",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"scroll",t=this,n=arguments[1],r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];this.logger&&this.logger.info("setAllDuration: mode "+e+" duration "+n+" force "+r),n&&(n=n||5e3,r&&(this.main.forceDuration=n),this.main.data.forEach((function(t){e===t.mode&&(t.duration=n)})),this.main.queue.forEach((function(r){e===r.mode&&(r.duration=n,r.pauseMove(),"paused"!==t.main.status&&r.startMove())})))}},{key:"setPlayRate",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"scroll",n=arguments[1];this.logger&&this.logger.info("setPlayRate: "+n),(0,d.isNumber)(n)&&n>0&&(this.main.playRate=n,this.main.queue.forEach((function(n){t===n.mode&&(n.pauseMove(),"paused"!==e.main.status&&n.startMove())})))}},{key:"setOpacity",value:function(e){this.logger&&this.logger.info("setOpacity: opacity "+e),this.container.style.opacity=e}},{key:"setFontSize",value:function(e,t){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{reflow:!0};this.logger&&this.logger.info("setFontSize: size "+e+" channelSize "+t),this.fontSize=e+"px",e&&(this.main.data.forEach((function(e){e.style&&(e.style.fontSize=n.fontSize)})),this.main.queue.forEach((function(e){e.options.style||(e.options.style={}),e.options.style.fontSize=n.fontSize,e.setFontSize(n.fontSize),t&&(e.top=e.channel_id[0]*t,e.topInit())}))),t&&(this.config.channelSize=t,r.reflow&&this.main.channel.resizeSync())}},{key:"setArea",value:function(e){this.logger&&this.logger.info("setArea: area "+e),this.config.area=e,!1!==e.reflow&&this.main.channel.resizeSync()}},{key:"hide",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"scroll";this.logger&&this.logger.info("hide: mode "+e),this.hideArr.indexOf(e)<0&&this.hideArr.push(e);var t=this.main.queue.filter((function(t){return e===t.mode||"color"===e&&t.color}));t.forEach((function(e){return e.remove()}))}},{key:"show",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"scroll";this.logger&&this.logger.info("show: mode "+e);var t=this.hideArr.indexOf(e);t>-1&&this.hideArr.splice(t,1)}},{key:"setDirection",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"r2l";this.logger&&this.logger.info("setDirection: direction "+e),this.emit("changeDirection",e)}},{key:"resize",value:function(){this.logger&&this.logger.info("resize"),this.emit("channel_resize")}},{key:"status",get:function(){return this.main.status}},{key:"state",get:function(){var e=this.main;return{status:e.status,comments:e.data,bullets:e.queue}}},{key:"containerPos",get:function(){return this.main.channel.containerPos}}]),t}(s.default);t.default=h},function(e,t,n){var r,i,o,s,l,u,c,d=n(7),p=n(24),f=Function.prototype.apply,h=Function.prototype.call,g=Object.create,y=Object.defineProperty,v=Object.defineProperties,m=Object.prototype.hasOwnProperty,x={configurable:!0,enumerable:!1,writable:!0};i=function(e,t){var n,i;return p(t),i=this,r.call(this,e,n=function(){o.call(i,e,n),f.call(t,this,arguments)}),n.__eeOnceListener__=t,this},l={on:r=function(e,t){var n;return p(t),m.call(this,"__ee__")?n=this.__ee__:(n=x.value=g(null),y(this,"__ee__",x),x.value=null),n[e]?"object"==a(n[e])?n[e].push(t):n[e]=[n[e],t]:n[e]=t,this},once:i,off:o=function(e,t){var n,r,i,o;if(p(t),!m.call(this,"__ee__"))return this;if(!(n=this.__ee__)[e])return this;if("object"==a(r=n[e]))for(o=0;i=r[o];++o)i!==t&&i.__eeOnceListener__!==t||(2===r.length?n[e]=r[o?0:1]:r.splice(o,1));else r!==t&&r.__eeOnceListener__!==t||delete n[e];return this},emit:s=function(e){var t,n,r,i,o;if(m.call(this,"__ee__")&&(i=this.__ee__[e]))if("object"==("undefined"===typeof i?"undefined":a(i))){for(n=arguments.length,o=new Array(n-1),t=1;t<n;++t)o[t-1]=arguments[t];for(i=i.slice(),t=0;r=i[t];++t)f.call(r,this,o)}else switch(arguments.length){case 1:h.call(i,this);break;case 2:h.call(i,this,arguments[1]);break;case 3:h.call(i,this,arguments[1],arguments[2]);break;default:for(n=arguments.length,o=new Array(n-1),t=1;t<n;++t)o[t-1]=arguments[t];f.call(i,this,o)}}},u={on:d(r),once:d(i),off:d(o),emit:d(s)},c=v({},u),e.exports=t=function(e){return null==e?g(c):v(Object(e),u)},t.methods=l},function(e,t,n){var r=n(3),i=n(8),a=n(12),o=n(20),s=n(21);(e.exports=function(e,t){var n,i,l,u,c;return arguments.length<2||"string"!=typeof e?(u=t,t=e,e=null):u=arguments[2],r(e)?(n=s.call(e,"c"),i=s.call(e,"e"),l=s.call(e,"w")):(n=l=!0,i=!1),c={value:t,configurable:n,enumerable:i,writable:l},u?a(o(u),c):c}).gs=function(e,t,n){var l,u,c,d;return"string"!=typeof e?(c=n,n=t,t=e,e=null):c=arguments[3],r(t)?i(t)?r(n)?i(n)||(c=n,n=void 0):n=void 0:(c=t,t=n=void 0):t=void 0,r(e)?(l=s.call(e,"c"),u=s.call(e,"e")):(l=!0,u=!1),d={get:t,set:n,configurable:l,enumerable:u},c?a(o(c),d):d}},function(e,t,n){var r=n(9),i=/^\s*class[\s{/}]/,a=Function.prototype.toString;e.exports=function(e){return!!r(e)&&!i.test(a.call(e))}},function(e,t,n){var r=n(10);e.exports=function(e){if("function"!=typeof e)return!1;if(!hasOwnProperty.call(e,"length"))return!1;try{if("number"!=typeof e.length)return!1;if("function"!=typeof e.call)return!1;if("function"!=typeof e.apply)return!1}catch(e){return!1}return!r(e)}},function(e,t,n){var r=n(11);e.exports=function(e){if(!r(e))return!1;try{return!!e.constructor&&e.constructor.prototype===e}catch(e){return!1}}},function(e,t,n){var r=n(3),i={object:!0,function:!0,undefined:!0};e.exports=function(e){return!!r(e)&&hasOwnProperty.call(i,"undefined"===typeof e?"undefined":a(e))}},function(e,t,n){e.exports=n(13)()?Object.assign:n(14)},function(e,t,n){e.exports=function(){var e,t=Object.assign;return"function"==typeof t&&(t(e={foo:"raz"},{bar:"dwa"},{trzy:"trzy"}),e.foo+e.bar+e.trzy==="razdwatrzy")}},function(e,t,n){var r=n(15),i=n(19),a=Math.max;e.exports=function(e,t){var n,o,s,l=a(arguments.length,2);for(e=Object(i(e)),s=function(r){try{e[r]=t[r]}catch(e){n||(n=e)}},o=1;o<l;++o)r(t=arguments[o]).forEach(s);if(void 0!==n)throw n;return e}},function(e,t,n){e.exports=n(16)()?Object.keys:n(17)},function(e,t,n){e.exports=function(){try{return Object.keys("primitive"),!0}catch(e){return!1}}},function(e,t,n){var r=n(2),i=Object.keys;e.exports=function(e){return i(r(e)?Object(e):e)}},function(e,t,n){e.exports=function(){}},function(e,t,n){var r=n(2);e.exports=function(e){if(!r(e))throw new TypeError("Cannot use null or undefined");return e}},function(e,t,n){var r=n(2),i=Array.prototype.forEach,a=Object.create,o=function(e,t){var n;for(n in e)t[n]=e[n]};e.exports=function(e){var t=a(null);return i.call(arguments,(function(e){r(e)&&o(Object(e),t)})),t}},function(e,t,n){e.exports=n(22)()?String.prototype.contains:n(23)},function(e,t,n){var r="razdwatrzy";e.exports=function(){return"function"==typeof r.contains&&!0===r.contains("dwa")&&!1===r.contains("foo")}},function(e,t,n){var r=String.prototype.indexOf;e.exports=function(e){return r.call(this,e,arguments[1])>-1}},function(e,t,n){e.exports=function(e){if("function"!=typeof e)throw new TypeError(e+" is not a function");return e}},function(e){e.exports=JSON.parse('{"version":"1.1.2"}')},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i="undefined"!=typeof window&&window.location.href.indexOf("danmu-debug")>-1,a=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.constructorName=t||""}return r(e,[{key:"info",value:function(e){for(var t,n=arguments.length,r=Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];i&&(t=console).log.apply(t,["[Danmu Log]["+this.constructorName+"]",e].concat(r))}}]),e}();t.default=a,e.exports=t.default},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=l(n(1)),o=l(n(28)),s=n(0);function l(e){return e&&e.__esModule?e:{default:e}}var u=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=("undefined"===typeof t?"undefined":a(t))&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return n.setLogger("control"),n.danmu=e,n.main=new o.default(e),e.config.defaultOff||n.main.start(),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+("undefined"===typeof t?"undefined":a(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"createSwitch",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return this.logger&&this.logger.info("createSwitch"),this.switchBtn=(0,s.createDom)("dk-switch",'<span class="txt">弹</span>',{},"danmu-switch "+(e?"danmu-switch-active":"")),this.switchBtn}},{key:"destroy",value:function(){for(var e in this.logger&&this.logger.info("destroy"),this.main.destroy(),this)s.hasOwnProperty.call(this,e)&&delete this[e]}}]),t}(i.default);t.default=u,e.exports=t.default},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=u(n(1)),o=u(n(29)),s=u(n(30)),l=n(0);function u(e){return e&&e.__esModule?e:{default:e}}var c=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=("undefined"===typeof t?"undefined":a(t))&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return n.setLogger("main"),n.danmu=e,n.container=e.container,n.channel=new s.default(e),n.data=[].concat(e.config.comments),n.playedData=[],n.queue=[],n.timer=null,n.playRate=1,n.retryStatus="normal",n.interval=e.config.interval,n.willChanges=[],n._status="idle",(0,l.attachEventListener)(e,"bullet_remove",n.updateQueue.bind(n),"destroy"),(0,l.attachEventListener)(e,"changeDirection",(function(e){n.danmu.direction=e}),"destroy"),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+("undefined"===typeof t?"undefined":a(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"_cancelDataHandleTimer",value:function(){this.handleId&&(clearTimeout(this.handleId),this.handleId=null),this.handleTimer&&(clearTimeout(this.handleTimer),this.handleTimer=null)}},{key:"destroy",value:function(){for(var e in this.logger&&this.logger.info("destroy"),this._cancelDataHandleTimer(),this.channel.destroy(),this.data=[],this)delete this[e]}},{key:"updateQueue",value:function(e){this.logger&&this.logger.info("updateQueue");var t=this;t.queue.some((function(n,r){return n.id===e.bullet.id&&(t.queue.splice(r,1),!0)})),t.data.some((function(t){return t.id===e.bullet.id&&(t.attached_=!1,!0)}))}},{key:"init",value:function(){var e=this;e.logger&&e.logger.info("init"),e.retryStatus="normal",e.sortData(),function t(){"closed"!==e._status||"stop"!==e.retryStatus?("playing"===e._status&&(e.readData(),e.dataHandle()),"stop"===e.retryStatus&&"paused"!==e._status||(e.handleTimer=setTimeout((function(){e.handleId=requestAnimationFrame((function(){t()}))}),250))):e._cancelDataHandleTimer()}()}},{key:"start",value:function(){this.logger&&this.logger.info("start"),this._status="playing",this.queue=[],this.container.innerHTML="",this.channel.reset(),this.init()}},{key:"stop",value:function(){this.logger&&this.logger.info("stop"),this._status="closed",this.retryStatus="stop",this.queue=[],this.container.innerHTML="",this.channel.reset()}},{key:"clear",value:function(){this.logger&&this.logger.info("clear"),this.channel.reset(),this.data=[],this.queue=[],this.container.innerHTML=""}},{key:"play",value:function(){var e=this;if("closed"!==this._status){this.logger&&this.logger.info("play"),this._status="playing";var t=this.channel.channels;t&&t.length>0&&["scroll","top","bottom"].forEach((function(n){e.queue.forEach((function(e){e.startMove(),e.resized=!0}));for(var r=0;r<t.length;r++)t[r].queue[n].forEach((function(e){e.resized=!1}))}))}else this.logger&&this.logger.info("play ignored")}},{key:"pause",value:function(){if("closed"!==this._status){this.logger&&this.logger.info("pause"),this._status="paused";var e=this.channel.channels;e&&e.length>0&&this.queue.forEach((function(e){e.pauseMove()}))}else this.logger&&this.logger.info("pause ignored")}},{key:"dataHandle",value:function(){"paused"!==this._status&&"closed"!==this._status&&this.queue.length&&this.queue.forEach((function(e){"waiting"===e.status&&e.startMove()}))}},{key:"readData",value:function(){if(this.danmu.isReady){var e=this,t=this.danmu,n=t.player,r=e.interval,i=e.channel,a=void 0,s=void 0;if(n){var u=n.currentTime?Math.floor(1e3*n.currentTime):0;s=e.data.filter((function(t){return!t.start&&e.danmu.hideArr.indexOf(t.mode)<0&&(!t.color||e.danmu.hideArr.indexOf("color")<0)&&(t.start=u),!t.attached_&&e.danmu.hideArr.indexOf(t.mode)<0&&(!t.color||e.danmu.hideArr.indexOf("color")<0)&&t.start-r<=u&&u<=t.start+r})),t.live&&(e.data=[])}else 0===(s=e.data.splice(0,1)).length&&(s=e.playedData.splice(0,1));if(s.length>0){i.updatePos();var c=2;e:for(var d,p=0;p<s.length;p++)if(d=s[p],e.forceDuration&&e.forceDuration!==d.duration&&(d.duration=e.forceDuration),(a=new o.default(t,d))&&!a.bulletCreateFail)if(a.attach(),d.attached_=!0,i.addBullet(a).result)e.queue.push(a),a.topInit(),c=2;else{for(var f in a.detach(),a)l.hasOwnProperty.call(a,f)&&delete a[f];if(a=null,d.attached_=!1,d.noDiscard&&(d.prior?e.data.unshift(d):e.data.push(d)),0===c)break e;c--}else{if(0===c)break e;c--}}}}},{key:"sortData",value:function(){this.data.sort((function(e,t){return(e.start||-1)-(t.start||-1)}))}},{key:"status",get:function(){return this._status}}]),t}(i.default);t.default=c,e.exports=t.default},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.Bullet=void 0;var r,i=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=n(1),s=(r=o)&&r.__esModule?r:{default:r},l=n(0);function u(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=("undefined"===typeof t?"undefined":a(t))&&"function"!=typeof t?e:t}var c=t.Bullet=function(e){function t(e,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var r=u(this,(t.__proto__||Object.getPrototypeOf(t)).call(this)),i=r,a=void 0;if(r.setLogger("bullet"),r.danmu=e,r.options=n,r.duration=n.duration,r.id=n.id,r.container=e.container,r.start=n.start,r.prior=n.prior,r.realTime=n.realTime,r.color=n.color,r.bookChannelId=n.bookChannelId,r.direction=e.direction,r.reuseDOM=!0,r.willChanges=[],r.domObj=e.domObj,n.el&&1===n.el.nodeType){if(n.el.parentNode)return u(r,{bulletCreateFail:!0});if(e.config.disableCopyDOM)a=n.el,r.reuseDOM=!1;else{a=r.domObj.use();var o=(0,l.copyDom)(n.el);n.eventListeners&&n.eventListeners.length>0&&n.eventListeners.forEach((function(e){o.addEventListener(e.event,e.listener,e.useCapture||!1)})),a.appendChild(o)}}else(a=r.domObj.use()).textContent=n.txt;if(r.onChangeDirection=function(e){i.direction=e},r.danmu.on("changeDirection",r.onChangeDirection),n.style){var s=n.style;Object.keys(s).forEach((function(e){(0,l.styleUtil)(a,e,s[e])}))}"top"===n.mode||"bottom"===n.mode?r.mode=n.mode:r.mode="scroll",r.el=a,n.like&&n.like.el&&r.setLikeDom(n.like.el,n.like.style),r.status="waiting";var c=void 0;if((0,l.isNumber)(e.config.bulletOffset)&&e.config.bulletOffset>=0)c=e.config.bulletOffset;else{var d=e.containerPos;c=d.width/10>100?100:d.width/10}var p=n.realTime?0:Math.floor(Math.random()*c);return r.updateOffset(p),r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+("undefined"===typeof t?"undefined":a(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),i(t,[{key:"updateOffset",value:function(e){this.random=e,(0,l.styleUtil)(this.el,"left",this.danmu.containerPos.width+e+"px")}},{key:"attach",value:function(){var e=this.el;this.container.appendChild(e),this.elPos=e.getBoundingClientRect(),"b2t"===this.direction?(this.width=this.elPos.height,this.height=this.elPos.width):(this.width=this.elPos.width,this.height=this.elPos.height),this.moveV&&(this.duration=(this.danmu.containerPos.width+this.random+this.width)/this.moveV*1e3),this.danmu.config&&(this.danmu.config.mouseControl&&(this.mouseoverFunWrapper=this.mouseoverFun.bind(this),e.addEventListener("mouseover",this.mouseoverFunWrapper,!1)),this.danmu.config.mouseEnterControl&&(this.mouseEnterFunWrapper=this.mouseoverFun.bind(this),e.addEventListener("mouseenter",this.mouseEnterFunWrapper,!1))),e.addEventListener("transitionend",this._onTransitionEnd,!1)}},{key:"detach",value:function(){var e=this.el;if(e){var t=this.danmu.config;t&&(t.mouseControl&&e.removeEventListener("mouseover",this.mouseoverFunWrapper,!1),t.mouseEnterControl&&e.removeEventListener("mouseenter",this.mouseEnterFunWrapper,!1)),e.removeEventListener("transitionend",this._onTransitionEnd,!1),e.parentNode&&e.parentNode.removeChild(e),this.reuseDOM&&this.domObj.unused(e),this.el=null}this.elPos=void 0,this.danmu.off("changeDirection",this.onChangeDirection)}},{key:"willChange",value:function(){var e=this.danmu.main.willChanges.concat(this.willChanges).join();(0,l.styleUtil)(this.el,"willChange",e)}},{key:"mouseoverFun",value:function(e){this.danmu&&this.danmu.mouseControl&&this.danmu.config.mouseControlPause||"waiting"===this.status||"end"===this.status||this.danmu&&this.danmu.emit("bullet_hover",{bullet:this,event:e})}},{key:"_onTransitionEnd",value:function(){this.status="end",this.remove()}},{key:"topInit",value:function(){this.logger&&this.logger.info("topInit #"+(this.options.txt||"[DOM Element]")+"#"),"b2t"===this.direction?((0,l.styleUtil)(this.el,"transformOrigin","left top"),(0,l.styleUtil)(this.el,"transform","translateX(-"+this.top+"px) translateY("+this.danmu.containerPos.height+"px) translateZ(0px) rotate(90deg)"),(0,l.styleUtil)(this.el,"transition","transform 0s linear 0s")):(0,l.styleUtil)(this.el,"top",this.top+"px")}},{key:"pauseMove",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this;if("paused"!==t.status&&("forcedPause"!==t.status&&(this.status="paused"),t._moveV=void 0,this.el))if(this.willChange(),"scroll"===this.mode){var n=t.danmu.containerPos;if(e){var r=((new Date).getTime()-t.moveTime)/1e3,i=r*this.moveV,a=0;a=t.moveMoreS-i>=0?"b2t"===this.direction?(t.moveMoreS-i)/t.moveContainerHeight*n.height:(t.moveMoreS-i)/t.moveContainerWidth*n.width:t.moveMoreS-i,"b2t"===this.direction?(0,l.styleUtil)(this.el,"transform","translateX(-"+this.top+"px) translateY("+a+"px) translateZ(0px) rotate(90deg)"):(0,l.styleUtil)(this.el,"left",a+"px")}else"b2t"===this.direction?(0,l.styleUtil)(this.el,"transform","translateX(-"+this.top+"px) translateY("+(this.el.getBoundingClientRect().top-n.top)+"px) translateZ(0px) rotate(90deg)"):(0,l.styleUtil)(this.el,"left",this.el.getBoundingClientRect().left-n.left+"px");"b2t"===this.direction||(0,l.styleUtil)(this.el,"transform","translateX(0px) translateY(0px) translateZ(0px)"),(0,l.styleUtil)(this.el,"transition","transform 0s linear 0s")}else this.pastDuration&&this.startTime?this.pastDuration=this.pastDuration+(new Date).getTime()-this.startTime:this.pastDuration=1}},{key:"startMove",value:function(e){if(this.hasMove||(this.danmu.emit("bullet_start",this),this.hasMove=!0),("forcedPause"!==this.status||e)&&this.el&&"start"!==this.status)if(this.status="start",this.willChanges=["transform","transition"],this.willChange(),(0,l.styleUtil)(this.el,"backface-visibility","hidden"),(0,l.styleUtil)(this.el,"perspective","500em"),"scroll"===this.mode){var t=this.danmu.containerPos;if("b2t"===this.direction){var n=(this.el.getBoundingClientRect().bottom-t.top)/this.moveV;(0,l.styleUtil)(this.el,"transition","transform "+n+"s linear 0s"),(0,l.styleUtil)(this.el,"transform","translateX(-"+this.top+"px) translateY(-"+this.height+"px) translateZ(0px) rotate(90deg)"),this.moveTime=(new Date).getTime(),this.moveMoreS=this.el.getBoundingClientRect().top-t.top,this.moveContainerHeight=t.height}else{if(!this.el)return;var r=this.el.getBoundingClientRect(),i=r.right-t.left,a=i/this.moveV;r.right>t.left?((0,l.styleUtil)(this.el,"transition","transform "+a+"s linear 0s"),(0,l.styleUtil)(this.el,"transform","translateX(-"+i+"px) translateY(0px) translateZ(0px)"),this.moveTime=(new Date).getTime(),this.moveMoreS=r.left-t.left,this.moveContainerWidth=t.width):(this.status="end",this.remove())}}else{var o=(new Date).getTime(),s=(this.startTime&&o-this.startTime>this.duration?o-this.startTime:this.duration)/1e3;(0,l.styleUtil)(this.el,"left","50%"),(0,l.styleUtil)(this.el,"margin","0 0 0 -"+this.width/2+"px"),(0,l.styleUtil)(this.el,"visibility","hidden"),(0,l.styleUtil)(this.el,"transition","visibility "+s+"s 0s"),this.pastDuration||(this.pastDuration=1),this.startTime=o}}},{key:"remove",value:function(){this.logger&&this.logger.info("remove #"+(this.options.txt||"[DOM Element]")+"#"),this.pauseMove(),this.el&&this.el.parentNode&&(this.willChanges=[],this.willChange(),this.detach(),this.options.el&&1===this.options.el.nodeType&&this.danmu.config.disableCopyDOM&&(0,l.styleUtil)(this.options.el,"transform","none"),this.danmu.emit("bullet_remove",{bullet:this}))}},{key:"setFontSize",value:function(e){this.el&&(this.el.style.fontSize=e)}},{key:"setLikeDom",value:function(e,t){if(e&&(Object.keys(t).forEach((function(n){e.style[n]=t[n]})),e.className="danmu-like",this.el)){var n=this.el.querySelector(".danmu-like");n&&this.el.removeChild(n),this.el.innerHTML=""+this.el.innerHTML+e.outerHTML}return e}},{key:"moveV",get:function(){var e=this._moveV;if(!e){if(this.options.moveV)e=this.options.moveV;else if(this.elPos){var t=this.danmu.containerPos;e=("b2t"===this.direction?t.height+this.height:t.width+this.width)/this.duration*1e3}e&&(e*=this.danmu.main.playRate,this._moveV=e)}return e}}]),t}(s.default);t.default=c},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r,i=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=n(1),s=(r=o)&&r.__esModule?r:{default:r},l=n(0),u=n(31),c=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=("undefined"===typeof t?"undefined":a(t))&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this)),r=n;return r.setLogger("channel"),r.danmu=e,r.reset(!0),r.direction=e.direction,r.channels=[],r.updatePos(),(0,l.attachEventListener)(n.danmu,"bullet_remove",(function(e){r.removeBullet(e.bullet)}),"destroy"),(0,l.attachEventListener)(n.danmu,"changeDirection",(function(e){r.direction=e}),"destroy"),(0,l.attachEventListener)(n.danmu,"channel_resize",(function(){r.resize()}),"destroy"),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+("undefined"===typeof t?"undefined":a(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),i(t,[{key:"updatePos",value:function(){var e=this.container.getBoundingClientRect();this.containerPos=e,this.containerWidth=e.width,this.containerHeight=e.height,this.containerTop=e.top,this.containerBottom=e.bottom,this.containerLeft=e.left,this.containerRight=e.right}},{key:"destroy",value:function(){for(var e in this.logger&&this.logger.info("destroy"),this.channels.splice(0,this.channels.length),this._cancelResizeTimer(),this)l.hasOwnProperty.call(this,e)&&delete this[e]}},{key:"addBullet",value:function(e){var t=this,n=this.danmu,r=this.channels,i=void 0,a=void 0,o=void 0;if("b2t"===t.direction?(a=this.channelWidth,o=Math.ceil(e.width/a)):(i=this.channelHeight,o=Math.ceil(e.height/i)),o>r.length)return{result:!1,message:"exceed channels.length, occupy="+o+",channelsSize="+r.length};for(var s=!0,l=void 0,u=-1,c=0,d=r.length;c<d;c++)if(r[c].queue[e.mode].some((function(t){return t.id===e.id})))return{result:!1,message:"exited, channelOrder="+c+",danmu_id="+e.id};if("scroll"===e.mode)for(var p=0,f=r.length-o;p<=f;p++){s=!0;for(var h=p;h<p+o;h++){if((l=r[h]).operating.scroll){s=!1;break}if(l.bookId.scroll&&l.bookId.scroll!==e.id){s=!1;break}l.operating.scroll=!0;var g=l.queue.scroll[0];if(g){var y=g.el.getBoundingClientRect();if("b2t"===t.direction){if(y.bottom>=t.containerPos.bottom){s=!1,l.operating.scroll=!1;break}}else if(y.right>=t.containerPos.right){s=!1,l.operating.scroll=!1;break}var v=void 0,m=g.moveV,x=void 0,b=e.moveV,_=void 0;if("b2t"===t.direction?(x=(v=y.bottom-t.containerTop)/m,_=t.containerHeight+e.random-v):(x=(v=y.right-t.containerLeft)/m,_=t.containerWidth+e.random-v),b>m){var k=_/(b-m);if(n.config.bOffset||(n.config.bOffset=0),x+n.config.bOffset>=k){var E=x*b-t.containerPos.width;E>0&&e.updateOffset(E+(1+Math.ceil(5*Math.random())))}}}l.operating.scroll=!1}if(s){u=p;break}}else if("top"===e.mode)for(var w=0,T=r.length-o;w<=T;w++){s=!0;for(var S=w;S<w+o;S++){if(S>Math.floor(r.length/2)){s=!1;break}if((l=r[S]).operating[e.mode]){s=!1;break}if((l.bookId[e.mode]||e.prior)&&l.bookId[e.mode]!==e.id){s=!1;break}if(l.operating[e.mode]=!0,l.queue[e.mode].length>0){s=!1,l.operating[e.mode]=!1;break}l.operating[e.mode]=!1}if(s){u=w;break}}else if("bottom"===e.mode)for(var C=r.length-o;C>=0;C--){s=!0;for(var O=C;O<C+o;O++){if(O<=Math.floor(r.length/2)){s=!1;break}if((l=r[O]).operating[e.mode]){s=!1;break}if((l.bookId[e.mode]||e.prior)&&l.bookId[e.mode]!==e.id){s=!1;break}if(l.operating[e.mode]=!0,l.queue[e.mode].length>0){s=!1,l.operating[e.mode]=!1;break}l.operating[e.mode]=!1}if(s){u=C;break}}if(-1!==u){for(var A=u,R=u+o;A<R;A++)(l=r[A]).operating[e.mode]=!0,l.queue[e.mode].unshift(e),e.prior&&(delete l.bookId[e.mode],t.logger&&t.logger.info(A+"号轨道恢复正常使用")),l.operating[e.mode]=!1;return e.prior&&(t.logger&&t.logger.info(e.id+"号优先弹幕运行完毕"),delete e.bookChannelId,n.player&&n.bulletBtn.main.data.some((function(t){return t.id===e.id&&(delete t.bookChannelId,!0)}))),e.channel_id=[u,o],"b2t"===t.direction?(e.top=u*a,t.danmu.config.area&&t.danmu.config.area.start&&(e.top+=t.containerWidth*t.danmu.config.area.start)):(e.top=u*i,t.danmu.config.area&&t.danmu.config.area.start&&(e.top+=t.containerHeight*t.danmu.config.area.start)),{result:e,message:"success"}}if(e.options.realTime){var D=0,L=-1,P=null;if(t.danmu.bulletBtn.main.queue.forEach((function(e,n){!e.prior&&!e.options.realTime&&e.el&&e.el.getBoundingClientRect().left>t.containerPos.right&&e.start>=D&&(D=e.start,L=n,P=e)})),P){P.remove(),t.removeBullet(P),t.danmu.bulletBtn.main.queue.splice(L,1),e.channel_id=P.channel_id;for(var M=P.channel_id[0],I=P.channel_id[0]+P.channel_id[1];M<I;M++)(l=r[M]).operating[e.mode]=!0,l.queue[e.mode].unshift(e),e.prior&&delete l.bookId[e.mode],l.operating[e.mode]=!1;return e.top=P.top,t.danmu.config.area&&t.danmu.config.area.start&&(e.top+=t.containerHeight*t.danmu.config.area.start),{result:e,message:"success"}}}if(e.prior)if(e.bookChannelId||t.danmu.live)n.player&&n.bulletBtn.main.data.some((function(n){return n.id===e.id&&(t.logger&&t.logger.info(e.id+"号优先弹幕将于2秒后再次请求注册"),n.start+=2e3,!0)}));else{u=-1;for(var B=0,U=r.length-o;B<=U;B++){s=!0;for(var j=B;j<B+o;j++)if(r[j].bookId[e.mode]){s=!1;break}if(s){u=B;break}}if(-1!==u){for(var N=u;N<u+o;N++)r[N].bookId[e.mode]=e.id,t.logger&&t.logger.info(N+"号轨道被"+e.id+"号优先弹幕预定");n.player&&n.bulletBtn.main.data.some((function(n){return n.id===e.id&&(t.logger&&t.logger.info(e.id+"号优先弹幕将于2秒后再次请求注册"),n.start+=2e3,n.bookChannelId=[u,o],t.logger&&t.logger.info(e.id+"号优先弹幕预定了"+u+"~"+(u+o-1)+"号轨道"),!0)}))}}return{result:!1,message:"no surplus will right"}}},{key:"removeBullet",value:function(e){this.logger&&this.logger.info("removeBullet "+(e.options.txt||"[DOM Element]"));for(var t=this.channels,n=e.channel_id,r=void 0,i=n[0],a=n[0]+n[1];i<a;i++)if(r=t[i]){r.operating[e.mode]=!0;var o=-1;r.queue[e.mode].some((function(t,n){return t.id===e.id&&(o=n,!0)})),o>-1&&r.queue[e.mode].splice(o,1),r.operating[e.mode]=!1}e.options.loop&&this.danmu.bulletBtn.main.playedData.push(e.options)}},{key:"resizeSync",value:function(){this.resize(!0)}},{key:"resize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.logger&&this.logger.info("resize");var t=this;function n(){var e=t.danmu,n=e.container,r=e.config,i=e.bulletBtn,a=void 0;if(t.updatePos(),t._cancelResizeTimer(),i.main.data&&i.main.data.forEach((function(e){e.bookChannelId&&(delete e.bookChannelId,t.logger&&t.logger.info("resize导致"+e.id+"号优先弹幕预定取消"))})),t.logger&&t.logger.info("resize导致所有轨道恢复正常使用"),t.width=t.containerWidth,t.height=t.containerHeight,r.area){var o=r.area,s=o.lines,c=o.start,d=o.end;(0,u.validAreaLineRule)(s)?a=s:c>=0&&d>=c&&("b2t"===t.direction?t.width=t.width*(d-c):t.height=t.height*(d-c))}t.container=n;var p=r.channelSize||(/mobile/gi.test(navigator.userAgent)?10:12);(0,l.isNumber)(a)||(a="b2t"===t.direction?Math.floor(t.width/p):Math.floor(t.height/p));for(var f=[],h=0;h<a;h++)f[h]={id:h,queue:{scroll:[],top:[],bottom:[]},operating:{scroll:!1,top:!1,bottom:!1},bookId:{}};if(t.channels&&t.channels.length<=f.length){for(var g=function(e){f[e]={id:e,queue:{scroll:[],top:[],bottom:[]},operating:{scroll:!1,top:!1,bottom:!1},bookId:{}},["scroll","top"].forEach((function(n){t.channels[e].queue[n].forEach((function(t){t.el&&f[e].queue[n].push(t)}))})),t.channels[e].queue.bottom.forEach((function(n){if(n.el&&(f[e+f.length-t.channels.length].queue.bottom.push(n),n.channel_id[0]+n.channel_id[1]-1===e)){var r=[].concat(n.channel_id);n.channel_id=[r[0]-t.channels.length+f.length,r[1]],n.top=n.channel_id[0]*p,t.danmu.config.area&&t.danmu.config.area.start&&(n.top+=t.containerHeight*t.danmu.config.area.start),n.topInit()}}))},y=0;y<t.channels.length;y++)g(y);for(var v=function(e){["scroll","top","bottom"].forEach((function(t){f[e].queue[t].forEach((function(e){e.resized=!1}))}))},m=0;m<f.length;m++)v(m);t.channels=f,"b2t"===t.direction?t.channelWidth=p:t.channelHeight=p}else if(t.channels&&t.channels.length>f.length){for(var x=function(e){f[e]={id:e,queue:{scroll:[],top:[],bottom:[]},operating:{scroll:!1,top:!1,bottom:!1},bookId:{}},["scroll","top","bottom"].forEach((function(n){if("top"===n&&e>Math.floor(f.length/2));else if("bottom"===n&&e<=Math.floor(f.length/2));else{var r="bottom"===n?e-f.length+t.channels.length:e;t.channels[r].queue[n].forEach((function(i,a){if(i.el&&(f[e].queue[n].push(i),"bottom"===n&&i.channel_id[0]+i.channel_id[1]-1===r)){var o=[].concat(i.channel_id);i.channel_id=[o[0]-t.channels.length+f.length,o[1]],i.top=i.channel_id[0]*p,t.danmu.config.area&&t.danmu.config.area.start&&(i.top+=t.containerHeight*t.danmu.config.area.start),i.topInit()}t.channels[r].queue[n].splice(a,1)}))}}))},b=0;b<f.length;b++)x(b);for(var _=function(e){["scroll","top","bottom"].forEach((function(t){f[e].queue[t].forEach((function(e){e.resized=!1}))}))},k=0;k<f.length;k++)_(k);t.channels=f,"b2t"===t.direction?t.channelWidth=p:t.channelHeight=p}t.resizing=!1}t.resizing||(t.resizing=!0,e?n():(this._cancelResizeTimer(),this.resizeId=requestAnimationFrame(n)))}},{key:"_cancelResizeTimer",value:function(){this.resizeId&&(cancelAnimationFrame(this.resizeId),this.resizeId=null)}},{key:"reset",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.logger&&this.logger.info("reset");var t=this,n=t.danmu,r=n.container,i=n.bulletBtn,a=n.config;function o(){var e=void 0,n=r.getBoundingClientRect();if(t.width=n.width,t.height=n.height,t.resetId&&(cancelAnimationFrame(t.resetId),t.resetId=null),a.area){var i=a.area,o=i.lines,s=i.start,c=i.end;(0,u.validAreaLineRule)(o)?e=o:s>=0&&c>=s&&("b2t"===t.direction?t.width=t.width*(c-s):t.height=t.height*(c-s))}var d=a.channelSize||(/mobile/gi.test(navigator.userAgent)?10:12);(0,l.isNumber)(e)||(e="b2t"===t.direction?Math.floor(t.width/d):Math.floor(t.height/d));for(var p=[],f=0;f<e;f++)p[f]={id:f,queue:{scroll:[],top:[],bottom:[]},operating:{scroll:!1,top:!1,bottom:!1},bookId:{}};t.channels=p,"b2t"===t.direction?t.channelWidth=d:t.channelHeight=d}t.container=r,i&&i.main&&i.main.queue.forEach((function(e){e.remove()})),t.channels&&t.channels.length>0&&["scroll","top","bottom"].forEach((function(e){for(var n=0;n<t.channels.length;n++)t.channels[n].queue[e].forEach((function(e){e.remove()}))})),i&&i.main&&i.main.data&&i.main.data.forEach((function(e){e.attached_=!1})),e?this.resetId=requestAnimationFrame(o):o()}}]),t}(s.default);t.default=c,e.exports=t.default},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.validAreaLineRule=function(e){return"number"==typeof e&&e>=0&&Number.isInteger(e)}},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=n(0),a=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t={initDOM:function(){return document.createElement("div")},initSize:10},this.init(t)}return r(e,[{key:"init",value:function(e){this.idleList=[],this.usingList=[],this._id=0,this.options=e,this._expand(e.initSize)}},{key:"use",value:function(){this.idleList.length||this._expand(1);var e=this.idleList.shift();return this.usingList.push(e),e}},{key:"unused",value:function(e){var t=this.usingList.indexOf(e);t<0||(this.usingList.splice(t,1),e.innerHTML="",e.textcontent="",this.clearElementStyle(e),this.idleList.push(e))}},{key:"_expand",value:function(e){for(var t=0;t<e;t++)this.idleList.push(this.options.initDOM(this._id++))}},{key:"destroy",value:function(){for(var e=0;e<this.idleList.length;e++)this.idleList[e].innerHTML="",this.idleList[e].textcontent="",this.clearElementStyle(this.idleList[e]);for(var t=0;t<this.usingList.length;t++)this.usingList[t].innerHTML="",this.usingList[t].textcontent="",this.clearElementStyle(this.usingList[t]);for(var n in this)i.hasOwnProperty.call(this,n)&&delete this[n]}},{key:"clearElementStyle",value:function(e){var t="undefined"!=typeof window?window.navigator.userAgent:null;t&&(t.indexOf("MSIE ")>-1||t.indexOf("Trident/")>-1?(0,i.styleUtil)(e,"transform","none"):e.setAttribute("style",""))}}]),e}();t.default=a,e.exports=t.default},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.destroyObserver=t.unObserver=t.addObserver=void 0;var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=n(0),a=new(function(){function e(){var t=this;if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.__handlers=[],window.ResizeObserver)try{this.observer=new window.ResizeObserver((0,i.throttle)((function(e){t.__trigger(e)}),100))}catch(e){}}return r(e,[{key:"addObserver",value:function(e,t){if(this.observer){this.observer&&this.observer.observe(e);for(var n=this.__handlers,r=-1,i=0;i<n.length;i++)n[i]&&e===n[i].target&&(r=i);r>-1?this.__handlers[r].handler.push(t):this.__handlers.push({target:e,handler:[t]})}}},{key:"unObserver",value:function(e){var t=-1;this.__handlers.map((function(n,r){e===n.target&&(t=r)})),this.observer&&this.observer.unobserve(e),t>-1&&this.__handlers.splice(t,1)}},{key:"destroyObserver",value:function(){this.observer&&this.observer.disconnect(),this.observer=null,this.__handlers=null}},{key:"__runHandler",value:function(e){for(var t=this.__handlers,n=0;n<t.length;n++)if(t[n]&&e===t[n].target){t[n].handler&&t[n].handler.map((function(e){try{e()}catch(e){console.error(e)}}));break}}},{key:"__trigger",value:function(e){var t=this;e.map((function(e){t.__runHandler(e.target)}))}}]),e}());t.addObserver=function(e,t){a.addObserver(e,t)},t.unObserver=function(e,t){a.unObserver(e,t)},t.destroyObserver=function(e,t){a.destroyObserver(e,t)}},function(e,t,n){var r=n(35);"string"==typeof r&&(r=[[e.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};n(37)(r,i),r.locals&&(e.exports=r.locals)},function(e,t,n){(e.exports=n(36)(!1)).push([e.i,".danmu{overflow:hidden;-webkit-user-select:none;-moz-user-select:none;user-select:none;-ms-user-select:none}.danmu>*{position:absolute;white-space:nowrap}.danmu-switch{width:32px;height:20px;border-radius:100px;background-color:#ccc;-webkit-box-sizing:border-box;box-sizing:border-box;outline:none;cursor:pointer;position:relative;text-align:center;margin:10px auto}.danmu-switch.danmu-switch-active{padding-left:12px;background-color:#f85959}.danmu-switch span.txt{width:20px;height:20px;line-height:20px;text-align:center;display:block;border-radius:100px;background-color:#fff;-webkit-box-shadow:-2px 0 0 0 rgba(0, 0, 0, .04);box-shadow:-2px 0 0 0 rgba(0, 0, 0, .04);font-family:PingFangSC;font-size:10px;font-weight:500;color:#f44336}",""])},function(e,t){e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n=function(e,t){var n,r=e[1]||"",i=e[3];if(!i)return r;if(t&&"function"==typeof btoa){var a=(n=i,"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(n))))+" */"),o=i.sources.map((function(e){return"/*# sourceURL="+i.sourceRoot+e+" */"}));return[r].concat(o).concat([a]).join("\n")}return[r].join("\n")}(t,e);return t[2]?"@media "+t[2]+"{"+n+"}":n})).join("")},t.i=function(e,n){"string"==typeof e&&(e=[[null,e,""]]);for(var r={},i=0;i<this.length;i++){var a=this[i][0];"number"==typeof a&&(r[a]=!0)}for(i=0;i<e.length;i++){var o=e[i];"number"==typeof o[0]&&r[o[0]]||(n&&!o[2]?o[2]=n:n&&(o[2]="("+o[2]+") and ("+n+")"),t.push(o))}},t}},function(e,t,n){var r,i,o={},s=(r=function(){return window&&document&&document.all&&!window.atob},function(){return void 0===i&&(i=r.apply(this,arguments)),i}),l=function(e){return document.querySelector(e)},u=function(e){var t={};return function(e){if("function"==typeof e)return e();if(void 0===t[e]){var n=l.call(this,e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}}(),c=null,d=0,p=[],f=n(38);function h(e,t){for(var n=0;n<e.length;n++){var r=e[n],i=o[r.id];if(i){i.refs++;for(var a=0;a<i.parts.length;a++)i.parts[a](r.parts[a]);for(;a<r.parts.length;a++)i.parts.push(b(r.parts[a],t))}else{var s=[];for(a=0;a<r.parts.length;a++)s.push(b(r.parts[a],t));o[r.id]={id:r.id,refs:1,parts:s}}}}function g(e,t){for(var n=[],r={},i=0;i<e.length;i++){var a=e[i],o=t.base?a[0]+t.base:a[0],s={css:a[1],media:a[2],sourceMap:a[3]};r[o]?r[o].parts.push(s):n.push(r[o]={id:o,parts:[s]})}return n}function y(e,t){var n=u(e.insertInto);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var r=p[p.length-1];if("top"===e.insertAt)r?r.nextSibling?n.insertBefore(t,r.nextSibling):n.appendChild(t):n.insertBefore(t,n.firstChild),p.push(t);else if("bottom"===e.insertAt)n.appendChild(t);else{if("object"!=a(e.insertAt)||!e.insertAt.before)throw new Error("[Style Loader]\n\n Invalid value for parameter 'insertAt' ('options.insertAt') found.\n Must be 'top', 'bottom', or Object.\n (https://github.com/webpack-contrib/style-loader#insertat)\n");var i=u(e.insertInto+" "+e.insertAt.before);n.insertBefore(t,i)}}function v(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e);var t=p.indexOf(e);t>=0&&p.splice(t,1)}function m(e){var t=document.createElement("style");return void 0===e.attrs.type&&(e.attrs.type="text/css"),x(t,e.attrs),y(e,t),t}function x(e,t){Object.keys(t).forEach((function(n){e.setAttribute(n,t[n])}))}function b(e,t){var n,r,i,a;if(t.transform&&e.css){if(!(a=t.transform(e.css)))return function(){};e.css=a}if(t.singleton){var o=d++;n=c||(c=m(t)),r=E.bind(null,n,o,!1),i=E.bind(null,n,o,!0)}else e.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(n=function(e){var t=document.createElement("link");return void 0===e.attrs.type&&(e.attrs.type="text/css"),e.attrs.rel="stylesheet",x(t,e.attrs),y(e,t),t}(t),r=T.bind(null,n,t),i=function(){v(n),n.href&&URL.revokeObjectURL(n.href)}):(n=m(t),r=w.bind(null,n),i=function(){v(n)});return r(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;r(e=t)}else i()}}e.exports=function(e,t){if("undefined"!=typeof DEBUG&&DEBUG&&"object"!=("undefined"===typeof document?"undefined":a(document)))throw new Error("The style-loader cannot be used in a non-browser environment");(t=t||{}).attrs="object"==a(t.attrs)?t.attrs:{},t.singleton||"boolean"==typeof t.singleton||(t.singleton=s()),t.insertInto||(t.insertInto="head"),t.insertAt||(t.insertAt="bottom");var n=g(e,t);return h(n,t),function(e){for(var r=[],i=0;i<n.length;i++){var a=n[i];(s=o[a.id]).refs--,r.push(s)}for(e&&h(g(e,t),t),i=0;i<r.length;i++){var s;if(0===(s=r[i]).refs){for(var l=0;l<s.parts.length;l++)s.parts[l]();delete o[s.id]}}}};var _,k=(_=[],function(e,t){return _[e]=t,_.filter(Boolean).join("\n")});function E(e,t,n,r){var i=n?"":r.css;if(e.styleSheet)e.styleSheet.cssText=k(t,i);else{var a=document.createTextNode(i),o=e.childNodes;o[t]&&e.removeChild(o[t]),o.length?e.insertBefore(a,o[t]):e.appendChild(a)}}function w(e,t){var n=t.css,r=t.media;if(r&&e.setAttribute("media",r),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}function T(e,t,n){var r=n.css,i=n.sourceMap,a=void 0===t.convertToAbsoluteUrls&&i;(t.convertToAbsoluteUrls||a)&&(r=f(r)),i&&(r+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(i))))+" */");var o=new Blob([r],{type:"text/css"}),s=e.href;e.href=URL.createObjectURL(o),s&&URL.revokeObjectURL(s)}},function(e,t){e.exports=function(e){var t="undefined"!=typeof window&&window.location;if(!t)throw new Error("fixUrls requires window.location");if(!e||"string"!=typeof e)return e;var n=t.protocol+"//"+t.host,r=n+t.pathname.replace(/\/[^\/]*$/,"/");return e.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,(function(e,t){var i,a=t.trim().replace(/^"(.*)"$/,(function(e,t){return t})).replace(/^'(.*)'$/,(function(e,t){return t}));return/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(a)?e:(i=0===a.indexOf("//")?a:0===a.indexOf("/")?n+a:r+a.replace(/^\.\//,""),"url("+JSON.stringify(i)+")")}))}}])}))}).call(this,n(135)(e))},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t,n){"use strict";n.r(t),t["default"]='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" width="40" height="40">\n  <path fill="#f85959" transform="scale(0.8 0.8)" d="M36.5,18.73a1.19,1.19,0,0,0,1-1.14V16.33a1.2,1.2,0,0,0-1-1.13l-.61-.08a1.75,1.75,0,0,1-1.3-.86l-.21-.36-.2-.36A1.72,1.72,0,0,1,34,12l.23-.58a1.18,1.18,0,0,0-.5-1.42l-1.1-.62a1.18,1.18,0,0,0-1.47.3l-.39.51a1.82,1.82,0,0,1-1.41.72c-.44,0-1.88-.27-2.22-.7l-.39-.49a1.18,1.18,0,0,0-1.48-.28l-1.09.64a1.19,1.19,0,0,0-.47,1.43l.25.59a1.87,1.87,0,0,1-.08,1.58c-.26.37-1.17,1.5-1.71,1.58l-.63.09a1.19,1.19,0,0,0-1,1.14l0,1.27a1.17,1.17,0,0,0,1,1.12l.61.08a1.74,1.74,0,0,1,1.3.87l.21.36.2.35A1.69,1.69,0,0,1,24,22.08l-.23.59a1.19,1.19,0,0,0,.5,1.42l1.1.62a1.19,1.19,0,0,0,1.48-.31l.38-.5a1.83,1.83,0,0,1,1.41-.72c.44,0,1.88.25,2.22.69l.39.49a1.18,1.18,0,0,0,1.48.28L33.86,24a1.19,1.19,0,0,0,.47-1.43L34.09,22a1.84,1.84,0,0,1,.07-1.58c.26-.37,1.17-1.5,1.72-1.58ZM31,18.94a2.76,2.76,0,0,1-4.65-1.2A2.71,2.71,0,0,1,27,15.13a2.76,2.76,0,0,1,4.64,1.2A2.7,2.7,0,0,1,31,18.94Z"/>\n  <path fill="#f85959" transform="scale(0.8 0.8)" d="M32,0H3.59A3.59,3.59,0,0,0,0,3.59v17A3.59,3.59,0,0,0,3.59,24.2H19.72a12.59,12.59,0,0,1-.81-1.2A11.73,11.73,0,0,1,35.54,7.28V3.59A3.59,3.59,0,0,0,32,0ZM13,14.18H4.29a1.52,1.52,0,0,1,0-3H13a1.52,1.52,0,0,1,0,3ZM16.45,8H4.29a1.51,1.51,0,0,1,0-3H16.45a1.51,1.51,0,1,1,0,3Z"/>\n</svg>\n'},function(e,t,n){var r,i=n(138);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .danmu-switch{-webkit-order:6;-moz-box-ordinal-group:7;order:6;z-index:26}.xgplayer-skin-default .xgplayer-danmu{display:none;position:absolute;top:0;left:0;right:0;height:100%;overflow:hidden;z-index:9;outline:none;pointer-events:none}.xgplayer-skin-default .xgplayer-danmu>*{position:absolute;white-space:nowrap;z-index:9;pointer-events:auto}.xgplayer-skin-default .xgplayer-danmu.xgplayer-has-danmu{display:block}.xgplayer-skin-default .xgplayer-panel{outline:none;-webkit-order:7;-moz-box-ordinal-group:8;order:7;width:40px;height:40px;display:inline-block;position:relative;font-family:PingFangSC-Regular;font-size:13px;color:hsla(0,0%,100%,.8);z-index:36}.xgplayer-skin-default .xgplayer-panel .xgplayer-panel-icon{cursor:pointer;position:absolute;margin-left:5px;top:10px}.xgplayer-skin-default .xgplayer-panel-active{display:block!important;bottom:30px}.xgplayer-skin-default .xgplayer-panel-slider{z-index:36;display:none;position:absolute;width:230px;height:230px;background:rgba(0,0,0,.54);border-radius:1px;padding:10px 20px;outline:none;left:-115px;bottom:40px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-hidemode{padding-bottom:10px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-hidemode-radio li{display:inline;list-style:none;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-hidemode ul{display:-webkit-flex;display:-moz-box;display:flex;-webkit-justify-content:space-around;justify-content:space-around}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-hidemode li{margin:0 12px;font-size:11px;color:#aaa}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-hidemode-font{margin-bottom:10px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-transparency{display:block;margin-top:10px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-transparency .xgplayer-transparency-line{-webkit-appearance:none;-moz-appearance:none;appearance:none;cursor:pointer;outline:none;width:150px;height:4px;background:#aaa;border-radius:4px;border-style:none;margin-left:10px;margin-top:-2px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-transparency .xgplayer-transparency-line::-moz-focus-outer{border:0!important}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-transparency .xgplayer-transparency-color::-webkit-slider-runnable-track{outline:none;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-transparency .xgplayer-transparency-color::-moz-range-track{outline:none;background-color:#aaa;border-color:transparent;cursor:pointer;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-transparency .xgplayer-transparency-color::-ms-track{outline:none;background-color:#aaa;color:transparent;border-color:transparent;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-transparency .xgplayer-transparency-bar::-webkit-slider-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:6px;width:6px;margin-top:-4px;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-transparency .xgplayer-transparency-bar::-moz-range-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:0;width:0;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-transparency .xgplayer-transparency-bar::-ms-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:6px;width:6px;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-transparency .xgplayer-transparency-bar::-moz-range-progress{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;height:4px;border-radius:4px;background:linear-gradient(90deg,#f85959,#f85959 100%,#aaa)}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea{display:block;margin-top:8px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea-name{display:inline-block;position:relative;top:-10px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea-control{display:inline-block}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea-control-up{width:150px;margin-left:10px;display:-moz-box;display:-webkit-flex;display:flex;-webkit-justify-content:space-between;-moz-box-pack:justify;justify-content:space-between;color:#aaa}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea-control-down{position:relative;top:-10px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea-control-down-dots{display:-webkit-flex;display:-moz-box;display:flex;width:150px;margin-left:10px;-webkit-justify-content:space-between;-moz-box-pack:justify;justify-content:space-between}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea-threequarters,.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea-twoquarters{margin-left:-6px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea-full{margin-right:3px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-line{-webkit-appearance:none;-moz-appearance:none;appearance:none;cursor:pointer;outline:none;width:150px;height:4px;background:#aaa;border-radius:4px;border-style:none;margin-left:10px;margin-top:-2px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-line::-moz-focus-outer{border:0!important}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-color::-webkit-slider-runnable-track{outline:none;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-color::-moz-range-track{outline:none;background-color:#aaa;border-color:transparent;cursor:pointer;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-color::-ms-track{outline:none;background-color:#aaa;color:transparent;border-color:transparent;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-bar::-webkit-slider-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:6px;width:6px;margin-top:-4px;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-bar::-moz-range-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:0;width:0;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-bar::-ms-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:6px;width:6px;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-full-dot,.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-onequarters-dot,.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-threequarters-dot,.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-twoquarters-dot,.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-zero-dot{width:3px;height:3px;border:3px solid #aaa;border-radius:50%;background-color:#aaa;position:relative;top:16px;z-index:-1}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed{display:block}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed-name{display:inline-block;position:relative;top:-10px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed-control{display:inline-block}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed-control-up{width:150px;margin-left:10px;display:-moz-box;display:-webkit-flex;display:flex;-webkit-justify-content:space-between;-moz-box-pack:justify;justify-content:space-between;color:#aaa}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed-control-down{position:relative;top:-10px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed-control-down-dots{display:-webkit-flex;display:-moz-box;display:flex;width:150px;margin-left:10px;-webkit-justify-content:space-between;-moz-box-pack:justify;justify-content:space-between}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-line{-webkit-appearance:none;-moz-appearance:none;appearance:none;cursor:pointer;outline:none;width:150px;height:4px;background:#aaa;border-radius:4px;border-style:none;margin-left:10px;margin-top:-2px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-line::-moz-focus-outer{border:0!important}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-color::-webkit-slider-runnable-track{outline:none;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-color::-moz-range-track{outline:none;background-color:#aaa;border-color:transparent;cursor:pointer;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-color::-ms-track{outline:none;background-color:#aaa;color:transparent;border-color:transparent;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-bar::-webkit-slider-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:6px;width:6px;margin-top:-4px;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-bar::-moz-range-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:0;width:0;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-bar::-ms-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:6px;width:6px;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-large-dot,.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-middle-dot,.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-small-dot{width:3px;height:3px;border:3px solid #aaa;border-radius:50%;background-color:#aaa;position:relative;top:16px;z-index:-1}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont{display:block}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont-name{display:inline-block;position:relative;top:-10px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont-control{display:inline-block}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont-control-up{width:150px;margin-left:10px;display:-moz-box;display:-webkit-flex;display:flex;-webkit-justify-content:space-between;-moz-box-pack:justify;justify-content:space-between;color:#aaa}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont-control-down{position:relative;top:-10px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont-control-down-dots{display:-webkit-flex;display:-moz-box;display:flex;width:150px;margin-left:10px;-webkit-justify-content:space-between;-moz-box-pack:justify;justify-content:space-between}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-line{-webkit-appearance:none;-moz-appearance:none;appearance:none;cursor:pointer;outline:none;width:150px;height:4px;background:#aaa;border-radius:4px;border-style:none;margin-left:10px;margin-top:-2px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-line::-moz-focus-outer{border:0!important}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-color::-webkit-slider-runnable-track{outline:none;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-color::-moz-range-track{outline:none;background-color:#aaa;border-color:transparent;cursor:pointer;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-color::-ms-track{outline:none;background-color:#aaa;color:transparent;border-color:transparent;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-bar::-webkit-slider-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:6px;width:6px;margin-top:-4px;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-bar::-moz-range-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:0;width:0;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-bar::-ms-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:6px;width:6px;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-large-dot,.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-middle-dot,.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-small-dot{width:3px;height:3px;border:3px solid #aaa;border-radius:50%;background-color:#aaa;position:relative;top:16px;z-index:-1}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0);n(140);var i=function(){var e=this;if(e.config.pip&&"function"===typeof e.video.requestPictureInPicture){var t=e.lang.PIP,n=(0,r.createDom)("xg-pip",'<p class="name"><span>'+t+"</span></p>",{tabindex:9},"xgplayer-pip");e.once("ready",(function(){e.controls.appendChild(n)})),["click","touchend"].forEach((function(t){n.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("pipBtnClick")}))}))}};t.default={name:"s_pip",method:i},e.exports=t["default"]},function(e,t,n){var r,i=n(141);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-pip{-webkit-order:9;-moz-box-ordinal-group:10;order:9;position:relative;outline:none;display:block;cursor:pointer;height:20px;top:10px}.xgplayer-skin-default .xgplayer-pip .name{text-align:center;font-family:PingFangSC-Regular;font-size:13px;line-height:20px;height:20px;color:hsla(0,0%,100%,.8)}.xgplayer-skin-default .xgplayer-pip .name span{width:60px;height:20px;line-height:20px;background:rgba(0,0,0,.38);border-radius:10px;display:inline-block;vertical-align:middle}.lang-is-jp .xgplayer-pip .name span{width:70px;height:20px}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0);n(143);var i=function(){var e=this;if(e.config.miniplayer){var t=e.lang.MINIPLAYER,n=(0,r.createDom)("xg-miniplayer",'<p class="name"><span>'+t+"</span></p>",{tabindex:9},"xgplayer-miniplayer");e.once("ready",(function(){e.controls.appendChild(n)})),["click","touchend"].forEach((function(t){n.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("miniplayerBtnClick")}))}))}};t.default={name:"s_miniplayer",method:i},e.exports=t["default"]},function(e,t,n){var r,i=n(144);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-miniplayer{-webkit-order:9;-moz-box-ordinal-group:10;order:9;position:relative;outline:none;display:block;cursor:pointer;height:20px;top:10px}.xgplayer-skin-default .xgplayer-miniplayer .name{text-align:center;font-family:PingFangSC-Regular;font-size:13px;line-height:20px;height:20px;color:hsla(0,0%,100%,.8)}.xgplayer-skin-default .xgplayer-miniplayer .name span{width:80px;height:20px;line-height:20px;background:rgba(0,0,0,.38);border-radius:10px;display:inline-block;vertical-align:middle}.xgplayer-skin-default .xgplayer-miniplayer-lay{position:absolute;top:26px;left:0;width:100%;height:100%;z-index:130;cursor:pointer;background-color:transparent;display:none}.xgplayer-skin-default .xgplayer-miniplayer-lay div{width:100%;height:100%}.xgplayer-skin-default .xgplayer-miniplayer-drag{cursor:move;position:absolute;top:0;left:0;width:100%;height:26px;line-height:26px;background-image:linear-gradient(rgba(0,0,0,.3),transparent);z-index:130;display:none}.xgplayer-skin-default .xgplayer-miniplayer-drag .drag-handle{width:100%}.xgplayer-skin-default.xgplayer-miniplayer-active{position:fixed;right:0;bottom:200px;width:320px;height:180px;z-index:110}.xgplayer-skin-default.xgplayer-miniplayer-active .xgplayer-controls,.xgplayer-skin-default.xgplayer-miniplayer-active .xgplayer-danmu{display:none}.xgplayer-skin-default.xgplayer-miniplayer-active .xgplayer-miniplayer-lay{display:block}.xgplayer-skin-default.xgplayer-miniplayer-active .xgplayer-miniplayer-drag{display:-webkit-flex;display:-moz-box;display:flex}.xgplayer-skin-default.xgplayer-inactive .xgplayer-miniplayer-drag{display:none}.lang-is-jp .xgplayer-miniplayer .name span{width:70px;height:20px}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(146),a=o(i);function o(e){return e&&e.__esModule?e:{default:e}}n(147);var s=function(){var e=this,t=e.config.playNext;if(t&&t.urlList){var n=(0,r.createDom)("xg-playnext",'<xg-icon class="xgplayer-icon">'+a.default+"</xg-icon>",{},"xgplayer-playnext"),i=e.lang.PLAYNEXT_TIPS,o=(0,r.createDom)("xg-tips",'<span class="xgplayer-tip-playnext">'+i+"</span>",{},"xgplayer-tips");n.appendChild(o),e.once("ready",(function(){e.controls.appendChild(n)})),["click","touchend"].forEach((function(t){n.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation(),(0,r.addClass)(e.root,"xgplayer-is-enter"),e.userGestureTrigEvent("playNextBtnClick")}))}));var s=function(){(0,r.addClass)(e.root,"xgplayer-playnext-inactive")};e.on("urlListEnd",s),e.once("destroy",l)}function l(){e.off("urlListEnd",s),e.off("destroy",l)}};t.default={name:"s_playNext",method:s},e.exports=t["default"]},function(e,t,n){"use strict";n.r(t),t["default"]='<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">\n  <path transform="scale(0.038 0.028)" d="M800 380v768h-128v-352l-320 320v-704l320 320v-352z"></path>\n</svg>\n'},function(e,t,n){var r,i=n(148);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-playnext{position:relative;-webkit-order:1;-moz-box-ordinal-group:2;order:1;display:block;cursor:pointer;top:-2px}.xgplayer-skin-default .xgplayer-playnext .xgplayer-icon div{position:absolute}.xgplayer-skin-default .xgplayer-playnext .xgplayer-tips .xgplayer-tip-playnext{display:block}.xgplayer-skin-default .xgplayer-playnext:hover{opacity:.85}.xgplayer-skin-default .xgplayer-playnext:hover .xgplayer-tips{display:block}.xgplayer-lang-is-en .xgplayer-playnext .xgplayer-tips{margin-left:-25px}.xgplayer-lang-is-jp .xgplayer-playnext .xgplayer-tips{margin-left:-38px}.xgplayer-skin-default.xgplayer-playnext-inactive .xgplayer-playnext{display:none}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(150),a=o(i);function o(e){return e&&e.__esModule?e:{default:e}}n(151);var s=function(){var e=this;if(e.config.rotate){var t=(0,r.createDom)("xg-rotate",'<xg-icon class="xgplayer-icon">'+a.default+"</xg-icon>",{},"xgplayer-rotate"),n=e.lang.ROTATE_TIPS,i=(0,r.createDom)("xg-tips",'<span class="xgplayer-tip-rotate">'+n+"</span>",{},"xgplayer-tips");t.appendChild(i),e.once("ready",(function(){e.controls.appendChild(t)})),["click","touchend"].forEach((function(n){t.addEventListener(n,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("rotateBtnClick")}))}))}};t.default={name:"s_rotate",method:s},e.exports=t["default"]},function(e,t,n){"use strict";n.r(t),t["default"]='<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 40 40" fill="none">\n  <g clip-path="url(#clip0)">\n    <path transform="scale(1.5 1.5)" d="M11.6665 9.16663H4.1665C2.78579 9.16663 1.6665 10.2859 1.6665 11.6666V15.8333C1.6665 17.214 2.78579 18.3333 4.1665 18.3333H11.6665C13.0472 18.3333 14.1665 17.214 14.1665 15.8333V11.6666C14.1665 10.2859 13.0472 9.16663 11.6665 9.16663Z" fill="white"/>\n    <path transform="scale(1.5 1.5)" fill-rule="evenodd" clip-rule="evenodd" d="M3.88148 4.06298C3.75371 4.21005 3.67667 4.40231 3.67749 4.61242C3.67847 4.87253 3.79852 5.10435 3.98581 5.25646L6.99111 8.05895C7.32771 8.37283 7.85502 8.35443 8.16891 8.01782C8.48279 7.68122 8.46437 7.15391 8.12778 6.84003L6.62061 5.43457L9.8198 5.4224C9.82848 5.42239 9.8372 5.42221 9.84591 5.4219C10.9714 5.38233 12.0885 5.6285 13.0931 6.13744C14.0976 6.64635 14.957 7.40148 15.5908 8.33234C16.2246 9.2632 16.6122 10.3394 16.7177 11.4606C16.823 12.5819 16.6427 13.7115 16.1934 14.7442C16.0098 15.1661 16.203 15.6571 16.6251 15.8408C17.0471 16.0243 17.5381 15.8311 17.7216 15.4091C18.2833 14.1183 18.5087 12.7063 18.3771 11.3047C18.2453 9.90318 17.7607 8.55792 16.9684 7.39433C16.1761 6.23073 15.1021 5.28683 13.8463 4.65065C12.5946 4.01651 11.203 3.70872 9.80072 3.75583L6.43415 3.76862L7.96326 2.12885C8.27715 1.79225 8.25872 1.26494 7.92213 0.951061C7.58553 0.63718 7.05822 0.655585 6.74433 0.99219L3.90268 4.0395C3.89545 4.04724 3.88841 4.05509 3.88154 4.06303L3.88148 4.06298Z" fill="white"/>\n  </g>\n  <defs>\n    <clipPath id="clip0">\n      <rect width="40" height="40" fill="white"/>\n    </clipPath>\n  </defs>\n</svg>\n'},function(e,t,n){var r,i=n(152);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-rotate{position:relative;-webkit-order:10;-moz-box-ordinal-group:11;order:10;display:block;cursor:pointer}.xgplayer-skin-default .xgplayer-rotate .xgplayer-icon{margin-top:7px;width:26px}.xgplayer-skin-default .xgplayer-rotate .xgplayer-icon div{position:absolute}.xgplayer-skin-default .xgplayer-rotate .xgplayer-tips{margin-left:-22px}.xgplayer-skin-default .xgplayer-rotate .xgplayer-tips .xgplayer-tip-rotate{display:block}.xgplayer-skin-default .xgplayer-rotate:hover{opacity:.85}.xgplayer-skin-default .xgplayer-rotate:hover .xgplayer-tips{display:block}.xgplayer-lang-is-en .xgplayer-rotate .xgplayer-tips{margin-left:-26px}.xgplayer-lang-is-jp .xgplayer-rotate .xgplayer-tips{margin-left:-38px}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(154),a=o(i);function o(e){return e&&e.__esModule?e:{default:e}}n(155);var s=function(){var e=this;if(e.config.reload){var t=(0,r.createDom)("xg-reload",'<xg-icon class="xgplayer-icon">'+a.default+"</xg-icon>",{},"xgplayer-reload"),n=e.lang.RELOAD_TIPS,i=(0,r.createDom)("xg-tips",'<span class="xgplayer-tip-reload">'+n+"</span>",{},"xgplayer-tips");t.appendChild(i),e.once("ready",(function(){e.controls.appendChild(t)})),["click","touchend"].forEach((function(n){t.addEventListener(n,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("reloadBtnClick")}))}))}};t.default={name:"s_reload",method:s},e.exports=t["default"]},function(e,t,n){"use strict";n.r(t),t["default"]='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">\n    <path fill="#FFF" fill-opacity="1" fill-rule="nonzero" d="M18.17 19.988a7.182 7.182 0 0 1-4.256 1.318 7.806 7.806 0 0 1-.595-.03c-.08-.008-.16-.021-.242-.031a8.004 8.004 0 0 1-.458-.071c-.094-.018-.185-.042-.276-.063a7.743 7.743 0 0 1-.439-.113c-.068-.022-.136-.047-.205-.07a7.03 7.03 0 0 1-.492-.181c-.037-.015-.072-.032-.108-.049a7.295 7.295 0 0 1-.554-.269l-.025-.012a7.347 7.347 0 0 1-2.111-1.753c-.03-.036-.057-.074-.086-.11a7.305 7.305 0 0 1-1.594-4.557h1.686a.123.123 0 0 0 .108-.064.119.119 0 0 0-.006-.125L5.684 9.532a.123.123 0 0 0-.103-.056.123.123 0 0 0-.102.056l-2.834 4.276a.121.121 0 0 0-.005.125c.022.04.064.064.107.064h1.687c0 2.025.627 3.902 1.693 5.454.013.021.022.044.037.066.11.159.233.305.352.455.043.057.085.116.13.171.175.213.36.413.55.61.02.018.036.038.054.055a9.447 9.447 0 0 0 2.91 1.996c.058.026.115.054.175.079.202.084.41.158.619.228.098.034.196.069.296.1.183.054.37.1.558.145.125.029.249.06.376.085.052.01.102.027.155.035.177.032.355.05.533.071.064.007.128.018.19.026.32.03.639.052.956.052a9.46 9.46 0 0 0 5.47-1.746 1.16 1.16 0 0 0 .282-1.608 1.143 1.143 0 0 0-1.6-.283zm5.397-5.991a9.604 9.604 0 0 0-1.685-5.441c-.016-.027-.026-.054-.043-.078-.132-.19-.276-.366-.419-.543-.017-.022-.032-.044-.05-.065a9.467 9.467 0 0 0-3.571-2.7l-.114-.051a11.2 11.2 0 0 0-.673-.248c-.082-.027-.163-.057-.247-.082a9.188 9.188 0 0 0-.6-.156c-.113-.026-.224-.055-.337-.077-.057-.011-.109-.028-.164-.037-.151-.027-.304-.039-.455-.058-.104-.013-.208-.03-.313-.04a10.05 10.05 0 0 0-.759-.039c-.045 0-.09-.007-.136-.007l-.025.003a9.45 9.45 0 0 0-5.46 1.737 1.16 1.16 0 0 0-.284 1.608c.363.523 1.08.65 1.6.284a7.182 7.182 0 0 1 4.222-1.32c.217.002.429.013.639.033.065.007.129.017.193.025.173.021.344.046.513.08.075.014.149.033.221.05.166.037.331.077.494.127l.152.051c.185.061.366.127.545.201l.054.025a7.308 7.308 0 0 1 2.741 2.067l.013.018a7.302 7.302 0 0 1 1.652 4.633h-1.686a.123.123 0 0 0-.108.064.12.12 0 0 0 .006.124l2.834 4.277c.022.033.06.054.103.054.042 0 .08-.021.102-.054l2.833-4.277a.12.12 0 0 0 .005-.124.123.123 0 0 0-.108-.064h-1.685z"/>\n</svg>\n'},function(e,t,n){var r,i=n(156);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-reload{position:relative;-webkit-order:1;-moz-box-ordinal-group:2;order:1;display:block;width:40px;height:40px;cursor:pointer}.xgplayer-skin-default .xgplayer-reload .xgplayer-icon{margin-top:7px;width:26px}.xgplayer-skin-default .xgplayer-reload .xgplayer-icon div{position:absolute}.xgplayer-skin-default .xgplayer-reload .xgplayer-tips{margin-left:-22px}.xgplayer-skin-default .xgplayer-reload .xgplayer-tips .xgplayer-tip-reload{display:block}.xgplayer-skin-default .xgplayer-reload:hover{opacity:.85}.xgplayer-skin-default .xgplayer-reload:hover .xgplayer-tips{display:block}.xgplayer-lang-is-en .xgplayer-reload .xgplayer-tips{margin-left:-26px}.xgplayer-lang-is-jp .xgplayer-reload .xgplayer-tips{margin-left:-38px}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0);n(158);var i=function(){var e=this;if(e.config.screenShot&&!e.config.screenShot.hideButton){var t=e.lang.SCREENSHOT,n=(0,r.createDom)("xg-screenshot",'<p class="name"><span>'+(e.config.screenShot.iconText||t)+"</span></p>",{tabindex:11},"xgplayer-screenshot");e.once("ready",(function(){e.controls.appendChild(n)})),["click","touchend"].forEach((function(t){n.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("screenShotBtnClick")}))}))}};t.default={name:"s_screenShot",method:i},e.exports=t["default"]},function(e,t,n){var r,i=n(159);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-screenshot{-webkit-order:11;-moz-box-ordinal-group:12;order:11;position:relative;outline:none;display:block;cursor:pointer;height:20px;top:10px}.xgplayer-skin-default .xgplayer-screenshot .name{text-align:center;font-family:PingFangSC-Regular;font-size:13px;line-height:20px;height:20px;color:hsla(0,0%,100%,.8)}.xgplayer-skin-default .xgplayer-screenshot .name span{width:-webkit-fit-content;width:-moz-fit-content;width:fit-content;padding:0 10px;height:20px;line-height:20px;background:rgba(0,0,0,.38);border-radius:10px;display:inline-block;vertical-align:middle}.xgplayer-lang-is-en .xgplayer-screenshot .name span,.xgplayer-lang-is-jp .xgplayer-screenshot .name span{width:75px;height:20px}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(9),i=a(r);function a(e){return e&&e.__esModule?e:{default:e}}n(78);var o=function(){if(this.config.nativeTextTrack){var e=this,t=e.root,n=i.default.util,r=n.createDom("xg-texttrack","",{tabindex:7},"xgplayer-texttrack"),a=e.config.nativeTextTrack;a&&Array.isArray(a)&&a.length>0&&(n.addClass(e.root,"xgplayer-is-texttrack"),e.once("canplay",(function(){var i=this,o=["<ul>"];o.push("<li class='"+(this.textTrackShowDefault?"":"selected")+"'}'>"+e.lang.OFF+"</li>"),a.forEach((function(e){o.push("<li class='"+(e.default&&i.textTrackShowDefault?"selected":"")+"'>"+e.label+"</li>")}));var s=e.lang.TEXTTRACK;o.push('</ul><p class="name">'+s+"</p>");var l=t.querySelector(".xgplayer-texttrack");if(l){l.innerHTML=o.join("");var u=l.querySelector(".name");e.config.textTrackActive&&"hover"!==e.config.textTrackActive||u.addEventListener("mouseenter",(function(e){e.preventDefault(),e.stopPropagation(),n.addClass(t,"xgplayer-texttrack-active"),l.focus()}))}else{r.innerHTML=o.join("");var c=r.querySelector(".name");e.config.textTrackActive&&"hover"!==e.config.textTrackActive||c.addEventListener("mouseenter",(function(t){t.preventDefault(),t.stopPropagation(),n.addClass(e.root,"xgplayer-texttrack-active"),r.focus()})),e.controls.appendChild(r)}}))),["touchend","click"].forEach((function(t){r.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation();var i=t.target||t.srcElement;if(i&&"li"===i.tagName.toLocaleLowerCase()){Array.prototype.forEach.call(i.parentNode.childNodes,(function(e){n.removeClass(e,"selected")})),n.addClass(i,"selected");var o=e.root.getElementsByTagName("Track");i.innerHTML===e.lang.OFF?(o[0].track.mode="hidden",o[0].src="",n.removeClass(e.root,"xgplayer-texttrack-active")):(o[0].style.display="block",n.addClass(e.root,"xgplayer-texttrack-active"),o[0].track.mode="showing",a.some((function(e){if(e.label===i.innerHTML)return o[0].src=e.src,e.kind&&(o[0].kind=e.kind),o[0].label=e.label,e.srclang&&(o[0].srclang=e.srclang),!0})),e.emit("textTrackChange",i.innerHTML))}else"click"!==e.config.textTrackActive||!i||"p"!==i.tagName.toLocaleLowerCase()&&"em"!==i.tagName.toLocaleLowerCase()||(n.addClass(e.root,"xgplayer-texttrack-active"),r.focus())}),!1)})),e.on("play",(function(){var r=t.querySelector(".xgplayer-texttrack ul"),i=t.getElementsByTagName("Track");e["hls"]&&r&&i&&(i[0].src="",Array.prototype.forEach.call(r.childNodes,(function(t){n.hasClass(t,"selected")&&(t.innerHTML===e.lang.OFF?(i[0].track.mode="hidden",i[0].src=""):(i[0].track.mode="hidden",a.some((function(e){if(e.label!==t.innerHTML)return i[0].src=e.src,e.kind&&(i[0].kind=e.kind),i[0].label=e.label,e.srclang&&(i[0].srclang=e.srclang),!0})),a.some((function(e){if(e.label===t.innerHTML)return setTimeout((function(){i[0].src=e.src,e.kind&&(i[0].kind=e.kind),i[0].label=e.label,e.srclang&&(i[0].srclang=e.srclang),i[0].track.mode="showing"})),!0}))))})),n.removeClass(e.root,"xgplayer-texttrack-active"))})),r.addEventListener("mouseleave",(function(t){t.preventDefault(),t.stopPropagation(),n.removeClass(e.root,"xgplayer-texttrack-active")}))}};t.default={name:"s_nativeTextTrack",method:o},e.exports=t["default"]},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-texttrack{-webkit-order:7;-moz-box-ordinal-group:8;order:7;width:60px;height:150px;z-index:18;position:relative;outline:none;display:none;cursor:default;margin-top:-119px}.xgplayer-skin-default .xgplayer-texttrack ul{display:none;list-style:none;min-width:78px;background:rgba(0,0,0,.54);border-radius:1px;position:absolute;bottom:30px;text-align:center;white-space:nowrap;left:50%;-webkit-transform:translateX(-50%);-ms-transform:translateX(-50%);transform:translateX(-50%);width:-webkit-fit-content;width:-moz-fit-content;width:fit-content;z-index:26;cursor:pointer}.xgplayer-skin-default .xgplayer-texttrack ul li{opacity:.7;font-family:PingFangSC-Regular;font-size:11px;color:hsla(0,0%,100%,.8);width:-webkit-fit-content;width:-moz-fit-content;width:fit-content;margin:auto;padding:6px 13px}.xgplayer-skin-default .xgplayer-texttrack ul li.selected,.xgplayer-skin-default .xgplayer-texttrack ul li:hover{color:#fff;opacity:1}.xgplayer-skin-default .xgplayer-texttrack .name{text-align:center;font-family:PingFangSC-Regular;font-size:13px;cursor:pointer;color:hsla(0,0%,100%,.8);position:absolute;bottom:0;width:60px;height:20px;line-height:20px;background:rgba(0,0,0,.38);border-radius:10px;display:inline-block;vertical-align:middle}.xgplayer-skin-default .xgplayer-texttrack.xgplayer-texttrack-hide{display:none}xg-text-track{transition:bottom .3s ease}.xgplayer-skin-default.xgplayer-is-texttrack .xgplayer-texttrack,.xgplayer-skin-default.xgplayer-texttrack-active .xgplayer-texttrack ul{display:block}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0);function i(e,t,n,r){if(0!==t.length){var i=[];i.push('<li data-type="off" class="'+(r?"":"selected")+'">'+n+"</li>"),t.forEach((function(e){i.push("<li data-id="+e.id+" data-language="+e.language+' class="'+(e.isDefault&&r?"selected":"")+'">'+e.label+"</li>")})),e.innerHTML=i.join("")}else e.innerHTML=""}n(78);var a=function(){var e=this;if(this.config.textTrack){var t=this.config.textTrack,n=e.lang.TEXTTRACK,a=(0,r.createDom)("xg-texttrack",'<ul></ul><p class="name">'+n+"</p>",{tabindex:7},"xgplayer-texttrack"),o=function(n){if(n.isListUpdate){var o=a.getElementsByTagName("ul")[0];t=n.list,i(o,n.list,e.lang.OFF,e.textTrackShowDefault),n.list.length>0?(0,r.addClass)(e.root,"xgplayer-is-texttrack"):(0,r.removeClass)(e.root,"xgplayer-is-texttrack"),0===n.list.length?(0,r.addClass)(a,"xgplayer-texttrack-hide"):(0,r.removeClass)(a,"xgplayer-texttrack-hide")}};t&&Array.isArray(t)&&(t.length>0&&(0,r.addClass)(e.root,"xgplayer-is-texttrack"),e.once("canplay",s),e.on("subtitle_change",o))}function s(){var n=e.root.querySelector(".xgplayer-texttrack");if(!n){e.controls.appendChild(a);var o=a.querySelector(".name");e.config.textTrackActive&&"hover"!==e.config.textTrackActive?o.addEventListener("click",(function(t){t.preventDefault(),t.stopPropagation(),(0,r.hasClass)(e.root,"xgplayer-texttrack-active")?(0,r.removeClass)(e.root,"xgplayer-texttrack-active"):(0,r.addClass)(e.root,"xgplayer-texttrack-active")})):(o.addEventListener("mouseenter",(function(t){t.preventDefault(),t.stopPropagation(),(0,r.addClass)(e.root,"xgplayer-texttrack-active"),a.focus()})),a.addEventListener("mouseleave",(function(t){t.preventDefault(),t.stopPropagation(),(0,r.removeClass)(e.root,"xgplayer-texttrack-active")})))}["touchend","click"].forEach((function(t){a.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation();var n=t.target||t.srcElement;if(n&&"li"===n.tagName.toLocaleLowerCase()){var i=n.getAttribute("data-id"),a=n.getAttribute("data-type"),o=n.getAttribute("data-language");Array.prototype.forEach.call(n.parentNode.childNodes,(function(e){(0,r.removeClass)(e,"selected")})),(0,r.addClass)(n,"selected"),"off"===a?(e.switchOffSubtile(),(0,r.removeClass)(e.root,"xgplayer-texttrack-active")):(e.switchSubTitle({id:i,language:o}),(0,r.addClass)(e.root,"xgplayer-texttrack-active"))}}))}));var s=a.getElementsByTagName("ul")[0];i(s,t,e.lang.OFF,e.textTrackShowDefault),0===t.length?(0,r.addClass)(a,"xgplayer-texttrack-hide"):(0,r.removeClass)(a,"xgplayer-texttrack-hide")}};t.default={name:"s_textTrack",method:a},e.exports=t["default"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0);n(164);var i=function(){var e=this,t=e.root,n=(0,r.createDom)("xg-error",'<span class="xgplayer-error-text">请<span class="xgplayer-error-refresh">刷新</span>试试</span>',{},"xgplayer-error");e.once("ready",(function(){t.appendChild(n)}));var i=n.querySelector(".xgplayer-error-text");e.config.lang&&"zh-cn"===e.config.lang?i.innerHTML=e.config.errorTips||'请<span class="xgplayer-error-refresh">刷新</span>试试':i.innerHTML=e.config.errorTips||'please try to <span class="xgplayer-error-refresh">refresh</span>';var a=null;function o(){(0,r.addClass)(e.root,"xgplayer-is-error"),a=n.querySelector(".xgplayer-error-refresh"),a&&["touchend","click"].forEach((function(t){a.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation(),e.autoplay=!0;var n=e.currentTime;e.once("playing",(function(){e.currentTime=n,(0,r.removeClass)(e.root,"xgplayer-is-error")})),e.src=e.config.url}))}))}function s(){e.off("error",o),e.off("destroy",s)}e.on("error",o),e.once("destroy",s)};t.default={name:"s_error",method:i},e.exports=t["default"]},function(e,t,n){var r,i=n(165);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-error{background:#000;display:none;position:absolute;left:0;top:0;width:100%;height:100%;z-index:125;font-family:PingFangSC-Regular;font-size:14px;color:#fff;text-align:center;line-height:100%;-webkit-justify-content:center;-moz-box-pack:center;justify-content:center;-webkit-align-items:center;-moz-box-align:center;align-items:center}.xgplayer-skin-default .xgplayer-error .xgplayer-error-refresh{color:#fa1f41;padding:0 3px;cursor:pointer}.xgplayer-skin-default .xgplayer-error .xgplayer-error-text{line-height:18px;margin:auto 6px}.xgplayer-skin-default.xgplayer-is-error .xgplayer-error{display:-webkit-flex;display:-moz-box;display:flex}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0);n(167);var i=function(){var e=this,t=e.config.lastPlayTime||0,n=e.config.lastPlayTimeHideDelay||0,i=null;if(!(t<=0)){i=(0,r.createDom)("xg-memoryplay",'<div class="xgplayer-memoryplay-spot"><div class="xgplayer-progress-tip">您上次观看到 <span class="xgplayer-lasttime">'+(0,r.format)(t)+'</span> ，为您自动续播 <span class="btn-close"><svg viewBox="64 64 896 896" focusable="false" class="" data-icon="close" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3-3.6-7.5-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9A7.95 7.95 0 0 0 203 838h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3 3.6 7.5 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z"></path></svg></span></div></div>',{},"xgplayer-memoryplay"),i.addEventListener("mouseover",(function(e){e.stopPropagation()}));var a=function(){i&&i.parentNode&&i.parentNode.removeChild(i),i=null};i.querySelector(".xgplayer-progress-tip .btn-close").addEventListener("click",a);var o=function(){n>0&&e.root.appendChild(i),e.emit("memoryPlayStart",t),n>0&&setTimeout((function(){a()}),1e3*n)};e.once("playing",o),e.once("ended",a)}};t.default={name:"s_memoryPlay",method:i},e.exports=t["default"]},function(e,t,n){var r,i=n(168);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-memoryplay-spot{position:absolute;height:32px;left:10px;bottom:46px;background:rgba(0,0,0,.5);border-radius:32px;line-height:32px;color:#ddd;z-index:15;padding:0 32px 0 16px}.xgplayer-skin-default .xgplayer-memoryplay-spot .xgplayer-lasttime{color:red;font-weight:700}.xgplayer-skin-default .xgplayer-memoryplay-spot .btn-close{position:absolute;width:16px;height:16px;right:10px;top:2px;cursor:pointer;color:#fff;font-size:16px}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(0),i=n(170),a=o(i);function o(e){return e&&e.__esModule?e:{default:e}}n(171);var s=function(){var e=this;if(e.config.airplay&&window.WebKitPlaybackTargetAvailabilityEvent){var t=(0,r.createDom)("xg-airplay",'<xg-icon class="xgplayer-icon">\n    <div class="xgplayer-icon-airplay">'+a.default+"</div>\n  </xg-icon>",{},"xgplayer-airplay"),n=(0,r.createDom)("xg-tips",'<span class="xgplayer-tip-airplay">'+e.lang.AIRPLAY_TIPS+"</span>",{},"xgplayer-tips");t.appendChild(n),e.once("ready",(function(){e.controls.appendChild(t),e.video.addEventListener("webkitplaybacktargetavailabilitychanged",(function(e){switch(e.availability){case"available":t.hidden=!1,t.disabled=!1;break;case"not-available":t.hidden=!0,t.disabled=!0;break}}))})),["click","touchend"].forEach((function(n){t.addEventListener(n,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("airplayBtnClick")}))}))}};t.default={name:"s_airplay",method:s},e.exports=t["default"]},function(e,t,n){"use strict";n.r(t),t["default"]='<svg t="1600422191774" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3100" width="28" height="28"><path d="M256 938.666667h512L512 597.333333 256 938.666667z m170.666667-85.333334l85.333333-113.781333L597.333333 853.333333H426.666667zM853.333333 85.333333H170.666667C99.946667 85.333333 42.666667 142.613333 42.666667 213.333333v426.666667c0 70.72 57.28 128 128 128h106.666666l64-85.333333H170.666667c-23.573333 0-42.666667-19.093333-42.666667-42.666667V213.333333c0-23.573333 19.093333-42.666667 42.666667-42.666666h682.666666c23.573333 0 42.666667 19.093333 42.666667 42.666666v426.666667c0 23.573333-19.093333 42.666667-42.666667 42.666667H682.666667l64 85.333333h106.666666c70.72 0 128-57.28 128-128V213.333333c0-70.72-57.28-128-128-128z" p-id="3101" fill="#ffffff"></path></svg>'},function(e,t,n){var r,i=n(172);"string"===typeof i&&(i=[[e.i,i,""]]);var a={hmr:!0};a.transform=r,a.insertInto=void 0;n(2)(i,a);i.locals&&(e.exports=i.locals)},function(e,t,n){t=e.exports=n(1)(!1),t.push([e.i,".xgplayer-skin-default .xgplayer-airplay{position:relative;-webkit-order:11;-moz-box-ordinal-group:12;order:11;display:block;cursor:pointer;margin-left:5px;margin-right:3px}.xgplayer-skin-default .xgplayer-airplay .xgplayer-icon{margin-top:6px;margin-left:6px}.xgplayer-skin-default .xgplayer-airplay .xgplayer-icon div{position:absolute}.xgplayer-skin-default .xgplayer-airplay .xgplayer-icon .xgplayer-icon-airplay{display:block}.xgplayer-skin-default .xgplayer-airplay .xgplayer-tips{position:absolute;right:0;left:auto}.xgplayer-skin-default .xgplayer-airplay .xgplayer-tips .xgplayer-tip-airplay{display:block}.xgplayer-skin-default .xgplayer-airplay:hover{opacity:.85}.xgplayer-skin-default .xgplayer-airplay:hover .xgplayer-tips{display:block}",""])}])}))}}]);