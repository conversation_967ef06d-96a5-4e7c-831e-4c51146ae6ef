"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[8442],{7799:function(e,a,t){t.r(a),t.d(a,{default:function(){return u}});var l=t(641),i=t(2644);const d={class:"custom-tree-node",style:{margin:"5px",color:"#000",padding:"5px"}},o={class:"left-panel"},r={class:"right-panel"},c={class:"dialog-footer"};function s(e,a,t,s,n,h){const b=(0,l.g2)("el-input"),p=(0,l.g2)("el-header"),u=(0,l.g2)("el-tree"),k=(0,l.g2)("el-main"),g=(0,l.g2)("el-container"),m=(0,l.g2)("el-aside"),j=(0,l.g2)("el-option"),f=(0,l.g2)("el-select"),y=(0,l.g2)("sc-select"),F=(0,l.g2)("el-button"),C=(0,l.g2)("el-button-group"),w=(0,l.g2)("el-table-column"),O=(0,l.g2)("scTable"),S=(0,l.g2)("el-form-item"),x=(0,l.g2)("el-form"),I=(0,l.g2)("el-dialog"),L=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)(l.FK,null,[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(g,{"element-loading-text":"请稍等..."},{default:(0,l.k6)((()=>[(0,l.bF)(m,{width:"260px"},{default:(0,l.k6)((()=>[(0,l.bF)(g,null,{default:(0,l.k6)((()=>[(0,l.bF)(p,null,{default:(0,l.k6)((()=>[(0,l.bF)(b,{placeholder:"输入关键字进行过滤",modelValue:n.filterText,"onUpdate:modelValue":a[0]||(a[0]=e=>n.filterText=e),clearable:""},null,8,["modelValue"])])),_:1}),(0,l.bF)(k,{class:"nopadding"},{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(u,{ref:"tree",class:"custom-tree menu","node-key":"Fid",props:{label:"FName",value:"Fid"},data:n.treeData,"current-node-key":"0","check-on-click-node":!0,"show-checkbox":!0,"highlight-current":!0,"expand-on-click-node":!1,"filter-node-method":h.filterNode,onCheckChange:h.treeClick,"default-expand-all":!0},{default:(0,l.k6)((({data:e})=>[(0,l.Lk)("span",d,[(0,l.Lk)("span",{class:"label",style:(0,i.Tr)(""!=e.FStatusName?"color:red":"")},(0,i.v_)(e.FName),5)])])),_:1},8,["data","filter-node-method","onCheckChange"])),[[L,n.treeLoading]])])),_:1})])),_:1})])),_:1}),(0,l.bF)(g,null,{default:(0,l.k6)((()=>[(0,l.bF)(p,null,{default:(0,l.k6)((()=>[(0,l.Lk)("div",o,[(0,l.bF)(f,{modelValue:n.jObjectSearch.areaId,"onUpdate:modelValue":a[1]||(a[1]=e=>n.jObjectSearch.areaId=e),filterable:"",onChange:h.change,style:{width:"165px","padding-left":"0"}},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(n.area,(e=>((0,l.uX)(),(0,l.Wv)(j,{key:e.Fid,label:e.FName,value:e.Fid},null,8,["label","value"])))),128))])),_:1},8,["modelValue","onChange"]),(0,l.bF)(y,{apiObj:[{value:"0",label:"待导出"},{value:"1",label:"已导出"}],modelValue:n.jObjectSearch.status,"onUpdate:modelValue":a[2]||(a[2]=e=>n.jObjectSearch.status=e),placeholder:"导出状态",onChange:h.upsearch,clearable:"",style:{width:"150px","padding-right":"10px"}},null,8,["modelValue","onChange"]),(0,l.bF)(F,{type:"primary",icon:"el-icon-refresh",onClick:h.update,disabled:n.updateLoading},{default:(0,l.k6)((()=>[(0,l.eW)(" 更新Cdkey")])),_:1},8,["onClick","disabled"]),(0,l.bF)(F,{type:"primary",icon:"el-icon-download",onClick:a[3]||(a[3]=e=>h.export1("勾选","勾选Cdkey")),disabled:n.updateLoading},{default:(0,l.k6)((()=>[(0,l.eW)(" 导出勾选")])),_:1},8,["disabled"]),(0,l.bF)(F,{type:"primary",icon:"el-icon-download",onClick:a[4]||(a[4]=e=>h.export1("每日","每日Cdkey（42组码）")),disabled:n.updateLoading},{default:(0,l.k6)((()=>[(0,l.eW)(" 导出每日")])),_:1},8,["disabled"]),(0,l.bF)(F,{type:"primary",icon:"el-icon-download",onClick:a[5]||(a[5]=e=>h.export1("整套","整套Cdkey（不含每日）")),disabled:n.updateLoading},{default:(0,l.k6)((()=>[(0,l.eW)(" 导出整套")])),_:1},8,["disabled"]),(0,l.bF)(C,{class:"ml-4",style:{"padding-left":"15px"}},{default:(0,l.k6)((()=>[(0,l.bF)(F,{type:"primary",icon:"el-icon-view",onClick:a[6]||(a[6]=e=>h.upsearch("预览")),disabled:n.updateLoading},{default:(0,l.k6)((()=>[(0,l.eW)(" 预览整套")])),_:1},8,["disabled"]),(0,l.bF)(F,{type:"primary",icon:"el-icon-warning",onClick:a[7]||(a[7]=()=>{this.dialog=!0})})])),_:1})]),(0,l.Lk)("div",r,[(0,l.bF)(b,{modelValue:n.jObjectSearch.search,"onUpdate:modelValue":a[8]||(a[8]=e=>n.jObjectSearch.search=e),placeholder:"任务名称 / Cdkey",class:"input-with-select",onChange:h.upsearch},null,8,["modelValue","onChange"]),(0,l.bF)(F,{type:"primary",onClick:h.upsearch,icon:"el-icon-search"},{default:(0,l.k6)((()=>[(0,l.eW)("查询")])),_:1},8,["onClick"])])])),_:1}),(0,l.bF)(k,{class:"nopadding"},{default:(0,l.k6)((()=>[(0,l.bF)(O,{ref:"table",apiObj:n.cdkeyApiObj,"row-key":"Fid",params:{jObjectSearch:n.jObjectSearch},onRowClick:h.rowClick,"row-Style":h.rowStyle,border:""},{default:(0,l.k6)((()=>[(0,l.bF)(w,{type:"selection",align:"center",width:"50"}),(0,l.bF)(w,{label:"导出状态",prop:"FStatusName",align:"center",width:"100"}),(0,l.bF)(w,{label:"分区名称",prop:"FAreaName",align:"center",width:"170"}),(0,l.bF)(w,{label:"账号名称",prop:"FCookieName","header-align":"center",width:"170","show-overflow-tooltip":""}),(0,l.bF)(w,{label:"任务名称",prop:"FTaskName","header-align":"center","show-overflow-tooltip":""}),(0,l.bF)(w,{label:"Cdkey名称",prop:"FName","header-align":"center","show-overflow-tooltip":"",sortable:""}),(0,l.bF)(w,{label:"Cdkey",prop:"FCdkey",align:"center",width:"150"}),(0,l.bF)(w,{label:"领取时间",prop:"FDate",align:"center",width:"160",sortable:""})])),_:1},8,["apiObj","params","onRowClick","row-Style"])])),_:1})])),_:1})])),_:1})),[[L,n.updateLoading]]),(0,l.bF)(I,{modelValue:n.dialog,"onUpdate:modelValue":a[13]||(a[13]=e=>n.dialog=e),title:"高级筛选",width:"600px"},{footer:(0,l.k6)((()=>[(0,l.Lk)("span",c,[(0,l.bF)(F,{onClick:a[11]||(a[11]=e=>{n.dialog=!1,n.jObjectSearch.taskId=[]})},{default:(0,l.k6)((()=>[(0,l.eW)("取消")])),_:1}),(0,l.bF)(F,{type:"primary",onClick:a[12]||(a[12]=e=>{h.upsearch("自定义"),n.dialog=!1,n.jObjectSearch.taskId=[]})},{default:(0,l.k6)((()=>[(0,l.eW)(" 确认 ")])),_:1})])])),default:(0,l.k6)((()=>[(0,l.bF)(x,{model:e.form},{default:(0,l.k6)((()=>[(0,l.bF)(S,{label:"任务名称"},{default:(0,l.k6)((()=>[((0,l.uX)(),(0,l.Wv)(y,{clearable:"",modelValue:n.jObjectSearch.taskId,"onUpdate:modelValue":a[9]||(a[9]=e=>n.jObjectSearch.taskId=e),multiple:"",params:{jObjectSearch:{areaId:n.jObjectSearch.areaId}},key:n.jObjectSearch.areaId,"collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":1,apiObj:e.$API.biliAreaTask.getAreaTaskList,prop:{label:"FTaskName",value:"Fid"},placeholder:"查询选中",style:{width:"100%","padding-left":"0"}},null,8,["modelValue","params","apiObj"]))])),_:1}),(0,l.bF)(S,{label:"查询几套"},{default:(0,l.k6)((()=>[(0,l.bF)(b,{type:"number",modelValue:n.jObjectSearch.num,"onUpdate:modelValue":a[10]||(a[10]=e=>n.jObjectSearch.num=e),placeholder:"查询几套"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])],64)}t(8743);var n=t(1132),h={name:"biliCdkey",data(){return{filterText:"",treeData:[],treeLoading:!0,cdkeyApiObj:null,updateLoading:!1,jObjectSearch:{search:"",areaId:"",status:"0",activityId:""},dialog:!1,checkboxList:[],area:[]}},watch:{filterText(e){this.$refs.tree.filter(e)}},async created(){let e=await this.$API.biliArea.getAreaList.post({jObjectSearch:{}});0==e.code&&(this.area=e.data.rows,this.area.length>0&&(this.jObjectSearch.areaId=this.area[0].Fid)),this.cdkeyApiObj=this.$API.biliCdkey.getCdkeyList,e=await this.$API.biliCookies.getCookiesList.post({jObjectSearch:{enable:1,areaId:this.jObjectSearch.areaId}}),this.treeLoading=!1,this.treeData=[{FName:"全部账号",Fid:"0",FStatusName:"",children:e.data.rows}]},async mounted(){},methods:{filterNode(e,a){return!e||-1!==a.FName.indexOf(e)},treeClick(){this.jObjectSearch.cookieId=String(this.$refs.tree.getCheckedKeys()),this.upsearch()},upsearch(e=""){this.checkboxList=[],this.$refs.table.upData({jObjectSearch:{type:e,...this.jObjectSearch}})},async update(){const e=n.Ks.service({lock:!0,text:"执行中...",background:"rgba(0, 0, 0, 0.7)"});let a=[];if(this.jObjectSearch.cookieId){let e=this.jObjectSearch.cookieId.split(",");for(let t in e)a.push({Fid:e[t]})}try{let e=await this.$API.biliQuartz.manualExecQuartz.post({jObjectParam:{array:a,areaId:this.jObjectSearch.areaId,jobName:"更新Cdkey列表",type:this.$TOOL.data.get("EXECMODE"),delay:1}});if(0!=e.code)throw new Error(e.message);this.$message.success(e.message),this.upsearch()}catch(t){this.$alert(t.message,"提示",{type:"error"})}e.close()},rowClick(e){this.$refs.table.toggleRowSelection(e),this.checkboxList.indexOf(e.Fid)>-1?this.checkboxList.splice(this.checkboxList.indexOf(e.Fid),1):this.checkboxList.push(e.Fid)},rowStyle(e){if(this.checkboxList.indexOf(e.row.Fid)>-1)return{backgroundColor:"var(--el-table-current-row-bg-color) !important"}},async export1(e,a){this.$confirm("确认要执行导出 "+a+" ?","提示",{type:"warning"}).then((async()=>{let a=this.$refs.table.getSelectionRows(),t="";if(a.length>0&&"勾选"==e)t=a.reduce(((e,a)=>e+","+a.Fid),"0");else if("每日"==e)t="0";else{if("整套"!=e)return void this.$message.error("请至少勾选一条数据");t="0"}this.updateLoading=!0;let l=await this.$API.biliCdkey.exportCdkeyList.post({jObjectSearch:{id:t,type:e,areaId:this.jObjectSearch.areaId}});if(this.updateLoading=!1,0==l.code){let e=document.createElement("a");e.style="display: none",e.target="_blank",e.download="cdkey",e.href=l.data,document.body.appendChild(e),e.click(),document.body.removeChild(e),this.upsearch()}else this.$alert(l.message,"提示",{type:"error"})}))},async change(){this.treeLoading=!0;let e=await this.$API.biliCookies.getCookiesList.post({jObjectSearch:{enable:1,areaId:this.jObjectSearch.areaId}});this.treeLoading=!1,this.treeData=[{FName:"全部账号",Fid:"0",FStatusName:"",children:e.data.rows}],this.jObjectSearch.activityId="",this.upsearch()}}},b=t(6262);const p=(0,b.A)(h,[["render",s],["__scopeId","data-v-bc4acae2"]]);var u=p}}]);