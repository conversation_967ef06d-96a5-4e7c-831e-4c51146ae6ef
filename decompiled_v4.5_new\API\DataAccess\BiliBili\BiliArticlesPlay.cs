using System;
using System.CodeDom.Compiler;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Text.RegularExpressions.Generated;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using API.BusService.BiliBili;
using API.Common;
using API.Models.Comm;
using Newtonsoft.Json.Linq;
using Quartz;

namespace API.DataAccess.BiliBili;

public class BiliArticlesPlay
{
	public static DataTable GetArticlesPlayList(string areaId, string search, string st, string et, int userId, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string text = " SELECT * " + SQLHelper.total + " FROM TArticlesPlay WHERE FUserId=" + userId;
		if (areaId != "")
		{
			text = text + " AND FAreaId=" + areaId;
		}
		if (search != "")
		{
			text = text + " AND (FCookieName LIKE '%" + search + "%' OR FBvid LIKE '%" + search + "%')";
		}
		if (st != "" && et != "")
		{
			text = text + " AND FDate BETWEEN '" + st + "' AND DATEADD(DAY,1, '" + et + "')";
		}
		return SQLHelper.BiliLocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static async Task ImportArticlesPlay(string src, int play, string type, int num, int group, string curTime, ISchedulerFactory schedulerFactory, User user)
	{
		ThreadPool.SetMinThreads(500, 500);
		ConcurrentBag<Task> tasks = new ConcurrentBag<Task>();
		List<Process?> processes = new List<Process>();
		try
		{
			JObject jObject = new JObject
			{
				["Accept-Encoding"] = "gzip, deflate, br, zstd",
				["Cache-Control"] = "no-cache",
				["Content-Type"] = "application/x-www-form-urlencoded",
				["Pragma"] = "no-cache",
				["Priority"] = "u=1, i",
				["Sec-Ch-Ua"] = "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
				["Sec-Ch-Ua-Mobile"] = "?0",
				["Sec-Ch-Ua-Platform"] = "\"Windows\"",
				["Sec-Fetch-Dest"] = "empty",
				["Sec-Fetch-Mode"] = "cors",
				["Sec-Fetch-Site"] = "same-site",
				["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
			};
			Dictionary<string, string> defaultHeaders = jObject.ToObject<Dictionary<string, string>>() ?? new Dictionary<string, string>();
			HttpClientFactory httpClientFactory = new HttpClientFactory("", defaultHeaders);
			Util.WriteLog("BiliBili", "稿件刷播", "稿件刷播", "刷播启动！", ConsoleColor.Green);
			JArray jArray = new JArray();
			string text = Directory.GetCurrentDirectory() + "\\WWWRoot\\" + src;
			DataTable dt = ExcelHelper.X2003.ExcelToTableForXLS(text);
			for (int i = 0; i < dt.Rows.Count; i++)
			{
				DataRow dr = dt.Rows[i];
				string text2 = "";
				try
				{
					string[] array = RsaEncrypt.RSADecrypt(Util.GetJObject(dr, "秘钥")).Split(',');
					string jObject2 = Util.GetJObject(dr, "名称");
					string text3 = array[0];
					string text4 = array[1];
					string text5 = array[2];
					string text6 = array[3];
					Util.WriteLog("BiliBili", "稿件刷播", "稿件刷播", "正在处理:" + text6 + "！");
					jArray.Add(new JObject
					{
						["FUserId"] = user.Id,
						["FCookieId"] = text3,
						["FCookieName"] = jObject2,
						["FAreaName"] = text4,
						["FAreaId"] = text5,
						["FBvid"] = text6,
						["FCid"] = "",
						["FAid"] = "",
						["FDate"] = curTime,
						["FStart"] = 0,
						["FEnd"] = -1,
						["FShare"] = i / group,
						["FShareSource"] = "等待中",
						["FPlayer"] = play
					});
				}
				catch (Exception ex)
				{
					Util.WriteLog("BiliBili", "稿件刷播", "稿件刷播", ex.Message.ToString() + "|" + text2, ConsoleColor.Red);
				}
			}
			File.Delete(text);
			Util.WriteLog("BiliBili", "稿件刷播", "稿件刷播", "所有视频处理完成，正在导入数据！");
			dt = jArray.ToObject<DataTable>() ?? new DataTable();
			if (dt.Rows.Count > 0)
			{
				SQLHelper.BiliLocalDB.SqlBulkCopyByDataTable("TArticlesPlay", dt);
				Util.WriteLog("BiliBili", "稿件刷播", "稿件刷播", "导入数据成功,共" + dt.Rows.Count + "个Bvid！");
				string sSql = " SELECT TOP 1 T1.*,T2.FAddress,T2.FUserName,T2.FPassword FROM TCookies T1 LEFT JOIN TProxy T2 ON T2.Fid=T1.FProxyId WHERE T1.FUserId=" + user.Id + " ORDER BY FSort";
				DataTable dtCookie = SQLHelper.BiliLocalDB.RunSqlDt(sSql);
				for (int j = 0; j <= dt.Rows.Count / group; j++)
				{
					DataTable dtCopy = new DataTable();
					Util.WriteLog("BiliBili", "稿件刷播", "稿件刷播", "正在获取代理地址！");
					JArray proxy = await GetProxy(httpClientFactory, user);
					Util.WriteLog("BiliBili", "稿件刷播", "稿件刷播", "以获取到" + proxy.Count + "个代理！");
					foreach (JToken item2 in proxy)
					{
						sSql = " SELECT ROW_NUMBER() OVER (ORDER BY NEWID() DESC)%" + num + " AS FNum,Fid,FBvid,FPlayer FROM TArticlesPlay WHERE FDate='" + curTime + "' AND FUserId=" + user.Id;
						sSql = sSql + " AND FCookieId IN (SELECT Fid FROM TCookies WHERE FExpirationTime>GETDATE() AND FEnable=1 AND FShare=" + j + " AND FUserId=" + user.Id + ")";
						DataTable shuffledTable = SQLHelper.BiliLocalDB.RunSqlDt(sSql);
						if (dtCopy.Rows.Count == 0)
						{
							dtCopy = shuffledTable.Copy();
						}
						string address = Util.GetJObject(item2, "FAddress");
						string userCode = Util.GetJObject(item2, "FUserCode");
						string userPWD = Util.GetJObject(item2, "FUserPWD");
						for (int k = 0; k < num; k++)
						{
							DataRow[] array2 = shuffledTable.Select("FNum=" + k);
							if (array2 == null || array2.Length == 0)
							{
								continue;
							}
							DataTable dtBvid = array2.CopyToDataTable();
							if (type == "分散")
							{
								bool windowStyle = bool.Parse(AppSettings.GetVal("Log"));
								string text7 = string.Concat(string.Join(",", (from row in dtBvid.Rows.OfType<DataRow>()
									select Convert.ToString(row["FBvid"])).ToArray()));
								Process item = Util.ProcessStart("\\AutoPlayer\\AutoPlayer.exe", "-a \"" + address + "\" -c \"" + userCode + "\" -w \"" + userPWD + "\" -b \"" + text7 + "\" -p " + play + " -u \"" + AppSettings.GetVal("Url", "Kestrel:Endpoints:Http") + "\"", windowStyle);
								processes.Add(item);
							}
							else
							{
								if (!(type == "集中"))
								{
									throw new Exception("刷播类型不正确！");
								}
								tasks.Add(Task.Run(() => VideoPlayProxy(dtBvid, address, userCode, userPWD, tasks)));
							}
							await Task.Delay(100);
						}
					}
					if (type == "分散")
					{
						int k = 1;
						while (k != 0)
						{
							k = processes.Count((Process t) => t != null && !t.HasExited);
							Util.WriteLog("BiliBili", "稿件刷播", "稿件刷播", "当前同时刷播的数量：" + k);
							await Task.Delay(5000);
						}
					}
					else if (type == "集中")
					{
						foreach (Task item3 in tasks)
						{
							item3.Wait();
						}
					}
					tasks.Add(Task.Run(() => VideoPlayCookie(dtCopy, dtCookie.Rows[0], tasks)));
					foreach (DataRow row in dtCopy.Rows)
					{
						DataCURD.Save(new JObject
						{
							["Fid"] = Util.GetJObject(row, "Fid"),
							["FShareSource"] = "已完成",
							["FStart"] = proxy.Count
						}, "TArticlesPlay", "编辑", "Fid", user.Id, user.Name, curTime, SQLHelper.BiliLocalDB.InitCnn());
					}
				}
				foreach (Task item4 in tasks)
				{
					item4.Wait();
				}
				Util.WriteLog("BiliBili", "稿件刷播", "稿件刷播", "任务全部分配！", ConsoleColor.Green);
				return;
			}
			throw new Exception("暂无数据,停止刷播！");
		}
		catch (Exception ex2)
		{
			Util.WriteLog("BiliBili", "稿件刷播", "稿件刷播", ex2.Message, ConsoleColor.Red);
		}
	}

	public static async Task<JArray> GetProxy(HttpClientFactory httpClientFactory, User user)
	{
		JArray jArray = new JArray();
		JArray proxy = new JArray();
		string sSql = " SELECT FAddress, FSuccessKey, FSuccessValue, FDataKey, FPrefix, FIPKey, FPortKey, FUserCodeKey, FUserPWDKey FROM TProxyGroup WHERE FUserId=" + user.Id + " AND FMethod='方式一' AND FEnable=1 ";
		DataTable dataTable = SQLHelper.BiliLocalDB.RunSqlDt(sSql);
		foreach (DataRow dr in dataTable.Rows)
		{
			await Task.Delay(1000);
			string jObject = Util.GetJObject(dr, "FAddress");
			if (jObject == "禁止在抢码时间刷播")
			{
				JObject jObject2 = await httpClientFactory.Get("https://api.bilibili.com/x/click-interface/click/now");
				if (!(Util.GetJObject(jObject2, "code") == "0"))
				{
					continue;
				}
				DateTime localDateTime = DateTimeOffset.FromUnixTimeSeconds(Util.GetJObject<long>(jObject2["data"], "now")).LocalDateTime;
				DateTime dateTime = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd 17:30:00"));
				DateTime dateTime2 = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd 18:10:00"));
				DateTime dateTime3 = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd 23:30:00"));
				DateTime dateTime4 = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd 02:10:00"));
				if ((!(localDateTime <= dateTime) && !(localDateTime >= dateTime2)) || !(localDateTime <= dateTime3) || !(localDateTime >= dateTime4))
				{
					continue;
				}
				Request model = new Request
				{
					jObjectParam = new JObject
					{
						["userId"] = user.Id,
						["userName"] = user.Name
					},
					jObjectSearch = new JObject { ["p1"] = "1" }
				};
				object obj = await Util.Request("/Common/GetHomeList", model);
				if (obj == null)
				{
					continue;
				}
				jObject2 = JObject.FromObject(obj);
				jArray = Util.GetJObject<JArray>(jObject2, "rows") ?? new JArray();
				for (int i = 0; i < jArray.Count; i++)
				{
					if (i == 0)
					{
						Util.WriteLog("BiliBili", "稿件刷播", "稿件刷播", "你要感谢→" + Util.GetJObject(jArray[i], "Message"), ConsoleColor.Green);
					}
					jObject = Util.GetJObject(jArray[i], "FAddress");
					string jObject3 = Util.GetJObject(jArray[i], "FUserName");
					string jObject4 = Util.GetJObject(jArray[i], "FPassword");
					proxy.Add(new JObject
					{
						["FAddress"] = jObject,
						["FUserCode"] = jObject3,
						["FUserPWD"] = jObject4
					});
				}
				continue;
			}
			string successKey = Util.GetJObject(dr, "FSuccessKey");
			string successValue = Util.GetJObject(dr, "FSuccessValue");
			string dataKey = Util.GetJObject(dr, "FDataKey");
			string prefix = Util.GetJObject(dr, "FPrefix");
			string ipKey = Util.GetJObject(dr, "FIPKey");
			string portKey = Util.GetJObject(dr, "FPortKey");
			string userCodeKey = Util.GetJObject(dr, "FUserCodeKey");
			string userPWDKey = Util.GetJObject(dr, "FUserPWDKey");
			string text = await httpClientFactory.GetStr(jObject);
			if (successKey == "")
			{
				jArray = JArray.Parse(text);
			}
			else
			{
				try
				{
					JObject jObject2 = JObject.Parse(text);
					JToken jToken = jObject2;
					if (!(Util.GetJObject(jObject2, successKey) == successValue))
					{
						throw new Exception("获取代理失败：" + text);
					}
					if (dataKey.Contains('.'))
					{
						string[] array = dataKey.Split('.');
						for (int j = 0; j < array.Length; j++)
						{
							if (j == array.Length - 1)
							{
								jToken = Util.GetJObject(jToken, array[j]);
								if (jToken.Type == JTokenType.String)
								{
									jArray = JArray.Parse(jToken.ToString());
								}
							}
							else
							{
								jToken = Util.GetJObject(jToken, array[j]);
								if (jToken.Type == JTokenType.String)
								{
									jToken = JObject.Parse(jToken.ToString());
								}
							}
						}
					}
					else
					{
						jArray = Util.GetJObject<JArray>(jObject2, dataKey) ?? new JArray();
					}
				}
				catch (Exception ex)
				{
					throw new Exception("获取代理失败：" + ex.Message);
				}
			}
			foreach (JToken item in jArray)
			{
				if (item.Type == JTokenType.String)
				{
					string[] array2 = item.ToString().Split(':');
					if (array2.Length == 4)
					{
						string text2 = array2[0];
						string text3 = array2[1];
						string text4 = array2[2];
						string text5 = array2[3];
						proxy.Add(new JObject
						{
							["FAddress"] = prefix + "://" + text2 + ((text3 == "") ? "" : (":" + text3)),
							["FUserCode"] = text4,
							["FUserPWD"] = text5
						});
					}
				}
				else
				{
					string jObject5 = Util.GetJObject(item, ipKey);
					string jObject6 = Util.GetJObject(item, portKey);
					string jObject7 = Util.GetJObject(item, userCodeKey);
					string jObject8 = Util.GetJObject(item, userPWDKey);
					if (jObject5 != "")
					{
						proxy.Add(new JObject
						{
							["FAddress"] = prefix + "://" + jObject5 + ((jObject6 == "") ? "" : (":" + jObject6)),
							["FUserCode"] = jObject7,
							["FUserPWD"] = jObject8
						});
					}
				}
			}
		}
		sSql = " SELECT * FROM TProxy WHERE FEnable=1 AND FGroupId IN (SELECT Fid FROM TProxyGroup WHERE FMethod='方式二' AND FUserId=" + user.Id + " AND FEnable=1) AND FUserId=" + user.Id;
		dataTable = SQLHelper.BiliLocalDB.RunSqlDt(sSql);
		foreach (DataRow row in dataTable.Rows)
		{
			string jObject9 = Util.GetJObject(row, "FAddress");
			string jObject10 = Util.GetJObject(row, "FUserName");
			string jObject11 = Util.GetJObject(row, "FPassword");
			proxy.Add(new JObject
			{
				["FAddress"] = jObject9,
				["FUserCode"] = jObject10,
				["FUserPWD"] = jObject11
			});
		}
		proxy = new JArray(proxy.Select((JToken x) => x).Distinct());
		if (!proxy.Any())
		{
			throw new Exception("暂无代理,停止刷播！");
		}
		return proxy;
	}

	public static async Task VideoPlayProxy(DataTable bvids, string address, string userCode, string userPWD, ConcurrentBag<Task> tasks)
	{
		JArray headers = new JArray
		{
			new JObject { ["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" },
			new JObject { ["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0" },
			new JObject { ["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" },
			new JObject { ["User-Agent"] = "Mozilla/5.0 (Macintosh; Intel Mac OS X 13_5) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15" },
			new JObject { ["User-Agent"] = "Mozilla/5.0 (Linux; Android 9; Pixel 3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36" },
			new JObject { ["User-Agent"] = "Mozilla/5.0 (iPhone; CPU iPhone OS 7_0_4 like Mac OS X) AppleWebKit/537.51.1 (KHTML, like Gecko) CriOS/31.0.1650.18 Mobile/11B554a Safari/8536.25" },
			new JObject { ["User-Agent"] = "Mozilla/5.0 (Linux; Android 4.2.1; M040 Build/JOP40D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.59 Mobile Safari/537.36" },
			new JObject { ["User-Agent"] = "Mozilla/5.0 (Linux; U; Android 4.4.4; zh-cn; M351 Build/KTU84P) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30" }
		};
		int error = 0;
		for (int j = 0; j < bvids.Rows.Count; j++)
		{
			if (error == 10)
			{
				Util.WriteLog("BiliBili", "稿件刷播", "稿件刷播", "错误次数过多," + address + "停止刷播！", ConsoleColor.Red, writeLog: false);
				break;
			}
			int play = int.Parse(Util.GetJObject(bvids.Rows[j], "FPlayer"));
			string bvid = Util.GetJObject(bvids.Rows[j], "FBvid");
			new Random((int)DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()).Next(11, 100);
			new Random((int)DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()).Next(1, 4);
			string from_spmid = "333.337.search-card.all.click";
			try
			{
				JObject jObject = new JObject
				{
					["Accept-Encoding"] = "gzip, deflate, br, zstd",
					["Cache-Control"] = "no-cache",
					["Content-Type"] = "application/x-www-form-urlencoded",
					["Origin"] = "https://www.bilibili.com",
					["Pragma"] = "no-cache",
					["Priority"] = "u=1, i",
					["Referer"] = "https://www.bilibili.com/video/" + bvid + "/?spm_id_from=333.337.search-card.all.click",
					["Sec-Ch-Ua-Mobile"] = "?0",
					["Sec-Ch-Ua-Platform"] = "\"Windows\"",
					["Sec-Fetch-Dest"] = "empty",
					["Sec-Fetch-Mode"] = "cors",
					["Sec-Fetch-Site"] = "same-site",
					["User-Agent"] = Util.GetJObject(headers[new Random((int)DateTimeOffset.Now.ToUnixTimeSeconds()).Next(0, headers.Count - 1)], "User-Agent")
				};
				HttpClientHandler defaultHandler = null;
				if (address != "")
				{
					defaultHandler = new HttpClientHandler
					{
						Proxy = new WebProxy
						{
							Address = new Uri(address),
							Credentials = new NetworkCredential(userCode, userPWD)
						}
					};
				}
				HttpClientFactory httpClientFactory = new HttpClientFactory("", jObject.ToObject<Dictionary<string, string>>(), defaultHandler);
				string cookie = await BusBiliUtil.CrateCookie(httpClientFactory);
				httpClientFactory.AddHeaders(null, cookie);
				JObject jObject2 = await httpClientFactory.Get("https://api.bilibili.com/x/web-interface/view?bvid=" + bvid);
				if (!(Util.GetJObject(jObject2, "code") == "0"))
				{
					continue;
				}
				int video_duration = Util.GetJObject<int>(jObject2["data"], "duration");
				Util.GetJObject<int>(Util.GetJObject<JToken>(jObject2["data"], "stat"), "view");
				Util.GetJObject<int>(Util.GetJObject<JToken>(jObject2["data"], "stat"), "share");
				string aid = Util.GetJObject(jObject2["data"], "aid");
				string cid = Util.GetJObject(Util.GetJObject<JArray>(jObject2["data"], "pages")?[0], "cid");
				string jObject3 = Util.GetJObject(jObject2["data"], "title");
				double playTime = (double)play * 1.0 / 100.0;
				if (!(bvid != "") || !(aid != "") || !(cid != ""))
				{
					continue;
				}
				int num = new Random().Next(2, 5);
				if (num > jObject3.Length)
				{
					num = jObject3.Length;
				}
				int startIndex = new Random().Next(0, jObject3.Length - num + 1);
				jObject3 = jObject3.Substring(startIndex, num);
				string referer = "https://search.bilibili.com/all?keyword=" + HttpUtility.UrlEncode(jObject3) + "&from_source=webtop_search&spm_id_from=333.1007&search_source=5";
				long w_stime = DateTimeOffset.Now.ToUnixTimeSeconds();
				long w_ftime = DateTimeOffset.Now.ToUnixTimeSeconds();
				long realtime = 0L;
				long played_time = 0L;
				long real_played_time = 0L;
				long last_play_progress_time = 0L;
				int quality = 0;
				string jObject4 = Util.GetJObject(Util.GetJObject<JToken>(await httpClientFactory.Get("https://api.bilibili.com/x/player/wbi/v2?aid=" + aid + "&cid=" + cid), "responseHeaders"), "Set-Cookie");
				string cookieByKey = Util.GetCookieByKey(jObject4, "sid");
				cookie = cookie + "; sid=" + cookieByKey;
				httpClientFactory.AddHeaders(null, cookie);
				string session = Util.CalcMD5(w_stime.ToString());
				new Dictionary<string, string>();
				new Dictionary<string, string>();
				jObject2 = await httpClientFactory.Get("https://api.bilibili.com/x/click-interface/click/now");
				if (Util.GetJObject(jObject2, "code") == "0")
				{
					w_stime = Util.GetJObject<long>(jObject2["data"], "now");
				}
				jObject2 = await httpClientFactory.Get("https://api.bilibili.com/x/click-interface/click/now");
				if (Util.GetJObject(jObject2, "code") == "0")
				{
					w_ftime = Util.GetJObject<long>(jObject2["data"], "now");
				}
				Dictionary<string, string> source = new Dictionary<string, string>
				{
					{ "w_aid", aid },
					{ "w_part", "1" },
					{
						"w_ftime",
						w_ftime.ToString()
					},
					{
						"w_stime",
						w_stime.ToString()
					},
					{ "w_type", "3" },
					{ "web_location", "1315873" },
					{
						"wts",
						DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString()
					}
				};
				Dictionary<string, string> dictionary = source.OrderBy((KeyValuePair<string, string> o) => o.Key).ToDictionary((KeyValuePair<string, string> o) => o.Key, (KeyValuePair<string, string> p) => p.Value);
				string text = "";
				foreach (KeyValuePair<string, string> item in dictionary)
				{
					text += ((text != "") ? ("&" + item.Key + "=" + WebUtility.UrlDecode(item.Value)) : (item.Key + "=" + WebUtility.UrlDecode(item.Value)));
				}
				string text2 = "aid=" + aid + "&cid=" + cid + "&part=1&lv=0&ftime=" + w_ftime + "&stime=" + w_stime + "&type=3&sub_type=0&refer_url=" + referer;
				text2 = text2 + "&outer=0&statistics={\"appId\":100,\"platform\":5,\"abtest\":\"\",\"version\":\"\"}&mobi_app=web&device=web&platform=web&spmid=333.788.0.0&from_spmid=" + from_spmid + "&session=" + session + "&csrf=";
				jObject2 = await httpClientFactory.Post("https://api.bilibili.com/x/click-interface/click/web/h5?" + BusBiliUtil.GetWRidPlus("7cd084941338484aae1ad9425b84077c", "4932caff0ff746eab6f01bf08b70ac45", text), text2);
				if (Util.GetJObject(jObject2, "code") == "0")
				{
					Util.WriteLog("BiliBili", bvid, "稿件刷播", bvid + "|代理：" + address + "|开始刷播", null, writeLog: false);
					jObject4 = Util.GetJObject(Util.GetJObject<JToken>(jObject2, "responseHeaders"), "Set-Cookie");
					string cookieByKey2 = Util.GetCookieByKey(jObject4, "rpdid");
					cookie = cookie + "; rpdid=" + cookieByKey2;
					httpClientFactory.AddHeaders(null, cookie);
					DateTime et = DateTime.Now.AddSeconds((double)video_duration * playTime);
					int i = -1;
					int play_type = 1;
					while (DateTime.Now <= et || i == -1)
					{
						if (i == -1)
						{
							i = 0;
						}
						else if (DateTime.Now <= et)
						{
							i += 15;
							await Task.Delay(15000);
							realtime = i - 1;
							played_time = i;
							real_played_time = i;
							last_play_progress_time = i;
							_ = i - new Random().Next(0, 1);
							play_type = 0;
						}
						if (playTime == 1.0 && DateTime.Now >= et)
						{
							realtime = video_duration - 1;
							played_time = -1L;
							real_played_time = i;
							last_play_progress_time = video_duration;
							_ = video_duration;
							play_type = 4;
						}
						source = new Dictionary<string, string>
						{
							{
								"w_start_ts",
								w_stime.ToString()
							},
							{ "w_aid", aid },
							{ "w_dt", "2" },
							{
								"w_realtime",
								realtime.ToString()
							},
							{
								"w_played_time",
								played_time.ToString()
							},
							{
								"w_real_played_time",
								real_played_time.ToString()
							},
							{
								"w_video_duration",
								video_duration.ToString()
							},
							{
								"w_last_play_progress_time",
								last_play_progress_time.ToString()
							},
							{ "web_location", "1315873" },
							{
								"wts",
								DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString()
							}
						};
						dictionary = source.OrderBy((KeyValuePair<string, string> o) => o.Key).ToDictionary((KeyValuePair<string, string> o) => o.Key, (KeyValuePair<string, string> p) => p.Value);
						string text3 = "";
						foreach (KeyValuePair<string, string> item2 in dictionary)
						{
							text3 += ((text3 != "") ? ("&" + item2.Key + "=" + WebUtility.UrlDecode(item2.Value)) : (item2.Key + "=" + WebUtility.UrlDecode(item2.Value)));
						}
						string text4 = "start_ts=" + w_stime + "&aid=" + aid + "&cid=" + cid + "&type=3&sub_type=0&dt=2&play_type=" + play_type;
						text4 = text4 + "&realtime=" + realtime + "&played_time=" + played_time + "&real_played_time=" + real_played_time + "&refer_url=" + referer;
						text4 = text4 + "&quality=" + quality + "&video_duration=" + video_duration + "&last_play_progress_time=" + last_play_progress_time;
						text4 = text4 + "&max_play_progress_time=" + last_play_progress_time + "&outer=0&statistics={\"appId\":100,\"platform\":5,\"abtest\":\"\",\"version\":\"\"}";
						text4 = text4 + "&mobi_app=web&device=web&platform=web&spmid=333.788.0.0&from_spmid=" + from_spmid + "&session=" + session;
						text4 += "&extra={\"player_version\":\"4.9.35\"}&csrf=";
						jObject2 = await httpClientFactory.Post("https://api.bilibili.com/x/click-interface/web/heartbeat?" + BusBiliUtil.GetWRidPlus("7cd084941338484aae1ad9425b84077", "c4932caff0ff746eab6f01bf08b70ac45", text3), text4);
						if (Util.GetJObject(jObject2, "code") == "0")
						{
							if (DateTime.Now >= et || play_type == 4)
							{
								Util.WriteLog("BiliBili", bvid, "稿件刷播", bvid + "|代理：" + address + "|视频" + video_duration + "秒 已观看" + realtime + "秒 观看结束 (" + tasks.Count((Task t) => !t.IsCompleted) + ")(" + (j + 1) + "/" + bvids.Rows.Count + ")", null, writeLog: false);
								break;
							}
							Util.WriteLog("BiliBili", bvid, "稿件刷播", bvid + "|代理：" + address + "|视频" + video_duration + "秒 已观看" + realtime + "秒 (" + tasks.Count((Task t) => !t.IsCompleted) + ")(" + (j + 1) + "/" + bvids.Rows.Count + ")", null, writeLog: false);
							continue;
						}
						throw new Exception(Util.GetJObject(jObject2, "message"));
					}
					continue;
				}
				throw new Exception(Util.GetJObject(jObject2, "message"));
			}
			catch (Exception ex)
			{
				Util.WriteLog("BiliBili", bvid, "稿件刷播", bvid + "|代理：" + address + "|" + ex.Message, ConsoleColor.Red, writeLog: false);
				error++;
			}
		}
	}

	public static async Task VideoPlayCookie(DataTable bvids, DataRow drCookie, ConcurrentBag<Task> tasks)
	{
		if (drCookie["Fid"].ToString() == null)
		{
		}
		if (drCookie["FKey"].ToString() == null)
		{
		}
		string cookie = drCookie["FCookie"].ToString() ?? "";
		string name = drCookie["FName"].ToString() ?? "";
		string header = drCookie["FHeaders"].ToString() ?? "";
		string csrf = drCookie["FCsrf"].ToString() ?? "";
		string text = drCookie["FAddress"].ToString() ?? "";
		string userName = drCookie["FUserName"].ToString() ?? "";
		string password = drCookie["FPassword"].ToString() ?? "";
		string identifying = drCookie["FIdentifying"].ToString() ?? "";
		int error = 0;
		try
		{
			HttpClientHandler defaultHandler = null;
			if (text != "")
			{
				defaultHandler = new HttpClientHandler
				{
					Proxy = new WebProxy
					{
						Address = new Uri(text),
						Credentials = new NetworkCredential(userName, password)
					}
				};
			}
			Dictionary<string, string> dictionary = HttpClientFactory.FormataHeader(header, cookie);
			dictionary.Remove("Accept-Language");
			HttpClientFactory httpClientFactory = new HttpClientFactory(name, dictionary, defaultHandler);
			string name2 = name;
			Util.WriteLog("BiliBili", name2, "稿件刷播", await httpClientFactory.GetIp(), null, writeLog: false);
			JObject jObject = await httpClientFactory.Get("https://api.bilibili.com/x/web-interface/nav");
			if (Util.GetJObject(jObject, "code") == "0")
			{
				string imgKey = MyRegex().Split(Util.GetJObject(Util.GetJObject<JToken>(jObject["data"], "wbi_img"), "img_url")).ToList().Last()
					.Replace(".png", "");
				string subKey = MyRegex().Split(Util.GetJObject(Util.GetJObject<JToken>(jObject["data"], "wbi_img"), "sub_url")).ToList().Last()
					.Replace(".png", "");
				for (int j = 0; j < bvids.Rows.Count; j++)
				{
					if (error == 10)
					{
						throw new Exception("错误次数过多,停止刷播！");
					}
					int play = int.Parse(Util.GetJObject(bvids.Rows[j], "FPlayer"));
					name2 = Util.GetJObject(bvids.Rows[j], "FBvid");
					new Random((int)DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()).Next(11, 100);
					new Random((int)DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()).Next(1, 4);
					string from_spmid = "333.337.search-card.all.click";
					Dictionary<string, string> temp = new Dictionary<string, string> { ["Referer"] = "https://www.bilibili.com/video/" + name2 + "/?spm_id_from=" + from_spmid };
					try
					{
						JObject jObject2 = await httpClientFactory.Get("https://api.bilibili.com/x/web-interface/view?bvid=" + name2, temp);
						if (!(Util.GetJObject(jObject2, "code") == "0"))
						{
							continue;
						}
						int video_duration = Util.GetJObject<int>(jObject2["data"], "duration");
						Util.GetJObject<int>(Util.GetJObject<JToken>(jObject2["data"], "stat"), "view");
						Util.GetJObject<int>(Util.GetJObject<JToken>(jObject2["data"], "stat"), "share");
						string aid = Util.GetJObject(jObject2["data"], "aid");
						string cid = Util.GetJObject(Util.GetJObject<JArray>(jObject2["data"], "pages")?[0], "cid");
						string jObject3 = Util.GetJObject(jObject2["data"], "title");
						double playTime = (double)play * 1.0 / 100.0;
						if (!(name2 != "") || !(aid != "") || !(cid != ""))
						{
							continue;
						}
						int num = new Random().Next(2, 5);
						if (num > jObject3.Length)
						{
							num = jObject3.Length;
						}
						int startIndex = new Random().Next(0, jObject3.Length - num + 1);
						jObject3 = jObject3.Substring(startIndex, num);
						string referer = "https://search.bilibili.com/all?keyword=" + HttpUtility.UrlEncode(jObject3) + "&from_source=webtop_search&spm_id_from=333.1007&search_source=5";
						long w_stime = DateTimeOffset.Now.ToUnixTimeSeconds();
						long w_ftime = DateTimeOffset.Now.ToUnixTimeSeconds();
						long realtime = 0L;
						long played_time = 0L;
						long real_played_time = 0L;
						long last_play_progress_time = 0L;
						int quality = 80;
						string session = Util.CalcMD5(w_stime.ToString());
						new Dictionary<string, string>();
						new Dictionary<string, string>();
						jObject2 = await httpClientFactory.Get("https://api.bilibili.com/x/click-interface/click/now", temp);
						if (Util.GetJObject(jObject2, "code") == "0")
						{
							w_stime = Util.GetJObject<long>(jObject2["data"], "now");
						}
						jObject2 = await httpClientFactory.Get("https://api.bilibili.com/x/click-interface/click/now", temp);
						if (Util.GetJObject(jObject2, "code") == "0")
						{
							w_ftime = Util.GetJObject<long>(jObject2["data"], "now");
						}
						Dictionary<string, string> source = new Dictionary<string, string>
						{
							{ "w_aid", aid },
							{ "w_part", "1" },
							{
								"w_ftime",
								w_ftime.ToString()
							},
							{
								"w_stime",
								w_stime.ToString()
							},
							{ "w_type", "3" },
							{ "web_location", "1315873" },
							{
								"wts",
								DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString()
							}
						};
						Dictionary<string, string> dictionary2 = source.OrderBy((KeyValuePair<string, string> o) => o.Key).ToDictionary((KeyValuePair<string, string> o) => o.Key, (KeyValuePair<string, string> p) => p.Value);
						string text2 = "";
						foreach (KeyValuePair<string, string> item in dictionary2)
						{
							text2 += ((text2 != "") ? ("&" + item.Key + "=" + WebUtility.UrlDecode(item.Value)) : (item.Key + "=" + WebUtility.UrlDecode(item.Value)));
						}
						string text3 = "mid=" + identifying + "&aid=" + aid + "&cid=" + cid + "&part=1&lv=6&ftime=" + w_ftime + "&stime=" + w_stime + "&type=3&sub_type=0&refer_url=" + referer;
						text3 = text3 + "&outer=0&statistics={\"appId\":100,\"platform\":5,\"abtest\":\"\",\"version\":\"\"}&mobi_app=web&device=web&platform=web&spmid=333.788.0.0&from_spmid=" + from_spmid + "&session=" + session + "&csrf=" + csrf;
						jObject2 = await httpClientFactory.Post("https://api.bilibili.com/x/click-interface/click/web/h5?" + BusBiliUtil.GetWRidPlus(imgKey, subKey, text2), text3, temp);
						if (Util.GetJObject(jObject2, "code") == "0")
						{
							Util.WriteLog("BiliBili", name2, "稿件刷播", name2 + "|账号：" + name + "|开始刷播", null, writeLog: false);
							DateTime et = DateTime.Now.AddSeconds((double)video_duration * playTime);
							int i = -1;
							int play_type = 1;
							while (DateTime.Now <= et || i == -1)
							{
								if (i == -1)
								{
									i = 0;
								}
								else if (DateTime.Now <= et)
								{
									i += 15;
									await Task.Delay(15000);
									realtime = i - 1;
									played_time = i;
									real_played_time = i;
									last_play_progress_time = i;
									_ = i - new Random().Next(0, 1);
									play_type = 0;
								}
								if (playTime == 1.0 && DateTime.Now >= et)
								{
									realtime = video_duration - 1;
									played_time = -1L;
									real_played_time = i;
									last_play_progress_time = video_duration;
									_ = video_duration;
									play_type = 4;
								}
								source = new Dictionary<string, string>
								{
									{
										"w_start_ts",
										w_stime.ToString()
									},
									{ "w_mid", identifying },
									{ "w_aid", aid },
									{ "w_dt", "2" },
									{
										"w_realtime",
										realtime.ToString()
									},
									{
										"w_played_time",
										played_time.ToString()
									},
									{
										"w_real_played_time",
										real_played_time.ToString()
									},
									{
										"w_video_duration",
										video_duration.ToString()
									},
									{
										"w_last_play_progress_time",
										last_play_progress_time.ToString()
									},
									{ "web_location", "1315873" },
									{
										"wts",
										DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString()
									}
								};
								dictionary2 = source.OrderBy((KeyValuePair<string, string> o) => o.Key).ToDictionary((KeyValuePair<string, string> o) => o.Key, (KeyValuePair<string, string> p) => p.Value);
								string text4 = "";
								foreach (KeyValuePair<string, string> item2 in dictionary2)
								{
									text4 += ((text4 != "") ? ("&" + item2.Key + "=" + WebUtility.UrlDecode(item2.Value)) : (item2.Key + "=" + WebUtility.UrlDecode(item2.Value)));
								}
								string text5 = "start_ts=" + w_stime + "&mid=" + identifying + "&aid=" + aid + "&cid=" + cid + "&type=3&sub_type=0&dt=2&play_type=" + play_type;
								text5 = text5 + "&realtime=" + realtime + "&played_time=" + played_time + "&real_played_time=" + real_played_time + "&refer_url=" + referer;
								text5 = text5 + "&quality=" + quality + "&video_duration=" + video_duration + "&last_play_progress_time=" + last_play_progress_time;
								text5 = text5 + "&max_play_progress_time=" + last_play_progress_time + "&outer=0&statistics={\"appId\":100,\"platform\":5,\"abtest\":\"\",\"version\":\"\"}";
								text5 = text5 + "&mobi_app=web&device=web&platform=web&spmid=333.788.0.0&from_spmid=" + from_spmid + "&session=" + session + "&csrf=" + csrf;
								jObject2 = await httpClientFactory.Post("https://api.bilibili.com/x/click-interface/web/heartbeat?" + BusBiliUtil.GetWRidPlus(imgKey, subKey, text4), text5, temp);
								if (Util.GetJObject(jObject2, "code") == "0")
								{
									if (DateTime.Now >= et || play_type == 4)
									{
										Util.WriteLog("BiliBili", name2, "稿件刷播", name2 + "|账号：" + name + "|视频" + video_duration + "秒 已观看" + realtime + "秒 观看结束 (" + tasks.Count((Task t) => !t.IsCompleted) + ")(" + (j + 1) + "/" + bvids.Rows.Count + ")", null, writeLog: false);
										break;
									}
									Util.WriteLog("BiliBili", name2, "稿件刷播", name2 + "|账号：" + name + "|视频" + video_duration + "秒 已观看" + realtime + "秒 (" + tasks.Count((Task t) => !t.IsCompleted) + ")(" + (j + 1) + "/" + bvids.Rows.Count + ")", null, writeLog: false);
									continue;
								}
								throw new Exception(Util.GetJObject(jObject2, "message"));
							}
							continue;
						}
						throw new Exception(Util.GetJObject(jObject2, "message"));
					}
					catch (Exception ex)
					{
						Util.WriteLog("BiliBili", name2, "稿件刷播", name2 + "|账号：" + name + "|" + ex.Message, ConsoleColor.Red, writeLog: false);
						error++;
					}
				}
				return;
			}
			throw new Exception(Util.GetJObject(jObject, "message"));
		}
		catch (Exception ex2)
		{
			Util.WriteLog("BiliBili", name, "稿件刷播", ex2.Message, ConsoleColor.Red, writeLog: false);
		}
	}

	private static readonly Regex _myRegex = new Regex("wbi/", RegexOptions.IgnoreCase);
	
	private static Regex MyRegex()
	{
		return _myRegex;
	}
}
