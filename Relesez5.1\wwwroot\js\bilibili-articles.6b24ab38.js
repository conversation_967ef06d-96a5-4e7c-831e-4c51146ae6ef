"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[8161,997,6860,1147],{7822:function(e,t,a){a.r(t),a.d(t,{default:function(){return S}});var l=a(641),i=a(2644);const s={class:"left-panel"},o={style:{float:"left",width:"150px"}},r={style:{float:"right",color:"var(--el-text-color-secondary)"}},d={style:{display:"none"}},c={style:{display:"none"}},n=(0,l.Lk)("br",null,null,-1),h={class:"right-panel"},p={class:"right-panel-search"},u={key:0},b=["href"],g={key:1},m={key:2};function k(e,t,a,k,j,y){const f=(0,l.g2)("el-option"),w=(0,l.g2)("el-select"),O=(0,l.g2)("sc-select"),F=(0,l.g2)("el-date-picker"),S=(0,l.g2)("el-button"),v=(0,l.g2)("el-tooltip"),C=(0,l.g2)("el-button-group"),$=(0,l.g2)("el-icon"),I=(0,l.g2)("el-dropdown-item"),_=(0,l.g2)("sc-upload-file"),A=(0,l.g2)("el-dropdown-menu"),x=(0,l.g2)("el-dropdown"),P=(0,l.g2)("el-input"),V=(0,l.g2)("el-header"),T=(0,l.g2)("el-table-column"),W=(0,l.g2)("el-popconfirm"),X=(0,l.g2)("scTable"),D=(0,l.g2)("el-main"),N=(0,l.g2)("el-container"),L=(0,l.g2)("materialDialog"),E=(0,l.g2)("titleDialog"),B=(0,l.gN)("auths"),K=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)(l.FK,null,[(0,l.bF)(N,null,{default:(0,l.k6)((()=>[(0,l.bF)(V,null,{default:(0,l.k6)((()=>[(0,l.Lk)("div",s,[(0,l.bF)(w,{modelValue:j.jObjectSearch.areaId,"onUpdate:modelValue":t[0]||(t[0]=e=>j.jObjectSearch.areaId=e),filterable:"",onChange:t[1]||(t[1]=e=>{j.jObjectSearch.taskId="",y.upsearch()}),style:{"padding-right":"10px",width:"180px"}},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(j.area,(e=>((0,l.uX)(),(0,l.Wv)(f,{key:e.Fid,label:e.FName,value:e.Fid},{default:(0,l.k6)((()=>[(0,l.Lk)("span",o,(0,i.v_)(e.FName),1),(0,l.Lk)("span",r,(0,i.v_)(e.FArticlesDate),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"]),(0,l.bF)(w,{modelValue:j.jObjectSearch.type,"onUpdate:modelValue":t[2]||(t[2]=e=>j.jObjectSearch.type=e),filterable:"",onChange:y.upsearch,style:{"padding-right":"10px",width:"100px"}},{default:(0,l.k6)((()=>[((0,l.uX)(),(0,l.CE)(l.FK,null,(0,l.pI)([{label:"播放量",value:1},{label:"BV号",value:2},{label:"未投稿",value:3},{label:"已锁定",value:9},{label:"已申诉",value:10}],(e=>(0,l.bF)(f,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue","onChange"]),(0,l.bF)(w,{modelValue:j.jObjectSearch.taskStatus,"onUpdate:modelValue":t[3]||(t[3]=e=>j.jObjectSearch.taskStatus=e),filterable:"",onChange:y.upsearch,placeholder:"任务状态",style:{"padding-right":"10px",width:"110px"}},{default:(0,l.k6)((()=>[((0,l.uX)(),(0,l.CE)(l.FK,null,(0,l.pI)([{label:"未完成",value:0},{label:"已完成",value:1},{label:"已领取",value:3}],(e=>(0,l.bF)(f,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue","onChange"]),((0,l.uX)(),(0,l.Wv)(O,{clearable:"",modelValue:j.jObjectSearch.taskId,"onUpdate:modelValue":t[4]||(t[4]=e=>j.jObjectSearch.taskId=e),params:{jObjectSearch:{areaId:j.jObjectSearch.areaId,type:"投稿"}},key:j.jObjectSearch.areaId,apiObj:e.$API.biliAreaTask.getAreaTaskList,prop:{label:"FTaskName",value:"Fid"},placeholder:"筛选选中任务的账号（投稿）",style:{width:"245px","padding-right":"10px"},onChange:y.upsearch},null,8,["modelValue","params","apiObj","onChange"])),(0,l.bF)(F,{modelValue:j.date,"onUpdate:modelValue":t[5]||(t[5]=e=>j.date=e),type:"date",onChange:y.upsearchDate,style:{width:"140px","margin-right":"10px"}},null,8,["modelValue","onChange"]),(0,l.bF)(S,{type:"primary",icon:"el-icon-refresh",onClick:y.update,disabled:j.loading},{default:(0,l.k6)((()=>[(0,l.eW)(" 更新数据")])),_:1},8,["onClick","disabled"]),(0,l.bF)(C,{style:{display:"flex","padding-left":"10px"}},{default:(0,l.k6)((()=>[(0,l.bF)(S,{type:"primary",icon:"el-icon-promotion",onClick:y.material,disabled:j.loading,style:{"border-radius":"4px 0px 0px 4px"}},{default:(0,l.k6)((()=>[(0,l.eW)("开始投稿")])),_:1},8,["onClick","disabled"]),(0,l.bF)(v,{class:"box-item",effect:"dark",content:"简易投稿，标题："+(0==j.titleNum?"请点击→高级功能→标题管理→上传":j.titleNum+" 个")+"，视频："+j.videoNum+" 个。",placement:"top"},{default:(0,l.k6)((()=>[(0,l.bF)(S,{onClick:y.easySubmit,type:"primary",icon:"el-icon-position",disabled:j.loading,style:{"border-radius":"0px 4px 4px 0px"}},null,8,["onClick","disabled"])])),_:1},8,["content"])])),_:1}),(0,l.bF)(x,{trigger:"click",class:"m-r"},{dropdown:(0,l.k6)((()=>[(0,l.bF)(A,{class:"drop-common"},{default:(0,l.k6)((()=>[(0,l.bF)(I,{onClick:y.openTitle,divided:""},{default:(0,l.k6)((()=>[(0,l.eW)("标题管理（"+(0,i.v_)(j.jObjectSearch.areaName)+"）",1)])),_:1},8,["onClick"]),(0,l.bF)(I,{onClick:y.openFolder,divided:""},{default:(0,l.k6)((()=>[(0,l.eW)("视频管理（"+(0,i.v_)(j.jObjectSearch.areaName)+"）",1)])),_:1},8,["onClick"]),(0,l.bF)(I,{onClick:y.splitVideo,divided:""},{default:(0,l.k6)((()=>[(0,l.bF)(v,{class:"box-item",effect:"dark",content:"拆分视频自动保存到对应分区",placement:"right"},{default:(0,l.k6)((()=>[(0,l.eW)(" 拆分视频（"+(0,i.v_)(j.jObjectSearch.areaName)+"） ",1)])),_:1})])),_:1},8,["onClick"]),(0,l.Lk)("div",d,[(0,l.bF)(_,{ref:"splitVideo",multiple:!0,"show-file-list":!1,"on-progress":y.onProgress,"on-success":y.fileSuccess,params:{jObjectParam:{path:"Bili\\Cookies\\"}},class:"upload-demo"},{default:(0,l.k6)((()=>[(0,l.bF)(I,{divided:""},{default:(0,l.k6)((()=>[(0,l.eW)("拆分视频")])),_:1})])),_:1},8,["on-progress","on-success"])]),(0,l.bF)(I,{onClick:y.download2,divided:""},{default:(0,l.k6)((()=>[(0,l.eW)("导出BV号（刷播格式）")])),_:1},8,["onClick"]),(0,l.bF)(I,{onClick:y.exportCookie,divided:""},{default:(0,l.k6)((()=>[(0,l.eW)("导出账号（勾选）")])),_:1},8,["onClick"]),(0,l.bo)(((0,l.uX)(),(0,l.CE)("div",null,[(0,l.bF)(I,{onClick:y.downloadAppeal,divided:""},{default:(0,l.k6)((()=>[(0,l.eW)("申诉模版")])),_:1},8,["onClick"]),(0,l.bF)(I,{onClick:y.uploadAppeal,divided:""},{default:(0,l.k6)((()=>[(0,l.bF)(v,{class:"box-item",effect:"dark",content:"导入申诉模版，勾选条数大于导入内容时，会重复使用",placement:"right"},{default:(0,l.k6)((()=>[(0,l.eW)(" 账号申诉（勾选） ")])),_:1})])),_:1},8,["onClick"]),(0,l.Lk)("div",c,[(0,l.bF)(_,{ref:"appeal",multiple:!0,"show-file-list":!1,"on-progress":y.onProgress,"on-success":y.fileSuccessAppeal,params:{jObjectParam:{path:"Bili\\Cookies\\"}},class:"upload-demo"},{default:(0,l.k6)((()=>[(0,l.bF)(I,{divided:""},{default:(0,l.k6)((()=>[(0,l.eW)("导入模版")])),_:1})])),_:1},8,["on-progress","on-success"])]),(0,l.bF)(I,{onClick:t[6]||(t[6]=e=>y.disable(0,"")),divided:""},{default:(0,l.k6)((()=>[(0,l.eW)("禁用投稿")])),_:1}),(0,l.bF)(I,{onClick:t[7]||(t[7]=e=>y.disable(0,0)),divided:""},{default:(0,l.k6)((()=>[(0,l.eW)("禁用全部")])),_:1}),(0,l.bF)(I,{onClick:y.delPlus,divided:""},{default:(0,l.k6)((()=>[(0,l.bF)(v,{class:"box-item",effect:"dark",content:"同时删除服务器信息，请谨慎操作！",placement:"right"},{default:(0,l.k6)((()=>[(0,l.eW)(" 删除账号 ")])),_:1})])),_:1},8,["onClick"])])),[[B,["vip"]]]),(0,l.bF)(I,{divided:""})])),_:1})])),default:(0,l.k6)((()=>[(0,l.bF)(S,{type:"primary",disabled:j.loading},{default:(0,l.k6)((()=>[(0,l.eW)(" 高级功能   "),(0,l.bF)($,null,{default:(0,l.k6)((()=>[((0,l.uX)(),(0,l.Wv)((0,l.$y)("el-icon-arrow-down-bold")))])),_:1})])),_:1},8,["disabled"])])),_:1}),(0,l.bF)(v,{placement:"top"},{content:(0,l.k6)((()=>[(0,l.eW)("*▲* 稿件流量受影响"),n,(0,l.eW)("*X*  稿件已锁定")])),default:(0,l.k6)((()=>[(0,l.bF)(S,{icon:"el-icon-Warning",style:{"margin-left":"10px"}},{default:(0,l.k6)((()=>[(0,l.eW)("图例")])),_:1})])),_:1})]),(0,l.Lk)("div",h,[(0,l.Lk)("div",p,[(0,l.bF)(P,{modelValue:j.jObjectSearch.search,"onUpdate:modelValue":t[8]||(t[8]=e=>j.jObjectSearch.search=e),placeholder:"账号名称 / (例:2025-06-15)"},null,8,["modelValue"]),(0,l.bF)(S,{type:"primary",icon:"el-icon-search",onClick:y.upsearch},{default:(0,l.k6)((()=>[(0,l.eW)(" 查询")])),_:1},8,["onClick"])])])])),_:1}),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(D,{class:"nopadding"},{default:(0,l.k6)((()=>[(0,l.bF)(X,{"cell-style":y.cellStyle,ref:"table",apiObj:j.apiObj,border:"",params:{jObjectSearch:j.jObjectSearch},stripe:"",remoteSort:"",remoteFilter:""},{default:(0,l.k6)((()=>[(0,l.bF)(T,{type:"selection",width:"50"}),(0,l.bF)(T,{label:"账号名称",prop:"FCookieName",align:"center",width:"140",sortable:"","show-overflow-tooltip":""}),(0,l.bF)(T,{label:"最后更新时间",prop:"FDate",align:"center",width:"100","show-overflow-tooltip":""}),10==j.jObjectSearch.type?((0,l.uX)(),(0,l.Wv)(T,{key:0,label:"稿件状态",prop:"FAppeal","header-align":"center",sortable:"",width:"400","show-overflow-tooltip":""})):(0,l.Q3)("",!0),(0,l.bF)(T,{label:j.total,prop:"FTotal",align:"center",width:"105",sortable:"","show-overflow-tooltip":""},null,8,["label"]),((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(j.columns,(e=>((0,l.uX)(),(0,l.Wv)(T,{sortable:"",key:e,label:e,prop:e,align:"center",width:2==j.jObjectSearch.type?140:80,"show-overflow-tooltip":""},{default:(0,l.k6)((t=>[2==j.jObjectSearch.type&&"open"==j.type?((0,l.uX)(),(0,l.CE)("div",u,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(String(t.row[e]).split(" "),(e=>((0,l.uX)(),(0,l.CE)("a",{key:e,href:"https://www.bilibili.com/"+e,target:"_blank",style:{color:"blue"}},(0,i.v_)(e)+"  ",9,b)))),128))])):2==j.jObjectSearch.type&&"del"==j.type?((0,l.uX)(),(0,l.CE)("div",g,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(String(t.row[e]).split(" "),(e=>((0,l.uX)(),(0,l.CE)("div",{key:e},[(0,l.bF)(W,{title:"确定删除?",onConfirm:a=>y.delArticles(e,t.row),width:"190"},{reference:(0,l.k6)((()=>[(0,l.bF)(S,{link:"",type:"danger"},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(e)+"  ",1)])),_:2},1024)])),_:2},1032,["onConfirm"])])))),128))])):((0,l.uX)(),(0,l.CE)("span",m,(0,i.v_)(t.row[e]),1))])),_:2},1032,["label","prop","width"])))),128))])),_:1},8,["cell-style","apiObj","params"])])),_:1})),[[K,j.loading]])])),_:1}),j.dialog.materia?((0,l.uX)(),(0,l.Wv)(L,{key:0,ref:"materiaDialog",onClosed:t[9]||(t[9]=e=>j.dialog.materia=!1),onSuccess:t[10]||(t[10]=e=>j.files=[])},null,512)):(0,l.Q3)("",!0),j.dialog.title?((0,l.uX)(),(0,l.Wv)(E,{key:1,ref:"titleDialog",onClosed:t[11]||(t[11]=e=>j.dialog.title=!1),onSuccess:y.showTitleVideo},null,8,["onSuccess"])):(0,l.Q3)("",!0)],64)}a(8743);var j=a(9951),y=a(1076),f=a(3959),w={name:"biliArticles",components:{materialDialog:j["default"],titleDialog:y["default"]},data(){return{apiObj:null,loading:!1,files:[],date:"",dialog:{materia:!1},jObjectSearch:{search:"",type:1,taskId:"",areaName:"",taskStatus:0},area:[],columns:[],type:"open",total:"合计",videoNum:0,titleNum:0}},async created(){let e=await this.$API.biliArea.getAreaList.post({jObjectSearch:{}});if(0==e.code&&(this.area=e.data.rows,this.area.length>0)){this.jObjectSearch.areaId=this.area[0].Fid,this.jObjectSearch.areaName=this.area[0].FName;let e=0;this.date=this.$TOOL.data.get("articles_"+this.jObjectSearch.areaId+"_date",this.area[0].FArticlesDate);for(let t=new Date;t>=new Date(this.date);t=new Date(t.getTime()-864e5))this.columns.push(String(t.getMonth()+1)+"."+t.getDate()),e++;this.total="合计-"+e}this.jObjectSearch.date=this.$TOOL.dateFormat(new Date(this.date),"yyyy-MM-dd"),this.apiObj=this.$API.biliArticles.getArticlesList,this.showTitleVideo()},methods:{downloadAppeal(){const e=[["电话号码","申述内容","邮箱地址"],["135XXXXXXXX","申诉内容别重复，尽量勾选多少号就写多少条","可以不填"]],t=f.Wp.aoa_to_sheet(e),a=f.Wp.book_new();f.Wp.book_append_sheet(a,t,"Sheet1"),f._h(a,"申诉模版.xls")},upsearch(){this.columns=[];let e=0,t=this.area.find((e=>e.Fid==this.jObjectSearch.areaId)).FArticlesDate;this.jObjectSearch.areaName=this.area.find((e=>e.Fid==this.jObjectSearch.areaId)).FName,this.date=this.$TOOL.data.get("articles_"+this.jObjectSearch.areaId+"_date",t);for(let a=new Date;a>=new Date(this.date);a=new Date(a.getTime()-864e5))this.columns.push(String(a.getMonth()+1)+"."+a.getDate()),e++;this.total="合计-"+e,this.$refs.table.upData({jObjectSearch:{...this.jObjectSearch,date:this.$TOOL.dateFormat(new Date(this.date),"yyyy-MM-dd")}}),this.type="open",this.showTitleVideo()},upsearchDate(){let e=this.area.find((e=>e.Fid==this.jObjectSearch.areaId)).FArticlesDate;new Date(e)>this.date&&(this.$message.warning("不能小于当前分区的激励日期，已自动调整到"+e),this.date=e),this.jObjectSearch.date=this.$TOOL.dateFormat(new Date(this.date),"yyyy-MM-dd"),this.$TOOL.data.set("articles_"+this.jObjectSearch.areaId+"_date",this.date),this.upsearch()},async update(){let e=this.$refs.table.getSelectionRows();if(0==e.length)return void this.$alert("请选择账号！","提示",{type:"error"});this.loading=!0;let t=await this.$API.biliArticles.updateArticles.post({jObjectParam:{array:e}});this.loading=!1,0==t.code?(this.$message.success("操作成功！"),this.upsearch()):this.$alert(t.message,"提示",{type:"error"})},material(){let e=this.$refs.table.getSelectionRows();0!=e.length?(this.dialog.materia=!0,this.$nextTick((()=>{this.$refs.materiaDialog.open("add",this.area.find((e=>e.Fid===this.jObjectSearch.areaId)),e,this.files)}))):this.$alert("请选择账号！","提示",{type:"error"})},cellStyle(e){return"▲"==e.row[e.column.property]?{color:"#e6a23c"}:"X"==e.row[e.column.property]?{color:"red"}:void 0},async download(){let e=this.$refs.table.getSelectionRows(),t=e.map((e=>e.FCookieId)).join(",");this.loading=!0;let a=await this.$API.biliArticles.getArticlesList.post({jObjectSearch:{export:1,type:2,areaId:this.jObjectSearch.areaId,search:this.jObjectSearch.search,taskId:this.jObjectSearch.taskId,cookieId:t}});if(this.loading=!1,0==a.code){let e=document.createElement("a");e.style="display: none",e.target="_blank",e.download="稿件BV号",e.href=a.data,document.body.appendChild(e),e.click(),document.body.removeChild(e)}else this.$alert(a.message,"提示",{type:"error"})},async download2(){this.$prompt("导出小于输入值播放量的Bvid","导出刷播格式Bvid",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnPressEscape:!0,closeOnClickModal:!0,autofocus:!0,inputType:"number"}).then((async({value:e})=>{let t=this.$refs.table.getSelectionRows(),a=t.map((e=>e.FCookieId)).join(",");this.loading=!0;let l=await this.$API.biliArticles.getArticlesList.post({jObjectSearch:{date:this.jObjectSearch.date,times:e,export:1,type:4,areaId:this.jObjectSearch.areaId,search:this.jObjectSearch.search,taskId:this.jObjectSearch.taskId,cookieId:a}});if(this.loading=!1,0==l.code){let e=document.createElement("a");e.style="display: none",e.target="_blank",e.download="稿件BV号",e.href=l.data,document.body.appendChild(e),e.click(),document.body.removeChild(e)}else this.$alert(l.message,"提示",{type:"error"})}))},fileSuccess(e){this.loading=!1,0==e.code?this.$prompt("视频时间。（单位：秒）  （0→自适应）  （注意分区，视频拆完自动保存到对应分区）","视频时间",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnPressEscape:!1,closeOnClickModal:!1,autofocus:!1,inputType:"number",inputValue:0}).then((async({value:t})=>{this.$message.success("上传成功，正在拆分，结果请看控制台..."),await this.$API.biliArticles.splitVideo.post({jObjectParam:{path:e.data.src,areaId:this.jObjectSearch.areaId,time:t}})})):this.$alert(e.message,"提示",{type:"error"})},fileSuccessAppeal(e){if(this.loading=!1,0==e.code){let t=this.$refs.table.getSelectionRows(),a=t.map((e=>e.FCookieId)).join(",");this.$confirm("确定申诉？","提示",{type:"warning"}).then((()=>{this.$API.biliArticles.uploadAppeal.post({jObjectParam:{src:e.data.src,cookieId:a}}),this.$message.success("结果请看控制台...")}))}else this.$alert(e.message,"提示",{type:"error"})},onProgress(){0==this.loading&&this.$message.warning("正在上传文件..."),this.loading=!0},del(){this.jObjectSearch.type=2,this.upsearch(),this.type="del"},async delArticles(e,t){let a=await this.$API.biliArticles.delArticles.post({jObjectParam:{val:e,cookieId:t.FCookieId}});0==a.code?this.$message.success("操作成功，请手动更新数据！"):this.$alert(a.message,"提示",{type:"error"})},async disable(e,t){let a=this.$refs.table.getSelectionRows();0!=a.length?this.$confirm("确认禁用投稿？（请去分组管理恢复）","提示",{type:"warning"}).then((async()=>{let l="";for(let e=0;e<a.length;e++)l+=""==l?a[e].FKey:","+a[e].FKey;let i=await this.$API.biliGroup.setOption.post({jObjectParam:{key:l,areaId:this.jObjectSearch.areaId,aricles:e,mileage:t}});0==i.code?(this.$message.success("操作完成！"),this.upsearch()):this.$alert(i.message,"提示",{type:"error"})})).catch((()=>{})):this.$alert("请至少选择一条数据","提示",{type:"warning"})},async delPlus(){this.$confirm("确认删除服务器未到期的账号！（该操作不可逆）","提示",{type:"warning"}).then((async()=>{try{let e=this.$refs.table.getSelectionRows();if(0==e.length)throw new Error("请选择账号！");let t=[];for(let l in e)t.push({Fid:e[l].FCookieId,FIdentifying:e[l].FIdentifying,FKey:e[l].FKey});this.loading=!0;let a=await this.$API.biliCookies.delCookiePlus.post({jObjectParam:{array:t}});if(this.loading=!1,0!=a.code)throw new Error(a.message);this.$message.success(a.message),this.upsearch()}catch(e){this.$alert(e.message,"提示",{type:"error"})}}))},easySubmit(e){let t=this.$refs.table.getSelectionRows();0!=t.length?this.videoNum<=t.length?this.$alert("视频不足！","提示",{type:"error"}):this.titleNum<=t.length?this.$alert("标题不足！","提示",{type:"error"}):this.$confirm("确定执行简易投稿？","提示",{type:"warning"}).then((async()=>{const a=t.reduce(((e,t,a)=>(a>0&&(e+=", "),e+t.FCookieId)),"");e=await this.$API.biliArticles.easySubmit.post({jObjectParam:{areaName:this.jObjectSearch.areaName,areaId:this.jObjectSearch.areaId,ids:a,time:this.$TOOL.data.get("articles_time",1),num:this.$TOOL.data.get("articles_num",6),title:this.$TOOL.data.get("articlesTitle",1)}}),0==e.code?this.$message.success("请查看控制台！"):this.$alert(e.message,"提示",{type:"error"})})).catch((()=>{})):this.$alert("请选择账号！","提示",{type:"error"})},openTitle(){this.dialog.title=!0,this.$nextTick((()=>{this.$refs.titleDialog.open("add",this.area.find((e=>e.Fid===this.jObjectSearch.areaId)))}))},splitVideo(){this.$refs.splitVideo.handleStart()},uploadAppeal(){let e=this.$refs.table.getSelectionRows();0!=e.length?this.$refs.appeal.handleStart():this.$alert("请选择账号！","提示",{type:"error"})},async showTitleVideo(){let e=await this.$API.biliArticles.getTitleVideo.post({jObjectSearch:this.jObjectSearch});0==e.code&&(this.videoNum=e.data.videoNum,this.titleNum=e.data.titleNum)},async openFolder(){await this.$API.biliLive.openFolder.post({jObjectParam:{wwwRootPath:"Bili\\Articles\\"+this.jObjectSearch.areaName}})},async exportCookie(){let e=this.$refs.table.getSelectionRows();if(0==e.length)return void this.$alert("请选择账号！","提示",{type:"error"});let t=[];for(let l in e)t.push({Fid:e[l].FCookieId});this.updateLoading=!0;let a=await this.$API.biliCookies.exportCookie.post({jObjectParam:{array:t}});if(this.updateLoading=!1,0==a.code){let e=document.createElement("a");e.style="display: none",e.target="_blank",e.download="Cookies",e.href=a.data,document.body.appendChild(e),e.click(),document.body.removeChild(e),this.upsearch()}else this.$alert(a.message,"提示",{type:"error"})}}},O=a(6262);const F=(0,O.A)(w,[["render",k]]);var S=F}}]);