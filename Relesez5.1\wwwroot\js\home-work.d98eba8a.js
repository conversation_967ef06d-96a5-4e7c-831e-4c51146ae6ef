"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[7212,9289],{8049:function(e,t,u){u.r(t),u.d(t,{default:function(){return o}});var a=u(641);function n(e,t,u,n,r,l){const s=(0,a.g2)("el-alert"),d=(0,a.g2)("myapp"),o=(0,a.g2)("el-card"),c=(0,a.g2)("el-col"),f=(0,a.g2)("el-row"),i=(0,a.g2)("el-main");return(0,a.uX)(),(0,a.Wv)(i,null,{default:(0,a.k6)((()=>[(0,a.bF)(s,{title:"根据角色配置,可让不同角色访问不同的控制台视图,参数值在登录成功后返回 dashboard:{type}",type:"success",style:{"margin-bottom":"20px"}}),(0,a.bF)(f,{gutter:15},{default:(0,a.k6)((()=>[(0,a.bF)(c,{lg:24},{default:(0,a.k6)((()=>[(0,a.bF)(o,{shadow:"never",header:"我的常用"},{default:(0,a.k6)((()=>[(0,a.bF)(d)])),_:1})])),_:1})])),_:1})])),_:1})}var r=u(4631),l={components:{myapp:r["default"]},data(){return{}},mounted(){this.$emit("on-mounted")},methods:{}},s=u(6262);const d=(0,s.A)(l,[["render",n]]);var o=d}}]);