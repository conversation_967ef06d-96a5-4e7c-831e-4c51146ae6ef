(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[8749],{6511:function(e,l,a){"use strict";a.r(l);var t=a(9841);const o={};let i=a(5868);i.keys().forEach((e=>{let l=i(e);o[e.replace(/^\.\/(.*)\.\w+$/,"$1")]=l.default})),l["default"]=(0,t.IG)(o)},695:function(e,l,a){"use strict";a.d(l,{A:function(){return L}});var t=a(641),o=a(2644),i=a(9322);const s=e=>((0,t.Qi)("data-v-1737b8a6"),e=e(),(0,t.jt)(),e),r={class:"sc-file-select"},d={class:"sc-file-select__side"},n={class:"sc-file-select__side-menu"},u={class:"el-tree-node__label"},c={key:0,class:"sc-file-select__side-msg"},m={class:"sc-file-select__files"},p={class:"sc-file-select__top"},f={key:0,class:"upload"},h={class:"tips"},b={class:"keyword"},g={class:"sc-file-select__list"},F={class:"sc-file-select__item__file"},k={class:"sc-file-select__item__upload"},v=["onClick"],y={class:"sc-file-select__item__file"},_={key:0,class:"sc-file-select__item__checkbox"},V={key:1,class:"sc-file-select__item__select"},w=s((()=>(0,t.Lk)("div",{class:"sc-file-select__item__box"},null,-1))),C={key:3,class:"item-file item-file-doc"},S={key:1,class:"sc-icon-file-list-fill",style:{color:"#999"}},j=["title"],$={class:"sc-file-select__pagination"},O={class:"sc-file-select__do"};function U(e,l,a,s,U,I){const A=(0,t.g2)("el-icon-folder"),x=(0,t.g2)("el-icon"),P=(0,t.g2)("el-tree"),T=(0,t.g2)("el-button"),L=(0,t.g2)("el-upload"),W=(0,t.g2)("el-icon-warning"),N=(0,t.g2)("el-input"),q=(0,t.g2)("el-empty"),X=(0,t.g2)("el-progress"),D=(0,t.g2)("el-image"),M=(0,t.g2)("el-icon-check"),E=(0,t.g2)("el-scrollbar"),R=(0,t.g2)("el-pagination"),z=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)("div",r,[(0,t.bo)(((0,t.uX)(),(0,t.CE)("div",d,[(0,t.Lk)("div",n,[(0,t.bF)(P,{ref:"group",class:"menu",data:U.menu,"node-key":U.treeProps.key,props:U.treeProps,"current-node-key":U.menu.length>0?U.menu[0][U.treeProps.key]:"","highlight-current":"",onNodeClick:I.groupClick},{default:(0,t.k6)((({node:e})=>[(0,t.Lk)("span",u,[(0,t.bF)(x,{class:"icon tree-icon"},{default:(0,t.k6)((()=>[(0,t.bF)(A)])),_:1}),(0,t.eW)((0,o.v_)(e.label),1)])])),_:1},8,["data","node-key","props","current-node-key","onNodeClick"])]),a.multiple?((0,t.uX)(),(0,t.CE)("div",c,[(0,t.eW)(" 已选择 "),(0,t.Lk)("b",null,(0,o.v_)(U.value.length),1),(0,t.eW)(" / "),(0,t.Lk)("b",null,(0,o.v_)(a.max),1),(0,t.eW)(" 项 ")])):(0,t.Q3)("",!0)])),[[z,U.menuLoading]]),(0,t.bo)(((0,t.uX)(),(0,t.CE)("div",m,[(0,t.Lk)("div",p,[a.hideUpload?(0,t.Q3)("",!0):((0,t.uX)(),(0,t.CE)("div",f,[(0,t.bF)(L,{class:"sc-file-select__upload",action:"",multiple:"","show-file-list":!1,accept:U.accept,"on-change":I.uploadChange,"before-upload":I.uploadBefore,"on-progress":I.uploadProcess,"on-success":I.uploadSuccess,"on-error":I.uploadError,"http-request":I.uploadRequest},{default:(0,t.k6)((()=>[(0,t.bF)(T,{type:"primary",icon:"el-icon-upload"},{default:(0,t.k6)((()=>[(0,t.eW)("本地上传")])),_:1})])),_:1},8,["accept","on-change","before-upload","on-progress","on-success","on-error","http-request"]),(0,t.Lk)("span",h,[(0,t.bF)(x,null,{default:(0,t.k6)((()=>[(0,t.bF)(W)])),_:1}),(0,t.eW)("大小不超过"+(0,o.v_)(a.maxSize)+"MB",1)])])),(0,t.Lk)("div",b,[(0,t.bF)(N,{modelValue:U.keyword,"onUpdate:modelValue":l[0]||(l[0]=e=>U.keyword=e),"prefix-icon":"el-icon-search",placeholder:"文件名搜索",clearable:"",onKeyup:(0,i.jR)(I.search,["enter"]),onClear:I.search},null,8,["modelValue","onKeyup","onClear"])])]),(0,t.Lk)("div",g,[(0,t.bF)(E,{ref:"scrollbar"},{default:(0,t.k6)((()=>[0==U.fileList.length&&0==U.data.length?((0,t.uX)(),(0,t.Wv)(q,{key:0,description:"无数据","image-size":80})):(0,t.Q3)("",!0),((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(U.fileList,((e,l)=>((0,t.uX)(),(0,t.CE)("div",{key:l,class:"sc-file-select__item"},[(0,t.Lk)("div",F,[(0,t.Lk)("div",k,[(0,t.bF)(X,{type:"circle",percentage:e.progress,width:70},null,8,["percentage"])]),(0,t.bF)(D,{src:e.tempImg,fit:"contain"},null,8,["src"])]),(0,t.Lk)("p",null,(0,o.v_)(e.name),1)])))),128)),((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(U.data,(e=>((0,t.uX)(),(0,t.CE)("div",{key:e[U.fileProps.key],class:(0,o.C4)(["sc-file-select__item",{active:U.value.includes(e[U.fileProps.url])}]),onClick:l=>I.select(e)},[(0,t.Lk)("div",y,[a.multiple?((0,t.uX)(),(0,t.CE)("div",_,[(0,t.bF)(x,null,{default:(0,t.k6)((()=>[(0,t.bF)(M)])),_:1})])):((0,t.uX)(),(0,t.CE)("div",V,[(0,t.bF)(x,null,{default:(0,t.k6)((()=>[(0,t.bF)(M)])),_:1})])),w,I._isImg(e[U.fileProps.url])?((0,t.uX)(),(0,t.Wv)(D,{key:2,src:e[U.fileProps.url],fit:"contain",lazy:""},null,8,["src"])):((0,t.uX)(),(0,t.CE)("div",C,[U.files[I._getExt(e[U.fileProps.url])]?((0,t.uX)(),(0,t.CE)("i",{key:0,class:(0,o.C4)(U.files[I._getExt(e[U.fileProps.url])].icon),style:(0,o.Tr)({color:U.files[I._getExt(e[U.fileProps.url])].color})},null,6)):((0,t.uX)(),(0,t.CE)("i",S))]))]),(0,t.Lk)("p",{title:e[U.fileProps.fileName]},(0,o.v_)(e[U.fileProps.fileName]),9,j)],10,v)))),128))])),_:1},512)]),(0,t.Lk)("div",$,[(0,t.bF)(R,{small:"",background:"",layout:"prev, pager, next",total:U.total,"page-size":U.pageSize,currentPage:U.currentPage,"onUpdate:currentPage":l[1]||(l[1]=e=>U.currentPage=e),onCurrentChange:I.reload},null,8,["total","page-size","currentPage","onCurrentChange"])]),(0,t.Lk)("div",O,[(0,t.RG)(e.$slots,"do",{},void 0,!0),(0,t.bF)(T,{icon:"el-icon-delete",type:"danger",disabled:U.value.length<=0,onClick:I.submit},{default:(0,t.k6)((()=>[(0,t.eW)("删除文件")])),_:1},8,["disabled","onClick"])])])),[[z,U.listLoading]])])}a(8743),a(2838);var I=a(1552),A={apiObj:I.A.common.upload,menuApiObj:I.A.biliArticles.getUserList,listApiObj:I.A.biliArticles.getFileList,successCode:0,maxSize:5,max:99,uploadParseData:function(e){return{id:e.data.id,fileName:e.data.fileName,url:e.data.src}},listParseData:function(e){return{rows:e.data.rows,total:e.data.total,msg:e.message,code:e.code}},request:{page:"page",pageSize:"pageSize",keyword:"keyword",menuKey:"groupId"},menuProps:{key:"id",label:"label",children:"children"},fileProps:{key:"id",fileName:"fileName",url:"url"},files:{doc:{icon:"sc-icon-file-word-2-fill",color:"#409eff"},docx:{icon:"sc-icon-file-word-2-fill",color:"#409eff"},xls:{icon:"sc-icon-file-excel-2-fill",color:"#67C23A"},xlsx:{icon:"sc-icon-file-excel-2-fill",color:"#67C23A"},ppt:{icon:"sc-icon-file-ppt-2-fill",color:"#F56C6C"},pptx:{icon:"sc-icon-file-ppt-2-fill",color:"#F56C6C"}}},x={props:{modelValue:null,hideUpload:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1},max:{type:Number,default:A.max},maxSize:{type:Number,default:A.maxSize},path:{type:String,default:"articles"},treeClick:{type:Function},listApiObj:{type:Object,default:A.listApiObj},menuApiObj:{type:Object,default:A.menuApiObj}},data(){return{keyword:null,pageSize:20,total:0,currentPage:1,data:[],menu:[],menuId:"",value:this.multiple?[]:"",fileList:[],accept:"audio/mp4, video/mp4",listLoading:!1,menuLoading:!1,treeProps:A.menuProps,fileProps:A.fileProps,files:A.files,uploadPath:this.path,fileType:"video"}},watch:{multiple(){this.value=this.multiple?[]:"",this.$emit("update:modelValue",JSON.parse(JSON.stringify(this.value)))}},async mounted(){await this.getMenu(),await this.getData()},methods:{async getMenu(){this.menuLoading=!0;var e=await this.menuApiObj.post({jObjectSearch:{}});this.menu=e.data,this.menuId=e.data[0].id,this.menuLoading=!1},async getData(){this.listLoading=!0;var e={[A.request.menuKey]:this.menuId,[A.request.page]:this.currentPage,[A.request.pageSize]:this.pageSize,[A.request.keyword]:this.keyword};e.type=this.fileType,e.path=this.path;var l=await this.listApiObj.post({jObjectSearch:e}),a=A.listParseData(l);this.data=a.rows,this.total=a.total,this.listLoading=!1,this.$refs.scrollbar.setScrollTop(0)},async radioChange(e){switch(e){case"image":this.accept="image/gif, image/jpeg, image/png";break;case"mp3":this.accept="audio/mpeg";break;case"video":this.accept="audio/mp4, video/mp4";break}await this.getData()},getPath(){return this.path+"\\"+this.fileType+"\\"+this.menuId},groupClick(e){this.menuId=e.id,this.currentPage=1,this.keyword=null,this.getData(),this.treeClick&&this.treeClick(e.id)},reload(){this.getData()},search(){this.currentPage=1,this.getData()},select(e){const l=e[this.fileProps.url];this.multiple?this.value.includes(l)?this.value.splice(this.value.findIndex((e=>e==l)),1):this.value.push(l):this.value.includes(l)?this.value="":this.value=l},submit(){const e=JSON.parse(JSON.stringify(this.value));this.$emit("update:modelValue",e),this.$emit("submit",e)},uploadChange(e,l){e.tempImg=URL.createObjectURL(e.raw),this.fileList=l},uploadBefore(e){const l=e.size/1024/1024<this.maxSize;if(!l)return this.$message.warning(`上传文件大小不能超过 ${this.maxSize}MB!`),!1},uploadRequest(e){var l=A.apiObj;const a=new FormData;a.append("file",e.file),a.append([A.request.menuKey],this.menuId),a.append("path",this.path+"\\"+this.fileType+"\\"+this.menuId),a.append("format",1),l.post(a,{onUploadProgress:l=>{e.onProgress(l)}}).then((l=>{e.onSuccess(l)})).catch((l=>{e.onError(l)}))},uploadProcess(e,l){l.progress=Number((e.loaded/e.total*100).toFixed(2))},uploadSuccess(e,l){this.fileList.splice(this.fileList.findIndex((e=>e.uid==l.uid)),1);var a=A.uploadParseData(e);this.data.unshift({[this.fileProps.key]:a.id,[this.fileProps.fileName]:a.fileName,[this.fileProps.url]:a.url}),this.multiple||(this.value=a.url)},uploadError(e){this.$notify.error({title:"上传文件错误",message:e})},_isImg(e){const l=[".jpg",".jpeg",".png",".gif",".bmp"],a=e.substring(e.lastIndexOf("."));return-1!=l.indexOf(a)},_getExt(e){return e.substring(e.lastIndexOf(".")+1)}}},P=a(6262);const T=(0,P.A)(x,[["render",U],["__scopeId","data-v-1737b8a6"]]);var L=T},8573:function(e,l,a){"use strict";a.d(l,{A:function(){return p}});var t=a(641);const o={class:"sc-video",ref:"scVideo"};function i(e,l,a,i,s,r){return(0,t.uX)(),(0,t.CE)("div",o,null,512)}var s=a(5614),r=a.n(s),d=a(7216),n=a.n(d),u={props:{src:{type:String,required:!0,default:""},autoplay:{type:Boolean,default:!1},controls:{type:Boolean,default:!0},loop:{type:Boolean,default:!1},isLive:{type:Boolean,default:!1},options:{type:Object,default:()=>{}}},data(){return{player:null}},watch:{src(e){this.player.hasStart?this.player.src=e:this.player.start(e)}},mounted(){this.isLive?this.initHls():this.init()},methods:{init(){this.player=new(r())({el:this.$refs.scVideo,url:this.src,autoplay:this.autoplay,loop:this.loop,controls:this.controls,fluid:!0,lang:"zh-cn",...this.options})},initHls(){this.player=new(n())({el:this.$refs.scVideo,url:this.src,autoplay:this.autoplay,loop:this.loop,controls:this.controls,fluid:!0,isLive:!0,ignores:["time","progress"],lang:"zh-cn",...this.options})}}},c=a(6262);const m=(0,c.A)(u,[["render",i],["__scopeId","data-v-f6306a2e"]]);var p=m},8706:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return d}});var t=a(641);function o(e,l,a,o,i,s){const r=(0,t.g2)("sc-tree-select"),d=(0,t.g2)("el-form-item"),n=(0,t.g2)("el-col"),u=(0,t.g2)("sc-select"),c=(0,t.g2)("el-input"),m=(0,t.g2)("el-row"),p=(0,t.g2)("el-form"),f=(0,t.g2)("el-button"),h=(0,t.g2)("el-tooltip"),b=(0,t.g2)("el-dialog"),g=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(b,{modelValue:i.visible,"onUpdate:modelValue":l[20]||(l[20]=e=>i.visible=e),title:i.titleMap[i.mode],width:1024,"destroy-on-close":"","close-on-click-modal":!1,onOpened:s.opened,onClosed:l[21]||(l[21]=l=>e.$emit("closed"))},{footer:(0,t.k6)((()=>[(0,t.bF)(h,{class:"box-item",effect:"dark",content:"同步会删除Cdkey信息，任务信息请谨慎操作！",placement:"top"},{default:(0,t.k6)((()=>[(0,t.bF)(f,{icon:"el-icon-refresh",onClick:l[17]||(l[17]=e=>s.asyncTask()),style:{float:"left"},loading:i.isSaveing},{default:(0,t.k6)((()=>[(0,t.eW)("同步数据")])),_:1},8,["loading"])])),_:1}),(0,t.bF)(h,{class:"box-item",effect:"dark",content:"从B站官网获取分区名称数据！",placement:"top"},{default:(0,t.k6)((()=>[(0,t.bF)(f,{icon:"el-icon-star",onClick:l[18]||(l[18]=e=>s.updateTask()),style:{float:"left"},loading:i.isSaveing},{default:(0,t.k6)((()=>[(0,t.eW)("同步分区")])),_:1},8,["loading"])])),_:1}),(0,t.bF)(h,{class:"box-item",effect:"dark",content:"编辑里程、抽奖和活动Act时，会删除除此act的所有Cdkey信息",placement:"top"},{default:(0,t.k6)((()=>[(0,t.bF)(f,{type:"primary",loading:i.isSaveing,onClick:l[19]||(l[19]=e=>s.save())},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["loading"])])),_:1})])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(p,{model:i.form,disabled:"show"==i.mode,ref:"dialogForm","label-width":"110px","element-loading-text":"更新任务中，请耐心等待！"},{default:(0,t.k6)((()=>[(0,t.bF)(m,null,{default:(0,t.k6)((()=>[(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请选择分区名称"}],label:"分区名称",prop:"Fid",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{clearable:"",modelValue:i.form.Fid,"onUpdate:modelValue":l[0]||(l[0]=e=>i.form.Fid=e),ref:"treeSelect",placeholder:"请选择分区名称",prop:{label:"name",value:"id",children:"list"},"default-expand-all":"",onNodeClick:s.treeSelectClick,params:{jObjectSearch:{name:"分区直播信息",field:"name",search:"手游"}},apiObj:e.$API.biliInterface.getInterfaceList,style:{width:"100%"}},null,8,["modelValue","onNodeClick","apiObj"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"领取方式",prop:"FType",rules:[{required:!0,message:"请选择领取方式"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(u,{clearable:"",modelValue:i.form.FType,"onUpdate:modelValue":l[1]||(l[1]=e=>i.form.FType=e),apiObj:[{label:"默认",value:"默认"},{label:"方式一",value:"方式一"},{label:"方式二",value:"方式二"}],placeholder:"正常情况请选择默认",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请输入排列序号"}],label:"排列序号",prop:"FSort",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(c,{type:"number",modelValue:i.form.FSort,"onUpdate:modelValue":l[2]||(l[2]=e=>i.form.FSort=e),placeholder:"排列序号"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"激励地址",prop:"FBlackboardUrl",rules:[{required:!0,message:"请输入激励地址"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(c,{modelValue:i.form.FBlackboardUrl,"onUpdate:modelValue":l[3]||(l[3]=e=>i.form.FBlackboardUrl=e),placeholder:"激励地址"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"领取地址",prop:"FReceiveUrl",rules:[{required:!0,message:"请输入领取地址"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(c,{modelValue:i.form.FReceiveUrl,"onUpdate:modelValue":l[4]||(l[4]=e=>i.form.FReceiveUrl=e),placeholder:"领取地址"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"奖励地址",prop:"FActivityUrl",rules:[{required:!0,message:"请输入奖励记录地址，领奖地址右上点击进去后的地址"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(c,{modelValue:i.form.FActivityUrl,"onUpdate:modelValue":l[5]||(l[5]=e=>i.form.FActivityUrl=e),placeholder:"奖励地址"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"里程act",prop:"FActivityId1"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{modelValue:i.form.FActivityId1,"onUpdate:modelValue":l[6]||(l[6]=e=>i.form.FActivityId1=e),placeholder:"和奖励地址组合使用，不可填0"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"抽奖act",prop:"FActivityId2"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{modelValue:i.form.FActivityId2,"onUpdate:modelValue":l[7]||(l[7]=e=>i.form.FActivityId2=e),placeholder:"和奖励地址组合使用，不可填0"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"活动act",prop:"FActivityId3"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{modelValue:i.form.FActivityId3,"onUpdate:modelValue":l[8]||(l[8]=e=>i.form.FActivityId3=e),placeholder:"和奖励地址组合使用，不可填0"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:12},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"里程Tag",prop:"FSubmissionTag"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{modelValue:i.form.FSubmissionTag,"onUpdate:modelValue":l[9]||(l[9]=e=>i.form.FSubmissionTag=e),placeholder:"浏览器获取直播天数，接口不用写"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:12},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"抽奖Tag",prop:"FLotteryDraw"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{modelValue:i.form.FLotteryDraw,"onUpdate:modelValue":l[10]||(l[10]=e=>i.form.FLotteryDraw=e),placeholder:"接口抽奖Tag"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:16},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"任务地址",prop:"FTaskUrl"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{modelValue:i.form.FTaskUrl,"onUpdate:modelValue":l[11]||(l[11]=e=>i.form.FTaskUrl=e),placeholder:"任务地址"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"ReceiveFrom",prop:"FReceiveFrom"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{modelValue:i.form.FReceiveFrom,"onUpdate:modelValue":l[12]||(l[12]=e=>i.form.FReceiveFrom=e),placeholder:"ReceiveFrom"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"任务Key",prop:"FTotalKey"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{modelValue:i.form.FTotalKey,"onUpdate:modelValue":l[13]||(l[13]=e=>i.form.FTotalKey=e),placeholder:"totalv2的task_ids集合，填上了直接刷新任务信息"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"激励日期",prop:"FArticlesDate"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{modelValue:i.form.FArticlesDate,"onUpdate:modelValue":l[14]||(l[14]=e=>i.form.FArticlesDate=e),placeholder:"激励日期"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"投稿Tag",prop:"FArticlesTag"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{modelValue:i.form.FArticlesTag,"onUpdate:modelValue":l[15]||(l[15]=e=>i.form.FArticlesTag=e),placeholder:"投稿Tag"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"MissionId",prop:"FArticlesMissionId"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{modelValue:i.form.FArticlesMissionId,"onUpdate:modelValue":l[16]||(l[16]=e=>i.form.FArticlesMissionId=e),placeholder:"不填根据投稿Tag自动获取，否则按照填写为主"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","disabled"])),[[g,i.isSaveing]])])),_:1},8,["modelValue","title","onOpened"])}var i={emits:["success","closed"],data(){return{mode:"add",titleMap:{add:"新增",edit:"编辑",show:"查看"},visible:!1,isSaveing:!1,form:{Fid:"",FType:"方式一",FSort:0,FReceiveUrl:"https://www.bilibili.com/blackboard/new-award-exchange.html?task_id=",FActivityUrl:"https://www.bilibili.com/blackboard/award-history.html?activity_id=",FTaskUrl:"https://api.bilibili.com/x/activity_components/mission/info?task_id=",FReceiveFrom:"missionPage"}}},mounted(){this.getSelect()},methods:{getSelect(){},open(e="add"){return this.mode=e,this.visible=!0,this},treeSelectClick(e){this.form.FParentId=e.parent_id,this.form.FName=e.name},save(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.isSaveing=!0,this.form.mode=this.mode;var l=await this.$API.biliArea.saveArea.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.$message.success("操作成功"),this.visible=!1):this.$alert(l.message,"提示",{type:"error"})}))},asyncTask(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.isSaveing=!0;var l=await this.$API.biliArea.updateAreaInfo.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.$message.success("操作成功"),this.visible=!1):this.$alert(l.message,"提示",{type:"error"})}))},async updateTask(){var e=await this.$API.biliInterface.update.post({jObjectParam:{name:"分区直播信息"}});this.isSaveing=!1,0==e.code?(this.$emit("success",this.form,this.mode),this.$message.success("操作成功"),this.visible=!1):this.$alert(e.message,"提示",{type:"error"})},async setData(e){Object.assign(this.form,e),this.form.Fid=String(e.Fid)},opened(){}}},s=a(6262);const r=(0,s.A)(i,[["render",o]]);var d=r},4420:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return d}});var t=a(641);function o(e,l,a,o,i,s){const r=(0,t.g2)("el-input"),d=(0,t.g2)("el-form-item"),n=(0,t.g2)("el-col"),u=(0,t.g2)("sc-select"),c=(0,t.g2)("el-row"),m=(0,t.g2)("el-form"),p=(0,t.g2)("el-button"),f=(0,t.g2)("el-dialog"),h=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(f,{modelValue:i.visible,"onUpdate:modelValue":l[9]||(l[9]=e=>i.visible=e),title:i.titleMap[i.mode],width:1024,"destroy-on-close":"",onOpened:s.opened,onClosed:l[10]||(l[10]=l=>e.$emit("closed"))},{footer:(0,t.k6)((()=>["show"!=i.mode?((0,t.uX)(),(0,t.Wv)(p,{key:0,type:"primary",loading:i.isSaveing,onClick:l[8]||(l[8]=e=>s.save())},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["loading"])):(0,t.Q3)("",!0)])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(m,{model:i.form,disabled:"show"==i.mode,ref:"dialogForm","label-width":"120px"},{default:(0,t.k6)((()=>[(0,t.bF)(c,null,{default:(0,t.k6)((()=>[(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请输入任务Key"}],label:"任务Key",prop:"FTaskKey",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FTaskKey,"onUpdate:modelValue":l[0]||(l[0]=e=>i.form.FTaskKey=e),placeholder:"任务Key"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:16},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"任务地址",prop:"FTaskUrl",rules:[{required:!0,message:"请输入任务地址"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FTaskUrl,"onUpdate:modelValue":l[1]||(l[1]=e=>i.form.FTaskUrl=e),placeholder:"任务地址"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请输入ReceiveFrom"}],label:"ReceiveFrom",prop:"FReceiveFrom",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FReceiveFrom,"onUpdate:modelValue":l[2]||(l[2]=e=>i.form.FReceiveFrom=e),placeholder:"ReceiveFrom"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请输入排列序号"}],label:"排列序号",prop:"FSort",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{type:"number",modelValue:i.form.FSort,"onUpdate:modelValue":l[3]||(l[3]=e=>i.form.FSort=e),placeholder:"排列序号"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请输入奖励单价"}],label:"奖励单价",prop:"FPrice",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{type:"number",modelValue:i.form.FPrice,"onUpdate:modelValue":l[4]||(l[4]=e=>i.form.FPrice=e),placeholder:"用于统计收益"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"强制状态",prop:"FCompulsory",rules:[{required:!0,message:"请选择强制状态"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(u,{modelValue:i.form.FCompulsory,"onUpdate:modelValue":l[5]||(l[5]=e=>i.form.FCompulsory=e),style:{width:"100%"},apiObj:[{label:"是",value:1},{label:"否",value:0}],placeholder:"强制状态"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"显示天数",prop:"FComplete",rules:[{required:!0,message:"请选择显示完成天数"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(u,{modelValue:i.form.FComplete,"onUpdate:modelValue":l[6]||(l[6]=e=>i.form.FComplete=e),style:{width:"100%"},apiObj:[{label:"是",value:1},{label:"否",value:0}],placeholder:"显示天数"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"每日任务",prop:"FDaily",rules:[{required:!0,message:"请选择每日任务"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(u,{modelValue:i.form.FDaily,"onUpdate:modelValue":l[7]||(l[7]=e=>i.form.FDaily=e),style:{width:"100%"},apiObj:[{label:"是",value:1},{label:"否",value:0}],placeholder:"每日任务"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","disabled"])),[[h,i.isSaveing]])])),_:1},8,["modelValue","title","onOpened"])}var i={emits:["success","closed"],data(){return{mode:"add",titleMap:{add:"新增",edit:"编辑",show:"查看"},visible:!1,isSaveing:!1,form:{Fid:0,FAreaId:0,FSort:0,FTaskUrl:"https://api.bilibili.com/x/activity/mission/single_task?id=",FTaskKey:"",FPrice:0,FBulletScreen:0,FCompulsory:0,FReceiveFrom:"missionPage",FEnable:1,FComplete:0,FDaily:0}}},mounted(){this.getSelect()},methods:{getSelect(){},open(e="add"){return this.mode=e,this.visible=!0,this},save(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.isSaveing=!0,this.form.mode=this.mode;var l=await this.$API.biliAreaTask.saveAreaTask.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.$message.success("操作成功"),this.visible=!1):this.$alert(l.message,"提示",{type:"error"})}))},async setData(e){Object.assign(this.form,e),"方式一"==this.form.FAreaType&&(this.form.FTaskUrl=" https://api.bilibili.com/x/activity_components/mission/info?task_id=")},opened(){}}},s=a(6262);const r=(0,s.A)(i,[["render",o]]);var d=r},1985:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return u}});var t=a(641);function o(e,l,a,o,i,s){const r=(0,t.g2)("el-table-column"),d=(0,t.g2)("el-option"),n=(0,t.g2)("el-select"),u=(0,t.g2)("el-input"),c=(0,t.g2)("sc-form-table"),m=(0,t.g2)("el-row"),p=(0,t.g2)("el-form-item"),f=(0,t.g2)("el-button"),h=(0,t.g2)("sc-upload-file"),b=(0,t.g2)("el-dialog"),g=(0,t.g2)("videoDialog"),F=(0,t.gN)("auth");return(0,t.uX)(),(0,t.CE)(t.FK,null,[(0,t.bF)(b,{modelValue:i.visible,"onUpdate:modelValue":l[8]||(l[8]=e=>i.visible=e),title:i.titleMap[i.mode]+"（"+i.area.FName+"）",width:"85%","destroy-on-close":"","close-on-click-modal":!1,onClosed:l[9]||(l[9]=l=>e.$emit("closed"))},{footer:(0,t.k6)((()=>[(0,t.bF)(p,{label:"投稿间隔：-1→同时投稿，非负数X→间隔X秒。",prop:"FSort",style:{float:"left"}},{default:(0,t.k6)((()=>[(0,t.bF)(u,{type:"number",modelValue:i.times,"onUpdate:modelValue":l[1]||(l[1]=e=>i.times=e),placeholder:"自己输入",style:{width:"100px"}},null,8,["modelValue"])])),_:1}),(0,t.bF)(h,{modelValue:i.form.files,"onUpdate:modelValue":l[2]||(l[2]=e=>i.form.files=e),multiple:!0,"show-file-list":!1,maxSize:10380902,onSuccess:s.onSuccessVideo,params:{jObjectParam:{path:"Bili\\Articles\\"+i.area.FName}},style:{width:"100px",float:"left"}},{default:(0,t.k6)((()=>[(0,t.bF)(f,{type:"primary",icon:"el-icon-upload",disabled:i.isSaveing,style:{"margin-left":"12px"}},{default:(0,t.k6)((()=>[(0,t.eW)("上传稿件")])),_:1},8,["disabled"])])),_:1},8,["modelValue","onSuccess","params"]),(0,t.bF)(h,{modelValue:i.form.filesTxt,"onUpdate:modelValue":l[3]||(l[3]=e=>i.form.filesTxt=e),multiple:!0,"show-file-list":!1,maxSize:10380902,onSuccess:s.onSuccessTxt,params:{jObjectParam:{path:"Bili\\Articles\\"+i.area.FName}},style:{width:"100px",float:"left","padding-left":"10px"}},{default:(0,t.k6)((()=>[(0,t.bF)(f,{type:"primary",icon:"el-icon-upload",disabled:i.isSaveing,style:{"margin-left":"12px"}},{default:(0,t.k6)((()=>[(0,t.eW)("上传标题")])),_:1},8,["disabled"])])),_:1},8,["modelValue","onSuccess","params"]),(0,t.bF)(f,{type:"primary",icon:"el-icon-video-camera",onClick:l[4]||(l[4]=e=>s.autoVideo())},{default:(0,t.k6)((()=>[(0,t.eW)("自动选视频")])),_:1}),(0,t.bF)(f,{type:"primary",icon:"el-icon-folder",onClick:l[5]||(l[5]=e=>s.openFolder())},{default:(0,t.k6)((()=>[(0,t.eW)("打开文件夹")])),_:1}),(0,t.bo)(((0,t.uX)(),(0,t.Wv)(f,{type:"primary",icon:"el-icon-video-play",loading:i.isSaveing,onClick:l[6]||(l[6]=e=>s.getVideo())},{default:(0,t.k6)((()=>[(0,t.eW)("获取视频")])),_:1},8,["loading"])),[[F,"articles"]]),(0,t.bF)(f,{type:"primary",icon:"el-icon-promotion",loading:i.isSaveing,onClick:l[7]||(l[7]=e=>s.submit())},{default:(0,t.k6)((()=>[(0,t.eW)("开始投稿")])),_:1},8,["loading"])])),default:(0,t.k6)((()=>[(0,t.bF)(m,{gutter:15},{default:(0,t.k6)((()=>[(0,t.bF)(c,{modelValue:i.form.apiList,"onUpdate:modelValue":l[0]||(l[0]=e=>i.form.apiList=e),hideAdd:!0},{default:(0,t.k6)((()=>[(0,t.bF)(r,{prop:"FCookieName",label:"账号名称","show-overflow-tooltip":"",width:"180",align:"center"}),(0,t.bF)(r,{prop:"FUrl",label:"稿件列表",width:"250",align:"center"},{default:(0,t.k6)((e=>[(0,t.bF)(n,{modelValue:e.row.FUrl,"onUpdate:modelValue":l=>e.row.FUrl=l,placeholder:"稿件列表",onChange:l=>s.change(e.row,e.$index)},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(i.files,(e=>((0,t.uX)(),(0,t.Wv)(d,{key:e.url,label:e.name,value:e.url},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),(0,t.bF)(r,{prop:"FTitle",label:"标题",width:"250",align:"center"},{default:(0,t.k6)((e=>[(0,t.bF)(u,{modelValue:e.row.FTitle,"onUpdate:modelValue":l=>e.row.FTitle=l,placeholder:"稿件标题"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),(0,t.bF)(r,{prop:"FTid",label:"分区父级",width:"130",align:"center"},{default:(0,t.k6)((e=>[(0,t.bF)(n,{modelValue:e.row.FTid,"onUpdate:modelValue":l=>e.row.FTid=l,placeholder:"分区父级"},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(i.tidList,(e=>((0,t.uX)(),(0,t.Wv)(d,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1}),(0,t.bF)(r,{prop:"FType",label:"分区",width:"100",align:"center"},{default:(0,t.k6)((e=>[(0,t.bF)(n,{modelValue:e.row.FType,"onUpdate:modelValue":l=>e.row.FType=l,placeholder:"稿件分区"},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(i.typeList,(e=>((0,t.uX)(),(0,t.Wv)(d,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1}),(0,t.bF)(r,{prop:"FTag",label:"标签",width:"250",align:"center"},{default:(0,t.k6)((e=>[(0,t.bF)(u,{modelValue:e.row.FTag,"onUpdate:modelValue":l=>e.row.FTag=l,placeholder:"稿件标签（非必填、自动生成）"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),(0,t.bF)(r,{prop:"FDesc",label:"简介",align:"center"},{default:(0,t.k6)((e=>[(0,t.bF)(u,{modelValue:e.row.FDesc,"onUpdate:modelValue":l=>e.row.FDesc=l,placeholder:"稿件简介（非必填）"},null,8,["modelValue","onUpdate:modelValue"])])),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["modelValue","title"]),i.dialog.video?((0,t.uX)(),(0,t.Wv)(g,{key:0,ref:"videoDialog",onClosed:l[10]||(l[10]=e=>i.dialog.video=!1),onSuccess:s.success},null,8,["onSuccess"])):(0,t.Q3)("",!0)],64)}a(8743);var i=a(695),s=a(7129),r={emits:["success","closed"],components:{scFileSelect:i.A,videoDialog:s["default"]},data(){return{mode:"add",titleMap:{add:"稿件编辑"},isSaveing:!1,visible:!1,area:{},dialog:{video:!1},files:[],filesTxt:[],times:5,tidList:[{label:"网络游戏",value:65},{label:"单机游戏",value:17},{label:"生活",value:21},{label:"手机游戏",value:172},{label:"电子竞技",value:171}],typeList:[{label:"娱乐",value:1002},{label:"鬼畜",value:1007},{label:"游戏",value:1008},{label:"知识",value:1010},{label:"vlog",value:1029}],form:{apiList:[],files:[]}}},mounted(){this.getSelect()},methods:{getSelect(){},open(e="add",l,a,t){this.mode=e,this.visible=!0,this.area=l,this.files=t;for(let o=0;o<a.length;o++)this.form.apiList.push({FCookieId:a[o].FCookieId,FCookieName:a[o].FCookieName,FTid:17,FType:1008,FUrl:t.length>o?t[o].url:"",FTitle:t.length>o?t[o].name:"",FTag:""});return this},change(e,l){if(e.FUrl){let a=this.files.find((l=>l.url==e.FUrl));this.form.apiList[l].FUrl=a.url,this.form.apiList[l].FTitle=a.name}},async submit(){this.isSaveing=!0;let e=await this.$API.biliArticles.submit.post({jObjectParam:{areaId:this.area.Fid,array:this.form.apiList,times:this.times}});0==e.code?(this.$emit("success",this.form,this.mode),this.visible=!1):this.$alert(e.message,"提示",{type:"error"}),this.isSaveing=!1},getVideo(){this.dialog.video=!0,this.$nextTick((()=>{this.$refs.videoDialog.open("add",this.area.FName)}))},success(e){for(let l=0;l<e.length;l++){this.files.push(e[l]);for(let a=0;a<this.form.apiList.length;a++)if(!this.form.apiList[a].FUrl){this.form.apiList[a].FUrl=e[l].url,this.form.apiList[a].FTitle=e[l].name;break}}},onSuccessVideo(e){if(0==e.code){this.files.push({url:e.data.src,name:e.data.fileName});for(let l=0;l<this.form.apiList.length;l++)if(!this.form.apiList[l].FUrl){this.form.apiList[l].FUrl=e.data.src,this.form.apiList[l].FTitle=e.data.fileName;break}}else this.$alert(e.message,"提示",{type:"error"})},async onSuccessTxt(e){if(0==e.code){e=await this.$API.biliArticles.doTxt.post({jObjectParam:{src:e.data.src}});for(let l=0;l<this.form.apiList.length&&l<e.data.length;l++)this.form.apiList[l].FTitle=e.data[l]}else this.$alert(e.message,"提示",{type:"error"})},async openFolder(){await this.$API.biliLive.openFolder.post({jObjectParam:{wwwRootPath:"Bili\\Articles\\"+this.area.FName}})},async autoVideo(){let e=await this.$API.biliArticles.autoVideo.post({jObjectParam:{num:this.form.apiList.length,path:"Bili\\Articles\\"+this.area.FName}});if(0==e.code){this.files=[];for(let l=0;l<e.data.length;l++){this.files.push(e.data[l]);for(let a=0;a<this.form.apiList.length;a++)if(!this.form.apiList[a].FUrl){this.form.apiList[a].FUrl=e.data[l].url,this.form.apiList[a].FTitle=e.data[l].name;break}}}else this.$alert(e.message,"提示",{type:"error"})}}},d=a(6262);const n=(0,d.A)(r,[["render",o]]);var u=n},7129:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return c}});var t=a(641);const o={class:"item"},i={class:"right-panel-search",style:{"padding-bottom":"15px"}};function s(e,l,a,s,r,d){const n=(0,t.g2)("el-image"),u=(0,t.g2)("el-aside"),c=(0,t.g2)("el-table-column"),m=(0,t.g2)("el-table"),p=(0,t.g2)("el-input"),f=(0,t.g2)("el-button"),h=(0,t.g2)("sc-video"),b=(0,t.g2)("el-main"),g=(0,t.g2)("el-container"),F=(0,t.g2)("el-dialog"),k=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(F,{modelValue:r.visible,"onUpdate:modelValue":l[4]||(l[4]=e=>r.visible=e),title:r.titleMap[r.mode],width:"85%","destroy-on-close":"","close-on-click-modal":!1,class:"dialogComm",onClosed:l[5]||(l[5]=l=>e.$emit("closed"))},{footer:(0,t.k6)((()=>[(0,t.bF)(f,{type:"primary",loading:r.isSaveing,onClick:l[3]||(l[3]=e=>d.doVideo())},{default:(0,t.k6)((()=>[(0,t.eW)("下载视频")])),_:1},8,["loading"])])),default:(0,t.k6)((()=>[(0,t.bF)(g,null,{default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(b,{class:"nopadding","element-loading-text":"执行中..."},{default:(0,t.k6)((()=>[(0,t.bF)(g,null,{default:(0,t.k6)((()=>[(0,t.bF)(u,{width:"870px",style:{background:"#fff",height:"500px","margin-top":"10px"}},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(r.list,(e=>((0,t.uX)(),(0,t.CE)("div",{key:e},[(0,t.Lk)("div",o,[(0,t.bF)(n,{style:{width:"200px",height:"300px",cursor:"pointer"},src:e.photo.coverUrl,fit:"cover",onClick:l=>d.update(e)},null,8,["src","onClick"])])])))),128))])),_:1}),(0,t.bF)(b,{style:{"padding-top":"0px"}},{default:(0,t.k6)((()=>[(0,t.bF)(g,null,{default:(0,t.k6)((()=>[(0,t.bF)(u,{style:{"padding-top":"15px","padding-right":"15px"}},{default:(0,t.k6)((()=>[(0,t.bF)(m,{data:r.tableData,border:"",style:{width:"100%"},height:"500px",onRowClick:d.rowClick},{default:(0,t.k6)((()=>[(0,t.bF)(c,{prop:"name",label:"稿件名称","show-overflow-tooltip":""})])),_:1},8,["data","onRowClick"])])),_:1}),(0,t.bF)(b,{style:{"padding-right":"0"}},{default:(0,t.k6)((()=>[(0,t.Lk)("div",i,[(0,t.bF)(p,{modelValue:r.jObjectSearch.search,"onUpdate:modelValue":l[0]||(l[0]=e=>r.jObjectSearch.search=e),placeholder:"模糊查询",clearable:""},null,8,["modelValue"]),(0,t.bF)(f,{disabled:r.isSaveing,type:"primary",icon:"el-icon-search",onClick:d.upsearch},{default:(0,t.k6)((()=>[(0,t.eW)(" 查询")])),_:1},8,["disabled","onClick"])]),(0,t.bF)(h,{src:r.playUrl,options:e.options},null,8,["src","options"]),(0,t.bF)(f,{type:"primary",icon:"el-icon-plus",onClick:l[1]||(l[1]=e=>d.add()),style:{"margin-top":"30px"},disabled:r.isSaveing},{default:(0,t.k6)((()=>[(0,t.eW)("添加视频")])),_:1},8,["disabled"]),(0,t.bF)(f,{type:"danger",icon:"el-icon-remove",onClick:l[2]||(l[2]=e=>{r.tableData=r.tableData.filter((e=>e.url!=r.playUrl))}),style:{"margin-top":"30px"},disabled:r.isSaveing},{default:(0,t.k6)((()=>[(0,t.eW)("移除视频")])),_:1},8,["disabled"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})),[[k,r.isSaveing]])])),_:1})])),_:1},8,["modelValue","title"])}a(8743);var r=a(8573),d={emits:["success","closed"],components:{scVideo:r.A},data(){return{mode:"add",titleMap:{add:"获取视频"},isSaveing:!1,visible:!1,playUrl:"",playName:"",list:[],jObjectSearch:{search:""},areaName:"",tableData:[]}},mounted(){this.getSelect()},methods:{getSelect(){},open(e,l){return this.areaName=l,this.mode=e,this.visible=!0,this},async upsearch(){if(!this.jObjectSearch.search)return void this.$alert("请输入查询内容！","提示",{type:"error"});this.playUrl="",this.updateLoading=!0;let e=await this.$API.biliArticles.getVideoList.post({jObjectSearch:this.jObjectSearch});this.updateLoading=!1,0==e.code?this.list=e.data:this.$alert(e.message,"提示",{type:"error"})},add(){if(""!=this.playUrl){if(this.tableData.length>0){let e=this.tableData.find((e=>e.url==this.playUrl));if(e)return void this.$alert("已添加过该视频！","提示",{type:"error"})}this.tableData.push({name:this.playName,url:this.playUrl})}else this.$alert("请先选择视频！","提示",{type:"error"})},update(e){this.playUrl=e.photo.photoH265Url?e.photo.photoH265Url:e.photo.manifest.adaptationSet[0].representation[0].url;let l=e.photo.caption.split(" ");this.playName="";for(let a=0;a<l.length;a++)if(-1==String(l[a]).indexOf("@")&&-1==l[a].indexOf("#")){this.playName=l[a];break}""==this.playName&&(this.playName=l[0])},rowClick(e){this.playUrl=e.url,this.playName=e.name},async doVideo(){this.isSaveing=!0;let e=await this.$API.biliArticles.doVideo.post({jObjectParam:{array:this.tableData,path:"Bili\\Articles\\"+this.areaName}});0==e.code?(this.$emit("success",e.data),this.visible=!1,this.$message.success("已添加到稿件列表！")):this.$alert(e.message,"提示",{type:"error"}),this.isSaveing=!1}}},n=a(6262);const u=(0,n.A)(d,[["render",s],["__scopeId","data-v-9f4f5a2e"]]);var c=u},302:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return d}});var t=a(641);function o(e,l,a,o,i,s){const r=(0,t.g2)("sc-select"),d=(0,t.g2)("el-form-item"),n=(0,t.g2)("el-col"),u=(0,t.g2)("el-input"),c=(0,t.g2)("el-text"),m=(0,t.g2)("el-row"),p=(0,t.g2)("el-form"),f=(0,t.g2)("el-button"),h=(0,t.g2)("el-dialog"),b=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(h,{modelValue:i.visible,"onUpdate:modelValue":l[10]||(l[10]=e=>i.visible=e),title:i.titleMap[i.mode],width:1200,"destroy-on-close":"",onClosed:l[11]||(l[11]=l=>e.$emit("closed"))},{footer:(0,t.k6)((()=>["show"!=i.mode?((0,t.uX)(),(0,t.Wv)(f,{key:0,type:"primary",loading:i.isSaveing,onClick:l[9]||(l[9]=e=>s.save())},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["loading"])):(0,t.Q3)("",!0)])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(p,{model:i.form,disabled:"show"==i.mode,ref:"dialogForm","label-width":"125px"},{default:(0,t.k6)((()=>[(0,t.bF)(m,null,{default:(0,t.k6)((()=>[(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请选择弹幕类型"}],label:"弹幕类型",prop:"FListen",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{clearable:"",modelValue:i.form.FListen,"onUpdate:modelValue":l[0]||(l[0]=e=>i.form.FListen=e),apiObj:[{label:"直播间",value:"直播间"},{label:"弹幕礼物观看",value:"弹幕礼物观看"}],placeholder:"弹幕类型",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),"弹幕礼物观看"!=i.form.FListen?((0,t.uX)(),(0,t.Wv)(n,{key:0,span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请选择触发方式"}],label:"触发方式",prop:"FTrigger",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{clearable:"",modelValue:i.form.FTrigger,"onUpdate:modelValue":l[1]||(l[1]=e=>i.form.FTrigger=e),apiObj:[{label:"收到弹幕",value:"收到弹幕"},{label:"收到礼物",value:"收到礼物"},{label:"进入直播间",value:"进入直播间"},{label:"开始直播",value:"开始直播"},{label:"结束直播",value:"结束直播"}],placeholder:"触发方式",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})):(0,t.Q3)("",!0),"弹幕礼物观看"!=i.form.FListen&&"收到弹幕"==i.form.FTrigger?((0,t.uX)(),(0,t.Wv)(n,{key:1,span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请选择监听内容逻辑关系"}],label:"逻辑关系",prop:"FLogic",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{clearable:"",modelValue:i.form.FLogic,"onUpdate:modelValue":l[2]||(l[2]=e=>i.form.FLogic=e),apiObj:[{label:"包含全部（与）",value:"与"},{label:"包含任意（或）",value:"或"}],placeholder:"监听内容逻辑关系",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})):(0,t.Q3)("",!0),"弹幕礼物观看"!=i.form.FListen&&"收到弹幕"==i.form.FTrigger?((0,t.uX)(),(0,t.Wv)(n,{key:2,span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"监听内容1",prop:"FKeyword1",rules:[{required:!0,message:"至少输入一个监听内容"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(u,{modelValue:i.form.FKeyword1,"onUpdate:modelValue":l[3]||(l[3]=e=>i.form.FKeyword1=e),placeholder:"监听内容1"},null,8,["modelValue"])])),_:1})])),_:1})):(0,t.Q3)("",!0),"弹幕礼物观看"!=i.form.FListen&&"收到弹幕"==i.form.FTrigger?((0,t.uX)(),(0,t.Wv)(n,{key:3,span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"监听内容2",prop:"FKeyword2"},{default:(0,t.k6)((()=>[(0,t.bF)(u,{modelValue:i.form.FKeyword2,"onUpdate:modelValue":l[4]||(l[4]=e=>i.form.FKeyword2=e),placeholder:"监听内容2"},null,8,["modelValue"])])),_:1})])),_:1})):(0,t.Q3)("",!0),"弹幕礼物观看"!=i.form.FListen&&"收到弹幕"==i.form.FTrigger?((0,t.uX)(),(0,t.Wv)(n,{key:4,span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"监听内容3",prop:"FKeyword3"},{default:(0,t.k6)((()=>[(0,t.bF)(u,{modelValue:i.form.FKeyword3,"onUpdate:modelValue":l[5]||(l[5]=e=>i.form.FKeyword3=e),placeholder:"监听内容3"},null,8,["modelValue"])])),_:1})])),_:1})):(0,t.Q3)("",!0),(0,t.bF)(n,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"发送弹幕",prop:"FMsg",rules:[{required:!0,message:"请输入发送弹幕内容"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(u,{modelValue:i.form.FMsg,"onUpdate:modelValue":l[6]||(l[6]=e=>i.form.FMsg=e),style:{width:"100%"},placeholder:"发送弹幕内容"},null,8,["modelValue"])])),_:1})])),_:1}),"弹幕礼物观看"!=i.form.FListen?((0,t.uX)(),(0,t.Wv)(n,{key:5,span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"变量替换"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{class:"mr-2"},{default:(0,t.k6)((()=>[(0,t.eW)('发送弹幕可替换对应内容："{礼物名称}","{用户名称}","{用户名称4位}","{游戏分区}"')])),_:1})])),_:1})])),_:1})):(0,t.Q3)("",!0),(0,t.bF)(n,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"分区名称",prop:"FArea"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{clearable:"",modelValue:i.form.FArea,"onUpdate:modelValue":l[7]||(l[7]=e=>i.form.FArea=e),params:{jObjectSearch:{}},multiple:"","collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":4,apiObj:e.$API.biliArea.getAreaList,prop:{label:"FName",value:"Fid"},placeholder:"不选：默认全部分区",style:{width:"100%"}},null,8,["modelValue","apiObj"])])),_:1})])),_:1}),(0,t.bF)(n,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"账号名称",prop:"FCookie"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{clearable:"",modelValue:i.form.FCookie,"onUpdate:modelValue":l[8]||(l[8]=e=>i.form.FCookie=e),params:{jObjectSearch:{}},multiple:"","collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":4,apiObj:e.$API.biliCookies.getCookiesList,prop:{label:"FName",value:"Fid"},placeholder:"不选：默认全部账号",style:{width:"100%"}},null,8,["modelValue","apiObj"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","disabled"])),[[b,i.isSaveing]])])),_:1},8,["modelValue","title"])}var i={emits:["success","closed"],data(){return{mode:"add",titleMap:{add:"新增",edit:"编辑",show:"查看"},visible:!1,isSaveing:!1,form:{Fid:0,FEnable:1}}},mounted(){this.getSelect()},methods:{getSelect(){},open(e="add"){return this.mode=e,this.visible=!0,this},save(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.isSaveing=!0,this.form.FCookieId=String(this.form.FCookie),this.form.FAreaId=String(this.form.FArea),this.form.FSys=0;var l=await this.$API.biliBulletSceen.editBulletSceen.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.$message.success("操作成功"),0!=this.form.Fid&&(this.visible=!1)):this.$alert(l.message,"提示",{type:"error"})}))},setData(e){Object.assign(this.form,e),"edit"==this.mode&&(""==this.form.FCookieId?this.form.FCookie=[]:this.form.FCookie=String(this.form.FCookieId).split(",").map(Number),""==this.form.FAreaId?this.form.FArea=[]:this.form.FArea=String(this.form.FAreaId).split(",").map(Number))}}},s=a(6262);const r=(0,s.A)(i,[["render",o]]);var d=r},6312:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return w}});var t=a(641),o=a(2644);const i=(0,t.Lk)("h3",null,"1.只勾选BiliBili时拥有所有权限",-1),s=(0,t.Lk)("h3",null,"2.拥有所有权限时可自己维护分区",-1),r=(0,t.Lk)("h3",null,"3.所有账号到期，才可调整分区",-1),d=(0,t.Lk)("h3",null,"4.付款后1小时左右到账",-1),n=(0,t.Lk)("h3",null,"5.需要其他分区请联系群主",-1),u=(0,t.Lk)("h3",null,"6.修改分区需要重新登录(充值成功)",-1),c=(0,t.Lk)("h3",null,"7.群号324043488",-1),m=(0,t.Lk)("h3",null,null,-1),p={class:"custom-tree-node1"},f={style:{"margin-left":"8px"}},h=["src"],b=(0,t.Lk)("h3",null,"请您支付",-1),g={style:{color:"#f00"}},F=(0,t.Lk)("h3",null,"注意小数部分否则充值不上！",-1),k=(0,t.Lk)("h3",null,"充值时间0点-23点",-1);function v(e,l,a,v,y,_){const V=(0,t.g2)("el-table-column"),w=(0,t.g2)("el-table"),C=(0,t.g2)("el-col"),S=(0,t.g2)("el-tree"),j=(0,t.g2)("el-main"),$=(0,t.g2)("el-container"),O=(0,t.g2)("el-row"),U=(0,t.g2)("el-form"),I=(0,t.g2)("el-button"),A=(0,t.g2)("el-dialog"),x=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(A,{modelValue:y.visible,"onUpdate:modelValue":l[2]||(l[2]=e=>y.visible=e),title:y.titleMap[y.mode],width:1200,"destroy-on-close":"","close-on-click-modal":!1,onOpened:_.opened,onClosed:l[3]||(l[3]=l=>e.$emit("closed"))},{footer:(0,t.k6)((()=>[""==y.src?((0,t.uX)(),(0,t.Wv)(I,{key:0,type:"primary",onClick:l[1]||(l[1]=e=>_.save()),disabled:y.isSaveing},{default:(0,t.k6)((()=>[(0,t.eW)("生成支付二维码")])),_:1},8,["disabled"])):(0,t.Q3)("",!0)])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(U,{model:y.form,ref:"dialogForm"},{default:(0,t.k6)((()=>[(0,t.bF)(O,{gutter:20},{default:(0,t.k6)((()=>[(0,t.bF)(C,{span:13},{default:(0,t.k6)((()=>[(0,t.bF)(w,{data:y.form.array,border:"",style:{height:"500px"}},{default:(0,t.k6)((()=>[(0,t.bF)(V,{prop:"FKey",label:"账号标识（编辑可以查看标识）",align:"center",width:"300px"}),(0,t.bF)(V,{prop:"FName",label:"账号名称",align:"center","show-overflow-tooltip":""}),(0,t.bF)(V,{prop:"FExpirationTime",label:"到期时间",align:"center"})])),_:1},8,["data"])])),_:1}),(0,t.bF)(C,{span:6},{default:(0,t.k6)((()=>[i,s,r,d,n,u,c,m,(0,t.bF)($,null,{default:(0,t.k6)((()=>[(0,t.bF)(j,{class:"nopadding",style:{height:"400px"}},{default:(0,t.k6)((()=>[(0,t.bF)(S,{ref:"tree",class:"menu","node-key":"value",data:y.treeData,modelValue:y.tree,"onUpdate:modelValue":l[0]||(l[0]=e=>y.tree=e),"default-checked-keys":e.treeValue,"highlight-current":"","default-expand-all":"","expand-on-click-node":!1,"check-strictly":"","show-checkbox":""},{default:(0,t.k6)((({node:e,data:l})=>[(0,t.Lk)("span",p,[(0,t.Lk)("span",null,(0,o.v_)(e.label),1),(0,t.Lk)("span",null,[(0,t.Lk)("span",f,(0,o.v_)(l.desc)+"元40天 ",1)])])])),_:1},8,["data","modelValue","default-checked-keys"])])),_:1})])),_:1})])),_:1}),""!=y.src?((0,t.uX)(),(0,t.Wv)(C,{key:0,span:5},{default:(0,t.k6)((()=>[(0,t.Lk)("img",{src:y.src,style:{width:"100%"}},null,8,h),b,(0,t.Lk)("h1",g,(0,o.v_)(y.money),1),F,k])),_:1})):(0,t.Q3)("",!0)])),_:1})])),_:1},8,["model"])),[[x,y.isSaveing]])])),_:1},8,["modelValue","title","onOpened"])}var y={emits:["success","closed"],data(){return{mode:"add",titleMap:{show:"充值时长（默认40天）"},visible:!1,isSaveing:!1,treeData:[],form:{array:[]},src:"",money:"",tree:[2]}},async mounted(){await this.getSelect()},methods:{async getSelect(){var e=await this.$API.biliCookies.getSysOrganizationList.post({jObjectParam:{}});this.treeData=e.data.tree,this.treeValue=e.data.treeValue},open(e="add"){return this.mode=e,this.visible=!0,this},save(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.isSaveing=!0,this.form.org=String(this.$refs.tree.getCheckedKeys(!1));var l=await this.$API.biliCookies.addExpiraation.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.money=l.data.money,this.src=l.data.image):this.$alert(l.message,"提示",{type:"error"})}))},async setData(e){Object.assign(this.form,e)},menuFilterNode(e,l){if(!e)return!0;var a=l.meta.title;return-1!==a.indexOf(e)},opened(){}}},_=a(6262);const V=(0,_.A)(y,[["render",v]]);var w=V},906:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return u}});var t=a(641),o=a(2644);function i(e,l,a,i,s,r){const d=(0,t.g2)("el-header"),n=(0,t.g2)("el-button"),u=(0,t.g2)("sc-upload-file"),c=(0,t.g2)("el-col"),m=(0,t.g2)("el-row"),p=(0,t.g2)("el-main"),f=(0,t.g2)("el-footer"),h=(0,t.g2)("el-container"),b=(0,t.g2)("el-aside"),g=(0,t.g2)("el-input"),F=(0,t.g2)("el-table-column"),k=(0,t.g2)("el-option"),v=(0,t.g2)("el-select"),y=(0,t.g2)("sc-form-table"),_=(0,t.g2)("el-dialog"),V=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(_,{class:"dialog-table",modelValue:s.visible,"onUpdate:modelValue":l[5]||(l[5]=e=>s.visible=e),title:s.titleMap[s.mode],width:1440,"destroy-on-close":"",onClosed:l[6]||(l[6]=l=>e.$emit("closed")),style:{height:"auto","max-height":"100%"}},{footer:(0,t.k6)((()=>[(0,t.bF)(n,{onClick:l[3]||(l[3]=e=>s.visible=!1)},{default:(0,t.k6)((()=>[(0,t.eW)("关 闭")])),_:1}),(0,t.bF)(n,{type:"primary",loading:s.isSaveing,onClick:l[4]||(l[4]=e=>r.save())},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["loading"])])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(h,{class:"layer-customer"},{default:(0,t.k6)((()=>[(0,t.bF)(b,{width:"600px"},{default:(0,t.k6)((()=>[(0,t.bF)(h,null,{default:(0,t.k6)((()=>[(0,t.bF)(d,null,{default:(0,t.k6)((()=>[(0,t.eW)("直播视频（建议上传16个视频）")])),_:1}),(0,t.bF)(p,null,{default:(0,t.k6)((()=>[(0,t.bF)(m,null,{default:(0,t.k6)((()=>[(0,t.bF)(c,{span:24,style:{position:"relative"}},{default:(0,t.k6)((()=>[(0,t.bF)(u,{modelValue:s.form.livePath,"onUpdate:modelValue":l[0]||(l[0]=e=>s.form.livePath=e),apiObj:s.uploadApi,limit:99,"on-remove":r.handleRemove,params:{jObjectParam:{path:"Bili\\Live\\"+s.form.areaName}},tip:"至少上传一个视频,保存后再批量移动,mp4格式,单文件不超过4G"+(""!=s.form.livePath?"          删除 ↓":"")},{default:(0,t.k6)((()=>[(0,t.bF)(n,{type:"primary",icon:"el-icon-upload"},{default:(0,t.k6)((()=>[(0,t.eW)("上传视频")])),_:1})])),_:1},8,["modelValue","apiObj","on-remove","params","tip"]),(0,t.bF)(n,{type:"primary",icon:"el-icon-folder",onClick:l[1]||(l[1]=e=>r.openFolder()),style:{position:"absolute",top:"0",right:"0"}},{default:(0,t.k6)((()=>[(0,t.eW)("打开文件夹")])),_:1})])),_:1})])),_:1})])),_:1}),(0,t.bF)(f,{height:"80px"},{default:(0,t.k6)((()=>[(0,t.eW)(" 视频转码："),(0,t.Lk)("span",null,(0,o.v_)(s.videoCode),1)])),_:1})])),_:1})])),_:1}),(0,t.bF)(h,null,{default:(0,t.k6)((()=>[(0,t.bF)(d,null,{default:(0,t.k6)((()=>[(0,t.eW)("礼物 / 弹幕 / 观看 （接口需配置*弹幕系统*，浏览器无需配置）")])),_:1}),(0,t.bF)(p,null,{default:(0,t.k6)((()=>[(0,t.bF)(y,{ref:"table",modelValue:s.form.liveConfig,"onUpdate:modelValue":l[2]||(l[2]=e=>s.form.liveConfig=e),addTemplate:s.addTemplate},{default:(0,t.k6)((()=>[(0,t.bF)(F,{prop:"FRoomNo",align:"center",label:"房间号"},{default:(0,t.k6)((e=>[(0,t.bF)(g,{modelValue:e.row.FRoomNo,"onUpdate:modelValue":l=>e.row.FRoomNo=l,class:"center",placeholder:"房间号"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),(0,t.bF)(F,{prop:"FGiftCode",align:"center",label:"礼物名称",width:"160"},{default:(0,t.k6)((e=>[(0,t.bF)(v,{modelValue:e.row.FGiftCode,"onUpdate:modelValue":l=>e.row.FGiftCode=l,placeholder:"礼物名称",class:"center",onChange:l=>r.giftChange(e.row,e.$index),clearable:""},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(s.giftList,(e=>((0,t.uX)(),(0,t.Wv)(k,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),(0,t.bF)(F,{prop:"FGiftNum",align:"center",label:"礼物数量",width:"100"},{default:(0,t.k6)((e=>[(0,t.bF)(g,{modelValue:e.row.FGiftNum,"onUpdate:modelValue":l=>e.row.FGiftNum=l,type:"number",class:"center",placeholder:"礼物数量"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),(0,t.bF)(F,{prop:"FBulletScreenNum",align:"center",label:"弹幕数量",width:"100"},{default:(0,t.k6)((e=>[(0,t.bF)(g,{modelValue:e.row.FBulletScreenNum,"onUpdate:modelValue":l=>e.row.FBulletScreenNum=l,type:"number",class:"center",placeholder:"弹幕数量"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),(0,t.bF)(F,{prop:"FWatchMinutes",align:"center",label:"观看分钟",width:"100"},{default:(0,t.k6)((e=>[(0,t.bF)(g,{modelValue:e.row.FWatchMinutes,"onUpdate:modelValue":l=>e.row.FWatchMinutes=l,type:"number",class:"center",placeholder:"观看分钟"},null,8,["modelValue","onUpdate:modelValue"])])),_:1})])),_:1},8,["modelValue","addTemplate"])])),_:1}),(0,t.bF)(f,{height:"50px"},{default:(0,t.k6)((()=>[(0,t.eW)(" 注意：数值 = 0 时 不发礼物、弹幕，不观看。自己给自己发弹幕、观看需要把自己填进去！ ")])),_:1})])),_:1})])),_:1})),[[V,s.isSaveing]])])),_:1},8,["modelValue","title"])}var s=a(8573),r={emits:["success","closed"],components:{scVideo:s.A},data(){return{mode:"show",titleMap:{show:""},visible:!1,isSaveing:!1,uploadApi:this.$API.common.upload,addTemplate:{Fid:0,FRoomNo:"",FGiftCode:"",FGiftNum:0,FBulletScreenNum:0,FGiftPrice:0,FWatchMinutes:"",FGiftName:""},form:{livePath:""},giftList:[],videoCode:""}},async created(){},async mounted(){await this.getSelect()},methods:{async getSelect(){let e=await this.$API.biliInterface.getInterfaceList.post({jObjectSearch:{name:"直播礼物"}});this.giftList=e.data.rows},async save(){this.isSaveing=!0;var e=await this.$API.biliLive.saveLive.post({jObjectParam:this.form});this.isSaveing=!1,0==e.code?(this.$emit("success",this.form,this.mode),this.visible=!1,this.$alert(e.message,"提示",{type:"success"})):this.$alert(e.message,"提示",{type:"error"})},open(e="show",l=""){return this.mode=e,this.titleMap.show=l,this.visible=!0,this},async setData(e){Object.assign(this.form,e);let l=await this.$API.biliLive.getLiveConfig.post({jObjectParam:e});0==l.code&&(Object.assign(this.form,l.data),this.videoCode=l.message)},upsearch(){this.$refs.table.upData({jObjectSearch:this.jObjectSearch})},giftChange(e,l){if(e.FGiftCode){let a=this.giftList.find((l=>l.id==e.FGiftCode));this.form.liveConfig[l].FGiftPrice=a.price,this.form.liveConfig[l].FGiftName=a.name}},async handleRemove(e){await this.$API.biliLive.delLive.post({jObjectParam:e})},async openFolder(){await this.$API.biliLive.openFolder.post({jObjectParam:{wwwRootPath:"Bili\\Live\\"+this.form.areaName}})}}},d=a(6262);const n=(0,d.A)(r,[["render",i],["__scopeId","data-v-3bbd5d04"]]);var u=n},1587:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return n}});var t=a(641),o=a(2644);function i(e,l,a,i,s,r){const d=(0,t.g2)("el-input"),n=(0,t.g2)("el-form-item"),u=(0,t.g2)("el-col"),c=(0,t.g2)("el-text"),m=(0,t.g2)("sc-select"),p=(0,t.g2)("el-row"),f=(0,t.g2)("el-form"),h=(0,t.g2)("el-button"),b=(0,t.g2)("el-dialog"),g=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(b,{modelValue:s.visible,"onUpdate:modelValue":l[14]||(l[14]=e=>s.visible=e),title:s.titleMap[s.mode],width:1200,"destroy-on-close":"","close-on-click-modal":!1,onOpened:r.opened,onClosed:l[15]||(l[15]=l=>e.$emit("closed"))},{footer:(0,t.k6)((()=>[(0,t.bF)(h,{type:"info",onClick:l[12]||(l[12]=e=>r.openChromium()),style:{float:"left"},loading:s.isSaveing},{default:(0,t.k6)((()=>[(0,t.eW)("打开浏览器")])),_:1},8,["loading"]),(0,t.bF)(h,{type:"warning",onClick:r.login},{default:(0,t.k6)((()=>[(0,t.eW)("1.登录账号")])),_:1},8,["onClick"]),"show"!=s.mode?((0,t.uX)(),(0,t.Wv)(h,{key:0,type:"primary",loading:s.isSaveing,onClick:l[13]||(l[13]=e=>r.save())},{default:(0,t.k6)((()=>[(0,t.eW)("2.保存信息")])),_:1},8,["loading"])):(0,t.Q3)("",!0)])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(f,{model:s.form,disabled:"show"==s.mode,ref:"dialogForm","label-width":"130px"},{default:(0,t.k6)((()=>[(0,t.bF)(p,null,{default:(0,t.k6)((()=>[(0,t.bF)(u,{span:6},{default:(0,t.k6)((()=>[(0,t.bF)(n,{rules:[{required:!0,message:"请输入Cookie"}],label:"","label-width":"0",prop:"FCookie",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(d,{modelValue:s.form.FCookie,"onUpdate:modelValue":l[0]||(l[0]=e=>s.form.FCookie=e),style:{width:"100%"},rows:25,type:"textarea",resize:"none",placeholder:"Cookie"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(u,{span:9},{default:(0,t.k6)((()=>[(0,t.bF)(n,{label:"Content-Type",prop:"Content-Type"},{default:(0,t.k6)((()=>[(0,t.bF)(d,{modelValue:s.model["Content-Type"],"onUpdate:modelValue":l[1]||(l[1]=e=>s.model["Content-Type"]=e),placeholder:"值",disabled:!0},null,8,["modelValue"])])),_:1}),(0,t.bF)(n,{label:"User-Agent",prop:"User-Agent"},{default:(0,t.k6)((()=>[(0,t.bF)(d,{modelValue:s.model["User-Agent"],"onUpdate:modelValue":l[2]||(l[2]=e=>s.model["User-Agent"]=e),placeholder:"值"},null,8,["modelValue"])])),_:1}),(0,t.bF)(n,{label:"","label-width":"40px",prop:"FHeaders"},{default:(0,t.k6)((()=>[(0,t.bF)(d,{modelValue:s.form.FHeaders,"onUpdate:modelValue":l[3]||(l[3]=e=>s.form.FHeaders=e),style:{width:"100%"},rows:20,type:"textarea",resize:"none",placeholder:"请求头，格式化请求头后会把请求头的参数写在这里,格式JSON，例子:                                                          {'User-Agent':'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/118.0','Content-Type':'application/x-www-form-urlencoded'"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(u,{span:9},{default:(0,t.k6)((()=>[(0,t.bF)(p,null,{default:(0,t.k6)((()=>[(0,t.bF)(u,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(n,{label:"","label-width":"100px"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{class:"mx-1"},{default:(0,t.k6)((()=>[(0,t.eW)((0,o.v_)(s.form.FKey),1)])),_:1})])),_:1})])),_:1}),(0,t.bF)(u,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(n,{rules:[{required:!0,message:"请输入序号"}],label:"序号",prop:"FSort",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(d,{type:"number",modelValue:s.form.FSort,"onUpdate:modelValue":l[4]||(l[4]=e=>s.form.FSort=e),placeholder:"自己输入"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(u,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(n,{rules:[{required:!0,message:"账号标识不能为空"}],label:"账号标识",prop:"FIdentifying",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(d,{modelValue:s.form.FIdentifying,"onUpdate:modelValue":l[5]||(l[5]=e=>s.form.FIdentifying=e),placeholder:"Cookie里的DedeUserID，自动更新"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(u,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(n,{rules:[{required:!0,message:"请输入账号CSRF"}],label:"CSRF",prop:"FCsrf",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(d,{modelValue:s.form.FCsrf,"onUpdate:modelValue":l[6]||(l[6]=e=>s.form.FCsrf=e),placeholder:"Cookie里的bili_jct，自动更新"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(u,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(n,{rules:[{required:!0,message:"请输入账号名称"}],label:"账号名称",prop:"FName",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(d,{modelValue:s.form.FName,"onUpdate:modelValue":l[7]||(l[7]=e=>s.form.FName=e),placeholder:"随意输入"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(u,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(n,{label:"代理地址",prop:"FProxyId"},{default:(0,t.k6)((()=>[(0,t.bF)(m,{clearable:"",modelValue:s.form.FProxyId,"onUpdate:modelValue":l[8]||(l[8]=e=>s.form.FProxyId=e),style:{width:"100%"},params:{jObjectSearch:{enable:""}},apiObj:e.$API.biliProxy.getProxyList,prop:{label:"FAddressName",value:"Fid"},placeholder:"代理地址"},null,8,["modelValue","apiObj"])])),_:1})])),_:1}),(0,t.bF)(u,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(n,{label:"房间号",prop:"FRoomId"},{default:(0,t.k6)((()=>[(0,t.bF)(d,{modelValue:s.form.FRoomId,"onUpdate:modelValue":l[9]||(l[9]=e=>s.form.FRoomId=e),placeholder:"直播房间号"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(u,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(n,{label:"房间标题",prop:"FRoomTitle"},{default:(0,t.k6)((()=>[(0,t.bF)(d,{modelValue:s.form.FRoomTitle,"onUpdate:modelValue":l[10]||(l[10]=e=>s.form.FRoomTitle=e),placeholder:"房间标题,不一致会自动修改"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(u,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(n,{label:"直播推流",prop:"FRoomRTMP"},{default:(0,t.k6)((()=>[(0,t.bF)(d,{type:"textarea",modelValue:s.form.FRoomRTMP,"onUpdate:modelValue":l[11]||(l[11]=e=>s.form.FRoomRTMP=e),rows:3,resize:"none",placeholder:"不填默认：-re -stream_loop -1 -i {视频路径} -vcodec copy -acodec aac -b:a 96k -f flv {代理地址} {RTMP}，不懂别动"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(u,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(n,{label:"注意事项",prop:"FGlobal"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{class:"mx-1"},{default:(0,t.k6)((()=>[(0,t.eW)("{}是动态参数,不懂别改，懂随意")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["model","disabled"])),[[g,s.isSaveing]])])),_:1},8,["modelValue","title","onOpened"])}var s={emits:["success","closed"],data(){return{mode:"add",titleMap:{add:"新增",edit:"编辑（如需代理请先配置代理，更新信息会调用B站接口）",show:"查看"},visible:!1,isSaveing:!1,model:{"Content-Type":"application/x-www-form-urlencoded","User-Agent":navigator.userAgent},form:{Fid:0,FCookie:"",FHeaders:"",FKey:"",FRoomRTMP:"-re -stream_loop -1 -i {视频路径} -vcodec copy -acodec aac -b:a 96k -f flv {代理地址} {RTMP}"}}},mounted(){this.getSelect()},methods:{getSelect(){},header(){this.$prompt("如何复制请求头(火狐) → https://blog.csdn.net/qq523176585/article/details/134173302","填写请求头",{confirmButtonText:"确定",autofocus:!0,cancelButtonText:"取消",inputType:"textarea",customClass:"custom-message-box-width",beforeClose:(e,l,a)=>{if("confirm"==e){let e=l.inputValue;if(e&&""!=e){const l=e.split("\n").filter(Boolean),t={};l.forEach((e=>{const[l,...a]=e.split(":");let o=String(a.join(":")).trim();if(""!=l&&""!=a.join(":"))switch(l){case"User-Agent":this.model["User-Agent"]=o,t[l]=o;break;case"Cookie":this.form.FCookie=o,delete t[l];break;case"Content-Type":case"Content-Length":case"Host":case"Referer":case"Origin":break;default:t[l]=o;break}})),t["Content-Type"]=this.model["Content-Type"],this.form.FHeaders=JSON.stringify(t),a()}else this.$alert("请填写请求头！","提示",{type:"error"})}else a()}})},async validateCookie(){this.isSaveing=!0;let e={};try{e=JSON.parse(this.form.FHeaders),e["Content-Type"]=this.model["Content-Type"],this.model["User-Agent"]&&""!=this.model["User-Agent"]?e["User-Agent"]=this.model["User-Agent"]:this.model["User-Agent"]=e["User-Agent"],this.form.FHeaders=JSON.stringify(e)}catch{this.form.FHeaders=JSON.stringify(this.model)}let l=await this.$API.biliCookies.validateCookie.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?Object.assign(this.form,l.data):this.$alert(l.message,"提示",{type:"error"})},open(e="add"){return this.mode=e,this.visible=!0,this},async login(){if(this.form.FProxyId)return void this.$alert("已配置代理，请勿使用浏览器功能！","提示",{type:"error"});if(!this.model["User-Agent"])return void this.$alert("请输入User-Agent","提示",{type:"error"});this.isSaveing=!0;let e=this.form;e.UserAgent=this.model["User-Agent"];let l=await this.$API.biliCookies.loginBili.post({jObjectParam:e});this.isSaveing=!1,0==l.code&&1==l.data.length?Object.assign(this.form,l.data[0]):this.$alert(l.message,"提示",{type:"error"})},async save(){await this.validateCookie(),this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.isSaveing=!0,this.form.FStatus="";var l=await this.$API.biliCookies.editCookie.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.visible=!1,this.$alert(l.message,"提示",{type:"success"})):this.$alert(l.message,"提示",{type:"error"})}))},async setData(e){Object.assign(this.form,e),this.form.FProxyId=0==e.FProxyId?"":e.FProxyId;try{this.model["User-Agent"]=JSON.parse(e.FHeaders)["User-Agent"]}catch(l){console.log(l.message)}},opened(){},async openChromium(){if(this.form.FProxyId)return void this.$alert("已配置代理，请勿使用浏览器功能！","提示",{type:"error"});if(!this.model["User-Agent"])return void this.$alert("请输入User-Agent","提示",{type:"error"});let e=this.form;e.UserAgent=this.model["User-Agent"],await this.$API.biliCookies.openChromium.post({jObjectParam:e})}}},r=a(6262);const d=(0,r.A)(s,[["render",i]]);var n=d},8505:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return c}});var t=a(641);const o={class:"left-panel"},i={class:"right-panel"};function s(e,l,a,s,r,d){const n=(0,t.g2)("el-option"),u=(0,t.g2)("el-select"),c=(0,t.g2)("el-button"),m=(0,t.g2)("el-popconfirm"),p=(0,t.g2)("el-header"),f=(0,t.g2)("el-table-column"),h=(0,t.g2)("el-switch"),b=(0,t.g2)("el-button-group"),g=(0,t.g2)("scTable"),F=(0,t.g2)("el-form"),k=(0,t.g2)("el-dialog");return(0,t.uX)(),(0,t.Wv)(k,{class:"dialog-table",modelValue:r.visible,"onUpdate:modelValue":l[3]||(l[3]=e=>r.visible=e),title:r.titleMap[r.mode],width:1440,"destroy-on-close":"",onClosed:l[4]||(l[4]=l=>e.$emit("closed")),style:{height:"auto","max-height":"100%"}},{footer:(0,t.k6)((()=>[(0,t.bF)(c,{onClick:l[2]||(l[2]=e=>r.visible=!1)},{default:(0,t.k6)((()=>[(0,t.eW)("关 闭")])),_:1})])),default:(0,t.k6)((()=>[(0,t.bF)(p,{style:{"padding-left":"0px","border-bottom":"0px"}},{default:(0,t.k6)((()=>[(0,t.Lk)("div",o,[(0,t.bF)(u,{modelValue:r.jObjectSearch.areaId,"onUpdate:modelValue":l[0]||(l[0]=e=>r.jObjectSearch.areaId=e),filterable:"",onChange:d.upsearch,style:{"padding-right":"10px"}},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(r.area,(e=>((0,t.uX)(),(0,t.Wv)(n,{key:e.Fid,label:e.FName,value:e.Fid,placeholder:"请选择分区"},null,8,["label","value"])))),128))])),_:1},8,["modelValue","onChange"]),(0,t.bF)(c,{type:"primary",icon:"el-icon-refresh",onClick:d.upsearch},{default:(0,t.k6)((()=>[(0,t.eW)(" 刷 新")])),_:1},8,["onClick"]),(0,t.bF)(c,{type:"primary",icon:"el-icon-search",onClick:l[1]||(l[1]=e=>{r.jObjectSearch.areaId="",d.upsearch()})},{default:(0,t.k6)((()=>[(0,t.eW)(" 查询全局")])),_:1}),(0,t.bF)(c,{type:"primary",icon:"el-icon-refresh-right",onClick:d.restoreDefault},{default:(0,t.k6)((()=>[(0,t.eW)(" 恢复默认")])),_:1},8,["onClick"]),(0,t.bF)(c,{type:"primary",icon:"el-icon-promotion",onClick:d.syncAll},{default:(0,t.k6)((()=>[(0,t.eW)(" 同步账号")])),_:1},8,["onClick"])]),(0,t.Lk)("div",i,[(0,t.bF)(m,{title:"删除该账号所有任务吗？",width:"220px",onConfirm:d.delAll},{reference:(0,t.k6)((()=>[(0,t.bF)(c,{type:"danger",icon:"el-icon-delete"},{default:(0,t.k6)((()=>[(0,t.eW)(" 全部删除")])),_:1})])),_:1},8,["onConfirm"])])])),_:1}),(0,t.bF)(F,{style:{height:"550px"}},{default:(0,t.k6)((()=>[(0,t.bF)(g,{ref:"table",apiObj:r.apiObj,"highlight-current-row":"","row-key":"Fid",params:{jObjectSearch:r.jObjectSearch},"row-Style":d.rowStyle,border:"",hideDo:!0,hidePagination:!0},{default:(0,t.k6)((()=>[(0,t.bF)(f,{label:"状态",prop:"FStatus",align:"center",width:"80",formatter:d.formatterStatus},null,8,["formatter"]),(0,t.bF)(f,{label:"是否强制",prop:"FCompulsoryName",align:"center",width:"75"}),(0,t.bF)(f,{label:"排序",prop:"FSort",align:"center",width:"70"}),(0,t.bF)(f,{label:"分区名称",prop:"FAreaName",align:"center",width:"110"}),(0,t.bF)(f,{label:"任务名称",prop:"FTaskName",align:"center","show-overflow-tooltip":""}),(0,t.bF)(f,{label:"奖励名称",prop:"FAwardName",align:"center","show-overflow-tooltip":""}),(0,t.bF)(f,{label:"总计库存",prop:"FStockTotal",align:"center",width:"75"}),(0,t.bF)(f,{label:"总计剩余",prop:"FStockConsumed",align:"center",width:"75",formatter:d.formatterStock},null,8,["formatter"]),(0,t.bF)(f,{label:"每日库存",prop:"FPeriodTotal",align:"center",width:"75"}),(0,t.bF)(f,{label:"每日剩余",prop:"FPeriodConsumed",align:"center",width:"75",formatter:d.formatterPeriod},null,8,["formatter"]),(0,t.bF)(f,{label:"每日任务",prop:"FDailyName",align:"center",width:"75"}),(0,t.bF)(f,{label:"是否启用",prop:"FEnable",align:"center",width:"75"},{default:(0,t.k6)((e=>[(0,t.bF)(h,{modelValue:e.row.FEnable,"onUpdate:modelValue":l=>e.row.FEnable=l,onChange:l=>d.enableSwitch(l,e.row),loading:e.row.$enable,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue","onChange","loading"])])),_:1}),(0,t.bF)(f,{label:"操作",fixed:"right",align:"center",width:"230"},{default:(0,t.k6)((e=>[(0,t.bF)(b,null,{default:(0,t.k6)((()=>[(0,t.bF)(c,{text:"",type:"primary",size:"small",onClick:l=>d.setSort(e.row,e.$index)},{default:(0,t.k6)((()=>[(0,t.eW)("排序")])),_:2},1032,["onClick"]),0==e.row.FCompulsory?((0,t.uX)(),(0,t.Wv)(c,{key:0,text:"",size:"small",style:{color:"#60F"},onClick:l=>d.setCompulsory(e.row,e.$index,1)},{default:(0,t.k6)((()=>[(0,t.eW)("强制")])),_:2},1032,["onClick"])):(0,t.Q3)("",!0),1==e.row.FCompulsory?((0,t.uX)(),(0,t.Wv)(c,{key:1,text:"",size:"small",style:{color:"#555"},onClick:l=>d.setCompulsory(e.row,e.$index,0)},{default:(0,t.k6)((()=>[(0,t.eW)("取消强制")])),_:2},1032,["onClick"])):(0,t.Q3)("",!0),(0,t.bF)(c,{text:"",type:"warning",size:"small",onClick:l=>d.updateTask(e.row,e.$index)},{default:(0,t.k6)((()=>[(0,t.eW)("更新")])),_:2},1032,["onClick"]),(0,t.bF)(m,{title:"确定删除？",onConfirm:l=>d.del(e.row,e.$index)},{reference:(0,t.k6)((()=>[(0,t.bF)(c,{text:"",type:"danger",size:"small"},{default:(0,t.k6)((()=>[(0,t.eW)("删除")])),_:1})])),_:2},1032,["onConfirm"])])),_:2},1024)])),_:1})])),_:1},8,["apiObj","params","row-Style"])])),_:1})])),_:1},8,["modelValue","title"])}var r=a(1132),d={emits:["success","closed"],data(){return{mode:"show",titleMap:{show:"任务信息"},visible:!1,isSaveing:!1,apiObj:null,jObjectSearch:{},area:[]}},async created(){let e=await this.$API.biliArea.getAreaList.post({jObjectSearch:{}});0==e.code&&(this.area=e.data.rows)},mounted(){this.getSelect()},methods:{getSelect(){},open(e="show"){return this.mode=e,this.visible=!0,this},async setData(e){Object.assign(this.jObjectSearch,e),this.apiObj=this.$API.biliCookiesTask.getCookiesTaskList},rowStyle(e){return 20==e.row.FStatus?"color:#6600FF":e.row.FStatus>=10?"color:#006600":e.row.FStatus<0?"color:#CC0000":0==e.row.FStatus?"color:#CC6600":void 0},formatterStock(e){return(Number(e.FStockTotal)-Number(e.FStockConsumed)).toFixed(0)},formatterPeriod(e){return(Number(e.FPeriodTotal)-Number(e.FPeriodConsumed)).toFixed(0)},formatterStatus(e){let l="";switch(e.FStatus){case-20:l="状态不正确";break;case-10:l="已禁用";break;case-8:l="库存不足";break;case-5:l="已领取";break;case 0:1==e.FReceiveStatus?l="已完成":3==e.FReceiveStatus&&(l="已领取");break;case 5:case 20:l=1==e.FReceiveStatus?"已完成":3==e.FReceiveStatus?"已领取":"";break;case 10:l="已完成";break;default:l="状态未知";break}return l},async setCompulsory(e,l,a){let t=await this.$API.biliCookiesTask.setCompulsory.post({jObjectParam:{Fid:e.Fid,FCompulsory:a}});0==t.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(t.message,"提示",{type:"error"})},async syncAll(){let e=await this.$API.biliCookiesTask.syncAll.post({jObjectParam:{cookieId:this.jObjectSearch.cookieId,areaId:this.jObjectSearch.areaId}});0==e.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(e.message,"提示",{type:"error"})},async restoreDefault(){let e=await this.$API.biliCookiesTask.restoreDefault.post({jObjectParam:{cookieId:this.jObjectSearch.cookieId,areaId:this.jObjectSearch.areaId}});0==e.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(e.message,"提示",{type:"error"})},async setSort(e){this.$prompt("","设置排序",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnPressEscape:!0,closeOnClickModal:!0,autofocus:!0,inputType:"number",inputPattern:/^[0-9-]/,inputErrorMessage:"请输入排序。（正负整数）"}).then((async({value:l})=>{let a=await this.$API.biliCookiesTask.setSort.post({jObjectParam:{Fid:e.Fid,FSort:l}});0==a.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(a.message,"提示",{type:"error"})}))},async delAll(){let e=await this.$API.biliCookiesTask.delAll.post({jObjectParam:{cookieId:this.jObjectSearch.cookieId,areaId:this.jObjectSearch.areaId}});0==e.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(e.message,"提示",{type:"error"})},async del(e){let l=await this.$API.biliCookiesTask.delAll.post({jObjectParam:{cookieId:this.jObjectSearch.cookieId,areaId:this.jObjectSearch.areaId,id:e.Fid}});0==l.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(l.message,"提示",{type:"error"})},async updateTask(e){const l=r.Ks.service({lock:!0,text:"执行中...",background:"rgba(0, 0, 0, 0.7)"});try{let l=await this.$API.biliQuartz.manualExecQuartz.post({jObjectParam:{array:[{Fid:this.jObjectSearch.cookieId}],areaId:e.FAreaId,jobName:"更新任务信息",type:this.$TOOL.data.get("EXECMODE"),param:e.FAreaTaskId,delay:1}});if(0!=l.code)throw new Error(l.message);this.$message.success(l.message),this.upsearch()}catch(a){this.$alert(a.message,"提示",{type:"error"})}l.close()},async enableSwitch(e,l){l.$enable=!0;let a=await this.$API.biliCookiesTask.enableSwitch.post({jObjectParam:l});0==a.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(a.message,"提示",{type:"error"}),delete l.$enable},upsearch(){this.$refs.table.upData({jObjectSearch:this.jObjectSearch})}}},n=a(6262);const u=(0,n.A)(d,[["render",s]]);var c=u},4803:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return d}});var t=a(641);function o(e,l,a,o,i,s){const r=(0,t.g2)("el-input"),d=(0,t.g2)("el-form-item"),n=(0,t.g2)("el-col"),u=(0,t.g2)("sc-select"),c=(0,t.g2)("el-row"),m=(0,t.g2)("el-form"),p=(0,t.g2)("el-button"),f=(0,t.g2)("el-dialog"),h=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(f,{modelValue:i.visible,"onUpdate:modelValue":l[14]||(l[14]=e=>i.visible=e),title:i.titleMap[i.mode],width:1200,"destroy-on-close":"",onClosed:l[15]||(l[15]=l=>e.$emit("closed"))},{footer:(0,t.k6)((()=>["show"!=i.mode?((0,t.uX)(),(0,t.Wv)(p,{key:0,type:"primary",loading:i.isSaveing,onClick:l[13]||(l[13]=e=>s.save())},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["loading"])):(0,t.Q3)("",!0)])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(m,{model:i.form,disabled:"show"==i.mode,ref:"dialogForm","label-width":"125px"},{default:(0,t.k6)((()=>[(0,t.bF)(c,null,{default:(0,t.k6)((()=>[(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"打码名称",prop:"FName",rules:[{required:!0,message:"请输入打码名称"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FName,"onUpdate:modelValue":l[0]||(l[0]=e=>i.form.FName=e),placeholder:"打码名称"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:16},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"请求地址",prop:"FUrl",rules:[{required:!0,message:"请输入请求地址"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FUrl,"onUpdate:modelValue":l[1]||(l[1]=e=>i.form.FUrl=e),placeholder:"请求地址"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请选择请求方式"}],label:"请求方式",prop:"FMethod",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(u,{clearable:"",modelValue:i.form.FMethod,"onUpdate:modelValue":l[2]||(l[2]=e=>i.form.FMethod=e),apiObj:[{label:"POST",value:1},{label:"GET",value:0}],placeholder:" 请求方式",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"gt键值",prop:"FGt",rules:[{required:!0,message:"请输入gt键值"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FGt,"onUpdate:modelValue":l[3]||(l[3]=e=>i.form.FGt=e),placeholder:"gt键值"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"challenge键值",prop:"FChallenge",rules:[{required:!0,message:"请输入challenge键值"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FChallenge,"onUpdate:modelValue":l[4]||(l[4]=e=>i.form.FChallenge=e),placeholder:"challenge键值"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"秘钥键值",prop:"FKey",rules:[{required:!0,message:"请输入秘钥键值"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FKey,"onUpdate:modelValue":l[5]||(l[5]=e=>i.form.FKey=e),placeholder:"秘钥键值"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:16},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"打码秘钥",prop:"FKeyValue",rules:[{required:!0,message:"请输入打码秘钥"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FKeyValue,"onUpdate:modelValue":l[6]||(l[6]=e=>i.form.FKeyValue=e),placeholder:"打码秘钥"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"成功键值",prop:"FSuccess",rules:[{required:!0,message:"成功键值"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FSuccess,"onUpdate:modelValue":l[7]||(l[7]=e=>i.form.FSuccess=e),placeholder:"成功键值"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"成功代码",prop:"FSuccessCode",rules:[{required:!0,message:"请输入成功代码"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FSuccessCode,"onUpdate:modelValue":l[8]||(l[8]=e=>i.form.FSuccessCode=e),placeholder:"成功代码"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"Validate键值",prop:"FResultValidate",rules:[{required:!0,message:"请输入Validate键值"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FResultValidate,"onUpdate:modelValue":l[9]||(l[9]=e=>i.form.FResultValidate=e),placeholder:"js的JSON取值写法，data.validate"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"类型键值",prop:"FType"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FType,"onUpdate:modelValue":l[10]||(l[10]=e=>i.form.FType=e),placeholder:"类型键值"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:16},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"类型数值",prop:"FTypeValue"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FTypeValue,"onUpdate:modelValue":l[11]||(l[11]=e=>i.form.FTypeValue=e),placeholder:"类型数值"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"其他参数",prop:"FParamValue"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FParamValue,"onUpdate:modelValue":l[12]||(l[12]=e=>i.form.FParamValue=e),style:{width:"100%"},rows:4,type:"textarea",resize:"none",placeholder:"JSON格式"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","disabled"])),[[h,i.isSaveing]])])),_:1},8,["modelValue","title"])}var i={emits:["success","closed"],data(){return{mode:"add",titleMap:{add:"新增",edit:"编辑",show:"查看"},visible:!1,isSaveing:!1,form:{Fid:0,FEnable:1}}},mounted(){this.getSelect()},methods:{getSelect(){},open(e="add"){return this.mode=e,this.visible=!0,this},save(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.isSaveing=!0;var l=await this.$API.biliGeetest.editGeetest.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.$message.success("操作成功"),this.visible=!1):this.$alert(l.message,"提示",{type:"error"})}))},setData(e){Object.assign(this.form,e)}}},s=a(6262);const r=(0,s.A)(i,[["render",o]]);var d=r},9475:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return d}});var t=a(641);function o(e,l,a,o,i,s){const r=(0,t.g2)("el-input"),d=(0,t.g2)("el-form-item"),n=(0,t.g2)("el-col"),u=(0,t.g2)("el-row"),c=(0,t.g2)("el-form"),m=(0,t.g2)("el-button"),p=(0,t.g2)("el-dialog"),f=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(p,{modelValue:i.visible,"onUpdate:modelValue":l[4]||(l[4]=e=>i.visible=e),title:i.titleMap[i.mode],width:768,"destroy-on-close":"",onClosed:l[5]||(l[5]=l=>e.$emit("closed"))},{footer:(0,t.k6)((()=>["show"!=i.mode?((0,t.uX)(),(0,t.Wv)(m,{key:0,type:"primary",loading:i.isSaveing,onClick:l[3]||(l[3]=e=>s.save())},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["loading"])):(0,t.Q3)("",!0)])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(c,{model:i.form,disabled:"show"==i.mode,ref:"dialogForm","label-width":"125px"},{default:(0,t.k6)((()=>[(0,t.bF)(u,null,{default:(0,t.k6)((()=>[(0,t.bF)(n,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"代理地址",prop:"FAddress",rules:[{required:!0,message:"请输入代理地址"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FAddress,"onUpdate:modelValue":l[0]||(l[0]=e=>i.form.FAddress=e),placeholder:"例如：http://***********:20182（注：这只是个例子，代理配错接口无法访问）"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"代理账号",prop:"FUserName"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FUserName,"onUpdate:modelValue":l[1]||(l[1]=e=>i.form.FUserName=e),placeholder:"代理账号（加白名单后可不填）"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"代理密码",prop:"FPassword"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FPassword,"onUpdate:modelValue":l[2]||(l[2]=e=>i.form.FPassword=e),placeholder:"代理密码（加白名单后可不填）"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","disabled"])),[[f,i.isSaveing]])])),_:1},8,["modelValue","title"])}var i={emits:["success","closed"],data(){return{mode:"add",titleMap:{add:"新增",edit:"编辑",show:"查看"},visible:!1,isSaveing:!1,form:{Fid:0,FEnable:1}}},mounted(){this.getSelect()},methods:{getSelect(){},open(e="add"){return this.mode=e,this.visible=!0,this},save(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.isSaveing=!0;var l=await this.$API.biliProxy.saveProxy.post({jObjectParam:{Fid:this.form.Fid,FAddress:this.form.FAddress,FUserName:this.form.FUserName,FPassword:this.form.FPassword,FEnable:this.form.FEnable}});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.$message.success("操作成功"),this.visible=!1):this.$alert(l.message,"提示",{type:"error"})}))},setData(e){Object.assign(this.form,e)}}},s=a(6262);const r=(0,s.A)(i,[["render",o]]);var d=r},7493:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return n}});var t=a(641);function o(e,l,a,o,i,s){const r=(0,t.g2)("el-input"),d=(0,t.g2)("el-form-item"),n=(0,t.g2)("el-col"),u=(0,t.g2)("el-row"),c=(0,t.g2)("el-form"),m=(0,t.g2)("el-button"),p=(0,t.g2)("el-tooltip"),f=(0,t.g2)("el-dialog"),h=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(f,{modelValue:i.visible,"onUpdate:modelValue":l[5]||(l[5]=e=>i.visible=e),title:i.titleMap[i.mode],width:400,"destroy-on-close":"","close-on-click-modal":!1,onOpened:s.opened,onClosed:l[6]||(l[6]=l=>e.$emit("closed"))},{footer:(0,t.k6)((()=>[(0,t.bF)(p,{class:"box-item",effect:"dark",content:"只会分享执行时间、随机区间、执行时长、任务名称、分区名称和参数内容。",placement:"top"},{default:(0,t.k6)((()=>[(0,t.bF)(m,{loading:i.isSaveing,onClick:l[2]||(l[2]=e=>s.share()),style:{float:"left"}},{default:(0,t.k6)((()=>[(0,t.eW)("分享配置")])),_:1},8,["loading"])])),_:1}),(0,t.bF)(m,{class:"left",loading:i.isSaveing,onClick:l[3]||(l[3]=e=>s.exports()),style:{float:"left"}},{default:(0,t.k6)((()=>[(0,t.eW)("导入配置")])),_:1},8,["loading"]),(0,t.bF)(m,{type:"primary",loading:i.isSaveing,onClick:l[4]||(l[4]=e=>s.save())},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["loading"])])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(c,{model:i.form,disabled:"show"==i.mode,ref:"dialogForm","label-width":"100px"},{default:(0,t.k6)((()=>[(0,t.bF)(u,null,{default:(0,t.k6)((()=>[(0,t.bF)(n,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"分组名称",prop:"FName",rules:[{required:!0,message:"请输入分组名称"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FName,"onUpdate:modelValue":l[0]||(l[0]=e=>i.form.FName=e),placeholder:"分组名称"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请输入排列序号"}],label:"排列序号",prop:"FSort",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{type:"number",modelValue:i.form.FSort,"onUpdate:modelValue":l[1]||(l[1]=e=>i.form.FSort=e),placeholder:"排列序号"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","disabled"])),[[h,i.isSaveing]])])),_:1},8,["modelValue","title","onOpened"])}var i=a(1132),s={emits:["success","closed"],data(){return{mode:"add",titleMap:{add:"新增",edit:"编辑",show:"查看"},visible:!1,isSaveing:!1,form:{Fid:0,FStatus:0,FSort:0}}},mounted(){this.getSelect()},methods:{getSelect(){},open(e="add"){return this.mode=e,this.visible=!0,this},treeSelectClick(e){this.form.FParentId=e.parent_id,this.form.FName=e.name},save(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.isSaveing=!0;var l=await this.$API.biliQuartzGroup.saveQuartzGroup.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.$message.success("操作成功"),this.visible=!1):this.$alert(l.message,"提示",{type:"error"})}))},async share(){this.isSaveing=!0;let e=await this.$API.biliQuartz.shareQuartz.post({jObjectParam:this.form});0==e.code?this.$prompt("分享配置只会分享执行时间、随机区间、执行时长、任务名称、分区名称和参数内容，其他不会分享。","分享配置",{confirmButtonText:"确定",autofocus:!0,cancelButtonText:"取消",inputType:"textarea",customClass:"custom-message-box-width",showCancelButton:!1,showConfirmButton:!1,inputValue:e.data}):this.$alert(e.message,"提示",{type:"error"}),this.isSaveing=!1},async exports(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.$prompt("注意：导入之后会清空原有的信息！","导入配置",{confirmButtonText:"确定",autofocus:!0,cancelButtonText:"取消",inputType:"textarea",customClass:"custom-message-box-width",beforeClose:async(e,l,a)=>{if("confirm"==e){let e=l.inputValue;if(e&&""!=e){this.isSaveing=!0,this.form.value=e;const l=i.Ks.service({lock:!0,text:"Loading",background:"rgba(0, 0, 0, 0.7)"});let t=await this.$API.biliQuartz.exportQuartz.post({jObjectParam:this.form});l.close(),0==t.code?(this.$emit("success",this.form,this.mode),this.$message.success("操作成功"),this.visible=!1,a()):this.$alert(t.message,"提示",{type:"error"}),this.isSaveing=!1}else this.$alert("请填写导入信息！","提示",{type:"error"})}else a()}})}))},async setData(e){Object.assign(this.form,e),this.form.Fid=String(e.Fid)},opened(){}}},r=a(6262);const d=(0,r.A)(s,[["render",o]]);var n=d},8120:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return n}});var t=a(641),o=a(2644);function i(e,l,a,i,s,r){const d=(0,t.g2)("el-time-picker"),n=(0,t.g2)("el-form-item"),u=(0,t.g2)("el-col"),c=(0,t.g2)("el-input"),m=(0,t.g2)("el-tooltip"),p=(0,t.g2)("sc-tree-select"),f=(0,t.g2)("sc-select"),h=(0,t.g2)("el-text"),b=(0,t.g2)("el-row"),g=(0,t.g2)("el-form"),F=(0,t.g2)("el-button"),k=(0,t.g2)("el-dialog"),v=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(k,{modelValue:s.visible,"onUpdate:modelValue":l[9]||(l[9]=e=>s.visible=e),title:s.titleMap[s.mode],width:1024,"destroy-on-close":"","close-on-click-modal":!1,onOpened:r.opened,onClosed:l[10]||(l[10]=l=>e.$emit("closed"))},{footer:(0,t.k6)((()=>["show"!=s.mode?((0,t.uX)(),(0,t.Wv)(F,{key:0,type:"primary",loading:s.isSaveing,onClick:l[8]||(l[8]=e=>r.save())},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["loading"])):(0,t.Q3)("",!0)])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(g,{model:s.form,disabled:"show"==s.mode,ref:"dialogForm","label-width":"110px"},{default:(0,t.k6)((()=>[(0,t.bF)(b,null,{default:(0,t.k6)((()=>[(0,t.bF)(u,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(n,{rules:[{required:!0,message:"请选择执行时间"}],label:"执行时间",prop:"FExecTime",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(d,{modelValue:s.form.FExecTime,"onUpdate:modelValue":l[0]||(l[0]=e=>s.form.FExecTime=e),editable:"",placeholder:"执行时间",format:"HH:mm:ss","value-format":"HH:mm:ss"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(u,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(m,{class:"box-item",effect:"dark",content:"定时任务启动后的随机X秒内执行",placement:"top"},{default:(0,t.k6)((()=>[(0,t.bF)(n,{label:"随机时间",prop:"FDifference",rules:[{required:!0,message:"请输入随机区间"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(c,{type:"number",modelValue:s.form.FDifference,"onUpdate:modelValue":l[1]||(l[1]=e=>s.form.FDifference=e),placeholder:"随机时间"},{append:(0,t.k6)((()=>[(0,t.eW)("秒")])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),(0,t.bF)(u,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(n,{label:"执行时长",prop:"FSeconds",rules:[{required:!0,message:"请输入执行时长"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(c,{type:"number",modelValue:s.form.FSeconds,"onUpdate:modelValue":l[2]||(l[2]=e=>s.form.FSeconds=e),placeholder:"执行时长"},{append:(0,t.k6)((()=>[(0,t.eW)("秒")])),_:1},8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(u,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(n,{rules:[{required:!0,message:"请选择任务名称"}],label:"任务名称",prop:"FJobId",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(p,{modelValue:s.form.FJobId,"onUpdate:modelValue":l[3]||(l[3]=e=>s.form.FJobId=e),ref:"treeSelect",placeholder:"请选择任务名称",prop:{label:"FName",value:"Fid",children:"list"},"default-expand-all":"",onNodeClick:r.treeSelectClick,params:{jObjectSearch:{}},initial:r.treeInitial,apiObj:e.$API.biliQuartz.getQuartzJobList,style:{width:"100%"}},null,8,["modelValue","onNodeClick","initial","apiObj"])])),_:1})])),_:1}),s.job.FType?((0,t.uX)(),(0,t.Wv)(u,{key:0,span:8},{default:(0,t.k6)((()=>[(0,t.bF)(n,{rules:[{required:!0,message:"请选择执行类型"}],label:"执行类型",prop:"FType",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(f,{clearable:"",modelValue:s.form.FType,"onUpdate:modelValue":l[4]||(l[4]=e=>s.form.FType=e),params:{jObjectSearch:{}},apiObj:[{Fid:"接口",FName:"接口"},{Fid:"浏览器",FName:"浏览器"}],prop:{label:"FName",value:"Fid"},placeholder:"执行类型",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})):(0,t.Q3)("",!0),s.job.FArea?((0,t.uX)(),(0,t.Wv)(u,{key:1,span:8},{default:(0,t.k6)((()=>[(0,t.bF)(n,{rules:[{required:!0,message:"请选择分区名称"}],label:"分区名称",prop:"FAreaId",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(f,{clearable:"",modelValue:s.form.FAreaId,"onUpdate:modelValue":l[5]||(l[5]=e=>s.form.FAreaId=e),params:{jObjectSearch:{}},apiObj:e.$API.biliArea.getAreaList,prop:{label:"FName",value:"Fid"},placeholder:" 分区名称",style:{width:"100%"}},null,8,["modelValue","apiObj"])])),_:1})])),_:1})):(0,t.Q3)("",!0),s.job.FCookie?((0,t.uX)(),(0,t.Wv)(u,{key:2,span:24},{default:(0,t.k6)((()=>[(0,t.bF)(n,{label:"账号名称",prop:"FCookie"},{default:(0,t.k6)((()=>[(0,t.bF)(f,{clearable:"",modelValue:s.form.FCookie,"onUpdate:modelValue":l[6]||(l[6]=e=>s.form.FCookie=e),params:{jObjectSearch:{}},multiple:"","collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":4,apiObj:e.$API.biliCookies.getCookiesList,prop:{label:"FName",value:"Fid"},placeholder:"不选：默认全部账号",style:{width:"100%"}},null,8,["modelValue","apiObj"])])),_:1})])),_:1})):(0,t.Q3)("",!0),s.job.FLabel?((0,t.uX)(),(0,t.Wv)(u,{key:3,span:24},{default:(0,t.k6)((()=>[(0,t.bF)(n,{label:s.job.FLabel,prop:"FParam"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{placeholder:s.job.FPlaceholder,modelValue:s.form.FParam,"onUpdate:modelValue":l[7]||(l[7]=e=>s.form.FParam=e)},null,8,["placeholder","modelValue"])])),_:1},8,["label"])])),_:1})):(0,t.Q3)("",!0),s.job.FTip?((0,t.uX)(),(0,t.Wv)(u,{key:4,span:24},{default:(0,t.k6)((()=>[(0,t.bF)(n,{label:"注意事项"},{default:(0,t.k6)((()=>[(0,t.bF)(h,{class:"mx-1"},{default:(0,t.k6)((()=>[(0,t.eW)((0,o.v_)(s.job.FTip),1)])),_:1})])),_:1})])),_:1})):(0,t.Q3)("",!0)])),_:1})])),_:1},8,["model","disabled"])),[[v,s.isSaveing]])])),_:1},8,["modelValue","title","onOpened"])}var s={emits:["success","closed"],data(){return{mode:"add",titleMap:{add:"新增",edit:"编辑",show:"查看"},visible:!1,isSaveing:!1,form:{Fid:0,FGroupId:0,FDifference:0,FSeconds:0,FJobId:"",FCookie:[]},job:{FArea:0,FCookie:0,FLabel:"",FPlaceholder:""}}},mounted(){this.getSelect()},methods:{getSelect(){},open(e="add"){return this.mode=e,this.visible=!0,this},treeSelectClick(e){this.job=e,this.form.FJobName=e.FName,0==this.job.FCookie&&(this.form.FCookie=[]),0==this.job.FArea&&(this.form.FAreaId=""),this.job.FLabel||(this.form.FParam="")},save(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.isSaveing=!0,this.form.FCookieId=String(this.form.FCookie);var l=await this.$API.biliQuartz.saveQuartz.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.$message.success("操作成功"),this.visible=!1):this.$alert(l.message,"提示",{type:"error"})}))},setData(e){Object.assign(this.form,e),"edit"==this.mode&&(""==this.form.FCookieId?this.form.FCookie=[]:this.form.FCookie=String(this.form.FCookieId).split(",").map(Number))},treeInitial(){this.$nextTick((()=>{"edit"==this.mode&&(this.job=this.$refs.treeSelect.getCurrentNode())}))},opened(){}}},r=a(6262);const d=(0,r.A)(s,[["render",i]]);var n=d},1570:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return d}});var t=a(641);function o(e,l,a,o,i,s){const r=(0,t.g2)("el-input"),d=(0,t.g2)("el-form-item"),n=(0,t.g2)("el-col"),u=(0,t.g2)("sc-select"),c=(0,t.g2)("el-row"),m=(0,t.g2)("el-form"),p=(0,t.g2)("el-button"),f=(0,t.g2)("el-tooltip"),h=(0,t.g2)("el-dialog"),b=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(h,{modelValue:i.visible,"onUpdate:modelValue":l[14]||(l[14]=e=>i.visible=e),title:i.titleMap[i.mode],width:1024,"destroy-on-close":"","close-on-click-modal":!1,onOpened:s.opened,onClosed:l[15]||(l[15]=l=>e.$emit("closed"))},{footer:(0,t.k6)((()=>[(0,t.bF)(f,{class:"box-item",effect:"dark",content:"同步会删除Cdkey信息，任务信息请谨慎操作！",placement:"top"},{default:(0,t.k6)((()=>[(0,t.bF)(p,{icon:"el-icon-refresh",onClick:l[12]||(l[12]=e=>s.asyncTask()),style:{float:"left"},loading:i.isSaveing},{default:(0,t.k6)((()=>[(0,t.eW)("同步数据")])),_:1},8,["loading"])])),_:1}),(0,t.bF)(f,{class:"box-item",effect:"dark",content:"编辑里程、抽奖和活动Act时，会删除除此act的所有Cdkey信息",placement:"top"},{default:(0,t.k6)((()=>[(0,t.bF)(p,{type:"primary",loading:i.isSaveing,onClick:l[13]||(l[13]=e=>s.save())},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["loading"])])),_:1})])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(m,{model:i.form,disabled:"show"==i.mode,ref:"dialogForm","label-width":"110px"},{default:(0,t.k6)((()=>[(0,t.bF)(c,null,{default:(0,t.k6)((()=>[(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请输入分区CID1"}],label:"分区CID1",prop:"FCid1",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FCid1,"onUpdate:modelValue":l[0]||(l[0]=e=>i.form.FCid1=e),placeholder:"分区CID1"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请输入分区CID2"}],label:"分区CID2",prop:"Fid",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.Fid,"onUpdate:modelValue":l[1]||(l[1]=e=>i.form.Fid=e),placeholder:"分区CID2"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请输入分区CID3"}],label:"分区CID3",prop:"FCid3",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FCid3,"onUpdate:modelValue":l[2]||(l[2]=e=>i.form.FCid3=e),placeholder:"分区CID3"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请输入分区名称"}],label:"分区名称",prop:"FName",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FName,"onUpdate:modelValue":l[3]||(l[3]=e=>i.form.FName=e),placeholder:"分区名称"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"领取方式",prop:"FType",rules:[{required:!0,message:"请选择领取方式"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(u,{clearable:"",modelValue:i.form.FType,"onUpdate:modelValue":l[4]||(l[4]=e=>i.form.FType=e),apiObj:[{label:"默认",value:"默认"},{label:"方式一",value:"方式一"},{label:"方式二",value:"方式二"}],placeholder:"正常情况请选择默认",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请输入排列序号"}],label:"排列序号",prop:"FSort",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{type:"number",modelValue:i.form.FSort,"onUpdate:modelValue":l[5]||(l[5]=e=>i.form.FSort=e),placeholder:"排列序号"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"任务地址",prop:"FTaskUrl",rules:[{required:!0,message:"请输入任务标识"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FTaskUrl,"onUpdate:modelValue":l[6]||(l[6]=e=>i.form.FTaskUrl=e),placeholder:"请求任务的URL"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"任务标识",prop:"FTaskId",rules:[{required:!0,message:"请输入任务标识"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FTaskId,"onUpdate:modelValue":l[7]||(l[7]=e=>i.form.FTaskId=e),placeholder:"任务标识用英文逗号(,)隔开"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"领取地址",prop:"FReceiveUrl",rules:[{required:!0,message:"请输入领取地址"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FReceiveUrl,"onUpdate:modelValue":l[8]||(l[8]=e=>i.form.FReceiveUrl=e),placeholder:"领取奖励的URL"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:16},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"奖励地址",prop:"FActivityUrl",rules:[{required:!0,message:"请输入奖励地址"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FActivityUrl,"onUpdate:modelValue":l[9]||(l[9]=e=>i.form.FActivityUrl=e),placeholder:"奖励列表的URL"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"奖励别名",prop:"FActAlias",rules:[{required:!0,message:"请输入奖励别名"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FActAlias,"onUpdate:modelValue":l[10]||(l[10]=e=>i.form.FActAlias=e),placeholder:"奖励别名"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"Referer",prop:"FReferer",rules:[{required:!0,message:"请输入Referer"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FReferer,"onUpdate:modelValue":l[11]||(l[11]=e=>i.form.FReferer=e),placeholder:"Referer"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","disabled"])),[[b,i.isSaveing]])])),_:1},8,["modelValue","title","onOpened"])}var i={emits:["success","closed"],data(){return{mode:"add",titleMap:{add:"新增",edit:"编辑",show:"查看"},visible:!1,isSaveing:!1,form:{Fid:"",FType:"默认",FSort:0,FTaskUrl:"https://www.douyu.com/japi/carnival/nc/web/roomTask/getStatus?taskIds=",FReceiveUrl:"",FActivityUrl:"https://www.douyu.com/japi/carnival/nc/giftbag/myrecord"}}},mounted(){this.getSelect()},methods:{getSelect(){},open(e="add"){return this.mode=e,this.visible=!0,this},treeSelectClick(e){this.form.FParentId=e.parent_id,this.form.FName=e.name},save(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.isSaveing=!0,this.form.mode=this.mode;var l=await this.$API.douyuArea.saveArea.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.$message.success("操作成功"),this.visible=!1):this.$alert(l.message,"提示",{type:"error"})}))},asyncTask(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.isSaveing=!0;var l=await this.$API.douyuArea.updateAreaInfo.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.$message.success("操作成功"),this.visible=!1):this.$alert(l.message,"提示",{type:"error"})}))},async setData(e){Object.assign(this.form,e),this.form.Fid=String(e.Fid)},opened(){}}},s=a(6262);const r=(0,s.A)(i,[["render",o]]);var d=r},6790:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return d}});var t=a(641);function o(e,l,a,o,i,s){const r=(0,t.g2)("el-input"),d=(0,t.g2)("el-form-item"),n=(0,t.g2)("el-col"),u=(0,t.g2)("sc-select"),c=(0,t.g2)("el-row"),m=(0,t.g2)("el-form"),p=(0,t.g2)("el-button"),f=(0,t.g2)("el-dialog"),h=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(f,{modelValue:i.visible,"onUpdate:modelValue":l[7]||(l[7]=e=>i.visible=e),title:i.titleMap[i.mode],width:1024,"destroy-on-close":"",onOpened:s.opened,onClosed:l[8]||(l[8]=l=>e.$emit("closed"))},{footer:(0,t.k6)((()=>["show"!=i.mode?((0,t.uX)(),(0,t.Wv)(p,{key:0,type:"primary",loading:i.isSaveing,onClick:l[6]||(l[6]=e=>s.save())},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["loading"])):(0,t.Q3)("",!0)])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(m,{model:i.form,disabled:"show"==i.mode,ref:"dialogForm","label-width":"120px"},{default:(0,t.k6)((()=>[(0,t.bF)(c,null,{default:(0,t.k6)((()=>[(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请输入任务Key"}],label:"任务标识",prop:"FTaskId",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FTaskId,"onUpdate:modelValue":l[0]||(l[0]=e=>i.form.FTaskId=e),placeholder:"任务标识"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:16},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"任务地址",prop:"FTaskUrl",rules:[{required:!0,message:"请输入任务地址"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FTaskUrl,"onUpdate:modelValue":l[1]||(l[1]=e=>i.form.FTaskUrl=e),placeholder:"任务地址"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请输入排列序号"}],label:"排列序号",prop:"FSort",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{type:"number",modelValue:i.form.FSort,"onUpdate:modelValue":l[2]||(l[2]=e=>i.form.FSort=e),placeholder:"排列序号"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请输入奖励单价"}],label:"奖励单价",prop:"FPrice",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{type:"number",modelValue:i.form.FPrice,"onUpdate:modelValue":l[3]||(l[3]=e=>i.form.FPrice=e),placeholder:"用于统计收益"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"显示天数",prop:"FComplete",rules:[{required:!0,message:"请选择显示完成天数"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(u,{modelValue:i.form.FComplete,"onUpdate:modelValue":l[4]||(l[4]=e=>i.form.FComplete=e),style:{width:"100%"},apiObj:[{label:"是",value:1},{label:"否",value:0}],placeholder:"显示天数"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"每日任务",prop:"FDaily",rules:[{required:!0,message:"请选择每日任务"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(u,{modelValue:i.form.FDaily,"onUpdate:modelValue":l[5]||(l[5]=e=>i.form.FDaily=e),style:{width:"100%"},apiObj:[{label:"是",value:1},{label:"否",value:0}],placeholder:"每日任务"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","disabled"])),[[h,i.isSaveing]])])),_:1},8,["modelValue","title","onOpened"])}var i={emits:["success","closed"],data(){return{mode:"add",titleMap:{add:"新增",edit:"编辑",show:"查看"},visible:!1,isSaveing:!1,form:{Fid:0,FAreaId:0,FSort:0,FTaskUrl:"https://www.douyu.com/japi/carnival/nc/web/roomTask/getStatus?taskIds=",FTaskKey:"",FPrice:0,FBulletScreen:0,FCompulsory:0,FReceiveFrom:"missionPage",FEnable:1,FComplete:0,FDaily:0}}},mounted(){this.getSelect()},methods:{getSelect(){},open(e="add"){return this.mode=e,this.visible=!0,this},save(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.isSaveing=!0,this.form.mode=this.mode;var l=await this.$API.douyuAreaTask.saveAreaTask.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.$message.success("操作成功"),this.visible=!1):this.$alert(l.message,"提示",{type:"error"})}))},async setData(e){Object.assign(this.form,e)},opened(){}}},s=a(6262);const r=(0,s.A)(i,[["render",o]]);var d=r},2826:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return u}});var t=a(641),o=a(2644);function i(e,l,a,i,s,r){const d=(0,t.g2)("el-header"),n=(0,t.g2)("el-button"),u=(0,t.g2)("sc-upload-file"),c=(0,t.g2)("el-col"),m=(0,t.g2)("el-row"),p=(0,t.g2)("el-main"),f=(0,t.g2)("el-footer"),h=(0,t.g2)("el-container"),b=(0,t.g2)("el-aside"),g=(0,t.g2)("el-input"),F=(0,t.g2)("el-table-column"),k=(0,t.g2)("el-option"),v=(0,t.g2)("el-select"),y=(0,t.g2)("sc-form-table"),_=(0,t.g2)("el-dialog"),V=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(_,{class:"dialog-table",modelValue:s.visible,"onUpdate:modelValue":l[5]||(l[5]=e=>s.visible=e),title:s.titleMap[s.mode],width:1440,"destroy-on-close":"",onClosed:l[6]||(l[6]=l=>e.$emit("closed")),style:{height:"auto","max-height":"100%"}},{footer:(0,t.k6)((()=>[(0,t.bF)(n,{onClick:l[3]||(l[3]=e=>s.visible=!1)},{default:(0,t.k6)((()=>[(0,t.eW)("关 闭")])),_:1}),(0,t.bF)(n,{type:"primary",loading:s.isSaveing,onClick:l[4]||(l[4]=e=>r.save())},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["loading"])])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(h,{class:"layer-customer"},{default:(0,t.k6)((()=>[(0,t.bF)(b,{width:"450px"},{default:(0,t.k6)((()=>[(0,t.bF)(h,null,{default:(0,t.k6)((()=>[(0,t.bF)(d,null,{default:(0,t.k6)((()=>[(0,t.eW)("直播视频")])),_:1}),(0,t.bF)(p,null,{default:(0,t.k6)((()=>[(0,t.bF)(m,null,{default:(0,t.k6)((()=>[(0,t.bF)(c,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(u,{modelValue:s.form.livePath,"onUpdate:modelValue":l[0]||(l[0]=e=>s.form.livePath=e),apiObj:s.uploadApi,limit:99,"on-remove":r.handleRemove,params:{jObjectParam:{path:s.form.areaName}},tip:"最多上传99个文件,单个文件不要超过2G,请上传mp4格式文件"+(""!=s.form.livePath?"               删除 ↓":"")},{default:(0,t.k6)((()=>[(0,t.bF)(n,{type:"primary",icon:"el-icon-upload"},{default:(0,t.k6)((()=>[(0,t.eW)("上传视频")])),_:1})])),_:1},8,["modelValue","apiObj","on-remove","params","tip"])])),_:1})])),_:1})])),_:1}),(0,t.bF)(f,{height:"80px"},{default:(0,t.k6)((()=>[(0,t.eW)(" 视频转码："),(0,t.Lk)("span",null,(0,o.v_)(s.videoCode),1)])),_:1})])),_:1})])),_:1}),(0,t.bF)(h,null,{default:(0,t.k6)((()=>[(0,t.bF)(d,null,{default:(0,t.k6)((()=>[(0,t.eW)("礼物 / 弹幕 / 观看")])),_:1}),(0,t.bF)(p,null,{default:(0,t.k6)((()=>[(0,t.bF)(y,{ref:"table",modelValue:s.form.liveConfig,"onUpdate:modelValue":l[1]||(l[1]=e=>s.form.liveConfig=e),addTemplate:s.addTemplate},{default:(0,t.k6)((()=>[(0,t.bF)(F,{prop:"FRoomNo",align:"center",label:"房间号"},{default:(0,t.k6)((e=>[(0,t.bF)(g,{modelValue:e.row.FRoomNo,"onUpdate:modelValue":l=>e.row.FRoomNo=l,class:"center",placeholder:"房间号"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),(0,t.bF)(F,{prop:"FGiftCode",align:"center",label:"礼物名称",width:"160"},{default:(0,t.k6)((e=>[(0,t.bF)(v,{modelValue:e.row.FGiftCode,"onUpdate:modelValue":l=>e.row.FGiftCode=l,placeholder:"礼物名称",class:"center",onChange:l=>r.giftChange(e.row,e.$index),clearable:""},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(s.giftList,(e=>((0,t.uX)(),(0,t.Wv)(k,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),(0,t.bF)(F,{prop:"FGiftNum",align:"center",label:"礼物数量",width:"90"},{default:(0,t.k6)((e=>[(0,t.bF)(g,{modelValue:e.row.FGiftNum,"onUpdate:modelValue":l=>e.row.FGiftNum=l,type:"number",class:"center",placeholder:"礼物数量"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),(0,t.bF)(F,{prop:"FBulletScreenNum",align:"center",label:"弹幕数量",width:"90"},{default:(0,t.k6)((e=>[(0,t.bF)(g,{modelValue:e.row.FBulletScreenNum,"onUpdate:modelValue":l=>e.row.FBulletScreenNum=l,type:"number",class:"center",placeholder:"弹幕数量"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),(0,t.bF)(F,{prop:"FWatchMinutes",align:"center",label:"观看分钟",width:"90"},{default:(0,t.k6)((e=>[(0,t.bF)(g,{modelValue:e.row.FWatchMinutes,"onUpdate:modelValue":l=>e.row.FWatchMinutes=l,type:"number",class:"center",placeholder:"观看分钟"},null,8,["modelValue","onUpdate:modelValue"])])),_:1})])),_:1},8,["modelValue","addTemplate"])])),_:1}),(0,t.bF)(f,{height:"50px"},{default:(0,t.k6)((()=>[(0,t.eW)(" 注意：数值 = 0 时 不发礼物、弹幕，不观看。自己给自己发弹幕、观看需要把自己填进去！ ")])),_:1})])),_:1}),(0,t.bF)(b,{width:"290px"},{default:(0,t.k6)((()=>[(0,t.bF)(h,null,{default:(0,t.k6)((()=>[(0,t.bF)(d,null,{default:(0,t.k6)((()=>[(0,t.eW)("弹幕配置")])),_:1}),(0,t.bF)(p,null,{default:(0,t.k6)((()=>[(0,t.bF)(y,{ref:"table",modelValue:s.form.liveBulletScreen,"onUpdate:modelValue":l[2]||(l[2]=e=>s.form.liveBulletScreen=e),addTemplate:{FContent:""}},{default:(0,t.k6)((()=>[(0,t.bF)(F,{prop:"FContent","header-align":"center",label:"弹幕内容"},{default:(0,t.k6)((e=>[(0,t.bF)(g,{modelValue:e.row.FContent,"onUpdate:modelValue":l=>e.row.FContent=l,placeholder:"弹幕内容"},null,8,["modelValue","onUpdate:modelValue"])])),_:1})])),_:1},8,["modelValue"])])),_:1}),(0,t.bF)(f,{height:"50px"},{default:(0,t.k6)((()=>[(0,t.eW)(" 注意：弹幕内容数要大于弹幕数量。 ")])),_:1})])),_:1})])),_:1})])),_:1})),[[V,s.isSaveing]])])),_:1},8,["modelValue","title"])}var s=a(8573),r={emits:["success","closed"],components:{scVideo:s.A},data(){return{mode:"show",titleMap:{show:""},visible:!1,isSaveing:!1,uploadApi:this.$API.common.upload,addTemplate:{Fid:0,FRoomNo:"",FGiftCode:"",FGiftNum:0,FBulletScreenNum:0,FGiftPrice:0,FWatchMinutes:"",FGiftName:"",FGiftSkinId:0},form:{livePath:""},giftList:[],videoCode:""}},async created(){},async mounted(){await this.getSelect()},methods:{async getSelect(){let e=await this.$API.douyuInterface.getInterfaceList.post({jObjectSearch:{name:"直播礼物"}});this.giftList=e.data.rows},async save(){this.isSaveing=!0;var e=await this.$API.douyuLive.saveLive.post({jObjectParam:this.form});this.isSaveing=!1,0==e.code?(this.$emit("success",this.form,this.mode),this.visible=!1,this.$alert(e.message,"提示",{type:"success"})):this.$alert(e.message,"提示",{type:"error"})},open(e="show",l=""){return this.mode=e,this.titleMap.show=l,this.visible=!0,this},async setData(e){Object.assign(this.form,e);let l=await this.$API.douyuLive.getLiveConfig.post({jObjectParam:e});0==l.code&&(Object.assign(this.form,l.data),this.videoCode=l.message)},upsearch(){this.$refs.table.upData({jObjectSearch:this.jObjectSearch})},giftChange(e,l){if(e.FGiftCode){let a=this.giftList.find((l=>l.id==e.FGiftCode));this.form.liveConfig[l].FGiftPrice=a.price,this.form.liveConfig[l].FGiftName=a.name,this.form.liveConfig[l].FGiftSkinId=a.skinId}},async handleRemove(e){await this.$API.douyuLive.delLive.post({jObjectParam:e})}}},d=a(6262);const n=(0,d.A)(r,[["render",i],["__scopeId","data-v-c9a51b06"]]);var u=n},1448:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return n}});var t=a(641),o=a(2644);function i(e,l,a,i,s,r){const d=(0,t.g2)("el-input"),n=(0,t.g2)("el-form-item"),u=(0,t.g2)("el-col"),c=(0,t.g2)("el-text"),m=(0,t.g2)("sc-select"),p=(0,t.g2)("el-row"),f=(0,t.g2)("el-form"),h=(0,t.g2)("el-button"),b=(0,t.g2)("el-dialog"),g=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(b,{modelValue:s.visible,"onUpdate:modelValue":l[11]||(l[11]=e=>s.visible=e),title:s.titleMap[s.mode],width:1200,"destroy-on-close":"","close-on-click-modal":!1,onOpened:r.opened,onClosed:l[12]||(l[12]=l=>e.$emit("closed"))},{footer:(0,t.k6)((()=>[(0,t.bF)(h,{type:"info",onClick:l[9]||(l[9]=e=>r.openChromium()),style:{float:"left"}},{default:(0,t.k6)((()=>[(0,t.eW)("打开浏览器")])),_:1}),(0,t.bF)(h,{type:"warning",onClick:r.login},{default:(0,t.k6)((()=>[(0,t.eW)("登录账号")])),_:1},8,["onClick"]),"show"!=s.mode?((0,t.uX)(),(0,t.Wv)(h,{key:0,type:"primary",loading:s.isSaveing,onClick:l[10]||(l[10]=e=>r.save()),disabled:!s.form.FIdentifying},{default:(0,t.k6)((()=>[(0,t.eW)("保存信息")])),_:1},8,["loading","disabled"])):(0,t.Q3)("",!0)])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(f,{model:s.form,disabled:"show"==s.mode,ref:"dialogForm","label-width":"130px"},{default:(0,t.k6)((()=>[(0,t.bF)(p,null,{default:(0,t.k6)((()=>[(0,t.bF)(u,{span:6},{default:(0,t.k6)((()=>[(0,t.bF)(n,{rules:[{required:!0,message:"请输入Cookie"}],label:"","label-width":"0",prop:"FCookie",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(d,{modelValue:s.form.FCookie,"onUpdate:modelValue":l[0]||(l[0]=e=>s.form.FCookie=e),style:{width:"100%"},rows:25,type:"textarea",resize:"none",placeholder:"Cookie"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(u,{span:9},{default:(0,t.k6)((()=>[(0,t.bF)(n,{label:"Content-Type",prop:"Content-Type"},{default:(0,t.k6)((()=>[(0,t.bF)(d,{modelValue:s.model["Content-Type"],"onUpdate:modelValue":l[1]||(l[1]=e=>s.model["Content-Type"]=e),placeholder:"值",disabled:!0},null,8,["modelValue"])])),_:1}),(0,t.bF)(n,{label:"User-Agent",prop:"User-Agent"},{default:(0,t.k6)((()=>[(0,t.bF)(d,{modelValue:s.model["User-Agent"],"onUpdate:modelValue":l[2]||(l[2]=e=>s.model["User-Agent"]=e),placeholder:"值"},null,8,["modelValue"])])),_:1}),(0,t.bF)(n,{label:"","label-width":"40px",prop:"FHeaders"},{default:(0,t.k6)((()=>[(0,t.bF)(d,{modelValue:s.form.FHeaders,"onUpdate:modelValue":l[3]||(l[3]=e=>s.form.FHeaders=e),style:{width:"100%"},rows:20,type:"textarea",resize:"none",placeholder:"请求头，格式化请求头后会把请求头的参数写在这里,格式JSON，例子:                                                          {'User-Agent':'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/118.0','Content-Type':'application/x-www-form-urlencoded'"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(u,{span:9},{default:(0,t.k6)((()=>[(0,t.bF)(p,null,{default:(0,t.k6)((()=>[(0,t.bF)(u,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(n,{label:"","label-width":"100px"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{class:"mx-1"},{default:(0,t.k6)((()=>[(0,t.eW)((0,o.v_)(s.form.FKey),1)])),_:1})])),_:1})])),_:1}),(0,t.bF)(u,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(n,{rules:[{required:!0,message:"请输入序号"}],label:"序号",prop:"FSort",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(d,{type:"number",modelValue:s.form.FSort,"onUpdate:modelValue":l[4]||(l[4]=e=>s.form.FSort=e),placeholder:"自己输入"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(u,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(n,{rules:[{required:!0,message:"账号标识不能为空"}],label:"账号标识",prop:"FIdentifying",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(d,{modelValue:s.form.FIdentifying,"onUpdate:modelValue":l[5]||(l[5]=e=>s.form.FIdentifying=e),placeholder:"更新信息时自动获取",disabled:!0},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(u,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(n,{rules:[{required:!0,message:"请输入账号名称"}],label:"账号名称",prop:"FName",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(d,{modelValue:s.form.FName,"onUpdate:modelValue":l[6]||(l[6]=e=>s.form.FName=e),placeholder:"更新信息时自动获取"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(u,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(n,{rules:[{required:!0,message:"请输入房间号"}],label:"房间号",prop:"FRoomId",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(d,{modelValue:s.form.FRoomId,"onUpdate:modelValue":l[7]||(l[7]=e=>s.form.FRoomId=e),placeholder:"更新信息时自动获取"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(u,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(n,{label:"代理地址",prop:"FProxyId"},{default:(0,t.k6)((()=>[(0,t.bF)(m,{clearable:"",modelValue:s.form.FProxyId,"onUpdate:modelValue":l[8]||(l[8]=e=>s.form.FProxyId=e),style:{width:"100%"},params:{jObjectSearch:{enable:""}},apiObj:e.$API.douyuProxy.getProxyList,prop:{label:"FAddressName",value:"Fid"},placeholder:"如需代理请配置再更新（代理配置）"},null,8,["modelValue","apiObj"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["model","disabled"])),[[g,s.isSaveing]])])),_:1},8,["modelValue","title","onOpened"])}var s={emits:["success","closed"],data(){return{mode:"add",titleMap:{add:"新增",edit:"编辑",show:"查看"},visible:!1,isSaveing:!1,model:{"Content-Type":"application/x-www-form-urlencoded","User-Agent":navigator.userAgent},form:{Fid:0,FCookie:"",FHeaders:"",FKey:""}}},mounted(){this.getSelect()},methods:{getSelect(){},header(){this.$prompt("如何复制请求头(火狐) → https://blog.csdn.net/qq523176585/article/details/134173302","填写请求头",{confirmButtonText:"确定",autofocus:!0,cancelButtonText:"取消",inputType:"textarea",customClass:"custom-message-box-width",beforeClose:(e,l,a)=>{if("confirm"==e){let e=l.inputValue;if(e&&""!=e){const l=e.split("\n").filter(Boolean),t={};l.forEach((e=>{const[l,...a]=e.split(":");let o=String(a.join(":")).trim();if(""!=l&&""!=a.join(":"))switch(l){case"User-Agent":this.model["User-Agent"]=o,t[l]=o;break;case"Cookie":this.form.FCookie=o,delete t[l];break;case"Content-Type":case"Content-Length":case"Host":case"Referer":case"Origin":break;default:t[l]=o;break}})),t["Content-Type"]=this.model["Content-Type"],this.form.FHeaders=JSON.stringify(t),a()}else this.$alert("请填写请求头！","提示",{type:"error"})}else a()}})},async validateCookie(){this.isSaveing=!0;let e={};try{e=JSON.parse(this.form.FHeaders),e["Content-Type"]=this.model["Content-Type"],this.model["User-Agent"]&&""!=this.model["User-Agent"]?e["User-Agent"]=this.model["User-Agent"]:this.model["User-Agent"]=e["User-Agent"],this.form.FHeaders=JSON.stringify(e)}catch{this.form.FHeaders=JSON.stringify(this.model)}let l=await this.$API.douyuCookies.validateCookie.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?Object.assign(this.form,l.data):this.$alert(l.message,"提示",{type:"error"})},open(e="add"){return this.mode=e,this.visible=!0,this},async login(){if(!this.model["User-Agent"])return void this.$alert("请输入User-Agent","提示",{type:"error"});this.isSaveing=!0;let e=this.form;e.UserAgent=this.model["User-Agent"];let l=await this.$API.douyuCookies.login.post({jObjectParam:e});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.visible=!1,this.$alert(l.message,"提示",{type:"success"})):this.$alert(l.message,"提示",{type:"error"})},save(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.isSaveing=!0,this.form.FStatus="";var l=await this.$API.douyuCookies.editCookie.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.visible=!1,this.$alert(l.message,"提示",{type:"success"})):this.$alert(l.message,"提示",{type:"error"})}))},async setData(e){Object.assign(this.form,e),this.form.FProxyId=0==e.FProxyId?"":e.FProxyId;try{this.model["User-Agent"]=JSON.parse(e.FHeaders)["User-Agent"]}catch(l){console.log(l.message)}},opened(){},async openChromium(){if(!this.model["User-Agent"])return void this.$alert("请输入User-Agent","提示",{type:"error"});let e=this.form;e.UserAgent=this.model["User-Agent"],await this.$API.douyuCookies.openChromium.post({jObjectParam:e})}}},r=a(6262);const d=(0,r.A)(s,[["render",i]]);var n=d},2765:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return u}});var t=a(641);const o={class:"left-panel"},i={class:"right-panel"};function s(e,l,a,s,r,d){const n=(0,t.g2)("el-option"),u=(0,t.g2)("el-select"),c=(0,t.g2)("el-button"),m=(0,t.g2)("el-popconfirm"),p=(0,t.g2)("el-header"),f=(0,t.g2)("el-table-column"),h=(0,t.g2)("el-switch"),b=(0,t.g2)("el-button-group"),g=(0,t.g2)("scTable"),F=(0,t.g2)("el-form"),k=(0,t.g2)("el-dialog");return(0,t.uX)(),(0,t.Wv)(k,{class:"dialog-table",modelValue:r.visible,"onUpdate:modelValue":l[3]||(l[3]=e=>r.visible=e),title:r.titleMap[r.mode],width:1440,"destroy-on-close":"",onClosed:l[4]||(l[4]=l=>e.$emit("closed")),style:{height:"auto","max-height":"100%"}},{footer:(0,t.k6)((()=>[(0,t.bF)(c,{onClick:l[2]||(l[2]=e=>r.visible=!1)},{default:(0,t.k6)((()=>[(0,t.eW)("关 闭")])),_:1})])),default:(0,t.k6)((()=>[(0,t.bF)(p,{style:{"padding-left":"0px","border-bottom":"0px"}},{default:(0,t.k6)((()=>[(0,t.Lk)("div",o,[(0,t.bF)(u,{modelValue:r.jObjectSearch.areaId,"onUpdate:modelValue":l[0]||(l[0]=e=>r.jObjectSearch.areaId=e),filterable:"",onChange:d.upsearch,style:{"padding-right":"10px"}},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(r.area,(e=>((0,t.uX)(),(0,t.Wv)(n,{key:e.Fid,label:e.FName,value:e.Fid,placeholder:"请选择分区"},null,8,["label","value"])))),128))])),_:1},8,["modelValue","onChange"]),(0,t.bF)(c,{type:"primary",icon:"el-icon-refresh",onClick:d.upsearch},{default:(0,t.k6)((()=>[(0,t.eW)(" 刷 新")])),_:1},8,["onClick"]),(0,t.bF)(c,{type:"primary",icon:"el-icon-search",onClick:l[1]||(l[1]=e=>{r.jObjectSearch.areaId="",d.upsearch()})},{default:(0,t.k6)((()=>[(0,t.eW)(" 查询全局")])),_:1}),(0,t.bF)(c,{type:"primary",icon:"el-icon-refresh-right",onClick:d.restoreDefault},{default:(0,t.k6)((()=>[(0,t.eW)(" 恢复默认")])),_:1},8,["onClick"]),(0,t.bF)(c,{type:"primary",icon:"el-icon-promotion",onClick:d.syncAll},{default:(0,t.k6)((()=>[(0,t.eW)(" 同步账号")])),_:1},8,["onClick"])]),(0,t.Lk)("div",i,[(0,t.bF)(m,{title:"删除该账号所有任务吗？",width:"220px",onConfirm:d.delAll},{reference:(0,t.k6)((()=>[(0,t.bF)(c,{type:"danger",icon:"el-icon-delete"},{default:(0,t.k6)((()=>[(0,t.eW)(" 全部删除")])),_:1})])),_:1},8,["onConfirm"])])])),_:1}),(0,t.bF)(F,{style:{height:"550px"}},{default:(0,t.k6)((()=>[(0,t.bF)(g,{ref:"table",apiObj:r.apiObj,"highlight-current-row":"","row-key":"Fid",params:{jObjectSearch:r.jObjectSearch},"row-Style":d.rowStyle,border:"",hideDo:!0,hidePagination:!0},{default:(0,t.k6)((()=>[(0,t.bF)(f,{label:"状态",prop:"FStatusName",align:"center",width:"100"}),(0,t.bF)(f,{label:"排序",prop:"FSort",align:"center",width:"80"}),(0,t.bF)(f,{label:"分区名称",prop:"FAreaName",align:"center",width:"150"}),(0,t.bF)(f,{label:"任务名称",prop:"FTaskName",align:"center","show-overflow-tooltip":""}),(0,t.bF)(f,{label:"奖励名称",prop:"FPrizeName",align:"center","show-overflow-tooltip":""}),(0,t.bF)(f,{label:"库存数量",prop:"FRemainDesc",align:"center",width:"100"}),(0,t.bF)(f,{label:"每日任务",prop:"FDailyName",align:"center",width:"90"}),(0,t.bF)(f,{label:"排序",prop:"FSort",align:"center",width:"90"}),(0,t.bF)(f,{label:"是否启用",prop:"FEnable",align:"center",width:"80"},{default:(0,t.k6)((e=>[(0,t.bF)(h,{modelValue:e.row.FEnable,"onUpdate:modelValue":l=>e.row.FEnable=l,onChange:l=>d.enableSwitch(l,e.row),loading:e.row.$enable,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue","onChange","loading"])])),_:1}),(0,t.bF)(f,{label:"操作",fixed:"right",align:"center",width:"170"},{default:(0,t.k6)((e=>[(0,t.bF)(b,null,{default:(0,t.k6)((()=>[(0,t.bF)(c,{text:"",type:"primary",size:"small",onClick:l=>d.setSort(e.row,e.$index)},{default:(0,t.k6)((()=>[(0,t.eW)("设置排序")])),_:2},1032,["onClick"])])),_:2},1024)])),_:1})])),_:1},8,["apiObj","params","row-Style"])])),_:1})])),_:1},8,["modelValue","title"])}var r={emits:["success","closed"],data(){return{mode:"show",titleMap:{show:"任务信息"},visible:!1,isSaveing:!1,apiObj:null,jObjectSearch:{},area:[]}},async created(){let e=await this.$API.douyuArea.getAreaList.post({jObjectSearch:{}});0==e.code&&(this.area=e.data.rows)},mounted(){this.getSelect()},methods:{getSelect(){},open(e="show"){return this.mode=e,this.visible=!0,this},async setData(e){Object.assign(this.jObjectSearch,e),this.apiObj=this.$API.douyuCookiesTask.getCookiesTaskList},rowStyle(e){return 0==e.row.FEnable?"color:#CC0000":1==e.row.FDaily?"color:#CC6600":2==e.row.FStatus?"color:#006600":3==e.row.FStatus||4==e.row.FStatus||5==e.row.FStatus?"color:#CC0000":void 0},formatterStock(e){return(Number(e.FStockTotal)-Number(e.FStockConsumed)).toFixed(0)},formatterPeriod(e){return(Number(e.FPeriodTotal)-Number(e.FPeriodConsumed)).toFixed(0)},async setCompulsory(e,l,a){let t=await this.$API.douyuCookiesTask.setCompulsory.post({jObjectParam:{Fid:e.Fid,FCompulsory:a}});0==t.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(t.message,"提示",{type:"error"})},async syncAll(){let e=await this.$API.douyuCookiesTask.syncAll.post({jObjectParam:{cookieId:this.jObjectSearch.cookieId,areaId:this.jObjectSearch.areaId}});0==e.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(e.message,"提示",{type:"error"})},async restoreDefault(){let e=await this.$API.douyuCookiesTask.restoreDefault.post({jObjectParam:{cookieId:this.jObjectSearch.cookieId,areaId:this.jObjectSearch.areaId}});0==e.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(e.message,"提示",{type:"error"})},async setSort(e){this.$prompt("","设置排序",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnPressEscape:!0,closeOnClickModal:!0,autofocus:!0,inputType:"number",inputPattern:/^[0-9-]/,inputErrorMessage:"请输入排序。（正负整数）"}).then((async({value:l})=>{let a=await this.$API.douyuCookiesTask.setSort.post({jObjectParam:{Fid:e.Fid,FSort:l}});0==a.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(a.message,"提示",{type:"error"})}))},async delAll(){let e=await this.$API.douyuCookiesTask.delAll.post({jObjectParam:{cookieId:this.jObjectSearch.cookieId,areaId:this.jObjectSearch.areaId}});0==e.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(e.message,"提示",{type:"error"})},async enableSwitch(e,l){l.$enable=!0;let a=await this.$API.douyuCookiesTask.enableSwitch.post({jObjectParam:l});0==a.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(a.message,"提示",{type:"error"}),delete l.$enable},upsearch(){this.$refs.table.upData({jObjectSearch:this.jObjectSearch})}}},d=a(6262);const n=(0,d.A)(r,[["render",s]]);var u=n},3636:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return d}});var t=a(641);function o(e,l,a,o,i,s){const r=(0,t.g2)("el-input"),d=(0,t.g2)("el-form-item"),n=(0,t.g2)("el-col"),u=(0,t.g2)("sc-select"),c=(0,t.g2)("el-row"),m=(0,t.g2)("el-form"),p=(0,t.g2)("el-button"),f=(0,t.g2)("el-dialog"),h=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(f,{modelValue:i.visible,"onUpdate:modelValue":l[14]||(l[14]=e=>i.visible=e),title:i.titleMap[i.mode],width:1200,"destroy-on-close":"",onClosed:l[15]||(l[15]=l=>e.$emit("closed"))},{footer:(0,t.k6)((()=>["show"!=i.mode?((0,t.uX)(),(0,t.Wv)(p,{key:0,type:"primary",loading:i.isSaveing,onClick:l[13]||(l[13]=e=>s.save())},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["loading"])):(0,t.Q3)("",!0)])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(m,{model:i.form,disabled:"show"==i.mode,ref:"dialogForm","label-width":"125px"},{default:(0,t.k6)((()=>[(0,t.bF)(c,null,{default:(0,t.k6)((()=>[(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"打码名称",prop:"FName",rules:[{required:!0,message:"请输入打码名称"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FName,"onUpdate:modelValue":l[0]||(l[0]=e=>i.form.FName=e),placeholder:"打码名称"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:16},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"请求地址",prop:"FUrl",rules:[{required:!0,message:"请输入请求地址"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FUrl,"onUpdate:modelValue":l[1]||(l[1]=e=>i.form.FUrl=e),placeholder:"请求地址"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请选择请求方式"}],label:"请求方式",prop:"FMethod",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(u,{clearable:"",modelValue:i.form.FMethod,"onUpdate:modelValue":l[2]||(l[2]=e=>i.form.FMethod=e),apiObj:[{label:"POST",value:1},{label:"GET",value:0}],placeholder:" 请求方式",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"gt键值",prop:"FGt",rules:[{required:!0,message:"请输入gt键值"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FGt,"onUpdate:modelValue":l[3]||(l[3]=e=>i.form.FGt=e),placeholder:"gt键值"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"challenge键值",prop:"FChallenge",rules:[{required:!0,message:"请输入challenge键值"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FChallenge,"onUpdate:modelValue":l[4]||(l[4]=e=>i.form.FChallenge=e),placeholder:"challenge键值"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"秘钥键值",prop:"FKey",rules:[{required:!0,message:"请输入秘钥键值"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FKey,"onUpdate:modelValue":l[5]||(l[5]=e=>i.form.FKey=e),placeholder:"秘钥键值"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:16},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"打码秘钥",prop:"FKeyValue",rules:[{required:!0,message:"请输入打码秘钥"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FKeyValue,"onUpdate:modelValue":l[6]||(l[6]=e=>i.form.FKeyValue=e),placeholder:"打码秘钥"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"成功键值",prop:"FSuccess",rules:[{required:!0,message:"成功键值"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FSuccess,"onUpdate:modelValue":l[7]||(l[7]=e=>i.form.FSuccess=e),placeholder:"成功键值"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"成功代码",prop:"FSuccessCode",rules:[{required:!0,message:"请输入成功代码"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FSuccessCode,"onUpdate:modelValue":l[8]||(l[8]=e=>i.form.FSuccessCode=e),placeholder:"成功代码"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"Validate键值",prop:"FResultValidate",rules:[{required:!0,message:"请输入Validate键值"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FResultValidate,"onUpdate:modelValue":l[9]||(l[9]=e=>i.form.FResultValidate=e),placeholder:"js的JSON取值写法，data.validate"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"类型键值",prop:"FType"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FType,"onUpdate:modelValue":l[10]||(l[10]=e=>i.form.FType=e),placeholder:"类型键值"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:16},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"类型数值",prop:"FTypeValue"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FTypeValue,"onUpdate:modelValue":l[11]||(l[11]=e=>i.form.FTypeValue=e),placeholder:"类型数值"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"其他参数",prop:"FParamValue"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FParamValue,"onUpdate:modelValue":l[12]||(l[12]=e=>i.form.FParamValue=e),style:{width:"100%"},rows:4,type:"textarea",resize:"none",placeholder:"JSON格式"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","disabled"])),[[h,i.isSaveing]])])),_:1},8,["modelValue","title"])}var i={emits:["success","closed"],data(){return{mode:"add",titleMap:{add:"新增",edit:"编辑",show:"查看"},visible:!1,isSaveing:!1,form:{Fid:0,FEnable:0}}},mounted(){this.getSelect()},methods:{getSelect(){},open(e="add"){return this.mode=e,this.visible=!0,this},save(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.isSaveing=!0;var l=await this.$API.douyuGeetest.editGeetest.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.$message.success("操作成功"),this.visible=!1):this.$alert(l.message,"提示",{type:"error"})}))},setData(e){Object.assign(this.form,e)}}},s=a(6262);const r=(0,s.A)(i,[["render",o]]);var d=r},9606:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return n}});var t=a(641);function o(e,l,a,o,i,s){const r=(0,t.g2)("el-input"),d=(0,t.g2)("el-form-item"),n=(0,t.g2)("el-col"),u=(0,t.g2)("el-row"),c=(0,t.g2)("el-form"),m=(0,t.g2)("el-button"),p=(0,t.g2)("el-tooltip"),f=(0,t.g2)("el-dialog"),h=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(f,{modelValue:i.visible,"onUpdate:modelValue":l[5]||(l[5]=e=>i.visible=e),title:i.titleMap[i.mode],width:400,"destroy-on-close":"","close-on-click-modal":!1,onOpened:s.opened,onClosed:l[6]||(l[6]=l=>e.$emit("closed"))},{footer:(0,t.k6)((()=>[(0,t.bF)(p,{class:"box-item",effect:"dark",content:"只会分享执行时间、随机区间、执行时长、任务名称、分区名称和参数内容。",placement:"top"},{default:(0,t.k6)((()=>[(0,t.bF)(m,{loading:i.isSaveing,onClick:l[2]||(l[2]=e=>s.share()),style:{float:"left"}},{default:(0,t.k6)((()=>[(0,t.eW)("分享配置")])),_:1},8,["loading"])])),_:1}),(0,t.bF)(m,{class:"left",loading:i.isSaveing,onClick:l[3]||(l[3]=e=>s.exports()),style:{float:"left"}},{default:(0,t.k6)((()=>[(0,t.eW)("导入配置")])),_:1},8,["loading"]),(0,t.bF)(m,{type:"primary",loading:i.isSaveing,onClick:l[4]||(l[4]=e=>s.save())},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["loading"])])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(c,{model:i.form,disabled:"show"==i.mode,ref:"dialogForm","label-width":"100px"},{default:(0,t.k6)((()=>[(0,t.bF)(u,null,{default:(0,t.k6)((()=>[(0,t.bF)(n,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"分组名称",prop:"FName",rules:[{required:!0,message:"请输入分组名称"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FName,"onUpdate:modelValue":l[0]||(l[0]=e=>i.form.FName=e),placeholder:"分组名称"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请输入排列序号"}],label:"排列序号",prop:"FSort",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{type:"number",modelValue:i.form.FSort,"onUpdate:modelValue":l[1]||(l[1]=e=>i.form.FSort=e),placeholder:"排列序号"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","disabled"])),[[h,i.isSaveing]])])),_:1},8,["modelValue","title","onOpened"])}var i=a(1132),s={emits:["success","closed"],data(){return{mode:"add",titleMap:{add:"新增",edit:"编辑",show:"查看"},visible:!1,isSaveing:!1,form:{Fid:0,FStatus:0,FSort:0}}},mounted(){this.getSelect()},methods:{getSelect(){},open(e="add"){return this.mode=e,this.visible=!0,this},treeSelectClick(e){this.form.FParentId=e.parent_id,this.form.FName=e.name},save(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.isSaveing=!0;var l=await this.$API.douyuQuartzGroup.saveQuartzGroup.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.$message.success("操作成功"),this.visible=!1):this.$alert(l.message,"提示",{type:"error"})}))},async share(){this.isSaveing=!0;let e=await this.$API.douyuQuartz.shareQuartz.post({jObjectParam:this.form});0==e.code?this.$prompt("分享配置只会分享执行时间、随机区间、执行时长、任务名称、分区名称和参数内容，其他不会分享。","分享配置",{confirmButtonText:"确定",autofocus:!0,cancelButtonText:"取消",inputType:"textarea",customClass:"custom-message-box-width",showCancelButton:!1,showConfirmButton:!1,inputValue:e.data}):this.$alert(e.message,"提示",{type:"error"}),this.isSaveing=!1},async exports(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.$prompt("注意：导入之后会清空原有的信息！","导入配置",{confirmButtonText:"确定",autofocus:!0,cancelButtonText:"取消",inputType:"textarea",customClass:"custom-message-box-width",beforeClose:async(e,l,a)=>{if("confirm"==e){let e=l.inputValue;if(e&&""!=e){this.isSaveing=!0,this.form.value=e;const l=i.Ks.service({lock:!0,text:"Loading",background:"rgba(0, 0, 0, 0.7)"});let t=await this.$API.douyuQuartz.exportQuartz.post({jObjectParam:this.form});l.close(),0==t.code?(this.$emit("success",this.form,this.mode),this.$message.success("操作成功"),this.visible=!1,a()):this.$alert(t.message,"提示",{type:"error"}),this.isSaveing=!1}else this.$alert("请填写导入信息！","提示",{type:"error"})}else a()}})}))},async setData(e){Object.assign(this.form,e),this.form.Fid=String(e.Fid)},opened(){}}},r=a(6262);const d=(0,r.A)(s,[["render",o]]);var n=d},1711:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return d}});var t=a(641);function o(e,l,a,o,i,s){const r=(0,t.g2)("el-time-picker"),d=(0,t.g2)("el-form-item"),n=(0,t.g2)("el-col"),u=(0,t.g2)("el-input"),c=(0,t.g2)("el-tooltip"),m=(0,t.g2)("sc-tree-select"),p=(0,t.g2)("sc-select"),f=(0,t.g2)("el-row"),h=(0,t.g2)("el-form"),b=(0,t.g2)("el-button"),g=(0,t.g2)("el-dialog"),F=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(g,{modelValue:i.visible,"onUpdate:modelValue":l[8]||(l[8]=e=>i.visible=e),title:i.titleMap[i.mode],width:1024,"destroy-on-close":"",onOpened:s.opened,onClosed:l[9]||(l[9]=l=>e.$emit("closed"))},{footer:(0,t.k6)((()=>["show"!=i.mode?((0,t.uX)(),(0,t.Wv)(b,{key:0,type:"primary",loading:i.isSaveing,onClick:l[7]||(l[7]=e=>s.save())},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["loading"])):(0,t.Q3)("",!0)])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(h,{model:i.form,disabled:"show"==i.mode,ref:"dialogForm","label-width":"110px"},{default:(0,t.k6)((()=>[(0,t.bF)(f,null,{default:(0,t.k6)((()=>[(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请选择执行时间"}],label:"执行时间",prop:"FExecTime",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FExecTime,"onUpdate:modelValue":l[0]||(l[0]=e=>i.form.FExecTime=e),editable:"",placeholder:"执行时间",format:"HH:mm:ss","value-format":"HH:mm:ss"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(c,{class:"box-item",effect:"dark",content:"定时任务启动后的随机X秒内执行",placement:"top"},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"随机时间",prop:"FDifference",rules:[{required:!0,message:"请输入随机区间"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(u,{type:"number",modelValue:i.form.FDifference,"onUpdate:modelValue":l[1]||(l[1]=e=>i.form.FDifference=e),placeholder:"随机时间"},{append:(0,t.k6)((()=>[(0,t.eW)("秒")])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"执行时长",prop:"FSeconds",rules:[{required:!0,message:"请输入执行时长"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(u,{type:"number",modelValue:i.form.FSeconds,"onUpdate:modelValue":l[2]||(l[2]=e=>i.form.FSeconds=e),placeholder:"执行时长"},{append:(0,t.k6)((()=>[(0,t.eW)("秒")])),_:1},8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请选择任务名称"}],label:"任务名称",prop:"FJobId",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(m,{modelValue:i.form.FJobId,"onUpdate:modelValue":l[3]||(l[3]=e=>i.form.FJobId=e),ref:"treeSelect",placeholder:"请选择任务名称",prop:{label:"FName",value:"Fid",children:"list"},"default-expand-all":"",onNodeClick:s.treeSelectClick,params:{jObjectSearch:{}},initial:s.treeInitial,apiObj:e.$API.douyuQuartz.getQuartzJobList,style:{width:"100%"}},null,8,["modelValue","onNodeClick","initial","apiObj"])])),_:1})])),_:1}),i.job.FArea?((0,t.uX)(),(0,t.Wv)(n,{key:0,span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请选择分区名称"}],label:"分区名称",prop:"FAreaId",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(p,{clearable:"",modelValue:i.form.FAreaId,"onUpdate:modelValue":l[4]||(l[4]=e=>i.form.FAreaId=e),params:{jObjectSearch:{}},apiObj:e.$API.douyuArea.getAreaList,prop:{label:"FName",value:"Fid"},placeholder:" 分区名称",style:{width:"100%"}},null,8,["modelValue","apiObj"])])),_:1})])),_:1})):(0,t.Q3)("",!0),i.job.FCookie?((0,t.uX)(),(0,t.Wv)(n,{key:1,span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请选择账号名称"}],label:"账号名称",prop:"FCookie",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(p,{clearable:"",modelValue:i.form.FCookie,"onUpdate:modelValue":l[5]||(l[5]=e=>i.form.FCookie=e),params:{jObjectSearch:{}},multiple:"","collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":4,apiObj:e.$API.douyuCookies.getCookiesList,prop:{label:"FName",value:"Fid"},placeholder:" 账号名称",style:{width:"100%"}},null,8,["modelValue","apiObj"])])),_:1})])),_:1})):(0,t.Q3)("",!0),i.job.FLabel?((0,t.uX)(),(0,t.Wv)(n,{key:2,span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:i.job.FLabel,prop:"FParam",rules:[{required:!0,message:"请输入"+i.job.FLabel+"！如不需要请输入-1"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(u,{type:"textarea",rows:5,resize:"none",placeholder:i.job.FPlaceholder,modelValue:i.form.FParam,"onUpdate:modelValue":l[6]||(l[6]=e=>i.form.FParam=e)},null,8,["placeholder","modelValue"])])),_:1},8,["label","rules"])])),_:1})):(0,t.Q3)("",!0)])),_:1})])),_:1},8,["model","disabled"])),[[F,i.isSaveing]])])),_:1},8,["modelValue","title","onOpened"])}var i={emits:["success","closed"],data(){return{mode:"add",titleMap:{add:"新增",edit:"编辑",show:"查看"},visible:!1,isSaveing:!1,form:{Fid:0,FGroupId:0,FDifference:0,FSeconds:0,FJobId:"",FCookie:[]},job:{FArea:0,FCookie:0,FLabel:"",FPlaceholder:""}}},mounted(){this.getSelect()},methods:{getSelect(){},open(e="add"){return this.mode=e,this.visible=!0,this},treeSelectClick(e){this.job=e,this.form.FJobName=e.FName,0==this.job.FCookie&&(this.form.FCookie=[]),0==this.job.FArea&&(this.form.FAreaId=""),this.job.FLabel&&this.job.FPlaceholder&&(this.form.FParam=this.job.FPlaceholder)},save(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.isSaveing=!0,this.form.FCookieId=String(this.form.FCookie);var l=await this.$API.douyuQuartz.saveQuartz.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.$message.success("操作成功"),this.visible=!1):this.$alert(l.message,"提示",{type:"error"})}))},setData(e){Object.assign(this.form,e),"edit"==this.mode&&(""==this.form.FCookieId?this.form.FCookie=[]:this.form.FCookie=String(this.form.FCookieId).split(",").map(Number))},treeInitial(){this.$nextTick((()=>{"edit"==this.mode&&(this.job=this.$refs.treeSelect.getCurrentNode())}))},opened(){}}},s=a(6262);const r=(0,s.A)(i,[["render",o]]);var d=r},5593:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return d}});var t=a(641);function o(e,l,a,o,i,s){const r=(0,t.g2)("el-switch"),d=(0,t.g2)("el-text"),n=(0,t.g2)("el-form-item"),u=(0,t.g2)("el-col"),c=(0,t.g2)("el-row"),m=(0,t.g2)("el-form"),p=(0,t.g2)("el-card");return(0,t.uX)(),(0,t.Wv)(p,{shadow:"hover",header:"控制面板",class:"item-background"},{default:(0,t.k6)((()=>[(0,t.bF)(m,{"label-width":"100px"},{default:(0,t.k6)((()=>[(0,t.bF)(c,null,{default:(0,t.k6)((()=>[(0,t.bF)(u,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(n,{label:"执行模式："},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.value,"onUpdate:modelValue":l[0]||(l[0]=e=>i.value=e),loading:i.loading,"inline-prompt":"","before-change":s.beforeChange,onChange:s.change,style:{"--el-switch-on-color":"#18a500","--el-switch-off-color":"#409eff"},"active-text":"  接 口  ","active-value":"接口","inactive-text":" 浏览器 ","inactive-value":"浏览器"},null,8,["modelValue","loading","before-change","onChange"]),(0,t.bF)(d,{class:"mr-2"},{default:(0,t.k6)((()=>[(0,t.eW)("   *按钮操作时会以当前模式执行，定时任务不影响*")])),_:1})])),_:1}),(0,t.bF)(n,{label:"浏览器："},{default:(0,t.k6)((()=>[(0,t.bF)(d,{class:"mr-2"},{default:(0,t.k6)((()=>[(0,t.eW)("谷歌内核，暂时无法配置代理，抢码类似油猴插件、控制台")])),_:1})])),_:1}),(0,t.bF)(n,{label:"接口："},{default:(0,t.k6)((()=>[(0,t.bF)(d,{class:"mr-2"},{default:(0,t.k6)((()=>[(0,t.eW)("可配置代理，速度快、占用资源少，配置代理后请勿使用*浏览器*模式")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})}var i={title:"控制面板",icon:"el-icon-setting",description:"控制面板",data(){return{value:this.$TOOL.data.get("EXECMODE","接口"),loading:!1}},mounted(){this.$TOOL.data.set("EXECMODE",this.value)},methods:{beforeChange(){return this.loading=!0,new Promise((e=>{setTimeout((()=>(this.loading=!1,e(!0))),500)}))},change(){this.$TOOL.data.set("EXECMODE",this.value),this.$message.success("已切换："+this.value+" 模式")}}},s=a(6262);const r=(0,s.A)(i,[["render",o],["__scopeId","data-v-2d3ff457"]]);var d=r},4082:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return f}});var t=a(641),o=a(2644);const i={class:"common-layout"},s={key:0,style:{"padding-top":"5px"}},r={class:"icon-box"},d={class:"dialog-footer"};function n(e,l,a,n,u,c){const m=(0,t.g2)("el-button"),p=(0,t.g2)("el-button-group"),f=(0,t.g2)("el-icon"),h=(0,t.g2)("el-calendar"),b=(0,t.g2)("el-main"),g=(0,t.g2)("el-container"),F=(0,t.g2)("el-card"),k=(0,t.g2)("el-option"),v=(0,t.g2)("el-select"),y=(0,t.g2)("el-table-column"),_=(0,t.g2)("el-date-picker"),V=(0,t.g2)("el-input"),w=(0,t.g2)("sc-form-table"),C=(0,t.g2)("el-form"),S=(0,t.g2)("el-dialog");return(0,t.uX)(),(0,t.CE)(t.FK,null,[(0,t.bF)(F,{shadow:"hover",header:"日历",class:"item-background"},{default:(0,t.k6)((()=>[(0,t.Lk)("div",i,[(0,t.bF)(g,null,{default:(0,t.k6)((()=>[(0,t.bF)(b,{width:"100%"},{default:(0,t.k6)((()=>[u.data?((0,t.uX)(),(0,t.Wv)(h,{key:0,modelValue:u.calendar,"onUpdate:modelValue":l[1]||(l[1]=e=>u.calendar=e),ref:"calendar",range:[u.data.min,u.data.max]},{header:(0,t.k6)((({})=>[(0,t.Lk)("span",null,(0,o.v_)(e.$TOOL.dateFormat(u.calendar??new Date,"yyyy-MM-dd")),1),(0,t.bF)(p,null,{default:(0,t.k6)((()=>[(0,t.bF)(m,{size:"small",onClick:l[0]||(l[0]=e=>c.selectDate("today"))},{default:(0,t.k6)((()=>[(0,t.eW)("今天")])),_:1})])),_:1})])),"date-cell":(0,t.k6)((({data:e})=>[(0,t.Lk)("p",{class:(0,o.C4)(e.isSelected?"is-selected":""),style:{position:"relative"}},[(0,t.eW)((0,o.v_)(e.day.split("-").slice(1).join("-"))+" ",1),u.remarks[e.day]?((0,t.uX)(),(0,t.Wv)(f,{key:0,style:{position:"absolute",right:"0px"}},{default:(0,t.k6)((()=>[((0,t.uX)(),(0,t.Wv)((0,t.$y)("el-icon-warning")))])),_:1})):(0,t.Q3)("",!0)],2),this.data["data"]&&this.data["data"][e.day]?((0,t.uX)(),(0,t.CE)("div",s,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(this.data["data"][e.day],((l,a)=>((0,t.uX)(),(0,t.CE)("span",{style:{display:"flex"},key:a},[(0,t.eW)((0,o.v_)(a)+": ",1),(0,t.Lk)("span",r,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(this.data["data"][e.day][a],(e=>((0,t.uX)(),(0,t.Wv)(f,{key:e},{default:(0,t.k6)((()=>[((0,t.uX)(),(0,t.Wv)((0,t.$y)("sc-icon-"+e.typeIcon)))])),_:2},1024)))),128))])])))),128))])):(0,t.Q3)("",!0)])),_:1},8,["modelValue","range"])):(0,t.Q3)("",!0)])),_:1})])),_:1})])])),_:1}),(0,t.bF)(S,{modelValue:u.dialog,"onUpdate:modelValue":l[4]||(l[4]=e=>u.dialog=e),title:"数据维护",width:"1200px"},{footer:(0,t.k6)((()=>[(0,t.Lk)("div",d,[(0,t.bF)(m,{onClick:c.a1,style:{float:"left"}},{default:(0,t.k6)((()=>[(0,t.eW)("配置信息")])),_:1},8,["onClick"]),(0,t.bF)(m,{onClick:l[3]||(l[3]=e=>u.dialog=!1)},{default:(0,t.k6)((()=>[(0,t.eW)("关闭")])),_:1}),(0,t.bF)(m,{type:"primary",onClick:c.save},{default:(0,t.k6)((()=>[(0,t.eW)("保存")])),_:1},8,["onClick"])])])),default:(0,t.k6)((()=>[(0,t.bF)(C,{model:e.form},{default:(0,t.k6)((()=>[(0,t.bF)(w,{ref:"table",modelValue:u.table,"onUpdate:modelValue":l[2]||(l[2]=e=>u.table=e),addTemplate:{Fid:0,FAreaName:"",FTypeIcon:"",FTypeName:"",FDays:""}},{default:(0,t.k6)((()=>[(0,t.bF)(y,{prop:"FAreaName",align:"center",label:"分区名称",width:"180"},{default:(0,t.k6)((e=>[(0,t.bF)(v,{modelValue:e.row.FAreaName,"onUpdate:modelValue":l=>e.row.FAreaName=l,placeholder:"分区名称",class:"center",clearable:""},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(u.areaList,(e=>((0,t.uX)(),(0,t.Wv)(k,{key:e.FName,label:e.FName,value:e.FName},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1}),(0,t.bF)(y,{prop:"FTypeName",align:"center",label:"平台名称",width:"140"},{default:(0,t.k6)((e=>[(0,t.bF)(v,{modelValue:e.row.FTypeName,"onUpdate:modelValue":l=>e.row.FTypeName=l,placeholder:"平台名称",class:"center",onChange:l=>c.change(e.row,e.$index),clearable:""},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(u.typeList,(e=>((0,t.uX)(),(0,t.Wv)(k,{key:e.FName,label:e.FName,value:e.FName},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),(0,t.bF)(y,{prop:"FDate",align:"center",label:"开始时间",width:"180"},{default:(0,t.k6)((e=>[(0,t.bF)(_,{modelValue:e.row.FDate,"onUpdate:modelValue":l=>e.row.FDate=l,type:"date",style:{width:"150px"}},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),(0,t.bF)(y,{prop:"FDays",align:"center",label:"天数"},{default:(0,t.k6)((e=>[(0,t.bF)(V,{modelValue:e.row.FDays,"onUpdate:modelValue":l=>e.row.FDays=l,type:"text",placeholder:"1,3,5,7,9,11"},null,8,["modelValue","onUpdate:modelValue"])])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["model"])])),_:1},8,["modelValue"])],64)}a(8743);var u=a(7198),c={title:"日历",description:"显示天数的日历组件",icon:"el-icon-calendar",data(){return{dialog:!1,calendar:null,table:[],areaList:[],remarks:{},typeList:[{FName:"B站",FIcon:"bili"},{FName:"抖音",FIcon:"dou-yin"},{FName:"快手",FIcon:"kuai-shou"}],data:{min:this.$TOOL.dateFormat(new Date,"yyyy-MM-dd",-10),max:this.$TOOL.dateFormat(new Date,"yyyy-MM-dd",11)}}},async mounted(){let e=await this.$API.biliArea.getAreaList.post({jObjectSearch:{}});this.areaList=e.data.rows},methods:{async a2(){await this.$API.home.saveWidgetsConfig.post({jObjectParam:{key:"REMARKS",val:JSON.stringify(this.remarks)}})},selectDate(e){this.$refs["calendar"].selectDate(e)},change(e,l){this.table[l].FTypeIcon=this.typeList.find((l=>l.FName==e.FTypeName)).FIcon},a1(){this.$prompt("","分享配置",{confirmButtonText:"确定",autofocus:!0,inputValue:JSON.stringify(this.table),cancelButtonText:"取消",inputType:"textarea",customClass:"custom-message-box-width",beforeClose:(e,l,a)=>{if("confirm"==e){let e=l.inputValue;if(e&&""!=e){try{this.table=JSON.parse(e)}catch{this.$alert("配置信息格式不正确！","提示",{type:"error"})}a()}else this.$alert("请填写配置信息！","提示",{type:"error"})}else a()}})},async save(){let e={},l=[];for(let o in this.table){let a=String(this.table[o].FDays).trim(),t=String(this.table[o].FAreaName).substring(0,1),i=String(this.table[o].FTypeName),s=String(this.table[o].FTypeIcon),r=this.table[o].FDate;if(""!=a){let o=a.split(",");for(let a in o){let d=this.$TOOL.dateFormat(r,"yyyy-MM-dd",Number(o[a])-1);l.push(d),this.remarks,e[d]||(e[d]={}),e[d][t]||(e[d][t]=[]),e[d][t].some((e=>e==i))||e[d][t].push({typeName:i,typeIcon:s})}}}let a={min:this.$TOOL.dateFormat(u.A.from(l).min(),"yyyy-MM-dd"),max:this.$TOOL.dateFormat(u.A.from(l).max(),"yyyy-MM-dd"),data:e,table:this.table},t=await this.$API.home.saveWidgetsConfig.post({jObjectParam:{key:"CALENDAR",val:JSON.stringify(a)}});0==t.code?(this.dialog=!1,this.data=a):this.$alert(t.message,"提示",{type:"error"})}}},m=a(6262);const p=(0,m.A)(c,[["render",n],["__scopeId","data-v-2b062bd5"]]);var f=p},5021:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return g}});var t=a(641);function o(e,l,a,o,i,s){const r=(0,t.g2)("scEcharts"),d=(0,t.g2)("el-card"),n=(0,t.gN)("loading");return(0,t.bo)(((0,t.uX)(),(0,t.Wv)(d,{shadow:"hover",header:"实时收入"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{ref:"c1",height:"300px",option:i.option},null,8,["option"])])),_:1})),[[n,i.loading]])}a(8743);var i=a(2644);function s(e,l,a,o,s,r){return(0,t.uX)(),(0,t.CE)("div",{ref:"scEcharts",style:(0,i.Tr)({height:a.height,width:a.width})},null,4)}var r=a(962);const d={color:["#409EFF","#36CE9E","#f56e6a","#626c91","#edb00d","#909399"],grid:{left:"3%",right:"3%",bottom:"10",top:"40",containLabel:!0},legend:{textStyle:{color:"#999"},inactiveColor:"rgba(128,128,128,0.4)"},categoryAxis:{axisLine:{show:!0,lineStyle:{color:"rgba(128,128,128,0.2)",width:1}},axisTick:{show:!1,lineStyle:{color:"#333"}},axisLabel:{color:"#999"},splitLine:{show:!1,lineStyle:{color:["#eee"]}},splitArea:{show:!1,areaStyle:{color:["rgba(255,255,255,0.01)","rgba(0,0,0,0.01)"]}}},valueAxis:{axisLine:{show:!1,lineStyle:{color:"#999"}},splitLine:{show:!0,lineStyle:{color:"rgba(128,128,128,0.2)"}}}};var n=d;r.registerTheme("T",n);const u=e=>e&&(e.__v_raw||e.valueOf()||e);var c={...r,name:"scEcharts",props:{height:{type:String,default:"100%"},width:{type:String,default:"100%"},nodata:{type:Boolean,default:!1},option:{type:Object,default:()=>{}}},data(){return{isActivat:!1,myChart:null}},watch:{option:{deep:!0,handler(e){u(this.myChart).setOption(e)}}},computed:{myOptions:function(){return this.option||{}}},activated(){this.isActivat||this.$nextTick((()=>{this.myChart.resize()}))},deactivated(){this.isActivat=!1},mounted(){this.isActivat=!0,this.$nextTick((()=>{this.draw()}))},methods:{draw(){var e=r.init(this.$refs.scEcharts,"T");e.setOption(this.myOptions),this.myChart=e,window.addEventListener("resize",(()=>e.resize()))}}},m=a(6262);const p=(0,m.A)(c,[["render",s]]);var f=p,h={title:"实时收入",icon:"el-icon-data-line",description:"Echarts组件演示",components:{scEcharts:f},data(){return{loading:!0,option:{}}},created(){var e=this;setTimeout((function(){e.loading=!1}),500);var l={tooltip:{trigger:"axis"},xAxis:{boundaryGap:!1,type:"category",data:function(){var e=new Date,l=[],a=30;while(a--)l.unshift(e.toLocaleTimeString().replace(/^\D*/,"")),e=new Date(e-2e3);return l}()},yAxis:[{type:"value",name:"价格",splitLine:{show:!1}}],series:[{name:"收入",type:"line",symbol:"none",lineStyle:{width:1,color:"#409EFF"},areaStyle:{opacity:.1,color:"#79bbff"},data:function(){var e=[],l=30;while(l--)e.push(Math.round(0*Math.random()));return e}()}]};this.option=l},mounted(){var e=this;setInterval((function(){var l=e.option;l.series[0].data.shift(),l.series[0].data.push(Math.round(100*Math.random())),l.xAxis.data.shift(),l.xAxis.data.push((new Date).toLocaleTimeString().replace(/^\D*/,""))}),2100)}};const b=(0,m.A)(h,[["render",o]]);var g=b},6328:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return n}});var t=a(641),o=a(2644);function i(e,l,a,i,s,r){const d=(0,t.g2)("sc-status-indicator"),n=(0,t.g2)("el-descriptions-item"),u=(0,t.g2)("el-descriptions"),c=(0,t.g2)("el-card");return(0,t.uX)(),(0,t.Wv)(c,{shadow:"hover",header:"数据库连接状态",class:"item-background",style:{height:"135px"}},{default:(0,t.k6)((()=>[(0,t.bF)(u,{column:2,border:""},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(s.array,(e=>((0,t.uX)(),(0,t.CE)("div",{key:e},[(0,t.bF)(n,{label:e.name,"label-align":"center",align:"center",width:"150px"},{default:(0,t.k6)((()=>[(0,t.bF)(d,{pulse:"",type:e.type},null,8,["type"])])),_:2},1032,["label"]),(0,t.bF)(n,{label:"延迟","label-align":"center",align:"center",width:"150px"},{default:(0,t.k6)((()=>[(0,t.eW)((0,o.v_)(e.milliseconds)+"毫秒",1)])),_:2},1024)])))),128))])),_:1})])),_:1})}var s={title:"连接状态",description:"检测数据库实时状态",icon:"el-icon-tickets",data(){return{array:[{name:"Bili",type:"info",milliseconds:0},{name:"斗鱼",type:"info",milliseconds:0}]}},async mounted(){await this.link(),setInterval((async()=>{await this.link()}),12e4)},methods:{async link(){let e=await this.$API.home.getLocalStatus.post();0==e.code&&(this.array=e.data)}}},r=a(6262);const d=(0,r.A)(s,[["render",i],["__scopeId","data-v-68602094"]]);var n=d},5511:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return p}});var t=a(641),o=a(2644);const i={class:"common-layout",style:{"margin-top":"-10px",border:"1px soild #ff0"}},s=["innerHTML"],r={class:"dialog-footer"};function d(e,l,a,d,n,u){const c=(0,t.g2)("el-button"),m=(0,t.g2)("el-popconfirm"),p=(0,t.g2)("el-table-column"),f=(0,t.g2)("scTable"),h=(0,t.g2)("el-card"),b=(0,t.g2)("el-input"),g=(0,t.g2)("el-form-item"),F=(0,t.g2)("el-col"),k=(0,t.g2)("sc-editor"),v=(0,t.g2)("el-row"),y=(0,t.g2)("el-form"),_=(0,t.g2)("el-dialog"),V=(0,t.gN)("auth"),w=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)(t.FK,null,[(0,t.bF)(h,{shadow:"hover",header:"公告",class:"item-background",style:{height:"320px"}},{default:(0,t.k6)((()=>[(0,t.Lk)("div",i,[(0,t.bF)(f,{ref:"table","show-header":-1==e.$TOOL.data.get("PERMISSIONS").indexOf("Fdashboard.edit"),hidePagination:"",hideDo:"",apiObj:e.$API.sysNotice.getSysNoticeList,height:"250",params:{jObjectSearch:{id:0}},stripe:"",style:{width:"100%"}},{default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(p,{type:"index",align:"center",width:"100",fixed:"left"},{header:(0,t.k6)((()=>[(0,t.bF)(c,{type:"primary",icon:"el-icon-plus",size:"small",circle:"",onClick:u.add},null,8,["onClick"])])),default:(0,t.k6)((e=>[(0,t.bF)(c,{type:"warning",icon:"el-icon-edit",size:"small",plain:"",circle:"",onClick:l=>u.rowEdit(e.row,e.$index)},null,8,["onClick"]),(0,t.bF)(m,{title:"确定删除吗？",onConfirm:l=>u.rowDel(e.row,e.$index),width:"250"},{reference:(0,t.k6)((()=>[(0,t.bF)(c,{type:"danger",icon:"el-icon-delete",size:"small",plain:"",circle:""})])),_:2},1032,["onConfirm"])])),_:1})),[[V,"dashboard.edit"]]),(0,t.bF)(p,{label:"标题",prop:"FTitle","header-align":"center",align:"left","show-overflow-tooltip":""},{default:(0,t.k6)((l=>[(0,t.bF)(c,{type:"primary",onClick:e=>u.show(l.row),link:""},{default:(0,t.k6)((()=>[(0,t.eW)((0,o.v_)(e.$TOOL.data.get("NOTICE-"+l.row.FDate)?"":"（新）")+" "+(0,o.v_)(l.row.FTitle),1)])),_:2},1032,["onClick"])])),_:1}),(0,t.bF)(p,{label:"发布时间",prop:"FDate",align:"center",width:"150"}),(0,t.bF)(p,{label:"点赞数量",prop:"FLike",align:"center",width:"100"}),(0,t.bF)(p,{label:"点赞",prop:"FLikes",align:"center",width:"80"},{default:(0,t.k6)((e=>[(0,t.bF)(c,{icon:0==e.row.FStar?"el-icon-star":"el-icon-star-filled",size:"small",plain:"",circle:"",onClick:l=>u.star(e.row,e.$index)},null,8,["icon","onClick"])])),_:1})])),_:1},8,["show-header","apiObj"])])])),_:1}),(0,t.bF)(_,{modelValue:n.dialog,"onUpdate:modelValue":l[5]||(l[5]=e=>n.dialog=e),title:n.title,width:"768px","close-on-click-modal":!1},{footer:(0,t.k6)((()=>[(0,t.Lk)("div",r,[(0,t.bF)(c,{onClick:l[4]||(l[4]=e=>n.dialog=!1)},{default:(0,t.k6)((()=>[(0,t.eW)("关闭")])),_:1}),""==n.html1?((0,t.uX)(),(0,t.Wv)(c,{key:0,type:"primary",onClick:u.save},{default:(0,t.k6)((()=>[(0,t.eW)("保存")])),_:1},8,["onClick"])):(0,t.Q3)("",!0)])])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(y,{model:n.form,disabled:"show"==e.mode,ref:"dialogForm","label-width":"0"},{default:(0,t.k6)((()=>[""!=n.html1?((0,t.uX)(),(0,t.CE)("div",{key:0,innerHTML:n.html1,class:"html",style:{"padding-left":"20px"}},null,8,s)):((0,t.uX)(),(0,t.Wv)(v,{key:1,gutter:"15"},{default:(0,t.k6)((()=>[(0,t.bF)(F,{span:18},{default:(0,t.k6)((()=>[(0,t.bF)(g,{"label-width":"100",prop:"FTitle",label:"公告标题",rules:[{required:!0,message:"请输入公告标题"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(b,{modelValue:n.form.FTitle,"onUpdate:modelValue":l[0]||(l[0]=e=>n.form.FTitle=e),placeholder:"公告标题"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(F,{span:3},{default:(0,t.k6)((()=>[(0,t.bF)(g,{prop:"FSort",rules:[{required:!0,message:"请输入排序"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(b,{type:"number",modelValue:n.form.FSort,"onUpdate:modelValue":l[1]||(l[1]=e=>n.form.FSort=e),placeholder:"排序"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(F,{span:3},{default:(0,t.k6)((()=>[(0,t.bF)(g,{prop:"FPrivate",rules:[{required:!0,message:"请输入条件标识"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(b,{type:"number",modelValue:n.form.FPrivate,"onUpdate:modelValue":l[2]||(l[2]=e=>n.form.FPrivate=e),placeholder:"条件标识"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(F,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(k,{modelValue:n.form.FHtml,"onUpdate:modelValue":l[3]||(l[3]=e=>n.form.FHtml=e),placeholder:"请输入",height:400},null,8,["modelValue"])])),_:1})])),_:1}))])),_:1},8,["model","disabled"])),[[w,e.isSaveing]])])),_:1},8,["modelValue","title"])],64)}const n=(0,t.$V)((()=>Promise.all([a.e(8774),a.e(6158),a.e(9683)]).then(a.bind(a,9683))));var u={title:"公告",components:{scEditor:n},description:"公告组件",icon:"el-icon-notification",data(){return{dialog:!1,title:"",html1:"",form:{Fid:"",FTitle:"",FPrivate:1,FHtml:"",FSort:0}}},async mounted(){},methods:{add(){this.html1="",this.dialog=!0,this.title="新增公告"},async rowEdit(e){this.html1="",this.dialog=!0,this.title="编辑公告";let l=await this.$API.sysNotice.getSysNoticeList.post({jObjectSearch:{id:e.Fid}});0==l.code?(this.form=l.data.rows[0],this.form.FHtml=l.data.rows[0].FHtml):this.$alert(l.message,"提示",{type:"error"})},async rowDel(e){let l=await this.$API.sysNotice.delSysNotice.post({jObjectParam:{id:e.Fid}});0==l.code?(this.$message.success(l.message),this.upsearch(),this.form={}):this.$alert(l.message,"提示",{type:"error"})},upsearch(){this.$refs.table.upData({jObjectSearch:{jObjectSearch:{id:0}}})},async save(){let e=await this.$API.sysNotice.saveSysNotice.post({jObjectParam:this.form});0==e.code?(this.dialog=!1,this.$message.success(e.message),this.upsearch()):this.$alert(e.message,"提示",{type:"error"})},async show(e){let l=await this.$API.sysNotice.getSysNoticeList.post({jObjectSearch:{id:e.Fid}});0==l.code?(this.html1=l.data.rows[0].FHtml,this.$TOOL.data.set("NOTICE-"+e.FDate,!0),this.title=e.FTitle,this.dialog=!0):this.$alert(l.message,"提示",{type:"error"})},async star(e){let l=await this.$API.sysNotice.starSysNotice.post({jObjectParam:{id:e.Fid}});0==l.code?this.upsearch():this.$alert(l.message,"提示",{type:"error"})}}},c=a(6262);const m=(0,c.A)(u,[["render",d],["__scopeId","data-v-87183090"]]);var p=m},369:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return p}});var t=a(641),o=a(2644);const i=e=>((0,t.Qi)("data-v-02bc7e68"),e=e(),(0,t.jt)(),e),s={class:"progress"},r={class:"percentage-value"},d=i((()=>(0,t.Lk)("div",{class:"percentage-label"},"当前进度",-1)));function n(e,l,a,i,n,u){const c=(0,t.g2)("el-progress"),m=(0,t.g2)("el-card");return(0,t.uX)(),(0,t.Wv)(m,{shadow:"hover",header:"进度环"},{default:(0,t.k6)((()=>[(0,t.Lk)("div",s,[(0,t.bF)(c,{type:"dashboard",percentage:85.5,width:160},{default:(0,t.k6)((({percentage:e})=>[(0,t.Lk)("div",r,(0,o.v_)(e)+"%",1),d])),_:1},8,["percentage"])])])),_:1})}var u={title:"进度环",icon:"el-icon-odometer",description:"进度环原子组件演示",data(){return{}}},c=a(6262);const m=(0,c.A)(u,[["render",n],["__scopeId","data-v-02bc7e68"]]);var p=m},6716:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return u}});var t=a(641),o=a(2644);const i={class:"time"};function s(e,l,a,s,r,d){const n=(0,t.g2)("el-card");return(0,t.uX)(),(0,t.Wv)(n,{shadow:"hover",header:"时钟",class:"item-background"},{default:(0,t.k6)((()=>[(0,t.Lk)("div",i,[(0,t.Lk)("h2",null,(0,o.v_)(r.time),1),(0,t.Lk)("p",null,(0,o.v_)(r.day),1),(0,t.Lk)("p",null,(0,o.v_)(r.message),1)])])),_:1})}var r={title:"时钟",icon:"el-icon-clock",description:"演示部件效果",data(){return{time:"",day:"",message:""}},async mounted(){this.showTime(),setInterval((()=>{this.showTime()}),1e3);let e=await this.$API.home.now.post({});this.message=e.data},methods:{showTime(){this.time=this.$TOOL.dateFormat(new Date,"hh:mm:ss"),this.day=this.$TOOL.dateFormat(new Date,"yyyy年MM月dd日")}}},d=a(6262);const n=(0,d.A)(r,[["render",s],["__scopeId","data-v-0ba440e2"]]);var u=n},8302:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return f}});var t=a(641),o=a(2644);const i={style:{height:"195px","text-align":"center"}},s=(0,t.Lk)("img",{src:"img/ver.svg",style:{height:"120px"}},null,-1),r={style:{"margin-top":"15px"}},d={style:{"margin-top":"5px"}},n={style:{"margin-top":"10px"}};function u(e,l,a,u,c,m){const p=(0,t.g2)("el-button"),f=(0,t.g2)("el-card");return(0,t.uX)(),(0,t.Wv)(f,{shadow:"hover",header:"版本信息",style:{height:"320px"}},{default:(0,t.k6)((()=>[(0,t.Lk)("div",i,[s,(0,t.Lk)("h2",r,(0,o.v_)(e.$CONFIG.APP_NAME)+(0,o.v_)(e.$CONFIG.APP_VER),1),(0,t.Lk)("p",d,"最新版本 "+(0,o.v_)(c.ver),1)]),(0,t.Lk)("div",n,[(0,t.bF)(p,{type:"primary",plain:"",round:"",onClick:m.golog},{default:(0,t.k6)((()=>[(0,t.eW)("更新日志")])),_:1},8,["onClick"]),(0,t.bF)(p,{type:"primary",plain:"",round:"",onClick:m.gogit},{default:(0,t.k6)((()=>[(0,t.eW)("gitee")])),_:1},8,["onClick"])])])),_:1})}var c={title:"版本信息",icon:"el-icon-monitor",description:"当前项目版本信息",data(){return{ver:"loading..."}},mounted(){this.getVer()},methods:{async getVer(){let e=await this.$API.home.getVer.post();0==e.code?this.ver=e.data:this.ver=e.message},golog(){},gogit(){}}},m=a(6262);const p=(0,m.A)(c,[["render",u]]);var f=p},7156:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return n}});var t=a(641),o=a(2644);function i(e,l,a,i,s,r){const d=(0,t.g2)("el-text"),n=(0,t.g2)("el-form-item"),u=(0,t.g2)("el-col"),c=(0,t.g2)("el-row"),m=(0,t.g2)("el-form"),p=(0,t.g2)("el-empty"),f=(0,t.g2)("el-card");return(0,t.uX)(),(0,t.Wv)(f,{shadow:"hover",header:"小提示",style:{"max-height":"300px"}},{default:(0,t.k6)((()=>[0!=s.rows.length?((0,t.uX)(),(0,t.Wv)(m,{key:0,"label-width":"110px"},{default:(0,t.k6)((()=>[(0,t.bF)(c,null,{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(s.rows,(e=>((0,t.uX)(),(0,t.Wv)(u,{span:24,key:e},{default:(0,t.k6)((()=>[(0,t.bF)(n,{label:e.FLabel},{default:(0,t.k6)((()=>[(0,t.bF)(d,null,{default:(0,t.k6)((()=>[(0,t.eW)((0,o.v_)(e.FValue),1)])),_:2},1024)])),_:2},1032,["label"])])),_:2},1024)))),128))])),_:1})])),_:1})):((0,t.uX)(),(0,t.Wv)(p,{key:1,"image-size":100}))])),_:1})}var s={title:"小提示",icon:"el-icon-present",description:"展示一些提示信息",data(){return{rows:[]}},async mounted(){let e=await this.$API.home.getTipList.post({jObjectSearch:{p1:"1"}});0==e.code?this.rows=e.data.rows:this.ver=e.message},methods:{}},r=a(6262);const d=(0,r.A)(s,[["render",i]]);var n=d},4631:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return b}});var t=a(641),o=a(2644);const i={class:"myMods"},s=["href"],r={href:"javascript:void(0)"},d={class:"setMods"},n={class:"setMods"};function u(e,l,a,u,c,m){const p=(0,t.g2)("el-icon"),f=(0,t.g2)("router-link"),h=(0,t.g2)("el-icon-plus"),b=(0,t.g2)("draggable"),g=(0,t.g2)("el-button"),F=(0,t.g2)("el-drawer");return(0,t.uX)(),(0,t.CE)("div",null,[(0,t.Lk)("ul",i,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(c.myMods,(l=>((0,t.uX)(),(0,t.CE)("li",{key:l.path,style:(0,o.Tr)({background:l.meta.color||"#909399"})},["link"==l.meta.type?((0,t.uX)(),(0,t.CE)("a",{key:0,href:l.path,target:"_blank"},[(0,t.bF)(p,null,{default:(0,t.k6)((()=>[((0,t.uX)(),(0,t.Wv)((0,t.$y)(l.meta.icon||e.el-e.icon-e.menu)))])),_:2},1024),(0,t.Lk)("p",null,(0,o.v_)(l.meta.title),1)],8,s)):((0,t.uX)(),(0,t.Wv)(f,{key:1,to:{path:l.path}},{default:(0,t.k6)((()=>[(0,t.bF)(p,null,{default:(0,t.k6)((()=>[((0,t.uX)(),(0,t.Wv)((0,t.$y)(l.meta.icon||e.el-e.icon-e.menu)))])),_:2},1024),(0,t.Lk)("p",null,(0,o.v_)(l.meta.title),1)])),_:2},1032,["to"]))],4)))),128)),(0,t.Lk)("li",{class:"modItem-add",onClick:l[0]||(l[0]=(...e)=>m.addMods&&m.addMods(...e))},[(0,t.Lk)("a",r,[(0,t.bF)(p,null,{default:(0,t.k6)((()=>[(0,t.bF)(h)])),_:1})])])]),(0,t.bF)(F,{title:"添加应用",modelValue:c.modsDrawer,"onUpdate:modelValue":l[4]||(l[4]=e=>c.modsDrawer=e),size:570,"destroy-on-close":""},{footer:(0,t.k6)((()=>[(0,t.bF)(g,{onClick:l[3]||(l[3]=e=>c.modsDrawer=!1)},{default:(0,t.k6)((()=>[(0,t.eW)("取消")])),_:1}),(0,t.bF)(g,{type:"primary",onClick:m.saveMods},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["onClick"])])),default:(0,t.k6)((()=>[(0,t.Lk)("div",d,[(0,t.Lk)("h4",null,"我的常用 ( "+(0,o.v_)(c.myMods.length)+" )",1),(0,t.bF)(b,{tag:"ul",modelValue:c.myMods,"onUpdate:modelValue":l[1]||(l[1]=e=>c.myMods=e),animation:"200","item-key":"path",group:"people"},{item:(0,t.k6)((({element:l})=>[(0,t.Lk)("li",{style:(0,o.Tr)({background:l.meta.color||"#909399"})},[(0,t.bF)(p,null,{default:(0,t.k6)((()=>[((0,t.uX)(),(0,t.Wv)((0,t.$y)(l.meta.icon||e.el-e.icon-e.menu)))])),_:2},1024),(0,t.Lk)("p",null,(0,o.v_)(l.meta.title),1)],4)])),_:1},8,["modelValue"])]),(0,t.Lk)("div",n,[(0,t.Lk)("h4",null,"全部应用 ( "+(0,o.v_)(c.filterMods.length)+" )",1),(0,t.bF)(b,{tag:"ul",modelValue:c.filterMods,"onUpdate:modelValue":l[2]||(l[2]=e=>c.filterMods=e),animation:"200","item-key":"path",sort:!1,group:"people"},{item:(0,t.k6)((({element:l})=>[(0,t.Lk)("li",{style:(0,o.Tr)({background:l.meta.color||"#909399"})},[(0,t.bF)(p,null,{default:(0,t.k6)((()=>[((0,t.uX)(),(0,t.Wv)((0,t.$y)(l.meta.icon||e.el-e.icon-e.menu)))])),_:2},1024),(0,t.Lk)("p",null,(0,o.v_)(l.meta.title),1)],4)])),_:1},8,["modelValue"])])])),_:1},8,["modelValue"])])}a(8743);var c=a(432),m=a.n(c),p={components:{draggable:m()},data(){return{mods:[],myMods:[],myModsName:[],filterMods:[],modsDrawer:!1}},mounted(){this.getMods()},methods:{addMods(){this.modsDrawer=!0},getMods(){this.myModsName=this.$TOOL.data.get("my-mods")||[];var e=this.$TOOL.data.get("MENU");this.filterMenu(e),this.myMods=this.mods.filter((e=>this.myModsName.includes(e.name))),this.filterMods=this.mods.filter((e=>!this.myModsName.includes(e.name)))},filterMenu(e){e.forEach((e=>{if(e.meta.hidden||"button"==e.meta.type)return!1;"iframe"==e.meta.type&&(e.path=`/i/${e.name}`),e.children&&e.children.length>0?this.filterMenu(e.children):this.mods.push(e)}))},saveMods(){const e=this.myMods.map((e=>e.name));this.$TOOL.data.set("my-mods",e),this.$message.success("设置常用成功"),this.modsDrawer=!1}}},f=a(6262);const h=(0,f.A)(p,[["render",u],["__scopeId","data-v-e4638424"]]);var b=h},5514:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return w}});var t=a(641),o=a(2644);const i=(0,t.Lk)("h3",null,"1.只勾选BiliBili时拥有所有权限",-1),s=(0,t.Lk)("h3",null,"2.拥有所有权限时可自己维护分区",-1),r=(0,t.Lk)("h3",null,"3.所有账号到期，才可调整分区",-1),d=(0,t.Lk)("h3",null,"4.付款后1小时左右到账",-1),n=(0,t.Lk)("h3",null,"5.需要其他分区请联系群主",-1),u=(0,t.Lk)("h3",null,"6.修改分区需要重新登录(充值成功)",-1),c=(0,t.Lk)("h3",null,"7.群号324043488",-1),m=(0,t.Lk)("h3",null,null,-1),p={class:"custom-tree-node1"},f={style:{"margin-left":"8px"}},h=["src"],b=(0,t.Lk)("h3",null,"请您支付",-1),g={style:{color:"#f00"}},F=(0,t.Lk)("h3",null,"注意小数部分否则充值不上！",-1),k=(0,t.Lk)("h3",null,"充值时间0点-23点",-1);function v(e,l,a,v,y,_){const V=(0,t.g2)("el-table-column"),w=(0,t.g2)("el-table"),C=(0,t.g2)("el-col"),S=(0,t.g2)("el-tree"),j=(0,t.g2)("el-main"),$=(0,t.g2)("el-container"),O=(0,t.g2)("el-row"),U=(0,t.g2)("el-form"),I=(0,t.g2)("el-button"),A=(0,t.g2)("el-dialog"),x=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(A,{modelValue:y.visible,"onUpdate:modelValue":l[1]||(l[1]=e=>y.visible=e),title:y.titleMap[y.mode],width:1200,"destroy-on-close":"","close-on-click-modal":!1,onOpened:_.opened,onClosed:l[2]||(l[2]=l=>e.$emit("closed"))},{footer:(0,t.k6)((()=>[""==y.src?((0,t.uX)(),(0,t.Wv)(I,{key:0,type:"primary",onClick:l[0]||(l[0]=e=>_.save()),disabled:y.isSaveing},{default:(0,t.k6)((()=>[(0,t.eW)("生成支付二维码")])),_:1},8,["disabled"])):(0,t.Q3)("",!0)])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(U,{model:y.form,ref:"dialogForm"},{default:(0,t.k6)((()=>[(0,t.bF)(O,{gutter:20},{default:(0,t.k6)((()=>[(0,t.bF)(C,{span:13},{default:(0,t.k6)((()=>[(0,t.bF)(w,{data:y.form.array,border:"",style:{height:"500px"}},{default:(0,t.k6)((()=>[(0,t.bF)(V,{prop:"FKey",label:"账号标识（编辑可以查看标识）",align:"center",width:"300px"}),(0,t.bF)(V,{prop:"FName",label:"账号名称",align:"center","show-overflow-tooltip":""}),(0,t.bF)(V,{prop:"FExpirationTime",label:"到期时间",align:"center"})])),_:1},8,["data"])])),_:1}),(0,t.bF)(C,{span:6},{default:(0,t.k6)((()=>[i,s,r,d,n,u,c,m,(0,t.bF)($,null,{default:(0,t.k6)((()=>[(0,t.bF)(j,{class:"nopadding",style:{height:"400px"}},{default:(0,t.k6)((()=>[(0,t.bF)(S,{ref:"tree",class:"menu","node-key":"value",data:y.treeData,"default-checked-keys":e.treeValue,"highlight-current":"","default-expand-all":"","expand-on-click-node":!1,"check-strictly":"","show-checkbox":""},{default:(0,t.k6)((({node:e,data:l})=>[(0,t.Lk)("span",p,[(0,t.Lk)("span",null,(0,o.v_)(e.label),1),(0,t.Lk)("span",null,[(0,t.Lk)("span",f,(0,o.v_)(l.desc)+"元 / 月 ",1)])])])),_:1},8,["data","default-checked-keys"])])),_:1})])),_:1})])),_:1}),""!=y.src?((0,t.uX)(),(0,t.Wv)(C,{key:0,span:5},{default:(0,t.k6)((()=>[(0,t.Lk)("img",{src:y.src,style:{width:"100%"}},null,8,h),b,(0,t.Lk)("h1",g,(0,o.v_)(y.money),1),F,k])),_:1})):(0,t.Q3)("",!0)])),_:1})])),_:1},8,["model"])),[[x,y.isSaveing]])])),_:1},8,["modelValue","title","onOpened"])}var y={emits:["success","closed"],data(){return{mode:"add",titleMap:{show:"充值时长（默认1个月）"},visible:!1,isSaveing:!1,treeData:[],form:{array:[]},src:"",money:""}},async mounted(){await this.getSelect()},methods:{async getSelect(){var e=await this.$API.ksCookies.getSysOrganizationList.post({jObjectParam:{}});this.treeData=e.data.tree,this.treeValue=e.data.treeValue},open(e="add"){return this.mode=e,this.visible=!0,this},save(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.isSaveing=!0,this.form.org=String(this.$refs.tree.getCheckedKeys(!1));var l=await this.$API.ksCookies.addExpiraation.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.money=l.data.money,this.src=l.data.image):this.$alert(l.message,"提示",{type:"error"})}))},async setData(e){Object.assign(this.form,e)},menuFilterNode(e,l){if(!e)return!0;var a=l.meta.title;return-1!==a.indexOf(e)},opened(){}}},_=a(6262);const V=(0,_.A)(y,[["render",v]]);var w=V},4988:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return d}});var t=a(641);function o(e,l,a,o,i,s){const r=(0,t.g2)("el-input"),d=(0,t.g2)("el-form-item"),n=(0,t.g2)("el-col"),u=(0,t.g2)("el-row"),c=(0,t.g2)("el-form"),m=(0,t.g2)("el-button"),p=(0,t.g2)("el-dialog"),f=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(p,{modelValue:i.visible,"onUpdate:modelValue":l[3]||(l[3]=e=>i.visible=e),title:i.titleMap[i.mode],width:1200,"destroy-on-close":"","close-on-click-modal":!1,onOpened:s.opened,onClosed:l[4]||(l[4]=l=>e.$emit("closed"))},{footer:(0,t.k6)((()=>["show"!=i.mode?((0,t.uX)(),(0,t.Wv)(m,{key:0,type:"primary",loading:i.isSaveing,onClick:l[2]||(l[2]=e=>s.save())},{default:(0,t.k6)((()=>[(0,t.eW)("保存信息")])),_:1},8,["loading"])):(0,t.Q3)("",!0)])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(c,{model:i.form,disabled:"show"==i.mode,ref:"dialogForm","label-width":"130px"},{default:(0,t.k6)((()=>[(0,t.bF)(u,null,{default:(0,t.k6)((()=>[(0,t.bF)(n,{span:11},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请输入手机Cookie"}],label:"","label-width":"0",prop:"FCookie",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FCookie,"onUpdate:modelValue":l[0]||(l[0]=e=>i.form.FCookie=e),style:{width:"100%"},rows:25,type:"textarea",resize:"none",placeholder:"手机Cookie用于抢码，必填"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:11,offset:2},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"","label-width":"0",prop:"FCookie2"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FCookie2,"onUpdate:modelValue":l[1]||(l[1]=e=>i.form.FCookie2=e),style:{width:"100%"},rows:25,type:"textarea",resize:"none",placeholder:"网站Cookie用于投稿"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","disabled"])),[[f,i.isSaveing]])])),_:1},8,["modelValue","title","onOpened"])}var i={emits:["success","closed"],data(){return{mode:"add",titleMap:{add:"新增",edit:"编辑",show:"查看"},visible:!1,isSaveing:!1,form:{Fid:0,FCookie:"",FCookie2:"",FKey:"",FIdentifying:""}}},mounted(){this.getSelect()},methods:{getSelect(){},open(e="add"){return this.mode=e,this.visible=!0,this},save(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.isSaveing=!0,this.form.FStatus="";var l=await this.$API.ksCookies.editCookie.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.visible=!1,this.$alert(l.message,"提示",{type:"success"})):this.$alert(l.message,"提示",{type:"error"})}))},async setData(e){Object.assign(this.form,e)},opened(){}}},s=a(6262);const r=(0,s.A)(i,[["render",o]]);var d=r},2595:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return u}});var t=a(641);const o={class:"left-panel"},i={style:{"padding-left":"10px",width:"1150px"}};function s(e,l,a,s,r,d){const n=(0,t.g2)("el-button"),u=(0,t.g2)("el-option"),c=(0,t.g2)("el-select"),m=(0,t.g2)("el-header"),p=(0,t.g2)("el-table-column"),f=(0,t.g2)("el-button-group"),h=(0,t.g2)("scTable"),b=(0,t.g2)("el-form"),g=(0,t.g2)("el-dialog"),F=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(g,{class:"dialog-table",modelValue:r.visible,"onUpdate:modelValue":l[2]||(l[2]=e=>r.visible=e),title:r.titleMap[r.mode],width:1440,"destroy-on-close":"",onClosed:l[3]||(l[3]=l=>e.$emit("closed")),style:{height:"auto","max-height":"100%"}},{footer:(0,t.k6)((()=>[(0,t.bF)(n,{onClick:l[1]||(l[1]=e=>r.visible=!1)},{default:(0,t.k6)((()=>[(0,t.eW)("关 闭")])),_:1})])),default:(0,t.k6)((()=>[(0,t.bF)(m,{style:{"padding-left":"0px","border-bottom":"0px"}},{default:(0,t.k6)((()=>[(0,t.Lk)("div",o,[(0,t.bF)(n,{type:"primary",icon:"el-icon-refresh-right",onClick:d.restoreDefault},{default:(0,t.k6)((()=>[(0,t.eW)("重置信息")])),_:1},8,["onClick"]),(0,t.bF)(n,{type:"primary",icon:"el-icon-promotion",onClick:d.syncAll},{default:(0,t.k6)((()=>[(0,t.eW)(" 同步账号")])),_:1},8,["onClick"]),(0,t.Lk)("div",i,[(0,t.bF)(c,{modelValue:r.jObjectSearch.taskId,"onUpdate:modelValue":l[0]||(l[0]=e=>r.jObjectSearch.taskId=e),filterable:"",multiple:"",clearable:"",onChange:d.upsearch,style:{width:"100%"}},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(r.area,(e=>((0,t.uX)(),(0,t.Wv)(u,{key:e.Fid,label:e.FName,value:e.Fid,placeholder:"请选择分区"},null,8,["label","value"])))),128))])),_:1},8,["modelValue","onChange"])])])])),_:1}),(0,t.bo)(((0,t.uX)(),(0,t.Wv)(b,{style:{height:"550px"}},{default:(0,t.k6)((()=>[(0,t.bF)(h,{ref:"table",apiObj:r.apiObj,"highlight-current-row":"","row-key":"Fid",params:{jObjectSearch:r.jObjectSearch},"row-Style":d.rowStyle,border:"",hideDo:!0,hidePagination:!0},{default:(0,t.k6)((()=>[(0,t.bF)(p,{label:"状态",prop:"FStatus",align:"center",width:"100",formatter:d.formatterStatus},null,8,["formatter"]),(0,t.bF)(p,{label:"分区名称",prop:"FTitle","header-align":"center",align:"left",width:"180","show-overflow-tooltip":""}),(0,t.bF)(p,{label:"任务名称",prop:"FSubTitle","header-align":"center",align:"left",width:"200","show-overflow-tooltip":""}),(0,t.bF)(p,{label:"奖励名称",prop:"FSubRewardName",align:"center",width:"110","show-overflow-tooltip":""}),(0,t.bF)(p,{label:"总计库存",prop:"FSubRewardLimitBold",align:"center",width:"90"}),(0,t.bF)(p,{label:"总计剩余",prop:"FSubRemainNum",align:"center",width:"90"}),(0,t.bF)(p,{label:"条件1",prop:"FSubCompleteRatio1","header-align":"center",align:"left","show-overflow-tooltip":"",width:"140"}),(0,t.bF)(p,{label:"条件2",prop:"FSubCompleteRatio2","header-align":"center",align:"left","show-overflow-tooltip":"",width:"140"}),(0,t.bF)(p,{label:"起始ID",prop:"FStartRecordId",align:"center",width:"130"}),(0,t.bF)(p,{label:"Cdkey",prop:"FSubCdkey",align:"center",width:"140"}),(0,t.bF)(p,{label:"操作",fixed:"right",align:"center",width:"180"},{default:(0,t.k6)((e=>[(0,t.bF)(f,null,{default:(0,t.k6)((()=>[0==e.row.FEnable?((0,t.uX)(),(0,t.Wv)(n,{key:0,text:"",size:"small",style:{color:"#60F"},onClick:l=>d.setCompulsory(e.row,e.$index,1)},{default:(0,t.k6)((()=>[(0,t.eW)("准备领取")])),_:2},1032,["onClick"])):(0,t.Q3)("",!0),1==e.row.FEnable?((0,t.uX)(),(0,t.Wv)(n,{key:1,text:"",size:"small",style:{color:"#555"},onClick:l=>d.setCompulsory(e.row,e.$index,0)},{default:(0,t.k6)((()=>[(0,t.eW)("取消领取")])),_:2},1032,["onClick"])):(0,t.Q3)("",!0),(0,t.bF)(n,{text:"",type:"primary",size:"small",onClick:l=>d.setStartRecordId(e.row,e.$index)},{default:(0,t.k6)((()=>[(0,t.eW)("配置起始ID")])),_:2},1032,["onClick"])])),_:2},1024)])),_:1})])),_:1},8,["apiObj","params","row-Style"])])),_:1})),[[F,r.loading]])])),_:1},8,["modelValue","title"])}var r={emits:["success","closed"],data(){return{mode:"show",titleMap:{show:"任务信息"},visible:!1,loading:!1,apiObj:null,jObjectSearch:{taskId:this.$TOOL.data.get("KSTASK")},area:[]}},async created(){let e=await this.$API.ksCookiesTask.getTaskList.post();0==e.code&&(this.area=e.data.rows)},mounted(){},methods:{open(e="show"){return this.mode=e,this.visible=!0,this},async setData(e){Object.assign(this.jObjectSearch,e),this.apiObj=this.$API.ksCookiesTask.getCookiesTaskList},rowStyle(e){return""!=e.row.FSubCdkey?"color:#CC6600":1==e.row.FEnable?"color:#006600":"0"==e.row.FSubRemainNum?"color:#CC0000":void 0},formatterStatus(e){let l="";return""!=e.FSubCdkey?l="已抢到":"0"==e.FSubRemainNum?l="无库存":1==e.FEnable&&(l="等待领取"),l},async setCompulsory(e,l,a){let t=await this.$API.ksCookiesTask.setReceive.post({jObjectParam:{Fid:e.Fid,FEnable:a}});0==t.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(t.message,"提示",{type:"error"}),this.loading=!1},async syncAll(){let e=await this.$API.ksCookiesTask.syncAll.post({jObjectParam:{cookieId:this.jObjectSearch.cookieId}});0==e.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(e.message,"提示",{type:"error"})},async restoreDefault(){this.loading=!0;let e=await this.$API.ksCookiesTask.updateCookiesTask.post({jObjectParam:{cookieId:this.jObjectSearch.cookieId}});this.loading=!1,0==e.code?(this.$message.success("更新成功！"),this.upsearch()):this.$alert(e.message,"提示",{type:"error"})},setStartRecordId(e){this.$prompt("从上一个ID开始跑如果知道起始ID可以自己配置","配置起始ID",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnPressEscape:!0,closeOnClickModal:!0,autofocus:!0,inputType:"number",inputValue:e.FStartRecordId}).then((async({value:l})=>{let a=await this.$API.ksCookiesTask.setStartRecordId.post({jObjectParam:{Fid:e.Fid,FStartRecordId:l}});0==a.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(a.message,"提示",{type:"error"})}))},upsearch(){this.$refs.table.upData({jObjectSearch:this.jObjectSearch}),this.$TOOL.data.set("KSTASK",this.jObjectSearch.taskId)}}},d=a(6262);const n=(0,d.A)(r,[["render",s]]);var u=n},3273:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return d}});var t=a(641);function o(e,l,a,o,i,s){const r=(0,t.g2)("el-time-picker"),d=(0,t.g2)("el-form-item"),n=(0,t.g2)("el-col"),u=(0,t.g2)("el-input"),c=(0,t.g2)("el-tooltip"),m=(0,t.g2)("sc-tree-select"),p=(0,t.g2)("sc-select"),f=(0,t.g2)("el-row"),h=(0,t.g2)("el-form"),b=(0,t.g2)("el-button"),g=(0,t.g2)("el-dialog"),F=(0,t.gN)("loading");return(0,t.uX)(),(0,t.Wv)(g,{modelValue:i.visible,"onUpdate:modelValue":l[10]||(l[10]=e=>i.visible=e),title:i.titleMap[i.mode],width:1024,"destroy-on-close":"",onOpened:e.opened,onClosed:l[11]||(l[11]=l=>e.$emit("closed"))},{footer:(0,t.k6)((()=>["show"!=i.mode?((0,t.uX)(),(0,t.Wv)(b,{key:0,type:"primary",loading:i.isSaveing,onClick:l[9]||(l[9]=e=>s.save())},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["loading"])):(0,t.Q3)("",!0)])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(h,{model:i.form,disabled:"show"==i.mode,ref:"dialogForm","label-width":"110px"},{default:(0,t.k6)((()=>[(0,t.bF)(f,null,{default:(0,t.k6)((()=>[(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请选择执行时间"}],label:"执行时间",prop:"FExecTime",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FExecTime,"onUpdate:modelValue":l[0]||(l[0]=e=>i.form.FExecTime=e),editable:"",placeholder:"执行时间",format:"HH:mm:ss","value-format":"HH:mm:ss"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(c,{class:"box-item",effect:"dark",content:"定时任务启动后的随机X秒内执行",placement:"top"},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"随机时间",prop:"FDifference",rules:[{required:!0,message:"请输入随机区间"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(u,{type:"number",modelValue:i.form.FDifference,"onUpdate:modelValue":l[1]||(l[1]=e=>i.form.FDifference=e),placeholder:"随机时间"},{append:(0,t.k6)((()=>[(0,t.eW)("秒")])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"执行时长",prop:"FSeconds",rules:[{required:!0,message:"请输入执行时长"}],required:""},{default:(0,t.k6)((()=>[(0,t.bF)(u,{type:"number",modelValue:i.form.FSeconds,"onUpdate:modelValue":l[2]||(l[2]=e=>i.form.FSeconds=e),placeholder:"执行时长"},{append:(0,t.k6)((()=>[(0,t.eW)("秒")])),_:1},8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请选择任务名称"}],label:"任务名称",prop:"FJobName",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(m,{modelValue:i.form.FJobName,"onUpdate:modelValue":l[3]||(l[3]=e=>i.form.FJobName=e),ref:"treeSelect",placeholder:"请选择任务名称",prop:{label:"FName",value:"FName",children:"list"},"default-expand-all":"",params:{jObjectSearch:{}},apiObj:e.$API.ksQuartz.getQuartzJobList,style:{width:"100%"}},null,8,["modelValue","apiObj"])])),_:1})])),_:1}),(0,t.bF)(n,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请选择账号名称"}],label:"账号名称",prop:"FCookie",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(p,{clearable:"",modelValue:i.form.FCookie,"onUpdate:modelValue":l[4]||(l[4]=e=>i.form.FCookie=e),params:{jObjectSearch:{}},multiple:"","collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":4,apiObj:e.$API.ksCookies.getCookiesList,prop:{label:"FName",value:"Fid"},placeholder:" 账号名称",style:{width:"100%"}},null,8,["modelValue","apiObj"])])),_:1})])),_:1}),"定时投稿"==i.form.FJobName?((0,t.uX)(),(0,t.Wv)(n,{key:0,span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请选择投稿活动"}],label:"投稿活动",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(p,{clearable:"",modelValue:i.FParamTask,"onUpdate:modelValue":l[5]||(l[5]=e=>i.FParamTask=e),apiObj:e.$API.ksCookiesTask.getTaskList,prop:{label:"FName",value:"FName"},placeholder:"投稿活动",style:{width:"100%"}},null,8,["modelValue","apiObj"])])),_:1})])),_:1})):(0,t.Q3)("",!0),"定时投稿"==i.form.FJobName?((0,t.uX)(),(0,t.Wv)(n,{key:1,span:8},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请选择投稿时间"}],label:"投稿时间",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(p,{clearable:"",modelValue:i.FParamTime,"onUpdate:modelValue":l[6]||(l[6]=e=>i.FParamTime=e),apiObj:[{value:" ",label:"立即投稿"},{value:"-2",label:"23:59:58"},{value:"-1",label:"23:59:59"},{value:"0",label:"00:00:00"},{value:"1",label:"00:00:01"},{value:"2",label:"00:00:02"}],placeholder:" 账号名称",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})):(0,t.Q3)("",!0),"正常领取奖励"==i.form.FJobName?((0,t.uX)(),(0,t.Wv)(n,{key:2,span:12},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"请求间隔",prop:"FTimes"},{default:(0,t.k6)((()=>[(0,t.bF)(u,{resize:"none",modelValue:i.FTimes,"onUpdate:modelValue":l[7]||(l[7]=e=>i.FTimes=e),placeholder:"每隔多久请求一次、单位毫秒，默认：100毫秒"},null,8,["modelValue"])])),_:1})])),_:1})):(0,t.Q3)("",!0),"正常领取奖励"==i.form.FJobName?((0,t.uX)(),(0,t.Wv)(n,{key:3,span:12},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"生命周期",prop:"FActive"},{default:(0,t.k6)((()=>[(0,t.bF)(u,{resize:"none",modelValue:i.FActive,"onUpdate:modelValue":l[8]||(l[8]=e=>i.FActive=e),placeholder:"请求存活时间、单位秒，默认5秒"},null,8,["modelValue"])])),_:1})])),_:1})):(0,t.Q3)("",!0)])),_:1})])),_:1},8,["model","disabled"])),[[F,i.isSaveing]])])),_:1},8,["modelValue","title","onOpened"])}var i={emits:["success","closed"],data(){return{mode:"add",titleMap:{add:"新增",edit:"编辑",show:"查看"},visible:!1,isSaveing:!1,FParamTask:"",FParamTime:"",FTimes:"",FActive:"",form:{Fid:0,FGroupId:0,FDifference:0,FSeconds:0,FJobId:"",FCookie:[],FParam:""}}},mounted(){this.getSelect()},methods:{getSelect(){},open(e="add"){return this.mode=e,this.visible=!0,this},save(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.isSaveing=!0,this.form.FCookieId=String(this.form.FCookie);var l=await this.$API.ksQuartz.saveQuartz.post({jObjectParam:{...this.form,FParam:JSON.stringify({Task:this.FParamTask,Time:this.FParamTime,Times:this.FTimes,Active:this.FActive})}});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.$message.success("操作成功"),this.visible=!1):this.$alert(l.message,"提示",{type:"error"})}))},setData(e){if(Object.assign(this.form,e),"edit"==this.mode){""==this.form.FCookieId?this.form.FCookie=[]:this.form.FCookie=String(this.form.FCookieId).split(",").map(Number);let e=JSON.parse(this.form.FParam);"定时投稿"==this.form.FJobName?(this.FParamTask=e.Task,this.FParamTime=e.Time):"正常领取奖励"==this.form.FJobName&&(this.FTimes=e.Times,this.FActive=e.Active)}}}},s=a(6262);const r=(0,s.A)(i,[["render",o]]);var d=r},9499:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return g}});var t=a(641),o=a(2644);const i={class:"common-header-left"},s={class:"common-header-logo"},r=["alt"],d={class:"common-header-title"},n={class:"common-header-right"},u={class:"common-container"},c={class:"common-title"},m={class:"common-main el-card"};function p(e,l,a,p,f,h){const b=(0,t.g2)("router-link"),g=(0,t.g2)("el-header"),F=(0,t.g2)("el-main"),k=(0,t.g2)("el-container");return(0,t.uX)(),(0,t.Wv)(k,null,{default:(0,t.k6)((()=>[(0,t.bF)(g,{style:{height:"50px"}},{default:(0,t.k6)((()=>[(0,t.Lk)("div",i,[(0,t.Lk)("div",s,[(0,t.Lk)("img",{alt:e.$CONFIG.APP_NAME,src:"img/logo.png"},null,8,r),(0,t.Lk)("label",null,(0,o.v_)(e.$CONFIG.APP_NAME),1)]),(0,t.Lk)("div",d,(0,o.v_)(a.title),1)]),(0,t.Lk)("div",n,[(0,t.bF)(b,{to:"/login"},{default:(0,t.k6)((()=>[(0,t.eW)("返回登录")])),_:1})])])),_:1}),(0,t.bF)(F,null,{default:(0,t.k6)((()=>[(0,t.Lk)("div",u,[(0,t.Lk)("h2",c,(0,o.v_)(a.title),1),(0,t.Lk)("div",m,[(0,t.RG)(e.$slots,"default")])])])),_:3})])),_:3})}var f={props:{title:{type:String,default:""}}},h=a(6262);const b=(0,h.A)(f,[["render",p]]);var g=b},5874:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return f}});var t=a(641),o=a(9322),i=a(2644);const s={style:{display:"flex","align-items":"center","justify-content":"space-between",width:"100%"}},r={style:{display:"flex","align-items":"center"}},d=["src"],n={class:"login-reg"};function u(e,l,a,u,c,m){const p=(0,t.g2)("el-input"),f=(0,t.g2)("el-form-item"),h=(0,t.g2)("el-checkbox"),b=(0,t.g2)("el-col"),g=(0,t.g2)("el-button"),F=(0,t.g2)("router-link"),k=(0,t.g2)("el-form");return(0,t.uX)(),(0,t.Wv)(k,{ref:"loginForm",model:c.form,rules:c.rules,"label-width":"0",size:"large"},{default:(0,t.k6)((()=>[(0,t.bF)(f,{prop:"userCode"},{default:(0,t.k6)((()=>[(0,t.bF)(p,{modelValue:c.form.userCode,"onUpdate:modelValue":l[0]||(l[0]=e=>c.form.userCode=e),placeholder:e.$t("login.userPlaceholder"),clearable:"","prefix-icon":"el-icon-user"},null,8,["modelValue","placeholder"])])),_:1}),(0,t.bF)(f,{prop:"userPWD"},{default:(0,t.k6)((()=>[(0,t.bF)(p,{modelValue:c.form.userPWD,"onUpdate:modelValue":l[1]||(l[1]=e=>c.form.userPWD=e),placeholder:e.$t("login.PWPlaceholder"),clearable:"","prefix-icon":"el-icon-lock","show-password":""},null,8,["modelValue","placeholder"])])),_:1}),(0,t.bF)(f,{prop:"verifyCode"},{default:(0,t.k6)((()=>[(0,t.Lk)("div",s,[(0,t.Lk)("div",null,[(0,t.bF)(p,{modelValue:c.form.verifyCode,"onUpdate:modelValue":l[2]||(l[2]=e=>c.form.verifyCode=e),onKeyup:(0,o.jR)(m.login,["enter"]),clearable:"",placeholder:"验证码"},null,8,["modelValue","onKeyup"])]),(0,t.Lk)("div",r,[(0,t.Lk)("img",{onClick:l[3]||(l[3]=(...e)=>m.changeverifyCode&&m.changeverifyCode(...e)),style:{cursor:"pointer"},src:c.verifyCode,fit:"cover"},null,8,d)])])])),_:1}),(0,t.bF)(f,{style:{"margin-bottom":"10px",display:"flex"}},{default:(0,t.k6)((()=>[(0,t.bF)(b,{span:4},{default:(0,t.k6)((()=>[(0,t.bF)(h,{modelValue:c.form.rememberMe,"onUpdate:modelValue":l[4]||(l[4]=e=>c.form.rememberMe=e),label:e.$t("login.rememberMe")},null,8,["modelValue","label"])])),_:1})])),_:1}),(0,t.bF)(f,null,{default:(0,t.k6)((()=>[(0,t.bF)(g,{loading:c.islogin,round:"",style:{width:"100%"},type:"primary",onClick:m.login},{default:(0,t.k6)((()=>[(0,t.eW)((0,i.v_)(e.$t("login.signIn")),1)])),_:1},8,["loading","onClick"])])),_:1}),(0,t.Lk)("div",n,[(0,t.eW)((0,i.v_)(e.$t("login.noAccount"))+" ",1),(0,t.bF)(F,{to:"/user_register"},{default:(0,t.k6)((()=>[(0,t.eW)((0,i.v_)(e.$t("login.createAccount")),1)])),_:1})])])),_:1},8,["model","rules"])}var c={data(){return{userType:"admin",form:{userCode:"",userPWD:"",verifyCode:""},rules:{userCode:[{required:!0,message:this.$t("login.userError"),trigger:"blur"}],userPWD:[{required:!0,message:this.$t("login.PWError"),trigger:"blur"}]},islogin:!1,verifyCode:""}},watch:{},async mounted(){await this.$API.auth.signOut.post(),this.form.rememberMe=this.$TOOL.data.get("REMEMBER_ME"),this.form.userCode=this.$TOOL.data.get("USERCODE"),this.form.userPWD=this.$TOOL.data.get("USERPWD"),this.changeverifyCode(),this.form.rememberMe&&setTimeout((async()=>{await this.login()}),1e4)},methods:{async login(){let e=await this.$refs.loginForm.validate().catch((()=>{}));if(!e)return!1;this.islogin=!0;let l={userCode:this.form.userCode,userPWD:this.$TOOL.crypto.MD5(this.form.userPWD),verifyCode:this.form.verifyCode,cookie:"token="+this.$TOOL.cookie.get("token"),rememberMe:!!this.form.rememberMe,ver:this.$CONFIG.APP_VER},a=await this.$API.auth.token.post(l);if(0!=a.code)return this.islogin=!1,this.$alert(a.message,"提示",{type:"error"}),this.changeverifyCode(),!1;this.$TOOL.cookie.set("token",this.$TOOL.cookie.get("token"),{expires:31536e3}),this.$TOOL.data.set("REMEMBER_ME",this.form.rememberMe),this.$TOOL.data.set("USERCODE",this.form.userCode),this.form.rememberMe?this.$TOOL.data.set("USERPWD",this.form.userPWD):this.$TOOL.data.set("USERPWD",""),this.$TOOL.data.set("USER_INFO",a.data.userInfo),this.$TOOL.data.set("MENU",a.data.menuInfo.menu),this.$TOOL.data.set("PERMISSIONS",a.data.menuInfo.permissions),this.$TOOL.data.set("DASHBOARDGRID",a.data.menuInfo.dashboardGrid),this.$TOOL.data.set("GRID",a.data.menuInfo.grid),this.$router.replace({path:"/"}),this.$message.success("Login Success 登录成功"),this.islogin=!1},async changeverifyCode(){let e=await this.$API.auth.verifyCode.get();0==e.code&&(this.verifyCode=e.data)}}},m=a(6262);const p=(0,m.A)(c,[["render",u]]);var f=p},6028:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return p}});var t=a(641),o=a(9322),i=a(2644);const s={class:"login-msg-yzm"},r={key:0},d={class:"login-reg"};function n(e,l,a,n,u,c){const m=(0,t.g2)("el-input"),p=(0,t.g2)("el-form-item"),f=(0,t.g2)("el-button"),h=(0,t.g2)("router-link"),b=(0,t.g2)("el-form");return(0,t.uX)(),(0,t.Wv)(b,{ref:"loginForm",model:u.form,rules:u.rules,"label-width":"0",size:"large",onKeyup:(0,o.jR)(c.login,["enter"])},{default:(0,t.k6)((()=>[(0,t.bF)(p,{prop:"phone"},{default:(0,t.k6)((()=>[(0,t.bF)(m,{modelValue:u.form.phone,"onUpdate:modelValue":l[0]||(l[0]=e=>u.form.phone=e),"prefix-icon":"el-icon-iphone",clearable:"",placeholder:e.$t("login.mobilePlaceholder")},{prepend:(0,t.k6)((()=>[(0,t.eW)("+86")])),_:1},8,["modelValue","placeholder"])])),_:1}),(0,t.bF)(p,{prop:"yzm",style:{"margin-bottom":"35px"}},{default:(0,t.k6)((()=>[(0,t.Lk)("div",s,[(0,t.bF)(m,{modelValue:u.form.yzm,"onUpdate:modelValue":l[1]||(l[1]=e=>u.form.yzm=e),"prefix-icon":"el-icon-unlock",clearable:"",placeholder:e.$t("login.smsPlaceholder")},null,8,["modelValue","placeholder"]),(0,t.bF)(f,{onClick:c.getYzm,disabled:u.disabled},{default:(0,t.k6)((()=>[(0,t.eW)((0,i.v_)(this.$t("login.smsGet")),1),u.disabled?((0,t.uX)(),(0,t.CE)("span",r," ("+(0,i.v_)(u.time)+")",1)):(0,t.Q3)("",!0)])),_:1},8,["onClick","disabled"])])])),_:1}),(0,t.bF)(p,null,{default:(0,t.k6)((()=>[(0,t.bF)(f,{type:"primary",style:{width:"100%"},loading:u.islogin,round:"",onClick:c.login},{default:(0,t.k6)((()=>[(0,t.eW)((0,i.v_)(e.$t("login.signIn")),1)])),_:1},8,["loading","onClick"])])),_:1}),(0,t.Lk)("div",d,[(0,t.eW)((0,i.v_)(e.$t("login.noAccount"))+" ",1),(0,t.bF)(h,{to:"/user_register"},{default:(0,t.k6)((()=>[(0,t.eW)((0,i.v_)(e.$t("login.createAccount")),1)])),_:1})])])),_:1},8,["model","rules","onKeyup"])}var u={data(){return{form:{phone:"",yzm:""},rules:{phone:[{required:!0,message:this.$t("login.mobileError")}],yzm:[{required:!0,message:this.$t("login.smsError")}]},disabled:!1,time:0,islogin:!1}},mounted(){},methods:{async getYzm(){var e=await this.$refs.loginForm.validateField("phone").catch((()=>{}));if(!e)return!1;this.$message.success(this.$t("login.smsSent")),this.disabled=!0,this.time=60;var l=setInterval((()=>{this.time-=1,this.time<1&&(clearInterval(l),this.disabled=!1,this.time=0)}),1e3)},async login(){var e=await this.$refs.loginForm.validate().catch((()=>{}));if(!e)return!1}}},c=a(6262);const m=(0,c.A)(u,[["render",n]]);var p=m},6284:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return d}});var t=a(641);function o(e,l,a,o,i,s){const r=(0,t.g2)("el-input"),d=(0,t.g2)("el-form-item"),n=(0,t.g2)("el-switch"),u=(0,t.g2)("el-form"),c=(0,t.g2)("el-scrollbar"),m=(0,t.g2)("el-button"),p=(0,t.g2)("el-dialog"),f=(0,t.gN)("auth");return(0,t.uX)(),(0,t.Wv)(p,{title:i.titleMap[i.mode],modelValue:i.visible,"onUpdate:modelValue":l[6]||(l[6]=e=>i.visible=e),width:400,"destroy-on-close":"",onClosed:l[7]||(l[7]=l=>e.$emit("closed")),"close-on-click-modal":!1},{footer:(0,t.k6)((()=>[(0,t.bF)(m,{onClick:l[4]||(l[4]=e=>i.visible=!1)},{default:(0,t.k6)((()=>[(0,t.eW)("取 消")])),_:1}),(0,t.bF)(m,{type:"primary",loading:i.isSaveing,onClick:l[5]||(l[5]=e=>s.submit())},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["loading"])])),default:(0,t.k6)((()=>[(0,t.bF)(c,{"max-height":"550px"},{default:(0,t.k6)((()=>[(0,t.bF)(u,{model:i.form,rules:i.rules,ref:"dialogForm","label-width":"80px"},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"名称",prop:"FName"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FName,"onUpdate:modelValue":l[0]||(l[0]=e=>i.form.FName=e),clearable:"",placeholder:"名称"},null,8,["modelValue"])])),_:1}),(0,t.bF)(d,{label:"排序",prop:"FSort"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FSort,"onUpdate:modelValue":l[1]||(l[1]=e=>i.form.FSort=e),clearable:"",placeholder:"排序"},null,8,["modelValue"])])),_:1}),(0,t.bo)(((0,t.uX)(),(0,t.Wv)(d,{label:"系统字典",prop:"FSys"},{default:(0,t.k6)((()=>[(0,t.bF)(n,{modelValue:i.form.FSys,"onUpdate:modelValue":l[2]||(l[2]=e=>i.form.FSys=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1})),[[f,"sysDictionary.showSys"]]),(0,t.bF)(d,{label:"启用",prop:"FEnable"},{default:(0,t.k6)((()=>[(0,t.bF)(n,{modelValue:i.form.FEnable,"onUpdate:modelValue":l[3]||(l[3]=e=>i.form.FEnable=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1})])),_:1},8,["title","modelValue"])}var i={emits:["success","closed"],data(){return{mode:"add",titleMap:{add:"新增字典组",edit:"编辑字典组"},visible:!1,isSaveing:!1,form:{Fid:0,FName:"",FSort:0,FSys:0,FEnable:1},rules:{FName:[{required:!0,message:"请输入名称"}]}}},mounted(){},methods:{open(e="add"){return this.mode=e,this.visible=!0,this},submit(){this.$refs.dialogForm.validate((async e=>{if(e){this.isSaveing=!0;var l=await this.$API.sysDictionary.dictGroupSave.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.visible=!1,this.$message.success("操作成功")):this.$alert(l.message,"提示",{type:"error"})}}))},setData(e){Object.assign(this.form,e)}}},s=a(6262);const r=(0,s.A)(i,[["render",o]]);var d=r},5535:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return d}});var t=a(641);function o(e,l,a,o,i,s){const r=(0,t.g2)("el-input"),d=(0,t.g2)("el-form-item"),n=(0,t.g2)("el-switch"),u=(0,t.g2)("el-form"),c=(0,t.g2)("el-button"),m=(0,t.g2)("el-dialog"),p=(0,t.gN)("auth");return(0,t.uX)(),(0,t.Wv)(m,{title:i.titleMap[i.mode],modelValue:i.visible,"onUpdate:modelValue":l[8]||(l[8]=e=>i.visible=e),width:400,"destroy-on-close":"",onClosed:l[9]||(l[9]=l=>e.$emit("closed")),"close-on-click-modal":!1},{footer:(0,t.k6)((()=>[(0,t.bF)(c,{onClick:l[6]||(l[6]=e=>i.visible=!1)},{default:(0,t.k6)((()=>[(0,t.eW)("取 消")])),_:1}),(0,t.bF)(c,{type:"primary",loading:i.isSaveing,onClick:l[7]||(l[7]=e=>s.submit())},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["loading"])])),default:(0,t.k6)((()=>[(0,t.bF)(u,{model:i.form,rules:i.rules,ref:"dialogForm","label-width":"80px"},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"名称",prop:"FName"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FName,"onUpdate:modelValue":l[0]||(l[0]=e=>i.form.FName=e),clearable:""},null,8,["modelValue"])])),_:1}),(0,t.bF)(d,{label:"助记码",prop:"FZjm"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FZJM,"onUpdate:modelValue":l[1]||(l[1]=e=>i.form.FZJM=e),placeholder:"如不填写，则自动生成。",clearable:""},null,8,["modelValue"])])),_:1}),(0,t.bF)(d,{label:"键值",prop:"FKey"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FKey,"onUpdate:modelValue":l[2]||(l[2]=e=>i.form.FKey=e),clearable:"",placeholder:"如不填写，则和名称一致。"},null,8,["modelValue"])])),_:1}),(0,t.bF)(d,{label:"排序",prop:"FSort"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FSort,"onUpdate:modelValue":l[3]||(l[3]=e=>i.form.FSort=e),clearable:""},null,8,["modelValue"])])),_:1}),(0,t.bo)(((0,t.uX)(),(0,t.Wv)(d,{label:"系统字典",prop:"FSys"},{default:(0,t.k6)((()=>[(0,t.bF)(n,{modelValue:i.form.FSys,"onUpdate:modelValue":l[4]||(l[4]=e=>i.form.FSys=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1})),[[p,"sysDictionary.showSys"]]),(0,t.bF)(d,{label:"启用",prop:"FEnable"},{default:(0,t.k6)((()=>[(0,t.bF)(n,{modelValue:i.form.FEnable,"onUpdate:modelValue":l[5]||(l[5]=e=>i.form.FEnable=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["title","modelValue"])}var i={emits:["success","closed"],data(){return{mode:"add",titleMap:{add:"新增项",edit:"编辑项"},visible:!1,isSaveing:!1,form:{Fid:0,FGroupId:0,FName:"",FKey:"",FZjm:"",FSort:0,FEnable:1,FSys:0},rules:{FName:[{required:!0,message:"请输入名称"}]},dic:[]}},mounted(){},methods:{open(e="add"){return this.mode=e,this.visible=!0,this},submit(){this.$refs.dialogForm.validate((async e=>{if(e){var l=await this.$API.sysDictionary.dictionarySave.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.visible=!1,this.$message.success("操作成功")):this.$alert(l.message,"提示",{type:"error"})}}))},setData(e){Object.assign(this.form,e)}}},s=a(6262);const r=(0,s.A)(i,[["render",o]]);var d=r},7138:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return _}});var t=a(641),o=a(2644);const i=e=>((0,t.Qi)("data-v-b7e98664"),e=e(),(0,t.jt)(),e),s=i((()=>(0,t.Lk)("div",{class:"el-form-item-msg"},null,-1))),r=i((()=>(0,t.Lk)("div",{class:"el-form-item-msg"},"菜单不显示在导航中，但用户依然可以访问，例如详情页",-1))),d=i((()=>(0,t.Lk)("h2",null,"接口权限",-1)));function n(e,l,a,i,n,u){const c=(0,t.g2)("el-empty"),m=(0,t.g2)("el-col"),p=(0,t.g2)("el-cascader"),f=(0,t.g2)("el-form-item"),h=(0,t.g2)("el-switch"),b=(0,t.g2)("el-input"),g=(0,t.g2)("el-radio-button"),F=(0,t.g2)("el-radio-group"),k=(0,t.g2)("sc-icon-select"),v=(0,t.g2)("el-color-picker"),y=(0,t.g2)("el-checkbox"),_=(0,t.g2)("el-button"),V=(0,t.g2)("el-row"),w=(0,t.g2)("el-form"),C=(0,t.g2)("el-table-column"),S=(0,t.g2)("sc-form-table");return(0,t.uX)(),(0,t.Wv)(V,{gutter:40},{default:(0,t.k6)((()=>[null==n.form.id?((0,t.uX)(),(0,t.Wv)(m,{key:0},{default:(0,t.k6)((()=>[(0,t.bF)(c,{description:"请选择左侧菜单后操作","image-size":100})])),_:1})):((0,t.uX)(),(0,t.CE)(t.FK,{key:1},[(0,t.bF)(m,{lg:12},{default:(0,t.k6)((()=>[(0,t.Lk)("h2",null,(0,o.v_)(n.form.meta.title||"新增菜单"),1),(0,t.bF)(w,{model:n.form,rules:n.rules,ref:"dialogForm","label-width":"100px","label-position":"right"},{default:(0,t.k6)((()=>[(0,t.bF)(V,null,{default:(0,t.k6)((()=>[(0,t.bF)(m,{lg:12},{default:(0,t.k6)((()=>[(0,t.bF)(f,{label:"上级菜单",prop:"parentId"},{default:(0,t.k6)((()=>[(0,t.bF)(p,{modelValue:n.form.parentId,"onUpdate:modelValue":l[0]||(l[0]=e=>n.form.parentId=e),options:n.menuOptions,props:n.menuProps,style:{width:"100%"},"show-all-levels":!1,placeholder:"顶级菜单",clearable:"",disabled:""},null,8,["modelValue","options","props"])])),_:1})])),_:1}),(0,t.bF)(m,{lg:6,justify:"end"},{default:(0,t.k6)((()=>[(0,t.bF)(f,{label:"是否启用",prop:"meta.title"},{default:(0,t.k6)((()=>[(0,t.bF)(h,{modelValue:n.form.enable,"onUpdate:modelValue":l[1]||(l[1]=e=>n.form.enable=e),onChange:u.UpdateEnable,loading:n.form.enable_switch,"active-value":"1","inactive-value":"0"},null,8,["modelValue","onChange","loading"])])),_:1})])),_:1}),(0,t.bF)(m,{lg:12,style:{width:"100%"}},{default:(0,t.k6)((()=>[(0,t.bF)(f,{label:"显示名称",prop:"meta.title"},{default:(0,t.k6)((()=>[(0,t.bF)(b,{modelValue:n.form.meta.title,"onUpdate:modelValue":l[2]||(l[2]=e=>n.form.meta.title=e),clearable:"",placeholder:"菜单显示名字"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(m,{lg:12,style:{width:"100%"}},{default:(0,t.k6)((()=>[(0,t.bF)(f,{label:"权限ID",prop:"id"},{default:(0,t.k6)((()=>[(0,t.bF)(b,{modelValue:n.form.id,"onUpdate:modelValue":l[3]||(l[3]=e=>n.form.id=e),clearable:"",disabled:0!=n.form.operateType},null,8,["modelValue","disabled"])])),_:1})])),_:1}),(0,t.bF)(m,{lg:24,style:{width:"100%"}},{default:(0,t.k6)((()=>[(0,t.bF)(f,{label:"组件别名",prop:"name",icon:"el-icon-eleme-filled"},{default:(0,t.k6)((()=>[(0,t.bF)(b,{modelValue:n.form.name,"onUpdate:modelValue":l[4]||(l[4]=e=>n.form.name=e),clearable:"",placeholder:"系统唯一且与内置组件名一致，否则缓存失效。Iframe显示源地址。"},null,8,["modelValue"]),s])),_:1})])),_:1}),(0,t.bF)(m,{lg:24,style:{width:"100%"}},{default:(0,t.k6)((()=>[(0,t.bF)(f,{label:"路由地址",prop:"path"},{default:(0,t.k6)((()=>[(0,t.bF)(b,{modelValue:n.form.path,"onUpdate:modelValue":l[5]||(l[5]=e=>n.form.path=e),clearable:"",placeholder:"",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(m,{md:24,style:{width:"100%"}},{default:(0,t.k6)((()=>[(0,t.bF)(f,{label:"视图地址",prop:"component"},{default:(0,t.k6)((()=>[(0,t.bF)(b,{modelValue:n.form.component,"onUpdate:modelValue":l[6]||(l[6]=e=>n.form.component=e),clearable:"",placeholder:"如父节点、链接或Iframe等没有视图的菜单不需要填写"},{prepend:(0,t.k6)((()=>[(0,t.eW)("views/")])),_:1},8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(m,{lg:24,style:{width:"100%"}},{default:(0,t.k6)((()=>[(0,t.bF)(f,{label:"菜单类型",prop:"meta.type"},{default:(0,t.k6)((()=>[(0,t.bF)(F,{modelValue:n.form.meta.type,"onUpdate:modelValue":l[7]||(l[7]=e=>n.form.meta.type=e)},{default:(0,t.k6)((()=>[(0,t.bF)(g,{label:"menu"},{default:(0,t.k6)((()=>[(0,t.eW)("菜单")])),_:1}),(0,t.bF)(g,{label:"iframe"},{default:(0,t.k6)((()=>[(0,t.eW)("Iframe")])),_:1}),(0,t.bF)(g,{label:"link"},{default:(0,t.k6)((()=>[(0,t.eW)("外链")])),_:1}),(0,t.bF)(g,{label:"button"},{default:(0,t.k6)((()=>[(0,t.eW)("按钮")])),_:1}),(0,t.bF)(g,{label:"sys"},{default:(0,t.k6)((()=>[(0,t.eW)("系统")])),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(m,{lg:12,style:{width:"100%"}},{default:(0,t.k6)((()=>[(0,t.bF)(f,{label:"菜单图标",prop:"meta.icon"},{default:(0,t.k6)((()=>[(0,t.bF)(k,{modelValue:n.form.meta.icon,"onUpdate:modelValue":l[8]||(l[8]=e=>n.form.meta.icon=e),AopState:n.form.app_enable,clearable:""},null,8,["modelValue","AopState"])])),_:1})])),_:1}),(0,t.bF)(m,{lg:12,style:{width:"100%"}},{default:(0,t.k6)((()=>[(0,t.bF)(f,{label:"菜单颜色",prop:"color"},{default:(0,t.k6)((()=>[(0,t.bF)(v,{modelValue:n.form.meta.color,"onUpdate:modelValue":l[9]||(l[9]=e=>n.form.meta.color=e),predefine:n.predefineColors},null,8,["modelValue","predefine"])])),_:1})])),_:1}),(0,t.bF)(m,{lg:12,style:{width:"100%"}},{default:(0,t.k6)((()=>[(0,t.bF)(f,{label:"重新定向",prop:"redirect"},{default:(0,t.k6)((()=>[(0,t.bF)(b,{modelValue:n.form.redirect,"onUpdate:modelValue":l[10]||(l[10]=e=>n.form.redirect=e),clearable:"",placeholder:"重定向地址"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(m,{lg:12,style:{width:"100%"}},{default:(0,t.k6)((()=>[(0,t.bF)(f,{label:"标签名称",prop:"meta.tag"},{default:(0,t.k6)((()=>[(0,t.bF)(b,{modelValue:n.form.meta.tag,"onUpdate:modelValue":l[11]||(l[11]=e=>n.form.meta.tag=e),clearable:"",placeholder:"菜单显示后面的标签"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(m,{lg:24,style:{width:"100%"}},{default:(0,t.k6)((()=>[(0,t.bF)(f,{label:"菜单高亮",prop:"active"},{default:(0,t.k6)((()=>[(0,t.bF)(b,{modelValue:n.form.active,"onUpdate:modelValue":l[12]||(l[12]=e=>n.form.active=e),clearable:"",placeholder:"子节点或详情页需要高亮的上级菜单路由地址"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(m,{lg:24},{default:(0,t.k6)((()=>[(0,t.bF)(f,{label:"整页路由",prop:"fullpage"},{default:(0,t.k6)((()=>[(0,t.bF)(h,{modelValue:n.form.meta.fullpage,"onUpdate:modelValue":l[13]||(l[13]=e=>n.form.meta.fullpage=e)},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(m,{lg:24,style:{width:"100%"}},{default:(0,t.k6)((()=>[(0,t.bF)(f,{label:"是否隐藏",prop:"meta.hidden"},{default:(0,t.k6)((()=>[(0,t.bF)(y,{modelValue:n.form.meta.hidden,"onUpdate:modelValue":l[14]||(l[14]=e=>n.form.meta.hidden=e)},{default:(0,t.k6)((()=>[(0,t.eW)("隐藏菜单")])),_:1},8,["modelValue"]),(0,t.bF)(y,{modelValue:n.form.meta.hiddenBreadcrumb,"onUpdate:modelValue":l[15]||(l[15]=e=>n.form.meta.hiddenBreadcrumb=e)},{default:(0,t.k6)((()=>[(0,t.eW)("隐藏面包屑")])),_:1},8,["modelValue"]),r])),_:1})])),_:1}),(0,t.bF)(m,{lg:24},{default:(0,t.k6)((()=>[(0,t.bF)(f,null,{default:(0,t.k6)((()=>[(0,t.bF)(_,{type:"primary",onClick:u.save,loading:n.loading},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["onClick","loading"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1}),(0,t.bF)(m,{lg:12,class:"apilist"},{default:(0,t.k6)((()=>[d,(0,t.bF)(S,{modelValue:n.form.apiList,"onUpdate:modelValue":l[16]||(l[16]=e=>n.form.apiList=e),addTemplate:n.apiListAddTemplate,placeholder:"暂无匹配接口权限"},{default:(0,t.k6)((()=>[(0,t.bF)(C,{prop:"name",label:"名称",width:"200",align:"center"},{default:(0,t.k6)((e=>[(0,t.bF)(b,{modelValue:e.row.name,"onUpdate:modelValue":l=>e.row.name=l,placeholder:"请输入内容"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),(0,t.bF)(C,{prop:"url",label:"Api url",align:"center"},{default:(0,t.k6)((e=>[(0,t.bF)(b,{modelValue:e.row.url,"onUpdate:modelValue":l=>e.row.url=l,placeholder:"请输入内容"},null,8,["modelValue","onUpdate:modelValue"])])),_:1})])),_:1},8,["modelValue","addTemplate"])])),_:1})],64))])),_:1})}a(8743);const u={class:"sc-icon-select"},c={class:"sc-icon-select__dialog",style:{margin:"-20px 0 -10px 0"}},m={class:"sc-icon-select__list"},p=["data-icon"];function f(e,l,a,i,s,r){const d=(0,t.g2)("el-input"),n=(0,t.g2)("el-form-item"),f=(0,t.g2)("el-form"),h=(0,t.g2)("el-tag"),b=(0,t.g2)("el-empty"),g=(0,t.g2)("el-icon"),F=(0,t.g2)("el-scrollbar"),k=(0,t.g2)("el-tab-pane"),v=(0,t.g2)("el-tabs"),y=(0,t.g2)("el-button"),_=(0,t.g2)("el-dialog");return(0,t.uX)(),(0,t.CE)("div",u,[(0,t.Lk)("div",{class:(0,o.C4)(["sc-icon-select__wrapper",{hasValue:s.value}]),onClick:l[1]||(l[1]=(...e)=>r.open&&r.open(...e))},[(0,t.bF)(d,{"prefix-icon":s.value||"el-icon-plus",modelValue:s.value,"onUpdate:modelValue":l[0]||(l[0]=e=>s.value=e),disabled:a.disabled,readonly:""},null,8,["prefix-icon","modelValue","disabled"])],2),(0,t.bF)(_,{title:"图标选择器",modelValue:s.dialogVisible,"onUpdate:modelValue":l[5]||(l[5]=e=>s.dialogVisible=e),width:760,"destroy-on-close":"","append-to-body":""},{footer:(0,t.k6)((()=>[(0,t.bF)(y,{onClick:r.clear,text:""},{default:(0,t.k6)((()=>[(0,t.eW)("清除")])),_:1},8,["onClick"]),(0,t.bF)(y,{onClick:l[4]||(l[4]=e=>s.dialogVisible=!1)},{default:(0,t.k6)((()=>[(0,t.eW)("取消")])),_:1})])),default:(0,t.k6)((()=>[(0,t.Lk)("div",c,[(0,t.bF)(f,{rules:{}},{default:(0,t.k6)((()=>[(0,t.bF)(n,{prop:"searchText"},{default:(0,t.k6)((()=>[(0,t.bF)(d,{class:"sc-icon-select__search-input","prefix-icon":"el-icon-search",modelValue:s.searchText,"onUpdate:modelValue":l[2]||(l[2]=e=>s.searchText=e),placeholder:"搜索",size:"large",clearable:""},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(v,null,{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(s.data,(e=>((0,t.uX)(),(0,t.Wv)(k,{key:e.name,lazy:""},{label:(0,t.k6)((()=>[(0,t.eW)((0,o.v_)(e.name)+" ",1),(0,t.bF)(h,{size:"small",type:"info"},{default:(0,t.k6)((()=>[(0,t.eW)((0,o.v_)(e.icons.length),1)])),_:2},1024)])),default:(0,t.k6)((()=>[(0,t.Lk)("div",m,[(0,t.bF)(F,null,{default:(0,t.k6)((()=>[(0,t.Lk)("ul",{onClick:l[3]||(l[3]=(...e)=>r.selectIcon&&r.selectIcon(...e))},[0==e.icons.length?((0,t.uX)(),(0,t.Wv)(b,{key:0,"image-size":100,description:"未查询到相关图标"})):(0,t.Q3)("",!0),((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(e.icons,(e=>((0,t.uX)(),(0,t.CE)("li",{key:e},[(0,t.Lk)("span",{"data-icon":e},null,8,p),(0,t.bF)(g,null,{default:(0,t.k6)((()=>[((0,t.uX)(),(0,t.Wv)((0,t.$y)(e)))])),_:2},1024)])))),128))])])),_:2},1024)])])),_:2},1024)))),128))])),_:1})])])),_:1},8,["modelValue"])])}var h={icons:[{name:"默认",icons:["el-icon-add-location","el-icon-aim","el-icon-alarm-clock","el-icon-apple","el-icon-arrow-down","el-icon-arrow-down-bold","el-icon-arrow-left","el-icon-arrow-left-bold","el-icon-arrow-right","el-icon-arrow-right-bold","el-icon-arrow-up","el-icon-arrow-up-bold","el-icon-avatar","el-icon-back","el-icon-baseball","el-icon-basketball","el-icon-bell","el-icon-bell-filled","el-icon-bicycle","el-icon-bottom","el-icon-bottom-left","el-icon-bottom-right","el-icon-bowl","el-icon-box","el-icon-briefcase","el-icon-brush","el-icon-brush-filled","el-icon-burger","el-icon-calendar","el-icon-camera","el-icon-camera-filled","el-icon-caret-bottom","el-icon-caret-left","el-icon-caret-right","el-icon-caret-top","el-icon-cellphone","el-icon-chat-dot-round","el-icon-chat-dot-square","el-icon-chat-line-round","el-icon-chat-line-square","el-icon-chat-round","el-icon-chat-square","el-icon-check","el-icon-checked","el-icon-cherry","el-icon-chicken","el-icon-circle-check","el-icon-circle-check-filled","el-icon-circle-close","el-icon-circle-close-filled","el-icon-circle-plus","el-icon-circle-plus-filled","el-icon-clock","el-icon-close","el-icon-close-bold","el-icon-cloudy","el-icon-coffee","el-icon-coffee-cup","el-icon-coin","el-icon-cold-drink","el-icon-collection","el-icon-collection-tag","el-icon-comment","el-icon-compass","el-icon-connection","el-icon-coordinate","el-icon-copy-document","el-icon-cpu","el-icon-credit-card","el-icon-crop","el-icon-d-arrow-left","el-icon-d-arrow-right","el-icon-d-caret","el-icon-data-analysis","el-icon-data-board","el-icon-data-line","el-icon-delete","el-icon-delete-filled","el-icon-delete-location","el-icon-dessert","el-icon-discount","el-icon-dish","el-icon-dish-dot","el-icon-document","el-icon-document-add","el-icon-document-checked","el-icon-document-copy","el-icon-document-delete","el-icon-document-remove","el-icon-download","el-icon-drizzling","el-icon-edit","el-icon-edit-pen","el-icon-eleme","el-icon-eleme-filled","el-icon-element-plus","el-icon-expand","el-icon-failed","el-icon-female","el-icon-files","el-icon-film","el-icon-filter","el-icon-finished","el-icon-first-aid-kit","el-icon-flag","el-icon-fold","el-icon-folder","el-icon-folder-add","el-icon-folder-checked","el-icon-folder-delete","el-icon-folder-opened","el-icon-folder-remove","el-icon-food","el-icon-football","el-icon-fork-spoon","el-icon-fries","el-icon-full-screen","el-icon-goblet","el-icon-goblet-full","el-icon-goblet-square","el-icon-goblet-square-full","el-icon-goods","el-icon-goods-filled","el-icon-grape","el-icon-grid","el-icon-guide","el-icon-headset","el-icon-help","el-icon-help-filled","el-icon-hide","el-icon-histogram","el-icon-home-filled","el-icon-hot-water","el-icon-house","el-icon-ice-cream","el-icon-ice-cream-round","el-icon-ice-cream-square","el-icon-ice-drink","el-icon-ice-tea","el-icon-info-filled","el-icon-iphone","el-icon-key","el-icon-knife-fork","el-icon-lightning","el-icon-link","el-icon-list","el-icon-loading","el-icon-location","el-icon-location-filled","el-icon-location-information","el-icon-lock","el-icon-lollipop","el-icon-magic-stick","el-icon-magnet","el-icon-male","el-icon-management","el-icon-map-location","el-icon-medal","el-icon-menu","el-icon-message","el-icon-message-box","el-icon-mic","el-icon-microphone","el-icon-milk-tea","el-icon-minus","el-icon-money","el-icon-monitor","el-icon-moon","el-icon-moon-night","el-icon-more","el-icon-more-filled","el-icon-mostly-cloudy","el-icon-mouse","el-icon-mug","el-icon-mute","el-icon-mute-notification","el-icon-no-smoking","el-icon-notebook","el-icon-notification","el-icon-odometer","el-icon-office-building","el-icon-open","el-icon-operation","el-icon-opportunity","el-icon-orange","el-icon-paperclip","el-icon-partly-cloudy","el-icon-pear","el-icon-phone","el-icon-phone-filled","el-icon-picture","el-icon-picture-filled","el-icon-picture-rounded","el-icon-pie-chart","el-icon-place","el-icon-platform","el-icon-plus","el-icon-pointer","el-icon-position","el-icon-postcard","el-icon-pouring","el-icon-present","el-icon-price-tag","el-icon-printer","el-icon-promotion","el-icon-question-filled","el-icon-rank","el-icon-reading","el-icon-reading-lamp","el-icon-refresh","el-icon-refresh-left","el-icon-refresh-right","el-icon-refrigerator","el-icon-remove","el-icon-remove-filled","el-icon-right","el-icon-scale-to-original","el-icon-school","el-icon-scissor","el-icon-search","el-icon-select","el-icon-sell","el-icon-semi-select","el-icon-service","el-icon-set-up","el-icon-setting","el-icon-share","el-icon-ship","el-icon-shop","el-icon-shopping-bag","el-icon-shopping-cart","el-icon-shopping-cart-full","el-icon-smoking","el-icon-soccer","el-icon-sold-out","el-icon-sort","el-icon-sort-down","el-icon-sort-up","el-icon-stamp","el-icon-star","el-icon-star-filled","el-icon-stopwatch","el-icon-success-filled","el-icon-sugar","el-icon-suitcase","el-icon-sunny","el-icon-sunrise","el-icon-sunset","el-icon-switch","el-icon-switch-button","el-icon-takeaway-box","el-icon-ticket","el-icon-tickets","el-icon-timer","el-icon-toilet-paper","el-icon-tools","el-icon-top","el-icon-top-left","el-icon-top-right","el-icon-trend-charts","el-icon-trophy","el-icon-turn-off","el-icon-umbrella","el-icon-unlock","el-icon-upload","el-icon-upload-filled","el-icon-user","el-icon-user-filled","el-icon-van","el-icon-video-camera","el-icon-video-camera-filled","el-icon-video-pause","el-icon-video-play","el-icon-view","el-icon-wallet","el-icon-wallet-filled","el-icon-warning","el-icon-warning-filled","el-icon-watch","el-icon-watermelon","el-icon-wind-power","el-icon-zoom-in","el-icon-zoom-out"]},{name:"扩展",icons:["sc-icon-vue","sc-icon-code","sc-icon-wechat","sc-icon-bug-fill","sc-icon-bug-line","sc-icon-file-word","sc-icon-file-excel","sc-icon-file-ppt","sc-icon-organization","sc-icon-upload","sc-icon-download","sc-icon-bili","sc-icon-dou-yu","sc-icon-dou-yin","sc-icon-kuai-shou"]}]},b={props:{modelValue:{type:String,default:""},disabled:{type:Boolean,default:!1}},data(){return{value:"",dialogVisible:!1,data:[],searchText:""}},watch:{modelValue(e){this.value=e},value(e){this.$emit("update:modelValue",e)},searchText(e){this.search(e)}},mounted(){this.value=this.modelValue,this.data.push(...h.icons)},methods:{open(){if(this.disabled)return!1;this.dialogVisible=!0},selectIcon(e){if("SPAN"!=e.target.tagName)return!1;this.value=e.target.dataset.icon,this.dialogVisible=!1},clear(){this.value="",this.dialogVisible=!1},search(e){if(e){const l=JSON.parse(JSON.stringify(h.icons));l.forEach((l=>{l.icons=l.icons.filter((l=>l.includes(e)))})),this.data=l}else this.data=JSON.parse(JSON.stringify(h.icons))}}},g=a(6262);const F=(0,g.A)(b,[["render",f],["__scopeId","data-v-608ca6b8"]]);var k=F,v={components:{scIconSelect:k},props:{menu:{type:Object,default:()=>{}}},data(){return{form:{id:"",parentId:"0",name:"",path:"",component:"",redirect:"",meta:{title:"",icon:"",active:"",color:"",type:"menu",fullpage:!1},enable:"1",enable_switch:!1,app_enable_switch:!1,apiList:[],operateType:0},menuOptions:[],menuProps:{value:"id",label:"title",checkStrictly:!0},predefineColors:["#ff4500","#ff8c00","#ffd700","#67C23A","#00ced1","#409EFF","#c71585"],rules:[],apiListAddTemplate:{name:"",url:""},loading:!1}},watch:{menu:{handler(){this.menuOptions=this.treeToMap(this.menu)},deep:!0}},methods:{treeToMap(e){const l=[];return e.forEach((e=>{var a={id:e.id,parentId:e.parentId,title:e.meta.title,children:e.children&&e.children.length>0?this.treeToMap(e.children):null};l.push(a)})),l},async UpdateEnable(){0!=this.form.operateType&&(this.form.enable_switch=!0,setTimeout((async()=>{let e={jObjectParam:{id:this.form.id,enable:this.form.enable}};var l=await this.$API.sysMenu.enableMenu.post(e);0==l.code?(this.form.enable_switch=!1,this.$message.success("操作成功")):this.$alert(l.message,"提示",{type:"error"})}),500))},async save(){this.loading=!0;var e=await this.$API.sysMenu.saveMenu.post({jObjectParam:this.form});this.loading=!1,0==e.code?this.$message.success("保存成功"):this.$alert(e.message,"提示",{type:"error"}),this.form.operateType=""},setData(e,l,a=0){this.form=e,this.form.apiList=e.apiList||[],this.form.parentId=l,this.form.operateType=a}}};const y=(0,g.A)(v,[["render",n],["__scopeId","data-v-b7e98664"]]);var _=y},4855:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return c}});var t=a(641),o=a(2644);const i={class:"custom-tree-node"},s={style:{"margin-left":"10px",color:"#778899"}};function r(e,l,a,r,d,n){const u=(0,t.g2)("el-input"),c=(0,t.g2)("el-tree"),m=(0,t.g2)("el-scrollbar"),p=(0,t.g2)("el-button"),f=(0,t.g2)("el-dialog");return(0,t.uX)(),(0,t.Wv)(f,{title:"权限设置",modelValue:d.visible,"onUpdate:modelValue":l[3]||(l[3]=e=>d.visible=e),width:450,"destroy-on-close":"",onClosed:l[4]||(l[4]=l=>e.$emit("closed")),"close-on-click-modal":!1},{footer:(0,t.k6)((()=>[(0,t.bF)(p,{onClick:l[1]||(l[1]=e=>d.visible=!1)},{default:(0,t.k6)((()=>[(0,t.eW)("取 消")])),_:1}),(0,t.bF)(p,{type:"primary",loading:d.isSaveing,onClick:l[2]||(l[2]=e=>n.submit())},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["loading"])])),default:(0,t.k6)((()=>[(0,t.bF)(m,{"max-height":"550px"},{default:(0,t.k6)((()=>[(0,t.bF)(u,{modelValue:d.filterText,"onUpdate:modelValue":l[0]||(l[0]=e=>d.filterText=e),placeholder:"名称 / ID",style:{width:"95%"}},null,8,["modelValue"]),(0,t.bF)(c,{ref:"menu","node-key":"id","default-checked-keys":d.menu.checked,class:"filter-tree",data:d.menu.list,props:d.menu.props,"default-expand-all":"","filter-node-method":n.filterNode,"show-checkbox":""},{default:(0,t.k6)((({data:e})=>[(0,t.Lk)("span",i,[(0,t.Lk)("span",null,(0,o.v_)(e.label),1),(0,t.Lk)("span",s,(0,o.v_)(e.id),1)])])),_:1},8,["default-checked-keys","data","props","filter-node-method"])])),_:1})])),_:1},8,["modelValue"])}var d={emits:["success","closed"],data(){return{visible:!1,isSaveing:!1,menu:{list:[],checked:[],props:{}},filterText:"",FRoleId:0}},watch:{filterText(e){this.$refs.menu.filter(e)}},mounted(){},methods:{open(e){this.FRoleId=e,this.getMenu(),this.visible=!0},filterNode(e,l){return!e||(l.label.includes(e)||l.id.includes(e))},async submit(){this.isSaveing=!0;let e=this.$refs.menu.getCheckedKeys().concat(this.$refs.menu.getHalfCheckedKeys());var l={jObjectParam:{id:this.FRoleId,list:e}},a=await this.$API.sysRole.saveRoleMenu.post(l);0==a.code?(this.visible=!1,this.$message.success("操作成功")):this.$alert(a.message,"提示",{type:"error"}),this.isSaveing=!1},async getMenu(){var e=await this.$API.sysRole.getSysRoleMenuList.post({jObjectSearch:{id:this.FRoleId}});this.menu.list=e.data.List,this.menu.checked=e.data.checked}}},n=a(6262);const u=(0,n.A)(d,[["render",r]]);var c=u},3653:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return d}});var t=a(641);function o(e,l,a,o,i,s){const r=(0,t.g2)("el-input"),d=(0,t.g2)("el-form-item"),n=(0,t.g2)("el-switch"),u=(0,t.g2)("el-form"),c=(0,t.g2)("el-button"),m=(0,t.g2)("el-dialog");return(0,t.uX)(),(0,t.Wv)(m,{title:i.titleMap[i.mode],modelValue:i.visible,"onUpdate:modelValue":l[6]||(l[6]=e=>i.visible=e),width:400,"destroy-on-close":"",onClosed:l[7]||(l[7]=l=>e.$emit("closed")),"close-on-click-modal":!1},{footer:(0,t.k6)((()=>[(0,t.bF)(c,{onClick:l[4]||(l[4]=e=>i.visible=!1)},{default:(0,t.k6)((()=>[(0,t.eW)("取 消")])),_:1}),"show"!=i.mode?((0,t.uX)(),(0,t.Wv)(c,{key:0,type:"primary",loading:i.isSaveing,onClick:l[5]||(l[5]=e=>s.submit())},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["loading"])):(0,t.Q3)("",!0)])),default:(0,t.k6)((()=>[(0,t.bF)(u,{model:i.form,rules:i.rules,disabled:"show"==i.mode,ref:"dialogForm","label-width":"80px"},{default:(0,t.k6)((()=>[(0,t.bF)(d,{label:"角色",prop:"FName"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FName,"onUpdate:modelValue":l[0]||(l[0]=e=>i.form.FName=e),clearable:"",placeholder:"名称"},null,8,["modelValue"])])),_:1}),(0,t.bF)(d,{label:"备注",prop:"FDescribe"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FDescribe,"onUpdate:modelValue":l[1]||(l[1]=e=>i.form.FDescribe=e),clearable:"",placeholder:"备注"},null,8,["modelValue"])])),_:1}),(0,t.bF)(d,{label:"排序",prop:"FSort"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FSort,"onUpdate:modelValue":l[2]||(l[2]=e=>i.form.FSort=e),clearable:"",placeholder:"排序"},null,8,["modelValue"])])),_:1}),(0,t.bF)(d,{label:"是否启用",prop:"FEnable"},{default:(0,t.k6)((()=>[(0,t.bF)(n,{modelValue:i.form.FEnable,"onUpdate:modelValue":l[3]||(l[3]=e=>i.form.FEnable=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules","disabled"])])),_:1},8,["title","modelValue"])}var i={emits:["success","closed"],data(){return{mode:"add",titleMap:{add:"新增角色",edit:"编辑角色",show:"查看角色"},visible:!1,isSaveing:!1,form:{Fid:0,FName:"",FDescribe:"",FSort:0,FEnable:1},rules:{FName:[{required:!0,message:"请输入名称"}]}}},mounted(){},methods:{open(e="add"){return this.mode=e,this.visible=!0,this},submit(){this.$refs.dialogForm.validate((async e=>{if(e){this.isSaveing=!0;let e=this.form;e["FGroup"]=String(this.form.FGroup);var l=await this.$API.sysRole.saveSysRole.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.visible=!1,this.$message.success("操作成功")):this.$alert(l.message,"提示",{type:"error"})}}))},setData(e){Object.assign(this.form,e)}}},s=a(6262);const r=(0,s.A)(i,[["render",o]]);var d=r},9572:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return n}});var t=a(641);const o={class:"edit_dev"};function i(e,l,a,i,s,r){const d=(0,t.g2)("el-transfer"),n=(0,t.g2)("el-main"),u=(0,t.g2)("el-button"),c=(0,t.g2)("el-dialog");return(0,t.uX)(),(0,t.Wv)(c,{title:"人员分配",modelValue:s.visible,"onUpdate:modelValue":l[3]||(l[3]=e=>s.visible=e),width:650,"destroy-on-close":"",onClosed:l[4]||(l[4]=l=>e.$emit("closed")),"close-on-click-modal":!1},{footer:(0,t.k6)((()=>[(0,t.bF)(u,{onClick:l[1]||(l[1]=e=>s.visible=!1)},{default:(0,t.k6)((()=>[(0,t.eW)("取 消")])),_:1}),(0,t.bF)(u,{type:"primary",loading:s.isSaveing,onClick:l[2]||(l[2]=e=>r.submit())},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["loading"])])),default:(0,t.k6)((()=>[(0,t.bF)(n,{class:"nopadding",style:{"text-align":"center"}},{default:(0,t.k6)((()=>[(0,t.Lk)("div",o,[(0,t.bF)(d,{modelValue:s.accountdata.Value,"onUpdate:modelValue":l[0]||(l[0]=e=>s.accountdata.Value=e),style:{"text-align":"left",display:"inline-block",height:"100%"},filterable:"",titles:["全部列表","当前列表"],data:s.accountdata.List},null,8,["modelValue","data"])])])),_:1})])),_:1},8,["modelValue"])}var s={emits:["success","closed"],data(){return{visible:!1,isSaveing:!1,accountdata:{List:[],Value:[]},FRoleId:0}},mounted(){},methods:{open(e){this.FRoleId=e,this.getSysRoleUserList(),this.visible=!0},async submit(){this.isSaveing=!0;var e={jObjectParam:{list:this.accountdata.Value,id:this.FRoleId}},l=await this.$API.sysRole.saveSysRoleUser.post(e);0==l.code?(this.$message.success("操作成功"),this.visible=!1):this.$alert(l.message,"提示",{type:"error"}),this.isSaveing=!1},async getSysRoleUserList(){var e=await this.$API.sysRole.getSysRoleUserList.post({jObjectSearch:{id:this.FRoleId}});this.accountdata=e.data}}},r=a(6262);const d=(0,r.A)(s,[["render",i],["__scopeId","data-v-2c2d7449"]]);var n=d},856:function(e,l,a){"use strict";a.r(l),a.d(l,{default:function(){return d}});var t=a(641);function o(e,l,a,o,i,s){const r=(0,t.g2)("el-input"),d=(0,t.g2)("el-form-item"),n=(0,t.g2)("el-col"),u=(0,t.g2)("sc-select"),c=(0,t.g2)("sc-tree-select"),m=(0,t.g2)("el-row"),p=(0,t.g2)("el-form"),f=(0,t.g2)("el-button"),h=(0,t.g2)("el-dialog");return(0,t.uX)(),(0,t.Wv)(h,{modelValue:i.visible,"onUpdate:modelValue":l[9]||(l[9]=e=>i.visible=e),title:i.titleMap[i.mode],width:700,"destroy-on-close":"","close-on-click-modal":!1,onOpened:s.opened,onClosed:l[10]||(l[10]=l=>e.$emit("closed"))},{footer:(0,t.k6)((()=>[(0,t.bF)(f,{onClick:l[7]||(l[7]=e=>i.visible=!1)},{default:(0,t.k6)((()=>[(0,t.eW)("取 消")])),_:1}),"show"!=i.mode?((0,t.uX)(),(0,t.Wv)(f,{key:0,type:"primary",loading:i.isSaveing,onClick:l[8]||(l[8]=e=>s.submit())},{default:(0,t.k6)((()=>[(0,t.eW)("保 存")])),_:1},8,["loading"])):(0,t.Q3)("",!0)])),default:(0,t.k6)((()=>[(0,t.bF)(p,{model:i.form,disabled:"show"==i.mode,ref:"dialogForm","label-width":"100px"},{default:(0,t.k6)((()=>[(0,t.bF)(m,null,{default:(0,t.k6)((()=>[(0,t.bF)(n,{md:12,lg:12},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请输入帐号！"}],label:"帐号",prop:"FUserCode",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FUserCode,"onUpdate:modelValue":l[0]||(l[0]=e=>i.form.FUserCode=e),placeholder:"帐号"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{md:12,lg:12},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请输入名称！"}],label:"名称",prop:"FUserName",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FUserName,"onUpdate:modelValue":l[1]||(l[1]=e=>i.form.FUserName=e),placeholder:"名称"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{md:12,lg:12},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请输入电话！"}],label:"电话",prop:"FTel",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(r,{modelValue:i.form.FTel,"onUpdate:modelValue":l[2]||(l[2]=e=>i.form.FTel=e),placeholder:"电话"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{md:12,lg:12},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请选择性别！"}],label:"性别",prop:"FSex",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(u,{clearable:"",modelValue:i.form.FSex,"onUpdate:modelValue":l[3]||(l[3]=e=>i.form.FSex=e),params:{jObjectSearch:{}},dic:"用户性别",style:{width:"100%"},placeholder:"请选择性别！"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(n,{md:24,lg:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请选择角色！"}],label:"角色",prop:"FRoleId",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(u,{clearable:"",modelValue:i.form.FRoleId,"onUpdate:modelValue":l[4]||(l[4]=e=>i.form.FRoleId=e),params:{jObjectSearch:{}},multiple:"","collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":2,apiObj:e.$API.sysRole.publicGetSysRoleList,prop:{label:"FName",value:"Fid"},placeholder:" 请选择角色！",style:{width:"100%"}},null,8,["modelValue","apiObj"])])),_:1})])),_:1}),(0,t.bF)(n,{md:24,lg:24},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请选择组织结构！"}],label:"组织结构",prop:"FOrganizationId",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(c,{multiple:"",clearable:"",modelValue:i.form.FOrganizationId,"onUpdate:modelValue":l[5]||(l[5]=e=>i.form.FOrganizationId=e),ref:"treeSelect","show-checkbox":"","check-strictly":"","default-expand-all":"",params:{jObjectSearch:{}},apiObj:e.$API.sysOrganization.getSysOrganizationList,style:{width:"100%"}},null,8,["modelValue","apiObj"])])),_:1})])),_:1}),(0,t.bF)(n,{md:12,lg:12},{default:(0,t.k6)((()=>[(0,t.bF)(d,{rules:[{required:!0,message:"请选择所在地！"}],label:"所在地",prop:"FProvinces",required:""},{default:(0,t.k6)((()=>[(0,t.bF)(u,{clearable:"",modelValue:i.form.FProvinces,"onUpdate:modelValue":l[6]||(l[6]=e=>i.form.FProvinces=e),apiObj:e.$API.sysDistrict.getSysDistrictList,params:{jObjectSearch:{parentCode:0}},style:{width:"100%"},placeholder:"请选择所在地"},null,8,["modelValue","apiObj"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","disabled"])])),_:1},8,["modelValue","title","onOpened"])}a(8743);var i={emits:["success","closed"],data(){return{mode:"add",titleMap:{add:"新增",edit:"编辑",show:"查看"},visible:!1,isSaveing:!1,form:{Fid:0,FRoleId:[],FOrganizationId:[]}}},mounted(){this.getSelect()},methods:{getSelect(){},open(e="add"){return this.mode=e,this.visible=!0,this},submit(){this.$refs.dialogForm.validate((async e=>{if(!e)return!1;this.isSaveing=!0;var l=await this.$API.sysUser.saveSysUser.post({jObjectParam:this.form});this.isSaveing=!1,0==l.code?(this.$emit("success",this.form,this.mode),this.visible=!1,this.$alert(l.message,"提示",{type:"success"})):this.$alert(l.message,"提示",{type:"error"})}))},async setData(e){Object.assign(this.form,e);let l=String(e.FRoleId).split(","),a=[];for(let o in l)a.push(Number(l[o]));this.form.FRoleId=a,l=String(this.form.FOrganizationId).split(",");let t=[];for(let o in l)t.push(Number(l[o]));this.form.FOrganizationId=t},opened(){}}},s=a(6262);const r=(0,s.A)(i,[["render",o]]);var d=r},5868:function(e,l,a){var t={"./about.vue":5593,"./calendar.vue":4082,"./echarts.vue":5021,"./localdb.vue":6328,"./notice.vue":5511,"./progress.vue":369,"./time.vue":6716,"./ver.vue":8302,"./welcome.vue":7156};function o(e){var l=i(e);return a(l)}function i(e){if(!a.o(t,e)){var l=new Error("Cannot find module '"+e+"'");throw l.code="MODULE_NOT_FOUND",l}return t[e]}o.keys=function(){return Object.keys(t)},o.resolve=i,e.exports=o,o.id=5868}}]);