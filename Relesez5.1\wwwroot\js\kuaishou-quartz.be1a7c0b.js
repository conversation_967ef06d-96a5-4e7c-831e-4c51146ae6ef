"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[9964],{194:function(e,t,a){a.r(t),a.d(t,{default:function(){return h}});var s=a(641),l=a(2644);const o={class:"left-panel"},i={class:"right-panel"};function r(e,t,a,r,n,c){const u=(0,s.g2)("el-button"),d=(0,s.g2)("el-input"),h=(0,s.g2)("el-header"),b=(0,s.g2)("el-popconfirm"),p=(0,s.g2)("el-button-group"),g=(0,s.g2)("el-table-column"),m=(0,s.g2)("el-tag"),f=(0,s.g2)("el-switch"),k=(0,s.g2)("scTable"),w=(0,s.g2)("el-main"),F=(0,s.g2)("el-container"),j=(0,s.g2)("jobDialog"),$=(0,s.gN)("loading");return(0,s.uX)(),(0,s.CE)(s.FK,null,[(0,s.bo)(((0,s.uX)(),(0,s.Wv)(F,{"element-loading-text":"请稍等..."},{default:(0,s.k6)((()=>[(0,s.bF)(h,null,{default:(0,s.k6)((()=>[(0,s.Lk)("div",o,[(0,s.bF)(u,{type:"primary",icon:"el-icon-plus",onClick:t[0]||(t[0]=e=>c.addJob())},{default:(0,s.k6)((()=>[(0,s.eW)("新增任务")])),_:1})]),(0,s.Lk)("div",i,[(0,s.bF)(d,{modelValue:n.jObjectSearch.search,"onUpdate:modelValue":t[1]||(t[1]=e=>n.jObjectSearch.search=e),placeholder:"任务名称",class:"input-with-select",onChange:c.upsearch},null,8,["modelValue","onChange"]),(0,s.bF)(u,{type:"primary",onClick:c.upsearch,icon:"el-icon-search"},{default:(0,s.k6)((()=>[(0,s.eW)("查询")])),_:1},8,["onClick"])])])),_:1}),(0,s.bF)(w,{class:"nopadding"},{default:(0,s.k6)((()=>[(0,s.bF)(k,{ref:"table",apiObj:n.taskApiObj,"highlight-current-row":"","row-key":"Fid",params:{jObjectSearch:n.jObjectSearch},border:""},{default:(0,s.k6)((()=>[(0,s.bF)(g,{label:"功能",fixed:"left",align:"center",width:"130"},{default:(0,s.k6)((e=>[(0,s.bF)(p,null,{default:(0,s.k6)((()=>["等待执行"==e.row.FStatus?((0,s.uX)(),(0,s.Wv)(b,{key:0,title:"确认执行？",onConfirm:t=>c.execJob(e.row,e.$index)},{reference:(0,s.k6)((()=>[(0,s.bF)(u,{text:"",size:"small",style:{color:"#006600"}},{default:(0,s.k6)((()=>[(0,s.eW)("执行")])),_:1})])),_:2},1032,["onConfirm"])):(0,s.Q3)("",!0),"正在执行"==e.row.FStatus?((0,s.uX)(),(0,s.Wv)(b,{key:1,title:"确认停止？",onConfirm:t=>c.stopJob(e.row,e.$index)},{reference:(0,s.k6)((()=>[(0,s.bF)(u,{text:"",type:"warning",size:"small"},{default:(0,s.k6)((()=>[(0,s.eW)("停止")])),_:1})])),_:2},1032,["onConfirm"])):(0,s.Q3)("",!0),"未启动"==e.row.FStatus?((0,s.uX)(),(0,s.Wv)(b,{key:2,title:"确认启动？",onConfirm:t=>c.startJob(e.row,e.$index)},{reference:(0,s.k6)((()=>[(0,s.bF)(u,{text:"",type:"success",size:"small"},{default:(0,s.k6)((()=>[(0,s.eW)("启动任务")])),_:1})])),_:2},1032,["onConfirm"])):(0,s.Q3)("",!0)])),_:2},1024)])),_:1}),(0,s.bF)(g,{label:"状态",prop:"FStatus",align:"center",width:"105"},{default:(0,s.k6)((e=>[(0,s.bF)(m,{type:"等待执行"==e.row.FStatus?"":"正在执行"==e.row.FStatus?"success":"已停用"==e.row.FStatus?"danger":"warning"},{default:(0,s.k6)((()=>[(0,s.eW)((0,l.v_)(e.row.FStatus),1)])),_:2},1032,["type"])])),_:1}),(0,s.bF)(g,{label:"执行时间",prop:"FExecTime",align:"center",width:"100"}),(0,s.bF)(g,{label:"执行时长",prop:"FSecondsName",align:"center",width:"100"}),(0,s.bF)(g,{label:"随机时间",prop:"FDifferenceName",align:"center",width:"100"}),(0,s.bF)(g,{label:"任务名称",prop:"FJobName",align:"center",width:"150"}),(0,s.bF)(g,{label:"账号名称",prop:"FCookieName","header-align":"center",align:"left","show-overflow-tooltip":""}),(0,s.bF)(g,{label:"参数内容",prop:"FParam",align:"center",width:"300","show-overflow-tooltip":""}),(0,s.bF)(g,{label:"是否启用",prop:"FEnable",align:"center",width:"100"},{default:(0,s.k6)((e=>[(0,s.bF)(f,{modelValue:e.row.FEnable,"onUpdate:modelValue":t=>e.row.FEnable=t,onChange:t=>c.enableSwitch(t,e.row),loading:e.row.$enable,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue","onChange","loading"])])),_:1}),(0,s.bF)(g,{label:"操作",fixed:"right",align:"center",width:"130"},{default:(0,s.k6)((e=>[(0,s.bF)(p,null,{default:(0,s.k6)((()=>[(0,s.bF)(u,{text:"",type:"primary",size:"small",onClick:t=>c.editJob(e.row,e.$index)},{default:(0,s.k6)((()=>[(0,s.eW)("编辑")])),_:2},1032,["onClick"]),(0,s.bF)(b,{title:"确定删除吗？",onConfirm:t=>c.delJob(e.row,e.$index)},{reference:(0,s.k6)((()=>[(0,s.bF)(u,{text:"",type:"danger",size:"small"},{default:(0,s.k6)((()=>[(0,s.eW)("删除")])),_:1})])),_:2},1032,["onConfirm"])])),_:2},1024)])),_:1})])),_:1},8,["apiObj","params"])])),_:1})])),_:1})),[[$,n.loading]]),n.dialog.job?((0,s.uX)(),(0,s.Wv)(j,{key:0,ref:"jobDialog",onSuccess:c.upsearch,onClosed:t[2]||(t[2]=e=>n.dialog.job=!1)},null,8,["onSuccess"])):(0,s.Q3)("",!0)],64)}var n=a(3273),c={name:"ksQuartz",components:{jobDialog:n["default"]},data(){return{dialog:{job:!1},filterText:"",taskApiObj:this.$API.ksQuartz.getQuartzList,loading:!1,status:null,jObjectSearch:{search:"",groupId:0}}},watch:{},async created(){},mounted(){},methods:{addJob(){this.dialog.job=!0,this.$nextTick((()=>{this.$refs.jobDialog.open("add").setData({FGroupId:0})}))},editJob(e){this.dialog.job=!0,this.$nextTick((()=>{this.$refs.jobDialog.open("edit").setData(e)}))},async delJob(e){this.loading=!0;let t=await this.$API.ksQuartz.delQuartz.post({jObjectParam:e});0==t.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(t.message,"提示",{type:"error"}),this.loading=!1},async enableSwitch(e,t){t.$enable=!0;let a=await this.$API.ksQuartz.enableSwitch.post({jObjectParam:t});0==a.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(a.message,"提示",{type:"error"}),delete t.$enable},async shutdownJob(e){this.loading=!0;let t=await this.$API.ksQuartz.shutdownQuartz.post({jObjectParam:e});0==t.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(t.message,"提示",{type:"error"}),this.loading=!1},async startJob(e){this.loading=!0;let t=await this.$API.ksQuartz.startQuartz.post({jObjectParam:e});0==t.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(t.message,"提示",{type:"error"}),this.loading=!1},async execJob(e){this.loading=!0;let t=await this.$API.ksQuartz.execQuartz.post({jObjectParam:e});0==t.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(t.message,"提示",{type:"error"}),this.loading=!1},async stopJob(e){this.loading=!0;let t=await this.$API.ksQuartz.stopQuartz.post({jObjectParam:e});0==t.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(t.message,"提示",{type:"error"}),this.loading=!1},upsearch(){this.$refs.table.upData({jObjectSearch:this.jObjectSearch})}}},u=a(6262);const d=(0,u.A)(c,[["render",r],["__scopeId","data-v-680c809b"]]);var h=d}}]);