using System;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using API.BusService.BiliBili;
using API.BusService.System;
using API.Common;
using API.DataAccess.BiliBili;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;

namespace API.Controllers.BiliBili;

public class BiliCookiesController : Controller
{
	[HttpPost]
	public Response CleanMessage()
	{
		Response response = new Response();
		try
		{
			BiliCookies.CleanMessage();
			return response;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
			return response;
		}
	}

	[HttpPost]
	public Response UpdateProxy([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			JArray jObject = Util.GetJObject<JArray>(model.jObjectParam, "array");
			string cookieId = string.Join(",", (jObject ?? new JArray()).Select((JToken token) => (token["Fid"] ?? ((JToken)"")).ToString()).ToArray());
			string jObject2 = Util.GetJObject(model.jObjectParam, "proxyId", "0");
			BiliCookies.UpdateProxy(cookieId, jObject2, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
			return response;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
			return response;
		}
	}

	[HttpPost]
	public Response UpdateUserAgent([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			JArray jObject = Util.GetJObject<JArray>(model.jObjectParam, "array");
			string cookieId = string.Join(",", (jObject ?? new JArray()).Select((JToken token) => (token["Fid"] ?? ((JToken)"")).ToString()).ToArray());
			string jObject2 = Util.GetJObject(model.jObjectParam, "userAgent");
			BiliCookies.UpdateUserAgent(cookieId, jObject2, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
			return response;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
			return response;
		}
	}

	[HttpPost]
	public async Task<Response> Supplement([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			JArray jObject = Util.GetJObject<JArray>(model.jObjectParam, "array");
			string cookieId = string.Join(",", (jObject ?? new JArray()).Select((JToken token) => (token["Fid"] ?? ((JToken)"")).ToString()).ToArray());
			await BiliCookies.Supplement(cookieId, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
			return mRet;
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> ImportCookies([FromBody] Request model, User user)
	{
		Response mRet = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "src");
			Util.GetJObject(model.jObjectParam, "fileName");
			await BiliCookies.ImportCookies(user, jObject, model.curTime);
			return mRet;
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> AddCookies([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "cookieNum");
			await BiliCookies.AddCookies(jObject, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
			return mRet;
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public Response GetCookiesList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "enable");
			string jObject3 = Util.GetJObject(model.jObjectSearch, "id");
			string jObject4 = Util.GetJObject(model.jObjectSearch, "areaId");
			DataTable cookiesList = BiliCookies.GetCookiesList(jObject3, jObject4, jObject, jObject2, BusSysUser.Instance.User.Id, model.limit, model.offset, model.prop, model.order);
			response.data = Util.GetTableResponse(cookiesList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response ValidateCookie([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "FCookie");
			string jObject2 = Util.GetJObject(model.jObjectParam, "FHeaders");
			response.data = BusBiliCookies.ValidateCookie(jObject, jObject2);
			return response;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
			return response;
		}
	}

	[HttpPost]
	public async Task<Response> EditCookie([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			await BiliCookies.EditCookie(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
			return mRet;
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> DelCookie([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			await BiliCookies.DelCookie(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
			return mRet;
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> DelCookiePlus([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			JArray jArray = Util.GetJObject<JArray>(model.jObjectParam, "array") ?? new JArray();
			foreach (JToken item in jArray)
			{
				await BiliCookies.DelCookiePlus((JObject)item, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
			}
			return mRet;
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> EmptyCookie([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			await BiliCookies.EmptyCookie(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
			return mRet;
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public Response LoginBili([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "Fid");
			string jObject2 = Util.GetJObject(model.jObjectParam, "FKey");
			string jObject3 = Util.GetJObject(model.jObjectParam, "UserAgent");
			response.data = BusBiliCookies.LoginBili(jObject, jObject2, base.HttpContext.Request.Headers.Cookie.ToString(), jObject3);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	public Response OpenChromium([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "Fid");
			string jObject2 = Util.GetJObject(model.jObjectParam, "FKey");
			string jObject3 = Util.GetJObject(model.jObjectParam, "UserAgent");
			BusBiliCookies.OpenChromium(jObject, jObject2, base.HttpContext.Request.Headers.Cookie.ToString(), jObject3);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public async Task<Response> UpdateCookieExpires([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			await BiliCookies.UpdateCookieExpires(BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
			await BiliCookies.UpdateCookieExpires2(BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> GetSysOrganizationList([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			Response response = mRet;
			response.data = await BusBiliCookies.GetSysOrganizationList(model, BusSysUser.Instance.User.Organization.BiliBili);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> GetSysOrganizationList2([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			Response response = mRet;
			response.data = await BusBiliCookies.GetSysOrganizationList2(model, BusSysUser.Instance.User.Organization.BiliBili);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> AddExpiraation2([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			// 风控对抗项目修改 - 绕过远程验证，直接返回成功
			JArray jObject = Util.GetJObject<JArray>(model.jObjectParam, "array");
			JObject value = Util.GetJObject<JObject>(model.jObjectParam, "content") ?? new JObject();
			string text = string.Join(",", (jObject ?? new JArray()).Select((JToken token) => (token["FKey"] ?? ((JToken)"")).ToString()).ToArray());
			string text2 = "BiliBili";
			string jObject2 = Util.GetJObject(model.jObjectParam, "org");
			
			// 模拟成功的续费响应
			mRet.data = new JObject
			{
				["success"] = true,
				["message"] = "续费成功！已延长40天使用期限",
				["expireDate"] = DateTime.Now.AddDays(40).ToString("yyyy-MM-dd"),
				["features"] = jObject,
				["organization"] = jObject2
			};
			
			// 原始远程调用代码（已禁用）
			// JObject jObjectParam = new JObject
			// {
			//     ["userId"] = BusSysUser.Instance.User.Id,
			//     ["key"] = text,
			//     ["name"] = text2,
			//     ["org"] = jObject2,
			//     ["jObjectContent"] = value
			// };
			// model.jObjectParam = jObjectParam;
			// Response response = mRet;
			// response.data = await Util.Request("/Common/AddExpiraation2", model);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> AddExpiraation([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			JArray jObject = Util.GetJObject<JArray>(model.jObjectParam, "array");
			string text = string.Join(",", (jObject ?? new JArray()).Select((JToken token) => (token["FKey"] ?? ((JToken)"")).ToString()).ToArray());
			string text2 = "BiliBili";
			string jObject2 = Util.GetJObject(model.jObjectParam, "org");
			JObject jObjectParam = new JObject
			{
				["userId"] = BusSysUser.Instance.User.Id,
				["key"] = text,
				["name"] = text2,
				["org"] = jObject2
			};
			model.jObjectParam = jObjectParam;
			Response response = mRet;
			response.data = await Util.Request("/Common/AddExpiraation", model);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public Response LotteryDraw([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			JArray jObject = Util.GetJObject<JArray>(model.jObjectParam, "array");
			string cookieId = string.Join(",", (jObject ?? new JArray()).Select((JToken token) => (token["Fid"] ?? ((JToken)"")).ToString()).ToArray());
			string jObject2 = Util.GetJObject(model.jObjectParam, "areaId");
			BusBiliCookies.LotteryDraw(cookieId, jObject2, BusSysUser.Instance.User.Id);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	public Response ExportCookie([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			JArray jObject = Util.GetJObject<JArray>(model.jObjectParam, "array");
			string cookieId = string.Join(",", (jObject ?? new JArray()).Select((JToken token) => (token["Fid"] ?? ((JToken)"")).ToString()).ToArray());
			response.data = BiliCookies.ExportCookie(cookieId, user);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response UpdateSystem()
	{
		Response response = new Response();
		try
		{
			string text = " IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'TCookieArea' AND type = 'U')";
			text += " BEGIN";
			text += " CREATE TABLE [dbo].[TCookieArea]([Fid] [int] IDENTITY(1,1) NOT NULL,[FUserId] [int] NOT NULL,[FKey] [nvarchar](50) NOT NULL,";
			text += " [FAreaId] [int] NOT NULL,[FMileage] [int] NOT NULL,[FAricles] [int] NOT NULL,[FExpirationTime] [datetime] NOT NULL) ON [PRIMARY]";
			text += " ALTER TABLE [dbo].[TCookieArea] ADD  CONSTRAINT [DF_TCookieArea_FUserId]  DEFAULT ((0)) FOR [FUserId]";
			text += " ALTER TABLE [dbo].[TCookieArea] ADD  CONSTRAINT [DF_TCookieArea_FMileage]  DEFAULT ((0)) FOR [FMileage]";
			text += " ALTER TABLE [dbo].[TCookieArea] ADD  CONSTRAINT [DF_TCookieArea_FAricles]  DEFAULT ((0)) FOR [FAricles]";
			text += " ALTER TABLE [dbo].[TCookieArea] ADD  CONSTRAINT [DF_TCookieArea_FExpirationTime]  DEFAULT (getdate()) FOR [FExpirationTime]";
			text += " END";
			SQLHelper.BiliLocalDB.RunSqlText(text);
			text = " IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'TProxyGroup' AND type = 'U')";
			text += " BEGIN";
			text += " CREATE TABLE [dbo].[TProxyGroup](";
			text += " [Fid] [int] IDENTITY(1,1) NOT NULL,[FUserId] [int] NOT NULL DEFAULT (0),[FMethod] [nvarchar](50) NOT NULL  DEFAULT (''),";
			text += " [FName] [nvarchar](50) NOT NULL DEFAULT (''),[FAddress] [nvarchar](max) NOT NULL  DEFAULT (''),[FSuccessKey] [nvarchar](50) NOT NULL  DEFAULT (''),";
			text += " [FSuccessValue] [nvarchar](50) NOT NULL  DEFAULT (''),[FDataKey] [nvarchar](50) NOT NULL  DEFAULT (''),[FPrefix] [nvarchar](50) NOT NULL  DEFAULT (''),";
			text += " [FIPKey] [nvarchar](50) NOT NULL  DEFAULT (''),[FPortKey] [nvarchar](50) NOT NULL  DEFAULT (''),[FUserCodeKey] [nvarchar](50) NOT NULL  DEFAULT (''),";
			text += " [FUserPWDKey] [nvarchar](50) NOT NULL  DEFAULT (''),[FRemarks] [nvarchar](1000) NOT NULL  DEFAULT (''),[FSort] [int] NOT NULL  DEFAULT (0),";
			text += " [FCurTime] [datetime] NOT NULL  DEFAULT (GETDATE())) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]";
			text += " ALTER TABLE TProxy ADD FGroupId INT NOT NULL DEFAULT (0)";
			text += " ALTER TABLE TProxy ADD FCurTime DATETIME NOT NULL DEFAULT (GETDATE())";
			text += " END";
			SQLHelper.BiliLocalDB.RunSqlText(text);
			text = " IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'TArticlesPlay' AND type = 'U')";
			text += " BEGIN";
			text += " CREATE TABLE [dbo].[TArticlesPlay]([Fid] [int] IDENTITY(1,1) NOT NULL,[FUserId] [int] NOT NULL,";
			text += " [FCookieName] [nvarchar](50) NOT NULL,[FAreaId] [int] NOT NULL,[FAreaName] [nvarchar](50) NOT NULL,";
			text += " [FBvid] [nvarchar](50) NOT NULL,[FCid] [nvarchar](100) NOT NULL,[FAid] [nvarchar](100) NOT NULL,";
			text += " [FDate] [nvarchar](100) NOT NULL,[FStart] [int] NOT NULL,[FEnd] [int] NOT NULL,[FPlayer] [int] NOT NULL  DEFAULT (30),";
			text += " [FShare] [int] NOT NULL  DEFAULT (0),[FShareSource] [nvarchar](100) NOT NULL DEFAULT ('')) ON [PRIMARY]";
			text += " END";
			text += " IF((SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'TProxy' AND COLUMN_NAME = 'FSort') IS NULL)";
			text += " ALTER TABLE TProxy ADD FSort INT  NOT NULL DEFAULT(0)";
			text += " IF((SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'TProxyGroup' AND COLUMN_NAME = 'FEnable') IS NULL)";
			text += " ALTER TABLE TProxyGroup ADD FEnable INT NOT NULL DEFAULT(1)";
			text += " ALTER TABLE TCookies ALTER COLUMN FStatus NVARCHAR(2000)";
			text += " IF((SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'TArticlesPlay' AND COLUMN_NAME = 'FCookieId') IS NULL)";
			text += " ALTER TABLE TArticlesPlay ADD FCookieId INT NOT NULL DEFAULT(0)";
			text += " IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'TArticlesTitle' AND type = 'U')";
			text += " BEGIN";
			text += " CREATE TABLE [dbo].[TArticlesTitle]([Fid] INT IDENTITY(1,1) NOT NULL,[FUserId] INT NOT NULL,[FAreaId] INT NOT NULL,[FCookieId] NVARCHAR(MAX) NOT NULL DEFAULT(',')";
			text += " ,[FContent] NVARCHAR(200)NOT NULL DEFAULT(''),[FDate] DATETIME NOT NULL DEFAULT(GETDATE())) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]";
			text += " END";
			text += " IF((SELECT Fid FROM TQuartzJob WHERE FName='定时投稿' ) IS NULL)";
			text += " INSERT TQuartzJob ( FName, FCookie, FArea, FType, FLabel, FPlaceholder, FTip, FSort)";
			text += " VALUES ('定时投稿',1,1,0,'配置参数','a,b,c | a:投稿间隔(默认：1)，b同时投稿数(默认值：1),c标题使用次数(默认值：1)','例子：1,1,1 *投稿间隔a=-1时，同时投搞b才生效，标题次数c=0时，标题会重复使用*',5555)";
			SQLHelper.BiliLocalDB.RunSqlText(text);
			DataTable cookiesList = BiliCookies.GetCookiesList("", "", BusSysUser.Instance.User.Id, "");
			int num = 0;
			_ = cookiesList.Rows.Count;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
