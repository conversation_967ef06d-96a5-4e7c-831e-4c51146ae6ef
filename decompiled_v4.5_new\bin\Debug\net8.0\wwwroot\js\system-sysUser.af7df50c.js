"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[3217],{7300:function(e,a,l){l.r(a),l.d(a,{default:function(){return p}});var t=l(641),s=l(2644);const i={class:"custom-tree-node"},o={class:"left-panel"},n={class:"right-panel"};function r(e,a,l,r,c,d){const h=(0,t.g2)("el-input"),b=(0,t.g2)("el-header"),p=(0,t.g2)("el-tree"),u=(0,t.g2)("el-main"),g=(0,t.g2)("el-container"),F=(0,t.g2)("el-aside"),m=(0,t.g2)("el-button"),k=(0,t.g2)("sc-select"),f=(0,t.g2)("el-table-column"),y=(0,t.g2)("el-switch"),j=(0,t.g2)("scTable"),w=(0,t.g2)("save-dialog"),v=(0,t.gN)("loading"),_=(0,t.gN)("auth");return(0,t.uX)(),(0,t.CE)(t.FK,null,[(0,t.bF)(g,null,{default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(F,{width:"250px"},{default:(0,t.k6)((()=>[(0,t.bF)(g,null,{default:(0,t.k6)((()=>[(0,t.bF)(b,null,{default:(0,t.k6)((()=>[(0,t.bF)(h,{placeholder:"输入关键字进行过滤",modelValue:c.roleFilterText,"onUpdate:modelValue":a[0]||(a[0]=e=>c.roleFilterText=e),onChange:d.cha_getDic,clearable:""},null,8,["modelValue","onChange"])])),_:1}),(0,t.bF)(u,{class:"nopadding"},{default:(0,t.k6)((()=>[(0,t.bF)(p,{ref:"role",class:"menu",data:c.roleList,props:c.roleProps,"highlight-current":!0,"node-key":"Fid","expand-on-click-node":!1,"filter-node-method":d.roleFilterNode,onNodeClick:d.roleClick},{default:(0,t.k6)((({data:e})=>[(0,t.Lk)("span",i,[(0,t.Lk)("span",{class:"label",style:(0,s.Tr)(0==e.FEnable?"color:red":"")},(0,s.v_)(e.FName),5)])])),_:1},8,["data","props","filter-node-method","onNodeClick"])])),_:1})])),_:1})])),_:1})),[[v,c.showRoleloading]]),(0,t.bF)(g,{class:"is-vertical"},{default:(0,t.k6)((()=>[(0,t.bF)(b,null,{default:(0,t.k6)((()=>[(0,t.Lk)("div",o,[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(m,{type:"primary",icon:"el-icon-plus",onClick:d.btn_add},{default:(0,t.k6)((()=>[(0,t.eW)(" 新增")])),_:1},8,["onClick"])),[[_,"sysUser.add"]]),(0,t.bo)(((0,t.uX)(),(0,t.Wv)(m,{type:"primary",plain:"",onClick:d.btn_reset},{default:(0,t.k6)((()=>[(0,t.eW)("重置密码")])),_:1},8,["onClick"])),[[_,"sysUser.reset"]]),(0,t.bF)(k,{clearable:"",modelValue:c.jObjectSearch.enable,"onUpdate:modelValue":a[1]||(a[1]=e=>c.jObjectSearch.enable=e),params:{jObjectSearch:{}},dic:"启用禁用",style:{width:"200px","padding-left":"5px"},placeholder:"请选择状态",onChange:d.btn_search},null,8,["modelValue","onChange"]),(0,t.bF)(k,{clearable:"",modelValue:c.jObjectSearch.provinces,"onUpdate:modelValue":a[2]||(a[2]=e=>c.jObjectSearch.provinces=e),apiObj:e.$API.sysDistrict.getSysDistrictList,params:{jObjectSearch:{parentCode:0}},style:{width:"200px","padding-left":"7px"},placeholder:"请选择省份",onChange:d.btn_search},null,8,["modelValue","apiObj","onChange"])]),(0,t.Lk)("div",n,[(0,t.bF)(h,{modelValue:c.jObjectSearch.search,"onUpdate:modelValue":a[3]||(a[3]=e=>c.jObjectSearch.search=e),placeholder:"名称/电话/助记码",class:"input-with-select",onChange:d.btn_search},null,8,["modelValue","onChange"]),(0,t.bF)(m,{type:"primary",onClick:d.btn_search,icon:"el-icon-search"},{default:(0,t.k6)((()=>[(0,t.eW)("查询")])),_:1},8,["onClick"])])])),_:1}),(0,t.bF)(u,{class:"nopadding"},{default:(0,t.k6)((()=>[(0,t.bF)(j,{ref:"table",apiObj:c.listApi,"row-key":"Fid",params:{jObjectSearch:c.jObjectSearch},border:"",pageSize:10,onSelectionChange:d.selectionChange,stripe:"",paginationLayout:"prev, pager, next"},{default:(0,t.k6)((()=>[(0,t.bF)(f,{type:"selection",width:"50"}),(0,t.bF)(f,{label:"#",type:"index",width:"50",align:"center"}),(0,t.bF)(f,{label:"用户账号",prop:"FUserCode",align:"center"}),(0,t.bF)(f,{label:"用户名称",prop:"FUserName",align:"center"}),(0,t.bF)(f,{label:"助记码",prop:"FZJM",align:"center"}),(0,t.bF)(f,{label:"联系电话",prop:"FTel",align:"center"}),(0,t.bF)(f,{label:"角色",prop:"FRoleName",align:"center"}),(0,t.bF)(f,{label:"组织结构",prop:"FOrganizationName",align:"center","show-overflow-tooltip":""}),(0,t.bF)(f,{label:"所在地",prop:"FProvincesName",align:"center"}),(0,t.bF)(f,{label:"最后登录时间",prop:"FLastLoginTime",align:"center",width:"150"}),(0,t.bo)(((0,t.uX)(),(0,t.Wv)(f,{label:"是否有效",prop:"FEnable",align:"center",width:"100"},{default:(0,t.k6)((e=>[(0,t.bF)(y,{modelValue:e.row.FEnable,"onUpdate:modelValue":a=>e.row.FEnable=a,onChange:a=>d.changeSwitch(a,e.row),loading:e.row.$switch_fenable,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue","onChange","loading"])])),_:1})),[[_,"sysUser.edit"]]),(0,t.bo)(((0,t.uX)(),(0,t.Wv)(f,{label:"操作",fixed:"right",align:"center",width:"140"},{default:(0,t.k6)((e=>[(0,t.bF)(m,{link:"",size:"small",type:"success",onClick:a=>d.btn_show(e.row),plain:""},{default:(0,t.k6)((()=>[(0,t.eW)("查看")])),_:2},1032,["onClick"]),(0,t.bo)(((0,t.uX)(),(0,t.Wv)(m,{link:"",type:"primary",size:"small",onClick:a=>d.btn_edit(e.row)},{default:(0,t.k6)((()=>[(0,t.eW)("编辑")])),_:2},1032,["onClick"])),[[_,"sysUser.edit"]])])),_:1})),[[_,"sysUser.edit"]])])),_:1},8,["apiObj","params","onSelectionChange"])])),_:1})])),_:1})])),_:1}),c.dialog.save?((0,t.uX)(),(0,t.Wv)(w,{key:0,ref:"saveDialog",onSuccess:d.btn_search,onClosed:a[4]||(a[4]=e=>c.dialog.save=!1)},null,8,["onSuccess"])):(0,t.Q3)("",!0)],64)}var c=l(856),d={name:"sysUser",components:{saveDialog:c["default"]},data(){return{dialog:{save:!1},showRoleloading:!0,roleList:[],roleFilterText:"",roleProps:{},listApi:this.$API.sysUser.getSysUserList,jObjectSearch:{search:"",enable:"1",roleId:"",provinces:""},selection:[],selectGroupId:0,selectGroupSys:1}},async mounted(){var e=await this.$API.sysRole.publicGetSysRoleList.post({jObjectSearch:{enable:1}});this.showRoleloading=!1,this.roleList=e.data.rows;let a={FName:"全部角色",Fid:""},l=[a,...e.data.rows];this.roleList=l,this.$nextTick((()=>{this.$refs["role"].setCurrentKey(this.roleList[0].Fid)}))},methods:{async cha_getDic(){var e=await this.$API.sysRole.publicGetSysRoleList.post({jObjectSearch:{search:this.roleFilterText,enable:1}});if(this.showRoleloading=!1,this.roleFilterText)this.roleList=e.data.rows;else{let a={FName:"全部角色",Fid:""},l=[a,...e.data.rows];this.roleList=l}this.roleList.length>0&&this.$nextTick((()=>{this.$refs["role"].setCurrentKey(this.roleList[0].Fid)}))},roleFilterNode(e,a){return!e||a.FName.includes(e)},roleClick(e){this.jObjectSearch.roleId=e.Fid,this.$refs.table.reload({jObjectSearch:this.jObjectSearch})},btn_add(){this.dialog.save=!0,this.$nextTick((()=>{this.$refs.saveDialog.open("add")}))},btn_edit(e){this.dialog.save=!0,this.$nextTick((()=>{this.$refs.saveDialog.open("edit").setData(e)}))},btn_show(e){this.dialog.save=!0,this.$nextTick((()=>{this.$refs.saveDialog.open("show").setData(e)}))},selectionChange(e){this.selection=e},changeSwitch(e,a){a.FEnable="1"==a.FEnable?"0":"1",a.$switch_fenable=!0,setTimeout((async()=>{var l=await this.$API.sysUser.enableSysUser.post({jObjectParam:{Fid:a.Fid,FEnable:e}});0==l.code?(delete a.$switch_fenable,a.FEnable=e,this.$message.success("操作成功")):this.$alert(l.message,"提示",{type:"error"})}),500)},btn_search(){this.$refs.table.reload({jObjectSearch:this.jObjectSearch})},async btn_reset(){let e=this.$refs.table.getSelectionRows();0!=e.length?this.$confirm("确定重置密码？","提示",{type:"warning"}).then((async()=>{var a=await this.$API.sysUser.resetPWD.post({jObjectParam:{arr:e}});0==a.code?this.$alert(a.message,"提示",{type:"success"}):this.$alert(a.message,"提示",{type:"error"})})).catch((()=>{})):this.$alert("请至少选中一行！","提示",{type:"error"})}}},h=l(6262);const b=(0,h.A)(d,[["render",r],["__scopeId","data-v-31e7f396"]]);var p=b}}]);