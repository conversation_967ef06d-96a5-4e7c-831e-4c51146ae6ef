"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[100],{308:function(e,i,t){t.r(i),t.d(i,{default:function(){return y}});var a=t(641),l=t(2644),s=t(9322);const c={class:"custom-tree-node"},o={class:"do"},n={class:"left-panel"},d={class:"right-panel"};function r(e,i,t,r,h,u){const p=(0,a.g2)("el-input"),b=(0,a.g2)("el-header"),g=(0,a.g2)("el-icon-edit"),y=(0,a.g2)("el-icon"),f=(0,a.g2)("el-icon-delete"),k=(0,a.g2)("el-tree"),F=(0,a.g2)("el-main"),D=(0,a.g2)("el-button"),m=(0,a.g2)("el-footer"),w=(0,a.g2)("el-container"),j=(0,a.g2)("el-aside"),_=(0,a.g2)("el-table-column"),C=(0,a.g2)("el-switch"),$=(0,a.g2)("el-popconfirm"),v=(0,a.g2)("scTable"),S=(0,a.g2)("dic-dialog"),x=(0,a.g2)("list-dialog"),O=(0,a.gN)("auth"),I=(0,a.gN)("loading"),W=(0,a.gN)("auths");return(0,a.uX)(),(0,a.CE)(a.FK,null,[(0,a.bF)(w,null,{default:(0,a.k6)((()=>[(0,a.bo)(((0,a.uX)(),(0,a.Wv)(j,{width:"300px"},{default:(0,a.k6)((()=>[(0,a.bF)(w,null,{default:(0,a.k6)((()=>[(0,a.bF)(b,null,{default:(0,a.k6)((()=>[(0,a.bF)(p,{placeholder:"输入关键字进行过滤",modelValue:h.dicFilterText,"onUpdate:modelValue":i[0]||(i[0]=e=>h.dicFilterText=e),clearable:"",onChange:u.getDic},null,8,["modelValue","onChange"])])),_:1}),(0,a.bF)(F,{class:"nopadding"},{default:(0,a.k6)((()=>[(0,a.bF)(k,{ref:"dic",class:"menu",data:h.dicList,props:h.dicProps,"highlight-current":!0,"check-strictly":"","node-key":"Fid","expand-on-click-node":!1,"filter-node-method":u.dicFilterNode,onNodeClick:u.dicClick},{default:(0,a.k6)((({data:e})=>[(0,a.Lk)("span",c,[(0,a.Lk)("span",{class:"label",style:(0,l.Tr)(0==e.FEnable?"color:red":"")},(0,l.v_)(e.FName),5),(0,a.Lk)("span",o,[(0,a.bo)(((0,a.uX)(),(0,a.Wv)(y,{onClick:(0,s.D$)((i=>u.dicEdit(e)),["stop"]),style:{width:"30px",height:"60px"}},{default:(0,a.k6)((()=>[(0,a.bF)(g)])),_:2},1032,["onClick"])),[[O,"sysDictionary.editGroup"]]),(0,a.bo)(((0,a.uX)(),(0,a.Wv)(y,{onClick:(0,s.D$)((i=>u.dicDel(e)),["stop"]),style:{width:"30px",height:"60px"}},{default:(0,a.k6)((()=>[(0,a.bF)(f)])),_:2},1032,["onClick"])),[[O,"sysDictionary.delGroup"]])])])])),_:1},8,["data","props","filter-node-method","onNodeClick"])])),_:1}),(0,a.bF)(m,{style:{height:"51px"}},{default:(0,a.k6)((()=>[(0,a.bo)(((0,a.uX)(),(0,a.Wv)(D,{type:"primary",size:"small",icon:"el-icon-plus",style:{width:"100%"},onClick:u.addDic},{default:(0,a.k6)((()=>[(0,a.eW)("添加字典组 ")])),_:1},8,["onClick"])),[[O,"sysDictionary.addGroup"]])])),_:1})])),_:1})])),_:1})),[[I,h.showDicloading]]),(0,a.bF)(w,{class:"is-vertical"},{default:(0,a.k6)((()=>[(0,a.bF)(b,null,{default:(0,a.k6)((()=>[(0,a.Lk)("div",n,[(0,a.bo)(((0,a.uX)(),(0,a.Wv)(D,{type:"primary",icon:"el-icon-plus",onClick:u.addInfo},{default:(0,a.k6)((()=>[(0,a.eW)(" 新增")])),_:1},8,["onClick"])),[[O,"sysDictionary.addDictionary"]])]),(0,a.Lk)("div",d,[(0,a.bF)(p,{modelValue:h.jObjectSearch.search,"onUpdate:modelValue":i[1]||(i[1]=e=>h.jObjectSearch.search=e),placeholder:"名称/键值/助记码",class:"input-with-select",onChange:u.btn_search},null,8,["modelValue","onChange"]),(0,a.bF)(D,{type:"primary",onClick:u.btn_search,icon:"el-icon-search"},{default:(0,a.k6)((()=>[(0,a.eW)("查询")])),_:1},8,["onClick"])])])),_:1}),(0,a.bF)(F,{class:"nopadding"},{default:(0,a.k6)((()=>[(0,a.bF)(v,{ref:"table",apiObj:h.listApi,"row-key":"Fid",params:{jObjectSearch:h.jObjectSearch},border:"",onSelectionChange:u.selectionChange,stripe:"",paginationLayout:"prev, pager, next"},{default:(0,a.k6)((()=>[(0,a.bF)(_,{type:"selection",width:"50"}),(0,a.bF)(_,{label:"#",type:"index",width:"50",align:"center"}),(0,a.bF)(_,{label:"名称",prop:"FName",width:"200",align:"center"}),(0,a.bF)(_,{label:"键值",prop:"FKey",width:"200",align:"center"}),(0,a.bF)(_,{label:"助记码",prop:"FZJM",width:"200",align:"center"}),(0,a.bF)(_,{label:"排序",prop:"FSort",width:"200",align:"center"}),(0,a.bo)(((0,a.uX)(),(0,a.Wv)(_,{label:"启用",prop:"fenable",width:"200",align:"center"},{default:(0,a.k6)((e=>[(0,a.bF)(C,{modelValue:e.row.FEnable,"onUpdate:modelValue":i=>e.row.FEnable=i,onChange:i=>u.changeSwitch(i,e.row),loading:e.row.$switch_fenable,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue","onChange","loading"])])),_:1})),[[O,"sysDictionary.editDictionary"]]),(0,a.bo)(((0,a.uX)(),(0,a.Wv)(_,{label:"操作",fixed:"right",align:"center",width:"140"},{default:(0,a.k6)((e=>[(0,a.bo)(((0,a.uX)(),(0,a.Wv)(D,{link:"",size:"small",type:"primary",onClick:i=>u.table_edit(e.row)},{default:(0,a.k6)((()=>[(0,a.eW)("编辑")])),_:2},1032,["onClick"])),[[O,"sysDictionary.editDictionary"]]),(0,a.bF)($,{title:"确定删除吗？",onConfirm:i=>u.table_del(e.row)},{reference:(0,a.k6)((()=>[(0,a.bo)(((0,a.uX)(),(0,a.Wv)(D,{link:"",size:"small",type:"danger"},{default:(0,a.k6)((()=>[(0,a.eW)("删除")])),_:1})),[[O,"sysDictionary.delDictionary"]])])),_:2},1032,["onConfirm"])])),_:1})),[[W,["sysDictionary.editDictionary","sysDictionary.delDictionary"]]])])),_:1},8,["apiObj","params","onSelectionChange"])])),_:1})])),_:1})])),_:1}),h.dialog.dic?((0,a.uX)(),(0,a.Wv)(S,{key:0,ref:"dicDialog",onSuccess:u.handleDicSuccess,onClosed:i[2]||(i[2]=e=>h.dialog.dic=!1)},null,8,["onSuccess"])):(0,a.Q3)("",!0),h.dialog.list?((0,a.uX)(),(0,a.Wv)(x,{key:1,ref:"listDialog",onSuccess:u.btn_search,onClosed:i[3]||(i[3]=e=>h.dialog.list=!1)},null,8,["onSuccess"])):(0,a.Q3)("",!0)],64)}var h=t(6284),u=t(5535),p={name:"sysDictionary",components:{dicDialog:h["default"],listDialog:u["default"]},data(){return{dialog:{dic:!1,info:!1},showDicloading:!0,dicList:[],dicFilterText:"",dicProps:{label:"FName"},listApi:this.$API.sysDictionary.getDictionaryList,jObjectSearch:{groupID:0,enable:""},selection:[],selectGroupId:0,selectGroupSys:1}},async mounted(){await this.getDic(),this.$nextTick((()=>{this.$refs["dic"].setCurrentKey(this.dicList[0].Fid),this.dicClick(this.dicList[0])}))},methods:{async getDic(){var e=await this.$API.sysDictionary.getSysDictGroupList.post({jObjectSearch:{search:this.dicFilterText,enable:""}});this.showDicloading=!1,this.dicList=e.data.rows,this.jObjectSearch={groupID:0,enable:""}},dicFilterNode(e,i){if(!e)return!0;var t=i.FName;return-1!==t.indexOf(e)},addDic(){this.dialog.dic=!0,this.$nextTick((()=>{this.$refs.dicDialog.open()}))},dicEdit(e){this.dialog.dic=!0,this.$nextTick((()=>{this.$refs.dicDialog.open("edit").setData(e)}))},dicClick(e){this.jObjectSearch={groupID:e.Fid,enable:""},this.selectGroupId=e.Fid,this.selectGroupSys=e.FSys,this.$refs.table.reload({jObjectSearch:this.jObjectSearch})},dicDel(e){this.$confirm(`确定删除 ${e.FName} 项吗？`,"提示",{type:"warning"}).then((async()=>{var i={jObjectParam:{id:e.Fid}},t=await this.$API.sysDictionary.delDictGroup.post(i);0==t.code?(this.getDic(),this.$message.success(t.message)):this.$alert(t.message,"提示",{type:"error"})})).catch((()=>{}))},addInfo(){this.dialog.list=!0,this.$nextTick((()=>{const e={FGroupId:this.selectGroupId,Fid:0,FEnable:1};this.$refs.listDialog.open("add").setData(e)}))},table_edit(e){this.dialog.list=!0,this.$nextTick((()=>{this.$refs.listDialog.open("edit").setData(e)}))},async table_del(e){var i={id:e.Fid},t=await this.$API.sysDictionary.delDictionary.post({jObjectParam:i});0==t.code?(this.listApiParams={fgroup_id:this.selectGroupId},this.$refs.table.reload({jObjectSearch:this.jObjectSearch}),this.$message.success("删除成功")):this.$alert(t.message,"提示",{type:"error"})},selectionChange(e){this.selection=e},changeSwitch(e,i){i.FEnable="1"==i.FEnable?"0":"1",i.$switch_fenable=!0,setTimeout((async()=>{var t=await this.$API.sysDictionary.enableDictionary.post({jObjectParam:{Fid:i.Fid,FEnable:e}});0==t.code?(delete i.$switch_fenable,i.FEnable=e,this.$message.success("操作成功")):this.$alert(t.message,"提示",{type:"error"})}),500)},handleDicSuccess(){this.getDic()},btn_search(){this.$refs.table.reload({jObjectSearch:this.jObjectSearch})}}},b=t(6262);const g=(0,b.A)(p,[["render",r],["__scopeId","data-v-b7b88e44"]]);var y=g}}]);