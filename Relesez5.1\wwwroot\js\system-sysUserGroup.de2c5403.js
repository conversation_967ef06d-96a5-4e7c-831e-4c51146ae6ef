"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[8998],{1245:function(e,t,a){a.r(t),a.d(t,{default:function(){return f}});var l=a(641),s=a(2644),i=a(9322);const n={class:"custom-tree-node"},r={class:"label"},c={key:0,class:"do"},o={key:1},d={class:"left-panel"},h={class:"right-panel"};function u(e,t,a,u,b,p){const g=(0,l.g2)("el-input"),k=(0,l.g2)("el-header"),f=(0,l.g2)("el-icon-edit"),F=(0,l.g2)("el-icon"),C=(0,l.g2)("el-icon-delete"),y=(0,l.g2)("el-tree"),_=(0,l.g2)("el-main"),j=(0,l.g2)("el-container"),m=(0,l.g2)("el-aside"),v=(0,l.g2)("el-button"),w=(0,l.g2)("el-button-group"),S=(0,l.g2)("el-table-column"),$=(0,l.g2)("scTable"),D=(0,l.g2)("save-dialog"),O=(0,l.gN)("loading"),U=(0,l.gN)("auth");return(0,l.uX)(),(0,l.CE)(l.FK,null,[(0,l.bF)(j,null,{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(m,{width:"250px"},{default:(0,l.k6)((()=>[(0,l.bF)(j,null,{default:(0,l.k6)((()=>[(0,l.bF)(k,null,{default:(0,l.k6)((()=>[(0,l.bF)(g,{placeholder:"输入关键字进行过滤",modelValue:b.treeSearch,"onUpdate:modelValue":t[0]||(t[0]=e=>b.treeSearch=e),onChange:p.treeSearchChange,clearable:""},null,8,["modelValue","onChange"])])),_:1}),(0,l.bF)(_,{class:"nopadding"},{default:(0,l.k6)((()=>[(0,l.bF)(y,{ref:"tree",class:"menu","node-key":"id",data:b.treeData,"highlight-current":!0,"expand-on-click-node":!1,onNodeClick:p.treeClick},{default:(0,l.k6)((({data:e})=>[(0,l.Lk)("span",n,[(0,l.Lk)("span",r,(0,s.v_)(e.label),1),e.FCount?((0,l.uX)(),(0,l.CE)("span",o,(0,s.v_)(e.FDesc),1)):((0,l.uX)(),(0,l.CE)("span",c,[(0,l.bF)(F,{onClick:(0,i.D$)((t=>p.btn_edit(e)),["stop"]),style:{width:"30px",height:"60px"}},{default:(0,l.k6)((()=>[(0,l.bF)(f)])),_:2},1032,["onClick"]),(0,l.bF)(F,{onClick:(0,i.D$)((t=>p.btn_del(e)),["stop"]),style:{width:"30px",height:"60px"}},{default:(0,l.k6)((()=>[(0,l.bF)(C)])),_:2},1032,["onClick"])]))])])),_:1},8,["data","onNodeClick"])])),_:1})])),_:1})])),_:1})),[[O,b.loading]]),(0,l.bF)(j,{class:"is-vertical"},{default:(0,l.k6)((()=>[(0,l.bF)(k,null,{default:(0,l.k6)((()=>[(0,l.Lk)("div",d,[(0,l.bF)(w,{style:{"padding-right":"15px"}},{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(v,{type:"primary",icon:"el-icon-plus",onClick:p.btn_add},{default:(0,l.k6)((()=>[(0,l.eW)(" 新增")])),_:1},8,["onClick"])),[[U,"sysUser.add"]])])),_:1})]),(0,l.Lk)("div",h,[(0,l.bF)(g,{modelValue:b.jObjectSearch.search,"onUpdate:modelValue":t[1]||(t[1]=e=>b.jObjectSearch.search=e),placeholder:"模糊查询",class:"input-with-select",onChange:p.btn_search},null,8,["modelValue","onChange"]),(0,l.bF)(v,{type:"primary",onClick:p.btn_search,icon:"el-icon-search"},{default:(0,l.k6)((()=>[(0,l.eW)("查询")])),_:1},8,["onClick"])])])),_:1}),(0,l.bF)(_,{class:"nopadding"},{default:(0,l.k6)((()=>[(0,l.bF)($,{ref:"table",apiObj:b.listApi,"row-key":"Fid",params:{jObjectSearch:b.jObjectSearch},border:"",pageSize:100,stripe:""},{default:(0,l.k6)((()=>[(0,l.bF)(S,{type:"selection",width:"50"}),(0,l.bF)(S,{label:"#",type:"index",width:"50",align:"center"}),(0,l.bF)(S,{label:"用户账号",width:"160","show-overflow-tooltip":"",prop:"FUserCode",align:"center"}),(0,l.bF)(S,{label:"账号标识",width:"300",prop:"FKey",align:"center"}),(0,l.bF)(S,{label:"B站标识",width:"180",prop:"FIdentifying",align:"center"}),(0,l.bF)(S,{label:"到期时间",width:"200",prop:"FExpirationTime",align:"center"}),(0,l.bF)(S,{label:"操作时间",width:"200",prop:"FDate",align:"center"}),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(S,{label:"操作",fixed:"right",align:"center",width:"140"},{default:(0,l.k6)((t=>[(0,l.bF)(v,{link:"",size:"small",type:"success",onClick:a=>e.btn_show(t.row),plain:""},{default:(0,l.k6)((()=>[(0,l.eW)("查看")])),_:2},1032,["onClick"]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(v,{link:"",type:"primary",size:"small",onClick:e=>p.btn_edit(t.row)},{default:(0,l.k6)((()=>[(0,l.eW)("编辑")])),_:2},1032,["onClick"])),[[U,"sysUser.edit"]])])),_:1})),[[U,"sysUser.edit"]])])),_:1},8,["apiObj","params"])])),_:1})])),_:1})])),_:1}),b.dialog.save?((0,l.uX)(),(0,l.Wv)(D,{key:0,ref:"saveDialog",onSuccess:t[2]||(t[2]=e=>{p.treeSearchChange()}),onClosed:t[3]||(t[3]=e=>b.dialog.save=!1)},null,512)):(0,l.Q3)("",!0)],64)}var b=a(2597),p={name:"sysUserGroup",components:{saveDialog:b["default"]},data(){return{dialog:{save:!1},loading:!0,treeData:[],treeSearch:"",listApi:this.$API.sysUserGroup.getUserCookieList,jObjectSearch:{},selection:[],selectGroupId:0,selectGroupSys:1}},async mounted(){await this.treeSearchChange()},methods:{async treeSearchChange(){this.loading=!0;var e=await this.$API.sysUserGroup.getSysUserGroupList.post({jObjectSearch:{search:this.treeSearch}});this.loading=!1,this.treeData=e.data,this.treeData.length>0&&(this.jObjectSearch.userId=this.treeData[0].FUserId,this.$nextTick((()=>{this.$refs["tree"].setCurrentKey(this.treeData[0].id)}))),this.btn_search()},treeClick(e){this.jObjectSearch.userId=e.FUserId,this.$refs.table.reload({jObjectSearch:this.jObjectSearch})},btn_add(){this.dialog.save=!0,this.$nextTick((()=>{this.$refs.saveDialog.open("add")}))},btn_edit(e){this.dialog.save=!0,this.$nextTick((()=>{this.$refs.saveDialog.open("edit").setData(e)}))},btn_del(e){this.$confirm(`确定删除 ${e.FName} 吗？`,"提示",{type:"warning"}).then((async()=>{var t={jObjectParam:{id:e.Fid}},a=await this.$API.sysUserGroup.del.post(t);0==a.code?(await this.treeSearchChange(),this.$message.success(a.message)):this.$alert(a.message,"提示",{type:"error"})})).catch((()=>{}))},btn_search(){this.$refs.table.reload({jObjectSearch:this.jObjectSearch})}}},g=a(6262);const k=(0,g.A)(p,[["render",u],["__scopeId","data-v-0eb9f44d"]]);var f=k}}]);