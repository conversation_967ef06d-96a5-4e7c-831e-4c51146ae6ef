using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using API.Common;
using Newtonsoft.Json.Linq;

namespace API.BusService.BiliBili
{
    /// <summary>
    /// 哔哩哔哩风控对抗核心模块
    /// 基于13+维度检测机制的完整对抗方案
    /// </summary>
    public class BusBiliAntiDetection
    {
        private static readonly Random _random = new Random();
        private static readonly object _lock = new object();
        
        // 设备指纹池 - 模拟真实设备特征
        private static readonly List<DeviceFingerprint> _devicePool = new List<DeviceFingerprint>();
        
        // 行为模式配置
        private static readonly BehaviorConfig _behaviorConfig = new BehaviorConfig();
        
        static BusBiliAntiDetection()
        {
            InitializeDevicePool();
            InitializeBehaviorPatterns();
        }

        #region 设备指纹伪造 (对抗25%权重检测)
        
        public class DeviceFingerprint
        {
            public string UserAgent { get; set; }
            public string Platform { get; set; }
            public string Language { get; set; }
            public string TimeZone { get; set; }
            public string ScreenResolution { get; set; }
            public string ColorDepth { get; set; }
            public string PixelRatio { get; set; }
            public string HardwareConcurrency { get; set; }
            public string DeviceMemory { get; set; }
            public List<string> Plugins { get; set; }
            public string WebGLVendor { get; set; }
            public string WebGLRenderer { get; set; }
            public string CanvasFingerprint { get; set; }
            public string AudioFingerprint { get; set; }
        }
        
        private static void InitializeDevicePool()
        {
            // 真实设备指纹池 - 基于统计数据的高频配置
            _devicePool.AddRange(new[]
            {
                new DeviceFingerprint
                {
                    UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    Platform = "Win32",
                    Language = "zh-CN,zh;q=0.9,en;q=0.8",
                    TimeZone = "Asia/Shanghai",
                    ScreenResolution = "1920x1080",
                    ColorDepth = "24",
                    PixelRatio = "1",
                    HardwareConcurrency = "8",
                    DeviceMemory = "8",
                    WebGLVendor = "Google Inc. (Intel)",
                    WebGLRenderer = "ANGLE (Intel, Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0, D3D11)",
                    Plugins = new List<string> { "PDF Viewer", "Chrome PDF Viewer", "Chromium PDF Viewer", "Microsoft Edge PDF Viewer", "WebKit built-in PDF" }
                },
                new DeviceFingerprint
                {
                    UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                    Platform = "Win32",
                    Language = "zh-CN,zh;q=0.9",
                    TimeZone = "Asia/Shanghai",
                    ScreenResolution = "1366x768",
                    ColorDepth = "24",
                    PixelRatio = "1",
                    HardwareConcurrency = "4",
                    DeviceMemory = "4",
                    WebGLVendor = "Google Inc. (NVIDIA)",
                    WebGLRenderer = "ANGLE (NVIDIA, NVIDIA GeForce GTX 1050 Ti Direct3D11 vs_5_0 ps_5_0, D3D11)",
                    Plugins = new List<string> { "PDF Viewer", "Chrome PDF Viewer", "Chromium PDF Viewer" }
                }
            });
            
            // 为每个设备生成唯一的Canvas和Audio指纹
            foreach (var device in _devicePool)
            {
                device.CanvasFingerprint = GenerateCanvasFingerprint(device);
                device.AudioFingerprint = GenerateAudioFingerprint(device);
            }
        }
        
        public static DeviceFingerprint GetRandomDevice()
        {
            lock (_lock)
            {
                return _devicePool[_random.Next(_devicePool.Count)];
            }
        }
        
        private static string GenerateCanvasFingerprint(DeviceFingerprint device)
        {
            // 基于设备特征生成一致的Canvas指纹
            var input = $"{device.UserAgent}{device.ScreenResolution}{device.WebGLRenderer}";
            using (var sha256 = SHA256.Create())
            {
                var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
                return Convert.ToBase64String(hash).Substring(0, 16);
            }
        }
        
        private static string GenerateAudioFingerprint(DeviceFingerprint device)
        {
            // 基于设备特征生成一致的Audio指纹
            var input = $"{device.Platform}{device.HardwareConcurrency}{device.WebGLVendor}";
            using (var sha256 = SHA256.Create())
            {
                var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
                return Convert.ToBase64String(hash).Substring(0, 12);
            }
        }
        
        #endregion
        
        #region 行为模拟引擎 (对抗20%权重检测)
        
        public class BehaviorConfig
        {
            public TimeSpan MinDelay { get; set; } = TimeSpan.FromMilliseconds(800);
            public TimeSpan MaxDelay { get; set; } = TimeSpan.FromMilliseconds(3000);
            public double VariabilityFactor { get; set; } = 0.3; // 30%的随机变化
            public int MaxBurstActions { get; set; } = 3; // 最大连续操作数
            public TimeSpan BurstCooldown { get; set; } = TimeSpan.FromSeconds(10);
        }
        
        private static DateTime _lastActionTime = DateTime.MinValue;
        private static int _burstActionCount = 0;
        
        public static async Task<TimeSpan> CalculateHumanDelay(string actionType = "default")
        {
            await Task.Delay(1); // 避免编译器警告
            
            lock (_lock)
            {
                var now = DateTime.Now;
                var timeSinceLastAction = now - _lastActionTime;
                
                // 检查是否需要冷却
                if (_burstActionCount >= _behaviorConfig.MaxBurstActions)
                {
                    if (timeSinceLastAction < _behaviorConfig.BurstCooldown)
                    {
                        var cooldownRemaining = _behaviorConfig.BurstCooldown - timeSinceLastAction;
                        _burstActionCount = 0; // 重置计数
                        return cooldownRemaining;
                    }
                    _burstActionCount = 0;
                }
                
                // 计算基础延迟
                var baseDelay = _behaviorConfig.MinDelay.TotalMilliseconds;
                var maxDelay = _behaviorConfig.MaxDelay.TotalMilliseconds;
                var range = maxDelay - baseDelay;
                
                // 添加随机性 - 使用正态分布模拟人类行为
                var randomFactor = GenerateNormalRandom(0.5, 0.2); // 均值0.5，标准差0.2
                randomFactor = Math.Max(0, Math.Min(1, randomFactor)); // 限制在[0,1]范围
                
                var delay = baseDelay + (range * randomFactor);
                
                // 根据操作类型调整延迟
                delay = AdjustDelayByActionType(delay, actionType);
                
                // 添加变化因子
                var variability = delay * _behaviorConfig.VariabilityFactor * (_random.NextDouble() - 0.5);
                delay += variability;
                
                _lastActionTime = now;
                _burstActionCount++;
                
                return TimeSpan.FromMilliseconds(Math.Max(100, delay)); // 最小100ms
            }
        }
        
        private static double GenerateNormalRandom(double mean, double stdDev)
        {
            // Box-Muller变换生成正态分布随机数
            var u1 = 1.0 - _random.NextDouble();
            var u2 = 1.0 - _random.NextDouble();
            var randStdNormal = Math.Sqrt(-2.0 * Math.Log(u1)) * Math.Sin(2.0 * Math.PI * u2);
            return mean + stdDev * randStdNormal;
        }
        
        private static double AdjustDelayByActionType(double baseDelay, string actionType)
        {
            return actionType.ToLower() switch
            {
                "login" => baseDelay * 1.5, // 登录操作更慢
                "cdkey" => baseDelay * 1.2, // 兑换操作稍慢
                "browse" => baseDelay * 0.8, // 浏览操作较快
                "click" => baseDelay * 0.6, // 点击操作快
                _ => baseDelay
            };
        }
        
        #endregion
        
        #region 网络层伪装 (对抗15%权重检测)
        
        public static Dictionary<string, string> GenerateAntiDetectionHeaders(DeviceFingerprint device)
        {
            var headers = new Dictionary<string, string>
            {
                ["User-Agent"] = device.UserAgent,
                ["Accept"] = "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                ["Accept-Language"] = device.Language,
                ["Accept-Encoding"] = "gzip, deflate, br",
                ["DNT"] = "1",
                ["Connection"] = "keep-alive",
                ["Upgrade-Insecure-Requests"] = "1",
                ["Sec-Fetch-Dest"] = "document",
                ["Sec-Fetch-Mode"] = "navigate",
                ["Sec-Fetch-Site"] = "none",
                ["Sec-Fetch-User"] = "?1",
                ["Cache-Control"] = "max-age=0"
            };
            
            // 添加随机的可选头部
            if (_random.NextDouble() > 0.5)
            {
                headers["sec-ch-ua"] = GenerateSecChUa(device.UserAgent);
                headers["sec-ch-ua-mobile"] = "?0";
                headers["sec-ch-ua-platform"] = $"\"{GetPlatformFromUA(device.UserAgent)}\"";
            }
            
            return headers;
        }
        
        private static string GenerateSecChUa(string userAgent)
        {
            // 从User-Agent提取Chrome版本并生成sec-ch-ua
            var chromeMatch = Regex.Match(userAgent, @"Chrome/(\d+)");
            if (chromeMatch.Success)
            {
                var version = chromeMatch.Groups[1].Value;
                return $"\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"{version}\", \"Google Chrome\";v=\"{version}\"";
            }
            return "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"";
        }
        
        private static string GetPlatformFromUA(string userAgent)
        {
            if (userAgent.Contains("Windows")) return "Windows";
            if (userAgent.Contains("Macintosh")) return "macOS";
            if (userAgent.Contains("Linux")) return "Linux";
            return "Windows";
        }
        
        private static void InitializeBehaviorPatterns()
        {
            // 初始化行为模式 - 预留扩展接口
        }
        
        #endregion
        
        #region 代理轮换机制 (对抗网络检测)
        
        public class ProxyRotationManager
        {
            private static readonly List<ProxyInfo> _proxyPool = new List<ProxyInfo>();
            private static int _currentProxyIndex = 0;
            
            public class ProxyInfo
            {
                public string Address { get; set; }
                public string Username { get; set; }
                public string Password { get; set; }
                public DateTime LastUsed { get; set; }
                public int FailureCount { get; set; }
                public bool IsActive { get; set; } = true;
            }
            
            public static ProxyInfo GetNextProxy()
            {
                lock (_lock)
                {
                    if (_proxyPool.Count == 0) return null;
                    
                    // 跳过失败次数过多的代理
                    var attempts = 0;
                    while (attempts < _proxyPool.Count)
                    {
                        var proxy = _proxyPool[_currentProxyIndex];
                        _currentProxyIndex = (_currentProxyIndex + 1) % _proxyPool.Count;
                        
                        if (proxy.IsActive && proxy.FailureCount < 3)
                        {
                            proxy.LastUsed = DateTime.Now;
                            return proxy;
                        }
                        attempts++;
                    }
                    
                    return null; // 所有代理都不可用
                }
            }
            
            public static void ReportProxyFailure(ProxyInfo proxy)
            {
                lock (_lock)
                {
                    proxy.FailureCount++;
                    if (proxy.FailureCount >= 5)
                    {
                        proxy.IsActive = false;
                    }
                }
            }
            
            public static void ReportProxySuccess(ProxyInfo proxy)
            {
                lock (_lock)
                {
                    proxy.FailureCount = Math.Max(0, proxy.FailureCount - 1);
                }
            }
        }
        
        #endregion
    }
}