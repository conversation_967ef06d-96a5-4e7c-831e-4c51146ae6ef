"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[3645,7865],{1577:function(e,t,a){a.r(t),a.d(t,{default:function(){return p}});var n=a(641),l=a(2644),s=a(9322);const i={class:"custom-tree-node el-tree-node__label"},r={class:"do"};function o(e,t,a,o,d,u){const c=(0,n.g2)("el-input"),m=(0,n.g2)("el-header"),h=(0,n.g2)("el-icon-plus"),p=(0,n.g2)("el-icon"),g=(0,n.g2)("el-tree"),f=(0,n.g2)("el-main"),b=(0,n.g2)("el-button"),k=(0,n.g2)("el-switch"),v=(0,n.g2)("el-form-item"),$=(0,n.g2)("el-footer"),y=(0,n.g2)("el-container"),F=(0,n.g2)("el-aside"),w=(0,n.g2)("save"),C=(0,n.gN)("loading");return(0,n.uX)(),(0,n.Wv)(y,null,{default:(0,n.k6)((()=>[(0,n.bo)(((0,n.uX)(),(0,n.Wv)(F,{width:"300px"},{default:(0,n.k6)((()=>[(0,n.bF)(y,null,{default:(0,n.k6)((()=>[(0,n.bF)(m,null,{default:(0,n.k6)((()=>[(0,n.bF)(c,{placeholder:"输入关键字进行过滤",modelValue:d.menuFilterText,"onUpdate:modelValue":t[0]||(t[0]=e=>d.menuFilterText=e),clearable:""},null,8,["modelValue"])])),_:1}),(0,n.bF)(f,{class:"nopadding"},{default:(0,n.k6)((()=>[(0,n.bF)(g,{ref:"menu",class:"menu","node-key":"id",data:d.menuList,props:d.menuProps,draggable:d.FDragDrop,"highlight-current":"","expand-on-click-node":!1,"check-strictly":"","show-checkbox":"","filter-node-method":u.menuFilterNode,onNodeClick:u.menuClick,onNodeDrop:u.nodeDrop},{default:(0,n.k6)((({node:e,data:t})=>[(0,n.Lk)("span",i,[(0,n.Lk)("span",{class:(0,l.C4)(u.TreeFountClass(e))},(0,l.v_)(e.label),3),(0,n.Lk)("span",r,[(0,n.bF)(p,{onClick:(0,s.D$)((a=>u.add(e,t)),["stop"])},{default:(0,n.k6)((()=>[(0,n.bF)(h)])),_:2},1032,["onClick"])])])])),_:1},8,["data","props","draggable","filter-node-method","onNodeClick","onNodeDrop"])])),_:1}),(0,n.bF)($,{style:{height:"51px"}},{default:(0,n.k6)((()=>[(0,n.bF)(b,{type:"primary",size:"small",icon:"el-icon-plus",style:{width:"42%"},onClick:t[1]||(t[1]=e=>u.add())},{default:(0,n.k6)((()=>[(0,n.eW)("添加")])),_:1}),(0,n.bF)(b,{type:"danger",size:"small",plan:"",icon:"el-icon-delete",style:{width:"18%"},onClick:u.delMenu},null,8,["onClick"]),(0,n.bF)(v,{class:"formitemMargin",label:"排序",prop:"meta.title","label-width":"50",style:{float:"right","margin-top":"-5px",width:"30%"}},{default:(0,n.k6)((()=>[(0,n.bF)(k,{modelValue:d.FDragDrop,"onUpdate:modelValue":t[2]||(t[2]=e=>d.FDragDrop=e),"active-value":!0,"inactive-value":!1},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1})),[[C,d.menuloading]]),(0,n.bF)(y,null,{default:(0,n.k6)((()=>[(0,n.bF)(f,{class:"nopadding",style:{padding:"20px"},ref:"main"},{default:(0,n.k6)((()=>[(0,n.bF)(w,{ref:"save",menu:d.menuList},null,8,["menu"])])),_:1},512)])),_:1})])),_:1})}var d=a(7138);let u=1;var c={name:"sysMenu",components:{save:d["default"]},data(){return{DMessagbox:"",menuloading:!1,menuList:[],menuProps:{label:e=>e.meta.title},menuFilterText:"",FDragDrop:!1}},watch:{menuFilterText(e){this.$refs.menu.filter(e)}},async mounted(){await this.getMenu(),this.$nextTick((()=>{this.$refs["menu"].setCurrentKey(100),this.$refs.save.setData(this.menuList[0],0,1),this.$refs.main.$el.scrollTop=0}))},methods:{async getMenu(){this.menuloading=!0;var e=await this.$API.sysMenu.getMenuList.post();this.menuloading=!1,this.menuList=e.data},menuClick(e,t){var a=1==t.level?void 0:t.parent.data.id;this.$refs.save.setData(e,a,1),this.$refs.main.$el.scrollTop=0},menuFilterNode(e,t){if(!e)return!0;var a=t.meta.title;return-1!==a.indexOf(e)},async nodeDrop(e,t,a){var n=await this.$API.sysMenu.sortMenu.post({jObjectParam:{draggingNodeId:e.data.id,dropNodeId:t.data.id,dropType:a}});0==n.code?this.$message.success("操作成功"):this.$alert(n.message,"提示",{type:"error"})},async add(e,t=null){var a="未命名"+u++,n={parentId:t?t.id:0,name:a,path:"",component:"",enable:"1",meta:{title:a,type:"menu"}};this.menuloading=!0;var l=await this.$API.sysMenu.getMenuId.post({jObjectSearch:{parentId:t?t.id:0}});this.menuloading=!1,n.id=l.data,this.$refs.menu.append(n,e),this.$refs.menu.setCurrentKey(n.id);var s=e?e.data.id:0;this.$refs.save.setData(n,s,0)},TreeFountClass(e){return"1"==e.data.enable?"label":"labelenable"},async delMenu(){var e=this.$refs.menu.getCheckedNodes();if(0==e.length)return this.$message.warning("请选择需要删除的项"),!1;var t="";let a="",n=!1;if(e.forEach(((l,s)=>{l.children&&l.children.length>0?(a+=`${l.meta.title},`,n=!0):s==e.length-1?t+=`${l.id}`:t+=`${l.id},`})),n)return this.$confirm(`${a}不可被删除。因为有子集菜单`,"提示",{confirmButtonText:"确定",showCancelButton:!1,type:"warning"}),!1;var l=await this.$prompt("是否确认删除？","提示",{inputPlaceholder:"请输入“确认删除”",type:"warning",confirmButtonText:"删除",cancelButtonText:"取消",inputValidator:e=>{if("确认删除"!=e)return"请输入“确认删除”"},confirmButtonClass:"el-button--danger"}).catch((()=>!1));if("confirm"!=l.action)return!1;this.menuloading=!0;var s=await this.$API.sysMenu.delMenu.post({jObjectParam:{id:t}});this.menuloading=!1,0==s.code?(e.forEach((e=>{var t=this.$refs.menu.getNode(e);t.isCurrent&&this.$refs.save.setData({}),this.$refs.menu.remove(e)})),this.$message.success("删除成功")):this.$alert(s.message,"提示",{type:"error"})}},computed:{}},m=a(6262);const h=(0,m.A)(c,[["render",o],["__scopeId","data-v-3b909742"]]);var p=h}}]);