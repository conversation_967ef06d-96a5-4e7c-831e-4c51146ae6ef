"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[3758],{2305:function(e,a,t){t.r(a),t.d(a,{default:function(){return b}});var l=t(641),r=t(2644);const o={class:"custom-tree-node",style:{margin:"5px",color:"#000",padding:"5px"}},i={class:"left-panel"},c={class:"right-panel"};function d(e,a,t,d,s,n){const h=(0,l.g2)("el-input"),u=(0,l.g2)("el-header"),b=(0,l.g2)("el-tree"),p=(0,l.g2)("el-main"),k=(0,l.g2)("el-container"),g=(0,l.g2)("el-aside"),y=(0,l.g2)("el-button"),m=(0,l.g2)("sc-select"),j=(0,l.g2)("el-option"),f=(0,l.g2)("el-select"),F=(0,l.g2)("el-table-column"),w=(0,l.g2)("scTable"),C=(0,l.gN)("loading");return(0,l.bo)(((0,l.uX)(),(0,l.Wv)(k,{"element-loading-text":"请稍等..."},{default:(0,l.k6)((()=>[(0,l.bF)(g,{width:"260px"},{default:(0,l.k6)((()=>[(0,l.bF)(k,null,{default:(0,l.k6)((()=>[(0,l.bF)(u,null,{default:(0,l.k6)((()=>[(0,l.bF)(h,{placeholder:"输入关键字进行过滤",modelValue:s.filterText,"onUpdate:modelValue":a[0]||(a[0]=e=>s.filterText=e),clearable:""},null,8,["modelValue"])])),_:1}),(0,l.bF)(p,{class:"nopadding"},{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(b,{ref:"tree",class:"custom-tree menu","node-key":"Fid",props:{label:"FName",value:"Fid"},data:s.treeData,"current-node-key":"0","check-on-click-node":!0,"show-checkbox":!0,"highlight-current":!0,"expand-on-click-node":!1,"filter-node-method":n.filterNode,onNodeClick:n.treeClick,"default-expand-all":!0},{default:(0,l.k6)((({data:e})=>[(0,l.Lk)("span",o,[(0,l.Lk)("span",{class:"label",style:(0,r.Tr)(""!=e.FStatusName?"color:red":"")},(0,r.v_)(e.FName),5)])])),_:1},8,["data","filter-node-method","onNodeClick"])),[[C,s.treeLoading]])])),_:1})])),_:1})])),_:1}),(0,l.bF)(k,null,{default:(0,l.k6)((()=>[(0,l.bF)(u,null,{default:(0,l.k6)((()=>[(0,l.Lk)("div",i,[(0,l.bF)(y,{type:"primary",icon:"el-icon-refresh",onClick:n.update,disabled:s.updateLoading},{default:(0,l.k6)((()=>[(0,l.eW)(" 更新")])),_:1},8,["onClick","disabled"]),(0,l.bF)(y,{type:"primary",icon:"el-icon-download",onClick:a[1]||(a[1]=e=>n.export1()),disabled:s.updateLoading},{default:(0,l.k6)((()=>[(0,l.eW)(" 导出勾选")])),_:1},8,["disabled"]),(0,l.bF)(m,{apiObj:[{value:"0",label:"待导出"},{value:"1",label:"已导出"}],modelValue:s.jObjectSearch.status,"onUpdate:modelValue":a[2]||(a[2]=e=>s.jObjectSearch.status=e),placeholder:"导出状态",onChange:n.upsearch,clearable:"",style:{width:"170px"}},null,8,["modelValue","onChange"]),(0,l.bF)(f,{modelValue:s.jObjectSearch.areaId,"onUpdate:modelValue":a[3]||(a[3]=e=>s.jObjectSearch.areaId=e),filterable:"",onChange:a[4]||(a[4]=e=>{s.jObjectSearch.activityId="",n.upsearch()}),style:{width:"170px"}},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(s.area,(e=>((0,l.uX)(),(0,l.Wv)(j,{key:e.Fid,label:e.FName,value:e.Fid},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),((0,l.uX)(),(0,l.Wv)(m,{apiObj:e.$API.douyuArea.GetAreaActivityIdList,modelValue:s.jObjectSearch.activityId,"onUpdate:modelValue":a[5]||(a[5]=e=>s.jObjectSearch.activityId=e),key:s.jObjectSearch.areaId,params:{jObjectSearch:{areaId:s.jObjectSearch.areaId}},placeholder:"活动名称",onChange:n.upsearch,clearable:"",style:{width:"170px"}},null,8,["apiObj","modelValue","params","onChange"]))]),(0,l.Lk)("div",c,[(0,l.bF)(h,{modelValue:s.jObjectSearch.search,"onUpdate:modelValue":a[6]||(a[6]=e=>s.jObjectSearch.search=e),placeholder:"名称 / Cdkey",class:"input-with-select",onChange:n.upsearch},null,8,["modelValue","onChange"]),(0,l.bF)(y,{type:"primary",onClick:n.upsearch,icon:"el-icon-search"},{default:(0,l.k6)((()=>[(0,l.eW)("查询")])),_:1},8,["onClick"])])])),_:1}),(0,l.bF)(p,{class:"nopadding"},{default:(0,l.k6)((()=>[(0,l.bF)(w,{ref:"table",apiObj:s.cdkeyApiObj,"row-key":"Fid",params:{jObjectSearch:s.jObjectSearch},onRowClick:n.rowClick,"row-Style":n.rowStyle,border:""},{default:(0,l.k6)((()=>[(0,l.bF)(F,{type:"selection",align:"center",width:"50"}),(0,l.bF)(F,{label:"导出状态",prop:"FStatusName",align:"center",width:"100"}),(0,l.bF)(F,{label:"分区名称",prop:"FAreaName",align:"center",width:"170"}),(0,l.bF)(F,{label:"账号名称",prop:"FCookieName",align:"center",width:"170","show-overflow-tooltip":""}),(0,l.bF)(F,{label:"任务名称",prop:"FTaskName",align:"center","show-overflow-tooltip":""}),(0,l.bF)(F,{label:"Cdkey名称",prop:"FName",align:"center",sortable:""}),(0,l.bF)(F,{label:"Cdkey",prop:"FCdkey",align:"center",width:"150"}),(0,l.bF)(F,{label:"领取时间",prop:"FDate",align:"center",width:"160",sortable:""})])),_:1},8,["apiObj","params","onRowClick","row-Style"])])),_:1})])),_:1})])),_:1})),[[C,s.updateLoading]])}t(8743);var s=t(1132),n={name:"douyuCdkey",data(){return{filterText:"",treeData:[],treeLoading:!0,cdkeyApiObj:null,updateLoading:!1,jObjectSearch:{search:"",areaId:"",status:"0",activityId:""},checkboxList:[],area:[]}},watch:{filterText(e){this.$refs.tree.filter(e)}},async created(){let e=await this.$API.douyuCookies.getCookiesList.post({jObjectSearch:{enable:1}});this.treeLoading=!1,this.treeData=[{FName:"全部账号",Fid:"0",FStatusName:"",children:e.data.rows}],e=await this.$API.douyuArea.getAreaList.post({jObjectSearch:{}}),0==e.code&&(this.area=e.data.rows,this.area.length>0&&(this.jObjectSearch.areaId=this.area[0].Fid)),this.cdkeyApiObj=this.$API.douyuCdkey.getCdkeyList},async mounted(){},methods:{filterNode(e,a){return!e||-1!==a.FName.indexOf(e)},treeClick(){this.jObjectSearch.cookieId=String(this.$refs.tree.getCheckedKeys()),this.upsearch()},upsearch(){this.checkboxList=[],this.$refs.table.upData({jObjectSearch:this.jObjectSearch})},async update(){let e=null;try{let a=this.$refs.tree.getCheckedKeys(),t=[];for(let e in a)t.push({Fid:a[e]});if(0==t.length)throw new Error("请选择账号！");e=s.Ks.service({lock:!0,text:"执行中...",background:"rgba(0, 0, 0, 0.7)"});let l=await this.$API.douyuQuartz.manualExecQuartz.post({jObjectParam:{array:t,areaId:this.jObjectSearch.areaId,jobName:"更新Cdkey列表",delay:1}});if(0!=l.code)throw new Error(l.message);this.$message.success(l.message),this.upsearch()}catch(a){this.$alert(a.message,"提示",{type:"error"})}e?.close()},rowClick(e){this.$refs.table.toggleRowSelection(e),this.checkboxList.indexOf(e.Fid)>-1?this.checkboxList.splice(this.checkboxList.indexOf(e.Fid),1):this.checkboxList.push(e.Fid)},rowStyle(e){if(this.checkboxList.indexOf(e.row.Fid)>-1)return{backgroundColor:"var(--el-table-current-row-bg-color) !important"}},async export1(){let e=this.$refs.table.getSelectionRows();if(e.length>0){const a=e.reduce(((e,a)=>e+","+a.Fid),"0");this.updateLoading=!0;let t=await this.$API.douyuCdkey.exportCdkeyList.post({jObjectSearch:{id:a}});this.updateLoading=!1;let l=document.createElement("a");l.style="display: none",l.target="_blank",l.download="cdkey",l.href=t.data,document.body.appendChild(l),l.click(),document.body.removeChild(l),this.upsearch(),console.log(t)}}}},h=t(6262);const u=(0,h.A)(n,[["render",d],["__scopeId","data-v-b0cfe3d4"]]);var b=u}}]);