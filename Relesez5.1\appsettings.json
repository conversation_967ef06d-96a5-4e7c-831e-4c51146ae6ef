{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "None"
    }
  },
  "AllowedHosts": "*",
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://localhost:50300" //双开使用必须修改，把IP改成局域网地址即可
      }
    }
  },
  "AppSettings": {
    "BiliLocalDB": "Data Source=(LocalDB)\\MSSQLLocalDB;AttachDbFilename={CurDir}\\Data\\ManageBiliDB.mdf;Integrated Security=True;Connect Timeout=30;Encrypt=True",
    "DouYuLocalDB": "Data Source=(LocalDB)\\MSSQLLocalDB;AttachDbFilename={CurDir}\\Data\\ManageDouYuDB.mdf;Integrated Security=True;Connect Timeout=30;Encrypt=True",
    "KSLocalDB": "Data Source=(LocalDB)\\MSSQLLocalDB;AttachDbFilename={CurDir}\\Data\\ManageKSDB.mdf;Integrated Security=True;Connect Timeout=30;Encrypt=True",
    "ServerUrl": "http://***************:50186" //服务器地址
  },
  "VueConfig": {
    "ApiUrl": "http://localhost:50300", //双开使用必须修改，如果放在本地使用改成局域网IP即可，如果是服务器使用改成服务器外网IP
    "AppKey": "88888888", //双开使用必须修改，长度为8的倍数
    "AppName": "抢码小工具"//脚本名称
  }
}