"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[8530],{1311:function(e,a,l){l.r(a),l.d(a,{default:function(){return b}});var t=l(641),i=l(2644);const r={class:"left-panel"},n={class:"right-panel"},o={class:"right-panel-search"};function s(e,a,l,s,c,d){const u=(0,t.g2)("sc-select"),h=(0,t.g2)("el-button"),b=(0,t.g2)("el-input"),p=(0,t.g2)("el-header"),g=(0,t.g2)("el-table-column"),F=(0,t.g2)("el-switch"),m=(0,t.g2)("el-popconfirm"),w=(0,t.g2)("el-button-group"),f=(0,t.g2)("scTable"),v=(0,t.g2)("el-main"),j=(0,t.g2)("el-container"),k=(0,t.g2)("saveDialog"),y=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)(t.FK,null,[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(j,null,{default:(0,t.k6)((()=>[(0,t.bF)(p,null,{default:(0,t.k6)((()=>[(0,t.Lk)("div",r,[(0,t.bF)(u,{clearable:"",modelValue:c.jObjectSearch.listen,"onUpdate:modelValue":a[0]||(a[0]=e=>c.jObjectSearch.listen=e),apiObj:[{label:"直播间",value:"直播间"},{label:"弹幕礼物观看",value:"弹幕礼物观看"}],placeholder:"弹幕类型",onChange:d.upsearch,style:{width:"100%","margin-right":"10px"}},null,8,["modelValue","onChange"]),(0,t.bF)(u,{clearable:"",modelValue:c.jObjectSearch.trigger,"onUpdate:modelValue":a[1]||(a[1]=e=>c.jObjectSearch.trigger=e),apiObj:[{label:"收到弹幕",value:"收到弹幕"},{label:"收到礼物",value:"收到礼物"},{label:"进入直播间",value:"进入直播间"},{label:"开始直播",value:"开始直播"},{label:"结束直播",value:"结束直播"}],placeholder:"触发方式",onChange:d.upsearch,style:{width:"100%","margin-right":"10px"}},null,8,["modelValue","onChange"]),(0,t.bF)(h,{type:"primary",icon:"el-icon-plus",onClick:a[2]||(a[2]=e=>d.add())},{default:(0,t.k6)((()=>[(0,t.eW)("新增")])),_:1}),(0,t.bF)(h,{type:"primary",icon:"el-icon-refresh",onClick:a[3]||(a[3]=e=>d.update())},{default:(0,t.k6)((()=>[(0,t.eW)("更新")])),_:1})]),(0,t.Lk)("div",n,[(0,t.Lk)("div",o,[(0,t.bF)(b,{modelValue:c.jObjectSearch.search,"onUpdate:modelValue":a[4]||(a[4]=e=>c.jObjectSearch.search=e),placeholder:"名称/地址",clearable:""},null,8,["modelValue"]),(0,t.bF)(h,{type:"primary",icon:"el-icon-search",onClick:d.upsearch},{default:(0,t.k6)((()=>[(0,t.eW)(" 查询")])),_:1},8,["onClick"])])])])),_:1}),(0,t.bF)(v,{class:"nopadding"},{default:(0,t.k6)((()=>[(0,t.bF)(f,{ref:"table",apiObj:c.apiObj,border:"",params:{jObjectSearch:c.jObjectSearch},stripe:"",remoteSort:"",remoteFilter:""},{default:(0,t.k6)((()=>[(0,t.bF)(g,{type:"selection",width:"50"}),(0,t.bF)(g,{label:"弹幕类型",prop:"FListen",align:"center",width:"110"}),(0,t.bF)(g,{label:"触发方式",prop:"FTrigger",align:"center",width:"110"}),(0,t.bF)(g,{label:"逻辑关系",prop:"FLogic",align:"center",width:"80"}),(0,t.bF)(g,{label:"监听内容",prop:"FKeyword",align:"center",width:"200"},{default:(0,t.k6)((e=>[(0,t.eW)((0,i.v_)(e.row.FKeyword1+(e.row.FKeyword2?","+e.row.FKeyword2:"")+(e.row.FKeyword3?","+e.row.FKeyword3:"")),1)])),_:1}),(0,t.bF)(g,{label:"发送内容",prop:"FMsg","header-align":"center"}),(0,t.bF)(g,{label:"游戏分区",prop:"FAreaName","header-align":"center",width:"200","show-overflow-tooltip":""}),(0,t.bF)(g,{label:"账号名称",prop:"FCookieName","header-align":"center","show-overflow-tooltip":""}),(0,t.bF)(g,{label:"系统默认",prop:"FSysName",align:"center",width:"80"}),(0,t.bF)(g,{label:"是否启用",prop:"FEnable","header-align":"center",width:"75"},{default:(0,t.k6)((e=>[(0,t.bF)(F,{modelValue:e.row.FEnable,"onUpdate:modelValue":a=>e.row.FEnable=a,onChange:a=>d.enableSwitch(a,e.row),loading:e.row.$enable,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue","onChange","loading"])])),_:1}),(0,t.bF)(g,{label:"操作",fixed:"right","header-align":"center",align:"left",width:"130"},{default:(0,t.k6)((e=>[(0,t.bF)(w,null,{default:(0,t.k6)((()=>[1!=e.row.FSys?((0,t.uX)(),(0,t.Wv)(h,{key:0,text:"",type:"primary",size:"small",onClick:a=>d.edit(e.row,e.$index)},{default:(0,t.k6)((()=>[(0,t.eW)("编辑")])),_:2},1032,["onClick"])):(0,t.Q3)("",!0),(0,t.bF)(m,{title:"确定删除吗？",onConfirm:a=>d.del(e.row,e.$index)},{reference:(0,t.k6)((()=>[(0,t.bF)(h,{text:"",type:"danger",size:"small"},{default:(0,t.k6)((()=>[(0,t.eW)("删除")])),_:1})])),_:2},1032,["onConfirm"])])),_:2},1024)])),_:1})])),_:1},8,["apiObj","params"])])),_:1})])),_:1})),[[y,c.updateLoading]]),c.dialog.save?((0,t.uX)(),(0,t.Wv)(k,{key:0,ref:"saveDialog",onSuccess:d.upsearch,onClosed:a[5]||(a[5]=e=>c.dialog.save=!1)},null,8,["onSuccess"])):(0,t.Q3)("",!0)],64)}var c=l(302),d={name:"biliBulletSceen",components:{saveDialog:c["default"]},data(){return{apiObj:this.$API.biliBulletSceen.getBulletSceenList,updateLoading:!1,dialog:{save:!1},jObjectSearch:{search:""}}},async created(){},methods:{add(){this.dialog.save=!0,this.$nextTick((()=>{this.$refs.saveDialog.open("add")}))},edit(e){this.dialog.save=!0,this.$nextTick((()=>{this.$refs.saveDialog.open("edit").setData(e)}))},async del(e){let a=await this.$API.biliBulletSceen.delBulletSceen.post({jObjectParam:e});0==a.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(a.message,"提示",{type:"error"})},async update(){this.updateLoading=!0;let e=await this.$API.biliBulletSceen.updateBulletSceen.post();this.updateLoading=!1,0==e.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(e.message,"提示",{type:"error"})},async enableSwitch(e,a){a.$enable=!0;let l=await this.$API.biliBulletSceen.editBulletSceen.post({jObjectParam:{Fid:a.Fid,FEnable:a.FEnable}});0==l.code?this.$message.success("操作成功"):this.$alert(l.message,"提示",{type:"error"}),this.upsearch()},upsearch(){this.$refs.table.upData({jObjectSearch:this.jObjectSearch})}}},u=l(6262);const h=(0,u.A)(d,[["render",s]]);var b=h}}]);