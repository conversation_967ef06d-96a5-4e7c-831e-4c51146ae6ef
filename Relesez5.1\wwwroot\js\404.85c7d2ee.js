"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[2389],{9080:function(o,r,n){n.r(r),n.d(r,{default:function(){return g}});var e=n(641);const t=o=>((0,e.Qi)("data-v-10b4695c"),o=o(),(0,e.jt)(),o),i={class:"router-err"},c=t((()=>(0,e.Lk)("div",{class:"router-err__icon"},[(0,e.Lk)("img",{src:"img/404.png"})],-1))),u={class:"router-err__content"},l=t((()=>(0,e.Lk)("h2",null,"无权限或找不到页面",-1))),a=t((()=>(0,e.Lk)("p",null,"当前页面无权限访问或者打开了一个不存在的链接，请检查当前账户权限和链接的可访问性。",-1)));function s(o,r,n,t,s,k){const d=(0,e.g2)("el-button");return(0,e.uX)(),(0,e.CE)("div",i,[c,(0,e.Lk)("div",u,[l,a,(0,e.bF)(d,{type:"primary",plain:"",round:"",onClick:k.gohome},{default:(0,e.k6)((()=>[(0,e.eW)("返回首页")])),_:1},8,["onClick"]),(0,e.bF)(d,{type:"primary",plain:"",round:"",onClick:k.gologin},{default:(0,e.k6)((()=>[(0,e.eW)("重新登录")])),_:1},8,["onClick"]),(0,e.bF)(d,{type:"primary",round:"",onClick:k.goback},{default:(0,e.k6)((()=>[(0,e.eW)("返回上一页")])),_:1},8,["onClick"])])])}n(8743);var k={methods:{gohome(){location.href="#/"},goback(){this.$router.go(-1)},gologin(){this.$router.push("/login")}}},d=n(6262);const p=(0,d.A)(k,[["render",s],["__scopeId","data-v-10b4695c"]]);var g=p}}]);