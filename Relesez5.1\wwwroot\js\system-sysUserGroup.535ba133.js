"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[8998,3420],{2822:function(e,t,a){a.r(t),a.d(t,{default:function(){return v}});var s=a(641),i=a(2644),o=a(9322);const n={class:"custom-tree-node"},r={class:"label"},l={key:0,class:"do"},d={key:1},c={class:"left-panel"},h={class:"slider-demo-block",style:{"padding-left":"10px","margin-right":"5px"}},p={class:"demonstration"},u={class:"slider-demo-block",style:{"padding-left":"10px","margin-right":"5px"}},g={class:"demonstration"},b={class:"slider-demo-block",style:{"padding-left":"10px","margin-right":"5px"}},k={class:"demonstration"},C={class:"right-panel"};function F(e,t,a,F,y,m){const f=(0,s.g2)("el-input"),w=(0,s.g2)("el-header"),_=(0,s.g2)("el-icon-edit"),v=(0,s.g2)("el-icon"),x=(0,s.g2)("el-icon-delete"),$=(0,s.g2)("el-tree"),j=(0,s.g2)("el-main"),S=(0,s.g2)("el-container"),O=(0,s.g2)("el-aside"),T=(0,s.g2)("el-button"),D=(0,s.g2)("el-table-column"),U=(0,s.g2)("scTable"),L=(0,s.g2)("save-dialog"),M=(0,s.g2)("show-dialog"),E=(0,s.gN)("loading"),I=(0,s.gN)("auth");return(0,s.uX)(),(0,s.CE)(s.FK,null,[(0,s.bF)(S,null,{default:(0,s.k6)((()=>[(0,s.bo)(((0,s.uX)(),(0,s.Wv)(O,{width:"250px"},{default:(0,s.k6)((()=>[(0,s.bF)(S,null,{default:(0,s.k6)((()=>[(0,s.bF)(w,null,{default:(0,s.k6)((()=>[(0,s.bF)(f,{placeholder:"输入关键字进行过滤",modelValue:y.treeSearch,"onUpdate:modelValue":t[0]||(t[0]=e=>y.treeSearch=e),onChange:m.treeSearchChange,clearable:""},null,8,["modelValue","onChange"])])),_:1}),(0,s.bF)(j,{class:"nopadding"},{default:(0,s.k6)((()=>[(0,s.bF)($,{ref:"tree",class:"menu","node-key":"id",data:y.treeData,"highlight-current":!0,"expand-on-click-node":!1,onNodeClick:m.treeClick},{default:(0,s.k6)((({data:e})=>[(0,s.Lk)("span",n,[(0,s.Lk)("span",r,(0,i.v_)(e.label),1),e.FCount?((0,s.uX)(),(0,s.CE)("span",d,(0,i.v_)(e.FCount),1)):((0,s.uX)(),(0,s.CE)("span",l,[(0,s.bF)(v,{onClick:(0,o.D$)((t=>m.btn_edit(e)),["stop"]),style:{width:"30px",height:"60px"}},{default:(0,s.k6)((()=>[(0,s.bF)(_)])),_:2},1032,["onClick"]),(0,s.bF)(v,{onClick:(0,o.D$)((t=>m.btn_del(e)),["stop"]),style:{width:"30px",height:"60px"}},{default:(0,s.k6)((()=>[(0,s.bF)(x)])),_:2},1032,["onClick"])]))])])),_:1},8,["data","onNodeClick"])])),_:1})])),_:1})])),_:1})),[[E,y.loading]]),(0,s.bF)(S,{class:"is-vertical"},{default:(0,s.k6)((()=>[(0,s.bF)(w,null,{default:(0,s.k6)((()=>[(0,s.Lk)("div",c,[(0,s.bo)(((0,s.uX)(),(0,s.Wv)(T,{type:"primary",icon:"el-icon-plus",onClick:m.btn_add},{default:(0,s.k6)((()=>[(0,s.eW)(" 新增分组")])),_:1},8,["onClick"])),[[I,"sysUser.add"]]),(0,s.bo)(((0,s.uX)(),(0,s.Wv)(T,{type:"primary",onClick:t[1]||(t[1]=e=>m.expirationTime(41))},{default:(0,s.k6)((()=>[(0,s.eW)(" 增加40天时间")])),_:1})),[[I,"sysUser.add"]]),(0,s.bo)(((0,s.uX)(),(0,s.Wv)(T,{type:"primary",onClick:t[2]||(t[2]=e=>m.expirationTime())},{default:(0,s.k6)((()=>[(0,s.eW)(" 手动设置时间")])),_:1})),[[I,"sysUser.add"]]),(0,s.Lk)("div",h,[(0,s.Lk)("span",p," 到期时间："+(0,i.v_)(y.FExpirationTime),1)]),(0,s.Lk)("div",u,[(0,s.Lk)("span",g," 额定："+(0,i.v_)(y.FCookiesCount)+"、实际："+(0,i.v_)(y.FCookiesCount2)+"、到期："+(0,i.v_)(y.FCookiesCount3),1)]),(0,s.Lk)("div",b,[(0,s.Lk)("span",k,"补退金额："+(0,i.v_)((0==y.FMoney2?"":y.FMoney2>0?"补":"退")+Math.abs(y.FMoney2))+" 、到期付款："+(0,i.v_)(y.FMoney3),1)])]),(0,s.Lk)("div",C,[(0,s.bF)(f,{modelValue:y.jObjectSearch.search,"onUpdate:modelValue":t[3]||(t[3]=e=>y.jObjectSearch.search=e),placeholder:"模糊查询",class:"input-with-select",onChange:m.btn_search},null,8,["modelValue","onChange"]),(0,s.bF)(T,{type:"primary",onClick:m.btn_search,icon:"el-icon-search"},{default:(0,s.k6)((()=>[(0,s.eW)("查询")])),_:1},8,["onClick"])])])),_:1}),(0,s.bF)(j,{class:"nopadding"},{default:(0,s.k6)((()=>[(0,s.bF)(U,{ref:"table",apiObj:y.listApi,"row-key":"Fid",params:{jObjectSearch:y.jObjectSearch},border:"",pageSize:100,stripe:""},{default:(0,s.k6)((()=>[(0,s.bF)(D,{type:"selection",width:"50"}),(0,s.bF)(D,{label:"#",type:"index",width:"50",align:"center"}),(0,s.bF)(D,{label:"用户账号",width:"160","show-overflow-tooltip":"",prop:"FUserCode",align:"center"}),(0,s.bF)(D,{label:"账号标识",width:"300",prop:"FKey",align:"center"}),(0,s.bF)(D,{label:"B站标识",width:"180",prop:"FIdentifying",align:"center"}),(0,s.bF)(D,{label:"到期时间",width:"200",prop:"FExpirationTime",align:"center"}),(0,s.bF)(D,{label:"操作时间",width:"200",prop:"FDate",align:"center"}),(0,s.bo)(((0,s.uX)(),(0,s.Wv)(D,{label:"操作",fixed:"left",align:"center",width:"80"},{default:(0,s.k6)((e=>[(0,s.bF)(T,{link:"",size:"small",type:"success",onClick:t=>m.btn_show(e.row),plain:""},{default:(0,s.k6)((()=>[(0,s.eW)("查看")])),_:2},1032,["onClick"])])),_:1})),[[I,"sysUser.edit"]])])),_:1},8,["apiObj","params"])])),_:1})])),_:1})])),_:1}),y.dialog.save?((0,s.uX)(),(0,s.Wv)(L,{key:0,ref:"saveDialog",onSuccess:m.treeSearchChange,onClosed:t[4]||(t[4]=e=>y.dialog.save=!1)},null,8,["onSuccess"])):(0,s.Q3)("",!0),y.dialog.show?((0,s.uX)(),(0,s.Wv)(M,{key:1,ref:"showDialog",onSuccess:t[5]||(t[5]=e=>{m.btn_search()}),onClosed:t[6]||(t[6]=e=>y.dialog.show=!1)},null,512)):(0,s.Q3)("",!0)],64)}var y=a(440),m=a(615),f={name:"sysUserGroup",components:{saveDialog:y["default"],showDialog:m["default"]},data(){return{dialog:{save:!1},loading:!0,treeData:[],treeSearch:"",listApi:this.$API.sysUserGroup.getUserCookieList,jObjectSearch:{},FExpirationTime:"",FCookiesCount:0,FCookiesCount2:0,FCookiesCount3:0,FMoney2:0,FMoney3:0}},async mounted(){await this.treeSearchChange()},methods:{async treeSearchChange(e=""){this.loading=!0;var t=await this.$API.sysUserGroup.getSysUserGroupList.post({jObjectSearch:{search:this.treeSearch}});this.loading=!1,this.treeData=t.data,this.treeData.length>0&&(e||(e=this.treeData[0].id),this.$nextTick((()=>{this.$refs["tree"].setCurrentKey(e);let t=this.$refs.tree.getCurrentNode();this.treeClick(t)}))),this.btn_search()},async getUserCookieList(e,t){let a=await this.$API.sysUserGroup.getUserCookieList.post({jObjectSearch:{userId:e,id:t}});this.FCookiesCount3=a.data.rows[0].FExpirationCount,this.FCookiesCount2=a.data.rows[0].FCount,this.FMoney2=a.data.rows[0].FMoney,this.FMoney3=a.data.rows[0].FExpirationMoney},async treeClick(e){this.jObjectSearch.userId=e.FUserId,this.FCookiesCount=e.FCookiesCount,this.FExpirationTime=String(e.FExpirationTime).split(" ")[0],this.$refs.table.reload({jObjectSearch:this.jObjectSearch}),this.getUserCookieList(e.FUserId,e.Fid)},btn_show(e){this.dialog.show=!0,this.$nextTick((()=>{this.$refs.showDialog.open("show").setData(e)}))},btn_add(){this.dialog.save=!0,this.$nextTick((()=>{this.$refs.saveDialog.open("add")}))},btn_edit(e){this.dialog.save=!0,this.$nextTick((()=>{this.$refs.saveDialog.open("edit").setData(e)}))},expirationTime(e){let t=this.$refs.tree.getCurrentNode();e?this.$confirm("确认给 "+t.label+" 增加40天吗？","提示",{type:"warning"}).then((async()=>{let a="";a=new Date(t.FExpirationTime)>new Date?this.$TOOL.dateFormat(new Date(t.FExpirationTime),"yyyy-MM-dd",e):this.$TOOL.dateFormat(new Date,"yyyy-MM-dd",e);let s=await this.$API.sysUserGroup.setExpirationTime.post({jObjectParam:{id:this.$refs.tree.getCurrentNode().Fid,expirationTime:a,index:this.FCookiesCount2}});0==s.code?(this.$message.success("操作成功"),this.treeSearchChange()):this.$alert(s.message,"提示",{type:"error"})})).catch((()=>{})):this.$prompt("","设置到期时间",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnPressEscape:!0,closeOnClickModal:!0,autofocus:!0,inputPattern:/^\d{4}-\d{2}-\d{2}$/,inputErrorMessage:"请输入正确的时间"}).then((async({value:e})=>{let t=await this.$API.sysUserGroup.setExpirationTime.post({jObjectParam:{id:this.$refs.tree.getCurrentNode().Fid,expirationTime:e,index:this.FCookiesCount2}});0==t.code?(this.$message.success("操作成功"),this.treeSearchChange()):this.$alert(t.message,"提示",{type:"error"})}))},btn_del(e){this.$confirm(`确定删除 ${e.FName} 吗？`,"提示",{type:"warning"}).then((async()=>{var t={jObjectParam:{id:e.Fid}},a=await this.$API.sysUserGroup.del.post(t);0==a.code?(await this.treeSearchChange(),this.$message.success(a.message)):this.$alert(a.message,"提示",{type:"error"})})).catch((()=>{}))},btn_search(){this.$refs.table.reload({jObjectSearch:this.jObjectSearch})}}},w=a(6262);const _=(0,w.A)(f,[["render",F],["__scopeId","data-v-11cde7a5"]]);var v=_}}]);