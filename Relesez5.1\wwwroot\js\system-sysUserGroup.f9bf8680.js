"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[8998],{1673:function(e,t,a){a.r(t),a.d(t,{default:function(){return F}});var l=a(641),s=a(2644),i=a(9322);const n={class:"custom-tree-node"},r={class:"label"},o={key:0,class:"do"},c={key:1},d={class:"left-panel"},h={class:"right-panel"};function u(e,t,a,u,p,b){const g=(0,l.g2)("el-input"),k=(0,l.g2)("el-header"),F=(0,l.g2)("el-icon-edit"),f=(0,l.g2)("el-icon"),y=(0,l.g2)("el-icon-delete"),C=(0,l.g2)("el-tree"),m=(0,l.g2)("el-main"),_=(0,l.g2)("el-container"),j=(0,l.g2)("el-aside"),w=(0,l.g2)("el-button"),v=(0,l.g2)("el-button-group"),S=(0,l.g2)("el-table-column"),O=(0,l.g2)("scTable"),$=(0,l.g2)("save-dialog"),x=(0,l.gN)("loading"),D=(0,l.gN)("auth");return(0,l.uX)(),(0,l.CE)(l.FK,null,[(0,l.bF)(_,null,{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(j,{width:"250px"},{default:(0,l.k6)((()=>[(0,l.bF)(_,null,{default:(0,l.k6)((()=>[(0,l.bF)(k,null,{default:(0,l.k6)((()=>[(0,l.bF)(g,{placeholder:"输入关键字进行过滤",modelValue:p.treeSearch,"onUpdate:modelValue":t[0]||(t[0]=e=>p.treeSearch=e),onChange:b.treeSearchChange,clearable:""},null,8,["modelValue","onChange"])])),_:1}),(0,l.bF)(m,{class:"nopadding"},{default:(0,l.k6)((()=>[(0,l.bF)(C,{ref:"tree",class:"menu","node-key":"id",data:p.treeData,"highlight-current":!0,"expand-on-click-node":!1,onNodeClick:b.treeClick},{default:(0,l.k6)((({data:t})=>[(0,l.Lk)("span",n,[(0,l.Lk)("span",r,(0,s.v_)(t.label),1),t.FCount?((0,l.uX)(),(0,l.CE)("span",c,(0,s.v_)(t.FCount),1)):((0,l.uX)(),(0,l.CE)("span",o,[(0,l.bF)(f,{onClick:(0,i.D$)((a=>e.btn_edit(t)),["stop"]),style:{width:"30px",height:"60px"}},{default:(0,l.k6)((()=>[(0,l.bF)(F)])),_:2},1032,["onClick"]),(0,l.bF)(f,{onClick:(0,i.D$)((e=>b.btn_del(t)),["stop"]),style:{width:"30px",height:"60px"}},{default:(0,l.k6)((()=>[(0,l.bF)(y)])),_:2},1032,["onClick"])]))])])),_:1},8,["data","onNodeClick"])])),_:1})])),_:1})])),_:1})),[[x,p.loading]]),(0,l.bF)(_,{class:"is-vertical"},{default:(0,l.k6)((()=>[(0,l.bF)(k,null,{default:(0,l.k6)((()=>[(0,l.Lk)("div",d,[(0,l.bF)(v,{style:{"padding-right":"15px"}},{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(w,{type:"primary",icon:"el-icon-plus",onClick:b.btn_add},{default:(0,l.k6)((()=>[(0,l.eW)(" 新增")])),_:1},8,["onClick"])),[[D,"sysUser.add"]])])),_:1})]),(0,l.Lk)("div",h,[(0,l.bF)(g,{modelValue:p.jObjectSearch.search,"onUpdate:modelValue":t[1]||(t[1]=e=>p.jObjectSearch.search=e),placeholder:"模糊查询",class:"input-with-select",onChange:b.btn_search},null,8,["modelValue","onChange"]),(0,l.bF)(w,{type:"primary",onClick:b.btn_search,icon:"el-icon-search"},{default:(0,l.k6)((()=>[(0,l.eW)("查询")])),_:1},8,["onClick"])])])),_:1}),(0,l.bF)(m,{class:"nopadding"},{default:(0,l.k6)((()=>[(0,l.bF)(O,{ref:"table",apiObj:p.listApi,"row-key":"Fid",params:{jObjectSearch:p.jObjectSearch},border:"",pageSize:100,stripe:""},{default:(0,l.k6)((()=>[(0,l.bF)(S,{type:"selection",width:"50"}),(0,l.bF)(S,{label:"#",type:"index",width:"50",align:"center"}),(0,l.bF)(S,{label:"用户账号",width:"160","show-overflow-tooltip":"",prop:"FUserCode",align:"center"}),(0,l.bF)(S,{label:"账号标识",width:"300",prop:"FKey",align:"center"}),(0,l.bF)(S,{label:"B站标识",width:"180",prop:"FIdentifying",align:"center"}),(0,l.bF)(S,{label:"到期时间",width:"200",prop:"FExpirationTime",align:"center"}),(0,l.bF)(S,{label:"操作时间",width:"200",prop:"FDate",align:"center"}),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(S,{label:"操作",fixed:"right",align:"center",width:"180"},{default:(0,l.k6)((t=>[(0,l.bF)(w,{link:"",size:"small",type:"success",onClick:a=>e.btn_show(t.row),plain:""},{default:(0,l.k6)((()=>[(0,l.eW)("查看")])),_:2},1032,["onClick"]),(0,l.bF)(w,{link:"",size:"small",type:"primary",onClick:e=>b.expirationTime(t.row),plain:""},{default:(0,l.k6)((()=>[(0,l.eW)("设置到期时间")])),_:2},1032,["onClick"])])),_:1})),[[D,"sysUser.edit"]])])),_:1},8,["apiObj","params"])])),_:1})])),_:1})])),_:1}),p.dialog.save?((0,l.uX)(),(0,l.Wv)($,{key:0,ref:"saveDialog",onSuccess:t[2]||(t[2]=e=>{b.treeSearchChange()}),onClosed:t[3]||(t[3]=e=>p.dialog.save=!1)},null,512)):(0,l.Q3)("",!0)],64)}var p=a(9325),b={name:"sysUserGroup",components:{saveDialog:p["default"]},data(){return{dialog:{save:!1},loading:!0,treeData:[],treeSearch:"",listApi:this.$API.sysUserGroup.getUserCookieList,jObjectSearch:{},selection:[],selectGroupId:0,selectGroupSys:1}},async mounted(){await this.treeSearchChange()},methods:{async treeSearchChange(){this.loading=!0;var e=await this.$API.sysUserGroup.getSysUserGroupList.post({jObjectSearch:{search:this.treeSearch}});this.loading=!1,this.treeData=e.data,this.treeData.length>0&&(this.jObjectSearch.userId=this.treeData[0].FUserId,this.$nextTick((()=>{this.$refs["tree"].setCurrentKey(this.treeData[0].id)}))),this.btn_search()},treeClick(e){this.jObjectSearch.userId=e.FUserId,this.$refs.table.reload({jObjectSearch:this.jObjectSearch})},btn_add(){this.dialog.save=!0,this.$nextTick((()=>{this.$refs.saveDialog.open("add")}))},expirationTime(e){this.$prompt("","设置到期时间",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnPressEscape:!0,closeOnClickModal:!0,autofocus:!0,inputValue:this.$TOOL.dateFormat(new Date(e.FExpirationTime),"yyyy-MM-dd"),inputPattern:/^\d{4}-\d{2}-\d{2}$/,inputErrorMessage:"请输入正确的时间"}).then((async({value:t})=>{console.log(e,t)}))},btn_del(e){this.$confirm(`确定删除 ${e.FName} 吗？`,"提示",{type:"warning"}).then((async()=>{var t={jObjectParam:{id:e.Fid}},a=await this.$API.sysUserGroup.del.post(t);0==a.code?(await this.treeSearchChange(),this.$message.success(a.message)):this.$alert(a.message,"提示",{type:"error"})})).catch((()=>{}))},btn_search(){this.$refs.table.reload({jObjectSearch:this.jObjectSearch})}}},g=a(6262);const k=(0,g.A)(b,[["render",u],["__scopeId","data-v-0bbbda04"]]);var F=k}}]);