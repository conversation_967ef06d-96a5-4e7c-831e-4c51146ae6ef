"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[7962],{2793:function(e,n,o){o.r(n),o.d(n,{default:function(){return h}});var t=o(641);const d={key:0};function a(e,n,o,a,u,s){const r=(0,t.g2)("el-skeleton"),l=(0,t.g2)("el-card"),i=(0,t.g2)("el-main"),h=(0,t.g2)("work"),c=(0,t.g2)("widgets");return(0,t.uX)(),(0,t.CE)(t.FK,null,[u.pageLoading?((0,t.uX)(),(0,t.CE)("div",d,[(0,t.bF)(i,null,{default:(0,t.k6)((()=>[(0,t.bF)(l,{shadow:"never"},{default:(0,t.k6)((()=>[(0,t.bF)(r,{rows:1})])),_:1}),(0,t.bF)(l,{shadow:"never",style:{"margin-top":"15px"}},{default:(0,t.k6)((()=>[(0,t.bF)(r)])),_:1})])),_:1})])):(0,t.Q3)("",!0),"1"==u.dashboard?((0,t.uX)(),(0,t.Wv)(h,{key:1,onOnMounted:s.onMounted},null,8,["onOnMounted"])):((0,t.uX)(),(0,t.Wv)(c,{key:2,onOnMounted:s.onMounted},null,8,["onOnMounted"]))],64)}const u=(0,t.$V)((()=>Promise.all([o.e(6158),o.e(8749),o.e(7212)]).then(o.bind(o,8049)))),s=(0,t.$V)((()=>Promise.all([o.e(7219),o.e(6158),o.e(8749),o.e(1170)]).then(o.bind(o,6912))));var r={name:"dashboard",components:{work:u,widgets:s},data(){return{pageLoading:!0,dashboard:"0"}},created(){this.dashboard=this.$TOOL.data.get("USER_INFO").dashboard||"0"},mounted(){},methods:{onMounted(){this.pageLoading=!1}}},l=o(6262);const i=(0,l.A)(r,[["render",a]]);var h=i}}]);