"use strict";(self["webpackChunkscui"]=self["webpackChunkscui"]||[]).push([[689],{261:function(e,l,a){a.r(l),a.d(l,{default:function(){return b}});var t=a(641);const o={class:"left-panel"},i={class:"right-panel"},s={class:"right-panel-search"};function r(e,l,a,r,n,c){const d=(0,t.g2)("el-button"),p=(0,t.g2)("el-input"),b=(0,t.g2)("el-header"),h=(0,t.g2)("el-table-column"),u=(0,t.g2)("el-switch"),g=(0,t.g2)("el-popconfirm"),w=(0,t.g2)("el-button-group"),F=(0,t.g2)("scTable"),f=(0,t.g2)("el-main"),v=(0,t.g2)("el-container"),m=(0,t.g2)("saveDialog"),k=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)(t.FK,null,[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(v,null,{default:(0,t.k6)((()=>[(0,t.bF)(b,null,{default:(0,t.k6)((()=>[(0,t.Lk)("div",o,[(0,t.bF)(d,{type:"primary",icon:"el-icon-plus",onClick:l[0]||(l[0]=e=>c.add())},{default:(0,t.k6)((()=>[(0,t.eW)("新增")])),_:1})]),(0,t.Lk)("div",i,[(0,t.Lk)("div",s,[(0,t.bF)(p,{modelValue:n.jObjectSearch.search,"onUpdate:modelValue":l[1]||(l[1]=e=>n.jObjectSearch.search=e),placeholder:"名称/地址",clearable:""},null,8,["modelValue"]),(0,t.bF)(d,{type:"primary",icon:"el-icon-search",onClick:c.upsearch},{default:(0,t.k6)((()=>[(0,t.eW)(" 查询")])),_:1},8,["onClick"])])])])),_:1}),(0,t.bF)(f,{class:"nopadding"},{default:(0,t.k6)((()=>[(0,t.bF)(F,{ref:"table",apiObj:n.apiObj,border:"",params:{jObjectSearch:n.jObjectSearch},stripe:"",remoteSort:"",remoteFilter:""},{default:(0,t.k6)((()=>[(0,t.bF)(h,{type:"selection",width:"50"}),(0,t.bF)(h,{label:"打码名称",prop:"FName",align:"center",width:"130","show-overflow-tooltip":""}),(0,t.bF)(h,{label:"请求地址",prop:"FUrl",align:"center","show-overflow-tooltip":""}),(0,t.bF)(h,{label:"请求方式",prop:"FMethodName",align:"center",width:"100","show-overflow-tooltip":""}),(0,t.bF)(h,{label:"gt键值",prop:"FGt",align:"center",width:"100","show-overflow-tooltip":""}),(0,t.bF)(h,{label:"challenge键值",prop:"FChallenge",align:"center",width:"130","show-overflow-tooltip":""}),(0,t.bF)(h,{label:"秘钥键值",prop:"FKey",align:"center",width:"100","show-overflow-tooltip":""}),(0,t.bF)(h,{label:"密钥数值",prop:"FKeyValue",align:"center","show-overflow-tooltip":""}),(0,t.bF)(h,{label:"成功键值",prop:"FSuccess",align:"center",width:"130","show-overflow-tooltip":""}),(0,t.bF)(h,{label:"成功代码",prop:"FSuccessCode",align:"center",width:"100","show-overflow-tooltip":""}),(0,t.bF)(h,{label:"validate键值",prop:"FResultValidate",align:"center",width:"130","show-overflow-tooltip":""}),(0,t.bF)(h,{label:"是否启用",prop:"FEnable",align:"center",width:"100"},{default:(0,t.k6)((e=>[(0,t.bF)(u,{modelValue:e.row.FEnable,"onUpdate:modelValue":l=>e.row.FEnable=l,onChange:l=>c.enableSwitch(l,e.row),loading:e.row.$enable,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue","onChange","loading"])])),_:1}),(0,t.bF)(h,{label:"操作",fixed:"right","header-align":"center",align:"left",width:"130"},{default:(0,t.k6)((e=>["http://127.0.0.1:1011/geetest3_combination"!=e.row.FUrl?((0,t.uX)(),(0,t.Wv)(w,{key:0},{default:(0,t.k6)((()=>[(0,t.bF)(d,{text:"",type:"primary",size:"small",onClick:l=>c.edit(e.row,e.$index)},{default:(0,t.k6)((()=>[(0,t.eW)("编辑")])),_:2},1032,["onClick"]),(0,t.bF)(g,{title:"确定删除吗？",onConfirm:l=>c.del(e.row,e.$index)},{reference:(0,t.k6)((()=>[(0,t.bF)(d,{text:"",type:"danger",size:"small"},{default:(0,t.k6)((()=>[(0,t.eW)("删除")])),_:1})])),_:2},1032,["onConfirm"])])),_:2},1024)):(0,t.Q3)("",!0)])),_:1})])),_:1},8,["apiObj","params"])])),_:1})])),_:1})),[[k,n.updateLoading]]),n.dialog.save?((0,t.uX)(),(0,t.Wv)(m,{key:0,ref:"saveDialog",onSuccess:c.upsearch,onClosed:l[2]||(l[2]=e=>n.dialog.save=!1)},null,8,["onSuccess"])):(0,t.Q3)("",!0)],64)}var n=a(4803),c={name:"biliGeetest",components:{saveDialog:n["default"]},data(){return{apiObj:this.$API.biliGeetest.getGeetestList,updateLoading:!1,dialog:{save:!1},jObjectSearch:{search:""}}},async created(){},methods:{add(){this.dialog.save=!0,this.$nextTick((()=>{this.$refs.saveDialog.open("add")}))},edit(e){this.dialog.save=!0,this.$nextTick((()=>{this.$refs.saveDialog.open("edit").setData(e)}))},async del(e){let l=await this.$API.biliGeetest.delGeetest.post({jObjectParam:e});0==l.code?(this.$message.success("操作成功"),this.upsearch()):this.$alert(l.message,"提示",{type:"error"})},async enableSwitch(e,l){l.$enable=!0;let a=await this.$API.biliGeetest.enableGeetest.post({jObjectParam:{Fid:l.Fid,FEnable:l.FEnable}});0==a.code?this.$message.success("操作成功"):this.$alert(a.message,"提示",{type:"error"}),this.upsearch()},upsearch(){this.$refs.table.upData({jObjectSearch:this.jObjectSearch})}}},d=a(6262);const p=(0,d.A)(c,[["render",r]]);var b=p}}]);